location ~ /dbk/(?<proxy>.*) {
    limit_except GET {
        deny all;
    }

    proxy_ssl_server_name on;

    set $api_keys_secret_name "DEBANK_API_KEY";
    proxy_set_header AccessKey $api_keys_secret_value;

    proxy_pass https://pro-openapi.debank.com/v1/$proxy?$args;

    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}
