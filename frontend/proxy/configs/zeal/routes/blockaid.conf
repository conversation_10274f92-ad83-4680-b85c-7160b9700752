location ~ /ba/(?<proxy>.*) {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BLOCKAID_API_KEY";
    proxy_set_header X-Api-Key $api_keys_secret_value;

    proxy_pass https://api.blockaid.io/$proxy;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}