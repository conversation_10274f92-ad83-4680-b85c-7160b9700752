location ~ /bundler/(?<biconomy_network_hex_id>0x[0-9a-fA-F]+) {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_BUNDLER_API_KEY";
    proxy_pass https://bundler.biconomy.io/api/v2/$biconomy_network_number/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}