// eslint-disable-next-line no-restricted-imports
import {
    decodeError<PERSON><PERSON><PERSON> as viemDecodeErrorResult,
    decodeEventLog as viemDecodeEventLog,
    decodeFunctionData as viemDecodeFunctionData,
    decodeFunctionR<PERSON>ult as viemDecodeFunctionResult,
    encodeAbiParameters as viemEncodeAbiParameters,
    encodeFunctionData as viemEncodeFunctionData,
    encodePacked as viemEncodePacked,
} from 'viem/utils'

export const decodeErrorResult = viemDecodeErrorResult
export const encodeAbiParameters = viemEncodeAbiParameters
export const encodeFunctionData = viemEncodeFunctionData
export const decodeFunctionResult = viemDecodeFunctionResult
export const encodePacked = viemEncodePacked
export const decodeFunctionData = viemDecodeFunctionData
export const decodeEventLog = viemDecodeEventLog
