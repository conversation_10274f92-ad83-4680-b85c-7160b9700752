import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { values } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    FetchPortfolioRequest,
    FetchPortfolioResponse,
} from '@zeal/domains/Account/api/fetchAccounts'
import { Address } from '@zeal/domains/Address'
import {
    CardConfig,
    ReadonlySignerIsNotSelectedCardConfig,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SubmittedOfframpTransaction } from '@zeal/domains/Currency/domains/BankTransfer'
import { SubmitedBridgesMap } from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { ConnectionMap } from '@zeal/domains/DApp/domains/ConnectionState'
import { WalletConnectInstanceLoadable } from '@zeal/domains/DApp/domains/WalletConnect/api/fetchWalletConnectInstance'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { AppRating } from '@zeal/domains/Feedback'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { InitialActiveTab, Mode } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { isFunded } from '@zeal/domains/Portfolio/helpers/IsFunded'
import {
    BankTransferInfo,
    BrowserTabState,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { Submited } from '@zeal/domains/TransactionRequest'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { StrippedHomeScreenWithBrowserTab } from './StrippedHomeScreenWithBrowserTab'
import { TabController } from './TabController'

type Props = {
    portfolioLoadable: LoadedReloadableData<
        FetchPortfolioResponse,
        FetchPortfolioRequest
    >
    account: Account
    accountsMap: AccountsMap
    appBrowserProviderScript: string | null
    bankTransferInfo: BankTransferInfo
    browserTabState: BrowserTabState
    cardConfig: CardConfig
    connections: ConnectionMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    earnTakerMetrics: EarnTakerMetrics
    encryptedPassword: string
    feePresetMap: FeePresetMap
    installationCampaign: string | null
    gasCurrencyPresetMap: GasCurrencyPresetMap
    initialActiveTab: InitialActiveTab
    installationId: string
    isEthereumNetworkFeeWarningSeen: boolean
    keyStoreMap: KeyStoreMap
    mode: Mode
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    notificationsConfig: NotificationsConfig
    portfolioMap: PortfolioMap
    sessionPassword: string
    submitedBridgesMap: SubmitedBridgesMap
    submittedOffRampTransactions: SubmittedOfframpTransaction[]
    transactionRequests: Record<Address, Submited[]>
    walletConnectInstanceLoadable: WalletConnectInstanceLoadable
    refreshContainerState: RefreshContainerState
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    referralConfig: ReferralConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof TabController>
    | Extract<
          MsgOf<typeof StrippedHomeScreenWithBrowserTab>,
          {
              type:
                  | 'on_virtual_card_order_created_animation_completed'
                  | 'add_wallet_clicked'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'account_item_clicked'
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_card_disconnected'
                  | 'import_card_owner_clicked'
                  | 'on_notifications_config_changed'
                  | 'on_switch_card_new_card_selected'
                  | 'on_account_label_change_submit'
                  | 'confirm_account_delete_click'
                  | 'on_rewards_warning_confirm_account_delete_click'
                  | 'on_recovery_kit_setup'
                  | 'on_add_private_key_click'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_bank_transfer_selected'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_select_rpc_click'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'transaction_cancel_failure_accepted'
                  | 'cancel_submitted'
                  | 'transaction_failure_accepted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_earn_configured'
                  | 'on_earn_deposit_success'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_switch_bank_transfer_provider_clicked'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'on_do_bank_transfer_clicked'
                  | 'on_card_onboarded_account_state_received'
                  | 'on_dissmiss_card_kyc_onboarding_widget_clicked'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_card_transactions_fetch_success'
                  | 'on_cashback_loaded'
                  | 'on_earn_last_recharge_transaction_hash_loaded'
                  | 'on_earn_updated'
                  | 'on_card_onboarded_state_refresh_pulled'
                  | 'on_gnosis_pay_account_created'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_rpc_change_confirmed'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_browser_url_change'
                  | 'on_portfolio_refresh_pulled'
                  | 'on_physical_card_activated_info_screen_closed'
          }
      >

type State =
    | { type: 'full_feature' }
    | {
          type: 'single_non_funded_wallet'
          cardConfig: ReadonlySignerIsNotSelectedCardConfig
      }
    | {
          type: 'single_non_funded_wallet_card_onboarded'
          cardConfig:
              | ReadonlySignerSelectedOnboardedCardConfig
              | ReadonlySignerSelectedCardConfig
      }

const calculateState = ({
    cardConfig,
    accountsMap,
    portfolioLoadable,
    currencyHiddenMap,
    defaultCurrencyConfig,
}: {
    installationId: string
    cardConfig: CardConfig
    accountsMap: AccountsMap
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    portfolioLoadable: LoadedReloadableData<
        FetchPortfolioResponse,
        FetchPortfolioRequest
    >
}): State => {
    const accounts = values(accountsMap)
    const empty = !isFunded({
        portfolio: portfolioLoadable.data.portfolio,
        currencyHiddenMap,
        defaultCurrencyConfig,
    })

    if (accounts.length === 1 && empty) {
        switch (cardConfig.type) {
            case 'card_readonly_signer_address_is_not_selected':
                return {
                    type: 'single_non_funded_wallet',
                    cardConfig,
                }
            case 'card_readonly_signer_address_is_selected':
            case 'card_readonly_signer_address_is_selected_fully_onboarded':
                return {
                    type: 'single_non_funded_wallet_card_onboarded',
                    cardConfig,
                }
            /* istanbul ignore next */
            default:
                return notReachable(cardConfig)
        }
    }

    return { type: 'full_feature' }
}

export const FeatureFork = ({
    initialActiveTab,
    account,
    installationId,
    earnTakerMetrics,
    connections,
    installationCampaign,
    browserTabState,
    portfolioLoadable,
    totalEarningsInDefaultCurrencyMap,
    earnHistoricalTakerUserCurrencyRateMap,
    sessionPassword,
    networkMap,
    currencyHiddenMap,
    currencyPinMap,
    mode,
    walletConnectInstanceLoadable,
    cardConfig,
    isEthereumNetworkFeeWarningSeen,
    celebrationConfig,
    appRating,
    appBrowserProviderScript,
    defaultCurrencyConfig,
    encryptedPassword,
    accountsMap,
    bankTransferInfo,
    submitedBridgesMap,
    feePresetMap,
    gasCurrencyPresetMap,
    customCurrencyMap,
    notificationsConfig,
    keyStoreMap,
    networkRPCMap,
    portfolioMap,
    submittedOffRampTransactions,
    transactionRequests,
    refreshContainerState,
    swapsIOSwapRequestsMap,
    transactionActivitiesCacheMap,
    referralConfig,
    onMsg,
}: Props) => {
    const state = calculateState({
        cardConfig,
        accountsMap,
        portfolioLoadable,
        installationId,
        currencyHiddenMap,
        defaultCurrencyConfig,
    })

    switch (state.type) {
        case 'full_feature':
            return (
                <TabController
                    referralConfig={referralConfig}
                    appRating={appRating}
                    installationCampaign={installationCampaign}
                    celebrationConfig={celebrationConfig}
                    refreshContainerState={refreshContainerState}
                    key={defaultCurrencyConfig.defaultCurrency.id}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialActiveTab={initialActiveTab}
                    totalEarningsInDefaultCurrencyMap={
                        totalEarningsInDefaultCurrencyMap
                    }
                    earnHistoricalTakerUserCurrencyRateMap={
                        earnHistoricalTakerUserCurrencyRateMap
                    }
                    swapsIOSwapRequestsMap={swapsIOSwapRequestsMap}
                    transactionActivitiesCacheMap={
                        transactionActivitiesCacheMap
                    }
                    earnTakerMetrics={earnTakerMetrics}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    notificationsConfig={notificationsConfig}
                    browserTabState={browserTabState}
                    cardConfig={cardConfig}
                    walletConnectInstanceLoadable={
                        walletConnectInstanceLoadable
                    }
                    mode={mode}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    customCurrencyMap={customCurrencyMap}
                    submitedBridgesMap={submitedBridgesMap}
                    connections={connections}
                    encryptedPassword={encryptedPassword}
                    sessionPassword={sessionPassword}
                    keystoreMap={keyStoreMap}
                    networkRPCMap={networkRPCMap}
                    transactionRequests={transactionRequests}
                    submittedOffRampTransactions={submittedOffRampTransactions}
                    portfolioLoadable={portfolioLoadable}
                    account={account}
                    portfolioMap={portfolioMap}
                    accounts={accountsMap}
                    bankTransferInfo={bankTransferInfo}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    appBrowserProviderScript={appBrowserProviderScript}
                    onMsg={onMsg}
                />
            )
        case 'single_non_funded_wallet':
        case 'single_non_funded_wallet_card_onboarded':
            return (
                <StrippedHomeScreenWithBrowserTab
                    historicalTakerUserCurrencyRateMap={
                        earnHistoricalTakerUserCurrencyRateMap
                    }
                    referralConfig={referralConfig}
                    installationCampaign={installationCampaign}
                    portfolioLoadable={portfolioLoadable}
                    refreshContainerState={refreshContainerState}
                    appRating={appRating}
                    celebrationConfig={celebrationConfig}
                    initialActiveTab={initialActiveTab}
                    account={account}
                    accountsMap={accountsMap}
                    cardConfig={state.cardConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    customCurrencyMap={customCurrencyMap}
                    bankTransferInfo={bankTransferInfo}
                    encryptedPassword={encryptedPassword}
                    notificationsConfig={notificationsConfig}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    earnTakerMetrics={earnTakerMetrics}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolio={portfolioLoadable.data.portfolio}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    browserTabState={browserTabState}
                    appBrowserProviderScript={appBrowserProviderScript}
                    mode={mode}
                    connections={connections}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'add_wallet_clicked':
                            case 'on_address_scanned':
                            case 'on_address_scanned_and_add_label':
                            case 'on_ethereum_network_fee_warning_understand_clicked':
                            case 'account_item_clicked':
                            case 'track_wallet_clicked':
                            case 'on_account_create_request':
                            case 'on_accounts_create_success_animation_finished':
                            case 'hardware_wallet_clicked':
                            case 'safe_wallet_clicked':
                            case 'recover_safe_wallet_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_card_disconnected':
                            case 'import_card_owner_clicked':
                            case 'on_notifications_config_changed':
                            case 'on_switch_card_new_card_selected':
                            case 'on_account_label_change_submit':
                            case 'confirm_account_delete_click':
                            case 'on_rewards_warning_confirm_account_delete_click':
                            case 'on_recovery_kit_setup':
                            case 'on_add_private_key_click':
                            case 'on_usd_taker_metrics_loaded':
                            case 'on_eur_taker_metrics_loaded':
                            case 'on_bank_transfer_selected':
                            case 'on_add_label_to_track_only_account_during_send':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_select_rpc_click':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'transaction_cancel_failure_accepted':
                            case 'cancel_submitted':
                            case 'transaction_failure_accepted':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'transaction_submited':
                            case 'on_top_up_transaction_complete_close':
                            case 'on_earn_configured':
                            case 'on_earn_deposit_success':
                            case 'on_swaps_io_swap_request_created':
                            case 'on_create_smart_wallet_clicked':
                            case 'on_switch_bank_transfer_provider_clicked':
                            case 'on_monerium_deposit_success_go_to_wallet_clicked':
                            case 'on_do_bank_transfer_clicked':
                            case 'on_card_onboarded_account_state_received':
                            case 'on_dissmiss_card_kyc_onboarding_widget_clicked':
                            case 'on_card_import_on_import_keys_clicked':
                            case 'on_onboarded_card_imported_success_animation_complete':
                            case 'on_get_cashback_currency_clicked':
                            case 'on_card_transactions_fetch_success':
                            case 'on_cashback_loaded':
                            case 'on_earn_last_recharge_transaction_hash_loaded':
                            case 'on_earn_updated':
                            case 'on_card_onboarded_state_refresh_pulled':
                            case 'on_gnosis_pay_account_created':
                            case 'on_app_rating_submitted':
                            case 'on_cashback_celebration_triggered':
                            case 'on_rpc_change_confirmed':
                            case 'on_card_imported_success_animation_complete':
                            case 'on_browser_url_change':
                            case 'on_portfolio_refresh_pulled':
                            case 'on_send_clicked':
                            case 'on_add_funds_click':
                            case 'on_buy_clicked':
                            case 'on_new_virtual_card_created_successfully':
                            case 'on_dismiss_add_to_wallet_banner_clicked':
                            case 'on_virtual_card_order_created_animation_completed':
                            case 'on_a_reward_claimed_successfully':
                            case 'on_a_rewards_configured':
                            case 'on_physical_card_activated_info_screen_closed':
                            case 'on_earn_recharge_configured':
                            case 'on_reacharege_configured_with_user_preferences':
                            case 'on_disconnect_dapps_click':
                            case 'on_delete_all_dapps_confirm_click':
                            case 'on_default_currency_selected':
                            case 'on_lock_zeal_click':
                            case 'on_open_fullscreen_view_click':
                            case 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found':
                                onMsg(msg)
                                break
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        default:
            return notReachable(state)
    }
}
