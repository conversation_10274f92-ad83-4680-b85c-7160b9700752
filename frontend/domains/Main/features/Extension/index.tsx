import { useCallback } from 'react'

import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { keys, mapValues, values } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import { CARD_TRANSACTIONS_CACHE_COUNT } from '@zeal/domains/Card/constants'
import { CASHBACK_TRANSACTIONS_CACHE_COUNT } from '@zeal/domains/Card/domains/Cashback/constants'
import { getActivationTimeOfFirstCard } from '@zeal/domains/Card/helpers/getActivationTimeOfFirstCard'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { InitialActiveTab, Mode } from '@zeal/domains/Main'
import { sendToActiveTabZWidget } from '@zeal/domains/Main/api/sendToActiveTabZWidget'
import { updateNetworkRPC } from '@zeal/domains/Network/helpers/updateNetworkRPC'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { Storage } from '@zeal/domains/Storage'
import {
    resetUserAReferralConfig,
    setUserAReferralConfig,
} from '@zeal/domains/Storage/api/setUserAReferralConfig'
import { DAppConnectionState } from '@zeal/domains/Storage/domains/DAppConnectionState'
import { addAccountsWithKeystores } from '@zeal/domains/Storage/helpers/addAccountsWithKeystores'
import { changeAccountLabel } from '@zeal/domains/Storage/helpers/changeAccountLabel'
import { dismissSubmittedBridge } from '@zeal/domains/Storage/helpers/dismissSubmittedBridge'
import { lock, logout } from '@zeal/domains/Storage/helpers/logout'
import { removeAccount } from '@zeal/domains/Storage/helpers/removeAccount'
import { removeGasCurrencyPreset } from '@zeal/domains/Storage/helpers/removeGasCurrencyPreset'
import { resetCardConfig } from '@zeal/domains/Storage/helpers/resetCardConfig'
import { saveFeePreset } from '@zeal/domains/Storage/helpers/saveFeePreset'
import { saveGasCurrencyPreset } from '@zeal/domains/Storage/helpers/saveGasCurrencyPreset'
import { toLocalStorage } from '@zeal/domains/Storage/helpers/toLocalStorage'
import { useReloadableStorage } from '@zeal/domains/Storage/hooks/useReloadableStorage'
import { cancelSubmittedToSubmitted } from '@zeal/domains/TransactionRequest/helpers/cancelSubmittedToSubmitted'
import { removeTransactionRequest } from '@zeal/domains/TransactionRequest/helpers/removeTransactionRequest'
import { TransactionActivitiesCache } from '@zeal/domains/Transactions'
import { TRANSACTION_ACTIVITIES_CACHE_COUNT } from '@zeal/domains/Transactions/constants'
import { getTransactionActivitiesCache } from '@zeal/domains/Transactions/helpers/getTransactionActivitiesCache'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { App } from './App'

type Props = {
    mode: Mode
    initialActiveTab: InitialActiveTab
    appBrowserProviderScript: string | null
    onMsg: (msg: Msg) => void
}

type Msg = Extract<
    MsgOf<typeof App>,
    {
        type:
            | 'add_wallet_clicked'
            | 'hardware_wallet_clicked'
            | 'import_card_owner_clicked'
            | 'import_keys_button_clicked'
            | 'on_accounts_create_success_animation_finished'
            | 'on_add_funds_click'
            | 'on_add_private_key_click'
            | 'on_address_scanned'
            | 'on_bank_transfer_selected'
            | 'on_bridge_clicked'
            | 'on_buy_clicked'
            | 'on_card_import_on_import_keys_clicked'
            | 'on_create_smart_wallet_clicked'
            | 'on_earn_deposit_success'
            | 'on_external_earn_deposit_completed_close_click'
            | 'on_get_cashback_currency_clicked'
            | 'on_get_started_clicked'
            | 'on_kyc_try_again_clicked'
            | 'on_meta_mask_mode_changed_pupup_refresh_page_clicked'
            | 'on_nba_cta_click'
            | 'on_open_fullscreen_view_click'
            | 'on_recovery_kit_setup'
            | 'on_send_clicked'
            | 'on_send_nft_click'
            | 'on_swap_clicked'
            | 'on_switch_bank_transfer_provider_clicked'
            | 'on_zwidget_expand_request'
            | 'recover_safe_wallet_clicked'
            | 'safe_wallet_clicked'
            | 'track_wallet_clicked'
            | 'on_address_scanned_and_add_label'
            | 'on_do_bank_transfer_clicked'
            | 'on_card_order_pending_payment_clicked'
            | 'on_physical_card_activated_info_screen_closed_wallet_not_funded'
    }
>

// TODO @resetko-zeal check why can't we use `calculateStorageState` helper
const calculateState = ({
    storage,
    sessionPassword,
}: {
    storage: Storage | null
    sessionPassword: string | null
}):
    | {
          type: 'get_started_splash_screen'
      }
    | {
          type: 'lock_screen'
          encryptedPassword: string
          storage: Storage
      }
    | {
          type: 'app'
          sessionPassword: string
          selectedAddress: Web3.address.Address
          storage: Storage
      } => {
    if (!storage || !storage.selectedAddress) {
        return { type: 'get_started_splash_screen' }
    }

    if (!sessionPassword) {
        return {
            type: 'lock_screen',
            storage,
            encryptedPassword: storage.encryptedPassword,
        }
    }

    return {
        type: 'app',
        storage,
        sessionPassword,
        selectedAddress: storage.selectedAddress as Web3.address.Address,
    }
}

export const Main = ({
    mode,
    initialActiveTab,
    onMsg,
    appBrowserProviderScript,
}: Props) => {
    const [loadable, setLoadable] = useReloadableStorage({
        type: 'loading',
        params: undefined,
    })

    const writeToLocalStorage = useCallback(
        async (writeFn: (_: Storage) => Storage | null) => {
            switch (loadable.type) {
                case 'error':
                case 'loading':
                    captureError(
                        new ImperativeError(
                            'Impossible state: Trying to write to storage when not yet read'
                        )
                    )
                    break

                case 'loaded':
                case 'reloading':
                case 'subsequent_failed':
                    const state = calculateState({
                        sessionPassword: loadable.data.sessionPassword,
                        storage: loadable.data.storage,
                    })

                    switch (state.type) {
                        case 'get_started_splash_screen':
                        case 'lock_screen':
                            captureError(
                                new ImperativeError(
                                    'Impossible state: Trying to write to storage when its not unlocked or missing',
                                    {
                                        type: state.type,
                                    }
                                )
                            )
                            break

                        case 'app':
                            const newStorage = writeFn(state.storage)
                            setLoadable({
                                ...loadable,
                                data: {
                                    ...loadable.data,
                                    storage: newStorage,
                                },
                            })
                            if (newStorage) {
                                await toLocalStorage(newStorage)
                            } else {
                                await logout()
                            }
                            break

                        default:
                            notReachable(state)
                    }

                    break

                default:
                    return notReachable(loadable)
            }
        },
        [setLoadable, loadable]
    )

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout title={null} actionBar={null} onClose={noop} />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={null}
                        onClose={noop}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId="no_installation_on_storage_error"
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'reloading':
        case 'subsequent_failed':
        case 'loaded':
            return (
                <App
                    appBrowserProviderScript={appBrowserProviderScript}
                    mode={mode}
                    initialActiveTab={initialActiveTab}
                    networkMap={loadable.data.networkMap}
                    installationId={loadable.data.installationId}
                    installationCampaign={loadable.data.installationCampaign}
                    storage={loadable.data.storage}
                    sessionPassword={loadable.data.sessionPassword}
                    referralConfig={loadable.data.referralConfig}
                    onMsg={async (msg) => {
                        const installationId = loadable.data.installationId

                        switch (msg.type) {
                            case 'on_add_private_key_click':
                            case 'track_wallet_clicked':
                            case 'hardware_wallet_clicked':
                            case 'add_wallet_clicked':
                            case 'on_bank_transfer_selected':
                            case 'safe_wallet_clicked':
                            case 'recover_safe_wallet_clicked':
                            case 'on_recovery_kit_setup':
                            case 'on_send_nft_click':
                            case 'on_swap_clicked':
                            case 'on_buy_clicked':
                            case 'on_bridge_clicked':
                            case 'on_send_clicked':
                            case 'on_kyc_try_again_clicked':
                            case 'on_open_fullscreen_view_click':
                            case 'on_zwidget_expand_request':
                            case 'import_keys_button_clicked':
                            case 'on_earn_deposit_success':
                            case 'on_get_cashback_currency_clicked':
                            case 'import_card_owner_clicked':
                            case 'on_card_import_on_import_keys_clicked':
                            case 'on_create_smart_wallet_clicked':
                            case 'on_address_scanned':
                            case 'on_add_funds_click':
                            case 'on_accounts_create_success_animation_finished':
                            case 'on_switch_bank_transfer_provider_clicked':
                            case 'on_meta_mask_mode_changed_pupup_refresh_page_clicked':
                            case 'on_get_started_clicked':
                            case 'on_do_bank_transfer_clicked':
                                onMsg(msg)
                                break

                            case 'on_a_rewards_configured':
                                await setUserAReferralConfig({
                                    userAReferralConfig:
                                        msg.userAReferralConfig,
                                })
                                break

                            case 'on_a_reward_claimed_successfully':
                                await writeToLocalStorage((storage) => {
                                    const existingCache =
                                        getTransactionActivitiesCache({
                                            address: msg.ownerAddress,
                                            transactionActivitiesCacheMap:
                                                storage.transactionActivitiesCacheMap,
                                        })

                                    return {
                                        ...storage,
                                        transactionActivitiesCacheMap: {
                                            ...storage.transactionActivitiesCacheMap,
                                            [msg.ownerAddress]: {
                                                ...existingCache,
                                                pendingARewardClaimTransactionActivity:
                                                    msg.pendingActivityItem,
                                            },
                                        },
                                    }
                                })
                                break

                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                // We don't change storage for safe transaction completeion because we don't store transaction requests for safe transactions
                                break

                            case 'on_earn_last_recharge_transaction_hash_loaded':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected':
                                            captureError(
                                                new ImperativeError(
                                                    `try to save card config last trx hash but storage is ${storage.cardConfig.type}`
                                                )
                                            )
                                            return storage
                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    lastRechargeTransactionHash:
                                                        msg.cardConfig
                                                            .lastRechargeTransactionHash,
                                                },
                                            }
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break

                            case 'transaction_request_completed':
                            case 'transaction_request_failed':
                            case 'transaction_request_replaced':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_submited':
                            case 'cancel_submitted':
                            case 'transaction_failure_accepted':
                                await writeToLocalStorage((storage) => {
                                    const from =
                                        msg.transactionRequest.account.address

                                    const currentRequests =
                                        storage.transactionRequests[from] ||
                                        null

                                    return {
                                        ...storage,
                                        transactionRequests: {
                                            ...storage.transactionRequests,
                                            [from]: removeTransactionRequest(
                                                currentRequests,
                                                msg.transactionRequest
                                            ),
                                        },
                                    }
                                })
                                break

                            case 'on_ethereum_network_fee_warning_understand_clicked':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    isEthereumNetworkFeeWarningSeen: true,
                                }))
                                break

                            case 'on_address_scanned_and_add_label':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await writeToLocalStorage((storage) =>
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        storage.selectedAddress
                                    )
                                )
                                onMsg(msg)
                                break

                            case 'on_withdrawal_monitor_fiat_transaction_success':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    submittedOffRampTransactions:
                                        storage.submittedOffRampTransactions.filter(
                                            (submitted) =>
                                                submitted.transactionHash !==
                                                msg.event.transactionHash
                                        ),
                                }))
                                break

                            case 'on_token_pin_click':
                                postUserEvent({
                                    type: 'AssetStarredEvent',
                                    assetType: 'token',
                                    installationId,
                                })
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    currencyPinMap: {
                                        ...storage.currencyPinMap,
                                        [msg.currency.id]: true,
                                    },
                                }))
                                break
                            case 'on_eur_taker_metrics_loaded':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    earnTakerMetrics: {
                                        ...storage.earnTakerMetrics,
                                        eur: msg.metrics,
                                    },
                                }))
                                break

                            case 'on_usd_taker_metrics_loaded':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    earnTakerMetrics: {
                                        ...storage.earnTakerMetrics,
                                        usd: msg.metrics,
                                    },
                                }))
                                break

                            case 'on_token_un_pin_click':
                                postUserEvent({
                                    type: 'AssetUnstarredEvent',
                                    assetType: 'token',
                                    installationId,
                                })
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    currencyPinMap: {
                                        ...storage.currencyPinMap,
                                        [msg.currency.id]: false,
                                    },
                                }))

                                break

                            case 'on_token_hide_click':
                                postUserEvent({
                                    type: 'AssetHiddenEvent',
                                    assetType: 'token',
                                    installationId,
                                })
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    currencyHiddenMap: {
                                        ...storage.currencyHiddenMap,
                                        [msg.token.balance.currency.id]: true,
                                    },
                                }))
                                break

                            case 'on_token_un_hide_click':
                                postUserEvent({
                                    type: 'AssetUnhiddenEvent',
                                    assetType: 'token',
                                    installationId,
                                })

                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    currencyHiddenMap: {
                                        ...storage.currencyHiddenMap,
                                        [msg.token.balance.currency.id]: false,
                                    },
                                }))
                                break

                            case 'on_dismiss_kyc_button_clicked':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.bankTransferInfo.type) {
                                        case 'not_started':
                                            captureError(
                                                new ImperativeError(
                                                    'Dismiss KYC button clicked when bank transfer not started'
                                                )
                                            )
                                            return storage

                                        case 'unblock_user_created':
                                            return {
                                                ...storage,
                                                bankTransferInfo: {
                                                    ...storage.bankTransferInfo,
                                                    sumSubAccessToken: null,
                                                },
                                            }
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(
                                                storage.bankTransferInfo
                                            )
                                    }
                                })
                                break

                            case 'on_delete_all_dapps_confirm_click':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    dApps: mapValues(
                                        storage.dApps,
                                        (_, connection) => ({
                                            type: 'disconnected',
                                            networkHexId:
                                                connection.networkHexId,
                                            dApp: connection.dApp,
                                            version: 1,
                                        })
                                    ),
                                }))
                                break

                            case 'on_disconnect_dapps_click':
                                await writeToLocalStorage((storage) => {
                                    const newApps = mapValues(
                                        storage.dApps,
                                        (
                                            _,
                                            connection: DAppConnectionState
                                        ): DAppConnectionState => {
                                            const { hostname } = connection.dApp
                                            const { dAppHostNames } = msg

                                            if (
                                                !dAppHostNames.includes(
                                                    hostname
                                                )
                                            ) {
                                                return connection
                                            }

                                            return {
                                                type: 'disconnected',
                                                version: 1,
                                                dApp: connection.dApp,
                                                networkHexId:
                                                    connection.networkHexId,
                                            }
                                        }
                                    )

                                    return {
                                        ...storage,
                                        dApps: newApps,
                                    }
                                })
                                break

                            case 'on_profile_change_confirm_click':
                                await writeToLocalStorage((storage) => {
                                    const account =
                                        storage.accounts[msg.account.address]
                                    if (!account) {
                                        throw new ImperativeError(
                                            'no account by address'
                                        )
                                    }
                                    return {
                                        ...storage,
                                        accounts: {
                                            ...storage.accounts,
                                            [account.address]: {
                                                ...account,
                                                avatarSrc: msg.src,
                                            },
                                        },
                                    }
                                })
                                break

                            case 'on_custom_currency_update_request':
                                await writeToLocalStorage((storage) => {
                                    return {
                                        ...storage,
                                        customCurrencies: {
                                            ...storage.customCurrencies,
                                            [msg.currency.id]: msg.currency,
                                        },
                                    }
                                })
                                break

                            case 'on_custom_currency_delete_request':
                                await writeToLocalStorage((storage) => {
                                    const customCurrencies = {
                                        ...storage.customCurrencies,
                                    }
                                    delete customCurrencies[msg.currency.id]
                                    return {
                                        ...storage,
                                        customCurrencies,
                                    }
                                })
                                break

                            case 'on_account_label_change_submit':
                                await writeToLocalStorage((storage) =>
                                    changeAccountLabel(
                                        storage,
                                        msg.account,
                                        msg.label
                                    )
                                )
                                break

                            case 'on_account_create_request':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )

                                await writeToLocalStorage((storage) =>
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                break

                            case 'portfolio_loaded':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    portfolios: {
                                        ...storage.portfolios,
                                        [msg.address]: msg.portfolio,
                                    },
                                    fetchedAt: msg.fetchedAt,
                                }))
                                break

                            case 'account_item_clicked':
                                const result = await sendToActiveTabZWidget({
                                    type: 'extension_to_zwidget_extension_address_change',
                                    address: msg.account.address,
                                })

                                switch (result.type) {
                                    case 'zwidget_not_active':
                                        await writeToLocalStorage(
                                            (storage) => ({
                                                ...storage,
                                                selectedAddress:
                                                    msg.account.address,
                                            })
                                        )
                                        break
                                    case 'message_sent_to_zwidget':
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(result)
                                }
                                break

                            case 'confirm_account_delete_click': {
                                await writeToLocalStorage((storage) =>
                                    removeAccount(storage, msg.account)
                                )
                                break
                            }

                            case 'on_rewards_warning_confirm_account_delete_click':
                                await writeToLocalStorage((storage) =>
                                    removeAccount(storage, msg.account)
                                )

                                if (
                                    msg.account.address ===
                                    msg.userAReferralConfig.ownerAddress
                                ) {
                                    await resetUserAReferralConfig()
                                }
                                break

                            case 'on_lock_zeal_click':
                                lock().catch(captureError)
                                break

                            case 'bridge_completed':
                                await writeToLocalStorage((storage) => {
                                    const fromAddress =
                                        msg.bridgeSubmitted.fromAddress

                                    const currentSubmitedBridges =
                                        storage.submitedBridges[fromAddress] ||
                                        []

                                    return {
                                        ...storage,
                                        submitedBridges: {
                                            ...storage.submitedBridges,
                                            [fromAddress]:
                                                currentSubmitedBridges.filter(
                                                    (bridge) =>
                                                        bridge.submittedBridge
                                                            .sourceTransactionHash !==
                                                        msg.bridgeSubmitted
                                                            .sourceTransactionHash
                                                ),
                                        },
                                    }
                                })
                                break

                            case 'on_dismiss_bridge_widget_click':
                                await writeToLocalStorage((storage) =>
                                    dismissSubmittedBridge(
                                        msg.bridgeSubmitted,
                                        storage
                                    )
                                )
                                break

                            case 'on_rpc_change_confirmed':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    networkRPCMap: {
                                        ...storage.networkRPCMap,
                                        [msg.network.hexChainId]:
                                            updateNetworkRPC({
                                                network: msg.network,
                                                initialRPCUrl:
                                                    msg.initialRPCUrl,
                                                networkRPCMap:
                                                    storage.networkRPCMap,
                                                rpcUrl: msg.rpcUrl,
                                            }),
                                    },
                                }))
                                break

                            case 'on_select_rpc_click':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    networkRPCMap: {
                                        ...storage.networkRPCMap,
                                        [msg.network.hexChainId]:
                                            msg.networkRPC,
                                    },
                                }))
                                break

                            case 'on_monerium_deposit_success_go_to_wallet_clicked':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    selectedAddress: msg.account.address,
                                }))
                                break

                            case 'on_add_label_to_track_only_account_during_send':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await writeToLocalStorage((storage) =>
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        storage.selectedAddress
                                    )
                                )
                                break

                            case 'transaction_cancel_failure_accepted': {
                                const { transactionRequest } = msg
                                const fromAddress =
                                    transactionRequest.account.address

                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    transactionRequests: {
                                        [fromAddress]: removeTransactionRequest(
                                            storage.transactionRequests[
                                                fromAddress
                                            ],
                                            cancelSubmittedToSubmitted(
                                                msg.transactionRequest
                                            )
                                        ),
                                    },
                                }))
                                break
                            }

                            case 'on_predefined_fee_preset_selected':
                                await writeToLocalStorage((storage) =>
                                    saveFeePreset({
                                        storage,
                                        feePreset: msg.preset,
                                        networkHexId: msg.networkHexId,
                                    })
                                )
                                break

                            case 'on_4337_auto_gas_token_selection_clicked':
                                await writeToLocalStorage((storage) =>
                                    removeGasCurrencyPreset({
                                        networkHexId: msg.network.hexChainId,
                                        storage,
                                    })
                                )
                                break

                            case 'on_4337_gas_currency_selected':
                                await writeToLocalStorage((storage) =>
                                    saveGasCurrencyPreset({
                                        storage,
                                        currencyId: msg.selectedGasCurrency.id,
                                        networkHexId:
                                            msg.selectedGasCurrency
                                                .networkHexChainId,
                                    })
                                )

                                break

                            case 'on_virtual_card_order_created_animation_completed':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                            captureError(
                                                new ImperativeError(
                                                    'got on_virtual_card_order_created_animation_completed in card_readonly_signer_address_is_not_selected'
                                                )
                                            )
                                            return storage

                                        case 'card_readonly_signer_address_is_selected':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    country:
                                                        msg.gnosisAccountState
                                                            .residentialAddress
                                                            ?.country || null,
                                                    cashback: {
                                                        type: 'not_eligible_for_cashback',
                                                    },
                                                    type: 'card_readonly_signer_address_is_selected_fully_onboarded',
                                                    lastSeenSafeAddress:
                                                        msg.gnosisAccountState
                                                            .cardSafe.address,
                                                    currency:
                                                        msg.gnosisAccountState
                                                            .cardSafe
                                                            .cryptoCurrency,
                                                    selectedCardId: null,
                                                    dissmissedAddToWalletBanner:
                                                        false,
                                                    cardTransactionsCache: null,
                                                    lastRechargeTransactionHash:
                                                        null,
                                                    lastDismissedKycBannerState:
                                                        null,
                                                    isCreatedViaZeal:
                                                        msg.gnosisAccountState
                                                            .isCreatedViaZeal,
                                                    userId: msg
                                                        .gnosisAccountState
                                                        .userId,
                                                    rewards: {
                                                        type: 'in_progress',
                                                        firstCardActivatedTimestampMS:
                                                            Date.now(),
                                                        spent: {
                                                            amount: 0n,
                                                            currency:
                                                                msg
                                                                    .gnosisAccountState
                                                                    .cardSafe
                                                                    .cryptoCurrency,
                                                        },
                                                    },
                                                },
                                            }

                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    type: 'card_readonly_signer_address_is_selected_fully_onboarded',
                                                    lastSeenSafeAddress:
                                                        msg.gnosisAccountState
                                                            .cardSafe.address,
                                                    currency:
                                                        msg.gnosisAccountState
                                                            .cardSafe
                                                            .cryptoCurrency,
                                                },
                                            }

                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break
                            case 'on_card_onboarded_account_state_received':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                            captureError(
                                                new ImperativeError(
                                                    'got on_card_onboarded_account_state_received in card_readonly_signer_address_is_not_selected'
                                                )
                                            )
                                            return storage

                                        case 'card_readonly_signer_address_is_selected':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    country:
                                                        msg.gnosisAccountState
                                                            .residentialAddress
                                                            ?.country || null,
                                                    cashback: {
                                                        type: 'not_eligible_for_cashback',
                                                    },
                                                    type: 'card_readonly_signer_address_is_selected_fully_onboarded',
                                                    lastSeenSafeAddress:
                                                        msg.gnosisAccountState
                                                            .cardSafe.address,
                                                    currency:
                                                        msg.gnosisAccountState
                                                            .cardSafe
                                                            .cryptoCurrency,
                                                    selectedCardId: null,
                                                    dissmissedAddToWalletBanner:
                                                        false,
                                                    cardTransactionsCache: null,
                                                    lastRechargeTransactionHash:
                                                        null,
                                                    lastDismissedKycBannerState:
                                                        null,
                                                    userId: msg
                                                        .gnosisAccountState
                                                        .userId,
                                                    rewards: msg
                                                        .gnosisAccountState
                                                        .isCreatedViaZeal
                                                        ? {
                                                              type: 'in_progress',
                                                              firstCardActivatedTimestampMS:
                                                                  getActivationTimeOfFirstCard(
                                                                      {
                                                                          gnosisPayAccountState:
                                                                              msg.gnosisAccountState,
                                                                      }
                                                                  ),
                                                              spent: {
                                                                  amount: 0n,
                                                                  currency:
                                                                      msg
                                                                          .gnosisAccountState
                                                                          .cardSafe
                                                                          .cryptoCurrency,
                                                              },
                                                          }
                                                        : {
                                                              type: 'not_eligible',
                                                          },
                                                    isCreatedViaZeal:
                                                        msg.gnosisAccountState
                                                            .isCreatedViaZeal,
                                                },
                                            }

                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    type: 'card_readonly_signer_address_is_selected_fully_onboarded',
                                                    lastSeenSafeAddress:
                                                        msg.gnosisAccountState
                                                            .cardSafe.address,
                                                    currency:
                                                        msg.gnosisAccountState
                                                            .cardSafe
                                                            .cryptoCurrency,
                                                },
                                            }

                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break

                            case 'on_card_imported_success_animation_complete':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    cardConfig: {
                                        type: 'card_readonly_signer_address_is_selected',
                                        readonlySignerAddress: msg
                                            .cardReadonlySigner
                                            .address as Web3.address.Address,
                                        lastDismissedOnboardingBannerState:
                                            null,
                                        rechargePreferences: null,
                                    },
                                }))
                                break
                            case 'on_gnosis_pay_account_created':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    cardConfig: {
                                        type: 'card_readonly_signer_address_is_selected',
                                        readonlySignerAddress: msg
                                            .cardReadonlySigner
                                            .address as Web3.address.Address,
                                        lastDismissedOnboardingBannerState:
                                            null,
                                        rechargePreferences:
                                            msg.rechargePreferences,
                                    },
                                }))
                                break
                            case 'on_dissmiss_card_kyc_onboarding_widget_clicked':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            return storage
                                        case 'card_readonly_signer_address_is_selected':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    type: 'card_readonly_signer_address_is_selected',
                                                    readonlySignerAddress:
                                                        storage.cardConfig
                                                            .readonlySignerAddress,
                                                    lastDismissedOnboardingBannerState:
                                                        msg.state,
                                                    rechargePreferences:
                                                        storage.cardConfig
                                                            .rechargePreferences,
                                                },
                                            }

                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break

                            case 'on_dissmiss_card_kyc_onboarded_widget_clicked':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected':
                                            return storage
                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    lastDismissedKycBannerState:
                                                        msg.kycStatus,
                                                },
                                            }

                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break

                            case 'on_onboarded_card_imported_success_animation_complete':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    cardConfig: {
                                        lastSeenSafeAddress:
                                            msg.lastSeenSafeAddress,
                                        type: 'card_readonly_signer_address_is_selected_fully_onboarded',
                                        readonlySignerAddress: msg
                                            .cardReadonlySigner
                                            .address as Web3.address.Address,
                                        selectedCardId: msg.selectedCardId,
                                        cardTransactionsCache: null,
                                        lastRechargeTransactionHash: null,
                                        currency: msg.currency,
                                        dissmissedAddToWalletBanner: false,
                                        lastDismissedKycBannerState: null,
                                        country: msg.country,
                                        cashback: {
                                            type: 'not_eligible_for_cashback',
                                        },
                                        isCreatedViaZeal: msg.isCreatedViaZeal,
                                        userId: msg.userId,
                                        rewards: msg.reward,
                                    },
                                }))
                                break

                            case 'on_browser_url_change':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    browserTabState: {
                                        lastVisitedURL: msg.newUrl,
                                    },
                                }))
                                break

                            case 'on_historical_taker_user_currency_rate_fetched':
                                await writeToLocalStorage((storage) => {
                                    const updatedMap = keys(
                                        msg.earnHistoricalTakerUserCurrencyRateMap
                                    ).reduce((map, takerType) => {
                                        map[takerType] = {
                                            ...map[takerType],
                                            ...msg
                                                .earnHistoricalTakerUserCurrencyRateMap[
                                                takerType
                                            ],
                                        }
                                        return map
                                    }, storage.earnHistoricalTakerUserCurrencyRateMap)

                                    return {
                                        ...storage,
                                        earnHistoricalTakerUserCurrencyRateMap:
                                            updatedMap,
                                    }
                                })
                                break
                            case 'on_swaps_io_swap_requests_fetched':
                                await writeToLocalStorage((storage) => {
                                    const updatedMap: SwapsIOSwapRequestsMap = {
                                        ...storage.swapsIOSwapRequestsMap,
                                        [msg.address]: values(
                                            [
                                                ...(storage
                                                    .swapsIOSwapRequestsMap[
                                                    msg.address
                                                ] || []),
                                                ...msg.swapsIOSwapRequests,
                                            ].reduce(
                                                (acc, swapRequest) => ({
                                                    ...acc,
                                                    [swapRequest.hash]:
                                                        swapRequest,
                                                }),
                                                {}
                                            )
                                        ),
                                    }

                                    return {
                                        ...storage,
                                        swapsIOSwapRequestsMap: updatedMap,
                                    }
                                })
                                break
                            case 'on_swaps_io_transaction_activity_swap_started':
                            case 'on_swaps_io_transaction_activity_failed':
                            case 'on_swaps_io_transaction_activity_completed':
                                await writeToLocalStorage((storage) => {
                                    const updatedMap: SwapsIOSwapRequestsMap = {
                                        ...storage.swapsIOSwapRequestsMap,
                                        [msg.transaction.swapsIOSwapRequest
                                            .sender]: (
                                            storage.swapsIOSwapRequestsMap[
                                                msg.transaction
                                                    .swapsIOSwapRequest.sender
                                            ] || []
                                        ).map((currentSwapRequest) =>
                                            currentSwapRequest.hash ===
                                            msg.transaction.swapsIOSwapRequest
                                                .hash
                                                ? msg.transaction
                                                      .swapsIOSwapRequest
                                                : currentSwapRequest
                                        ),
                                    }

                                    return {
                                        ...storage,
                                        swapsIOSwapRequestsMap: updatedMap,
                                    }
                                })
                                break
                            case 'on_swaps_io_swap_request_created':
                                await writeToLocalStorage((storage) => {
                                    const updatedMap: SwapsIOSwapRequestsMap = {
                                        ...storage.swapsIOSwapRequestsMap,
                                        [msg.request.sender]: [
                                            ...(storage.swapsIOSwapRequestsMap[
                                                msg.request.sender
                                            ] || []),
                                            msg.request,
                                        ],
                                    }

                                    return {
                                        ...storage,
                                        swapsIOSwapRequestsMap: updatedMap,
                                    }
                                })
                                break
                            case 'on_earnings_fetched':
                                await writeToLocalStorage((storage) => {
                                    const updatedMap = keys(
                                        msg.newRates
                                    ).reduce((map, takerType) => {
                                        map[takerType] = {
                                            ...map[takerType],
                                            ...msg.newRates[takerType],
                                        }
                                        return map
                                    }, storage.earnHistoricalTakerUserCurrencyRateMap)

                                    return {
                                        ...storage,
                                        earnHistoricalTakerUserCurrencyRateMap:
                                            updatedMap,
                                        totalEarningsInDefaultCurrencyMap: {
                                            ...storage.totalEarningsInDefaultCurrencyMap,
                                            [msg.address]:
                                                msg.totalEarningsInDefaultCurrency,
                                        },
                                    }
                                })
                                break
                            case 'on_physical_card_activated_info_screen_closed_wallet_not_funded':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected':
                                            return storage
                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    selectedCardId: msg.card.id,
                                                },
                                            }

                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                onMsg(msg)
                                break
                            case 'on_physical_card_activated_info_screen_closed':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected':
                                            return storage
                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    selectedCardId: msg.card.id,
                                                },
                                            }

                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break
                            case 'on_switch_card_new_card_selected':
                            case 'on_new_virtual_card_created_successfully':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected':
                                            captureError(
                                                new ImperativeError(
                                                    'Selected card switched when card signer address is not selected or not fully onboarded'
                                                )
                                            )
                                            return storage
                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    selectedCardId: msg.cardId,
                                                },
                                            }

                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break

                            case 'on_card_transactions_fetch_success':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected':
                                            captureError(
                                                new ImperativeError(
                                                    'Card transactions fetch success when card is not onboarded'
                                                )
                                            )
                                            return storage

                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    cardTransactionsCache:
                                                        msg.transactions.slice(
                                                            0,
                                                            CARD_TRANSACTIONS_CACHE_COUNT
                                                        ),
                                                },
                                            }

                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break

                            case 'on_cashback_loaded':
                                await writeToLocalStorage((storage) => {
                                    const cardConfig = storage.cardConfig
                                    switch (cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected':
                                            return storage

                                        case 'card_readonly_signer_address_is_selected_fully_onboarded': {
                                            switch (msg.cardCashBack.type) {
                                                case 'eligible_for_cashback_has_cashback':
                                                    return {
                                                        ...storage,
                                                        cardConfig: {
                                                            ...cardConfig,
                                                            cashback: {
                                                                ...msg.cardCashBack,
                                                                cashbackTransactions:
                                                                    msg.cardCashBack.cashbackTransactions.slice(
                                                                        0,
                                                                        CASHBACK_TRANSACTIONS_CACHE_COUNT
                                                                    ),
                                                            },
                                                        },
                                                    }

                                                case 'eligible_for_cashback_no_cashback':
                                                case 'not_eligible_for_cashback':
                                                    return {
                                                        ...storage,
                                                        cardConfig: {
                                                            ...cardConfig,
                                                            cashback:
                                                                msg.cardCashBack,
                                                        },
                                                    }

                                                default:
                                                    return notReachable(
                                                        msg.cardCashBack
                                                    )
                                            }
                                        }

                                        default:
                                            return notReachable(cardConfig)
                                    }
                                })
                                break

                            case 'on_notifications_config_changed':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    notificationsConfig:
                                        msg.notificationsConfig,
                                }))
                                break

                            case 'on_earn_updated':
                                await writeToLocalStorage((storage) => {
                                    const existingCache =
                                        unsafe_GetPortfolioCache2({
                                            address: msg.ownerAddress,
                                            portfolioMap: storage.portfolios,
                                        })

                                    return existingCache
                                        ? {
                                              ...storage,
                                              portfolios: {
                                                  ...storage.portfolios,
                                                  [msg.ownerAddress]: {
                                                      ...existingCache,
                                                      earn: msg.earn,
                                                  },
                                              },
                                          }
                                        : storage
                                })
                                break

                            case 'on_card_disconnected':
                                await writeToLocalStorage((storage) =>
                                    resetCardConfig({
                                        storage,
                                        cardReadonlySigner:
                                            msg.cardReadonlySigner,
                                    })
                                )
                                break

                            case 'on_default_currency_selected':
                                postUserEvent({
                                    type: 'DefaultCurrencySetEvent',
                                    currency: msg.currency.code,
                                    installationId,
                                })
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    defaultCurrencyConfig: {
                                        defaultCurrency: msg.currency,
                                    },
                                    portfolios: {},
                                }))
                                break

                            case 'on_app_rating_submitted':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    appRating: msg.rating,
                                }))
                                break

                            case 'on_cashback_celebration_triggered':
                                await writeToLocalStorage((storage) => ({
                                    ...storage,
                                    celebrationConfig: {
                                        ...storage.celebrationConfig,
                                        cashback: {
                                            ...storage.celebrationConfig
                                                .cashback,
                                            largestRewardsBalanceCelebrated:
                                                msg.totalRewardsBalance,
                                        },
                                    },
                                }))
                                break
                            case 'on_earn_celebration_triggered':
                                await writeToLocalStorage((storage) => {
                                    const newEarn =
                                        storage.celebrationConfig.earn
                                    newEarn[
                                        msg.owner
                                            .address as Web3.address.Address
                                    ] = msg.earnCelebrationConfig
                                    return {
                                        ...storage,
                                        celebrationConfig: {
                                            ...storage.celebrationConfig,
                                            earn: newEarn,
                                        },
                                    }
                                })
                                break
                            case 'on_transaction_activities_loaded': {
                                await writeToLocalStorage((storage) => {
                                    const existingCache: TransactionActivitiesCache =
                                        getTransactionActivitiesCache({
                                            address: msg.address,
                                            transactionActivitiesCacheMap:
                                                storage.transactionActivitiesCacheMap,
                                        })

                                    return {
                                        ...storage,
                                        transactionActivitiesCacheMap: {
                                            ...storage.transactionActivitiesCacheMap,
                                            [msg.address]: {
                                                ...existingCache,
                                                transactionActivities:
                                                    msg.transactionActivities.slice(
                                                        0,
                                                        TRANSACTION_ACTIVITIES_CACHE_COUNT
                                                    ),
                                                pendingSwapsIOTransactionActivities:
                                                    msg.pendingSwapsIOTransactionActivities.slice(
                                                        0,
                                                        TRANSACTION_ACTIVITIES_CACHE_COUNT
                                                    ),
                                            },
                                        },
                                    }
                                })
                                break
                            }

                            case 'on_pending_breward_claim_transaction_activity_completed':
                            case 'on_pending_breward_claim_transaction_activity_failed':
                                await writeToLocalStorage((storage) => {
                                    const existingCache =
                                        getTransactionActivitiesCache({
                                            address: msg.address,
                                            transactionActivitiesCacheMap:
                                                storage.transactionActivitiesCacheMap,
                                        })

                                    return {
                                        ...storage,
                                        transactionActivitiesCacheMap: {
                                            ...storage.transactionActivitiesCacheMap,
                                            [msg.address]: {
                                                ...existingCache,
                                                pendingBRewardClaimTransactionActivity:
                                                    null,
                                            },
                                        },
                                    }
                                })
                                break

                            case 'on_pending_areward_claim_transaction_activity_completed':
                            case 'on_pending_areward_claim_transaction_activity_failed':
                                await writeToLocalStorage((storage) => {
                                    const existingCache =
                                        getTransactionActivitiesCache({
                                            address: msg.ownerAddress,
                                            transactionActivitiesCacheMap:
                                                storage.transactionActivitiesCacheMap,
                                        })

                                    return {
                                        ...storage,
                                        transactionActivitiesCacheMap: {
                                            ...storage.transactionActivitiesCacheMap,
                                            [msg.ownerAddress]: {
                                                ...existingCache,
                                                pendingARewardClaimTransactionActivity:
                                                    null,
                                            },
                                        },
                                    }
                                })
                                break

                            case 'on_pending_send_transaction_activity_completed':
                            case 'on_pending_send_transaction_activity_failed':
                                await writeToLocalStorage((storage) => {
                                    const existingCache =
                                        getTransactionActivitiesCache({
                                            address: msg.address,
                                            transactionActivitiesCacheMap:
                                                storage.transactionActivitiesCacheMap,
                                        })
                                    return {
                                        ...storage,
                                        transactionActivitiesCacheMap: {
                                            ...storage.transactionActivitiesCacheMap,
                                            [msg.address]: {
                                                ...existingCache,
                                                pendingSendTransactionActivities:
                                                    existingCache.pendingSendTransactionActivities.filter(
                                                        (trx) =>
                                                            trx
                                                                .submittedUserOperation
                                                                .userOperationHash !==
                                                            msg
                                                                .pendingSendTransactionActivity
                                                                .submittedUserOperation
                                                                .userOperationHash
                                                    ),
                                            },
                                        },
                                    }
                                })
                                break

                            case 'on_card_b_reward_dissmiss_clicked':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected':
                                            captureError(
                                                new ImperativeError(
                                                    'Dismiss Breward banner click for card in not fully onboarded state'
                                                )
                                            )
                                            return storage
                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            switch (
                                                storage.cardConfig.rewards.type
                                            ) {
                                                case 'expired':
                                                    return {
                                                        ...storage,
                                                        cardConfig: {
                                                            ...storage.cardConfig,
                                                            rewards: {
                                                                ...storage
                                                                    .cardConfig
                                                                    .rewards,
                                                                dismissed: true,
                                                            },
                                                        },
                                                    }
                                                case 'not_eligible':
                                                case 'in_progress':
                                                case 'ready_to_claim':
                                                case 'claimed':
                                                    captureError(
                                                        new ImperativeError(
                                                            `Dismiss Breward banner click for reward in ${storage.cardConfig.rewards.type}`
                                                        )
                                                    )
                                                    return storage

                                                default:
                                                    return notReachable(
                                                        storage.cardConfig
                                                            .rewards
                                                    )
                                            }

                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break
                            case 'card_breward_claimed':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected':
                                            captureError(
                                                new ImperativeError(
                                                    'not fully onboarded card config while b reward changing state'
                                                )
                                            )
                                            return storage
                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            const existingCache: TransactionActivitiesCache =
                                                getTransactionActivitiesCache({
                                                    address: msg.address,
                                                    transactionActivitiesCacheMap:
                                                        storage.transactionActivitiesCacheMap,
                                                })

                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    rewards: msg.reward,
                                                },
                                                transactionActivitiesCacheMap: {
                                                    ...storage.transactionActivitiesCacheMap,
                                                    [msg.address]: {
                                                        ...existingCache,
                                                        pendingBRewardClaimTransactionActivity:
                                                            msg.pendingBRewardTransactionActivity,
                                                    },
                                                },
                                            }

                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break
                            case 'card_brewards_updated':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected':
                                            captureError(
                                                new ImperativeError(
                                                    'not fully onboarded card config while b reward changing state'
                                                )
                                            )
                                            return storage
                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    rewards: msg.reward,
                                                },
                                            }

                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break

                            case 'on_dismiss_add_to_wallet_banner_clicked':
                                await writeToLocalStorage((storage) => {
                                    switch (storage.cardConfig.type) {
                                        case 'card_readonly_signer_address_is_not_selected':
                                        case 'card_readonly_signer_address_is_selected':
                                            captureError(
                                                new ImperativeError(
                                                    'Dismiss Apple/Google banner click for card in not fully onboarded state'
                                                )
                                            )
                                            return storage
                                        case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                            return {
                                                ...storage,
                                                cardConfig: {
                                                    ...storage.cardConfig,
                                                    dissmissedAddToWalletBanner:
                                                        true,
                                                },
                                            }

                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(
                                                storage.cardConfig
                                            )
                                    }
                                })
                                break
                            case 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found':
                                await writeToLocalStorage((storage) => {
                                    return {
                                        ...storage,
                                        cardConfig: {
                                            type: 'card_readonly_signer_address_is_not_selected',
                                        },
                                    }
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
