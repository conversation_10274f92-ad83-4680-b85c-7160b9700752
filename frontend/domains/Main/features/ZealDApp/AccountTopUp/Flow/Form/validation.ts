import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { values } from '@zeal/toolkit/Object'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { Account } from '@zeal/domains/Account'
import { CryptoCurrency, FiatCurrency } from '@zeal/domains/Currency'
import { FXRate2 } from '@zeal/domains/FXRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney } from '@zeal/domains/Money'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Connected, ConnectionState } from '../../../types'
import { TopUpRequest } from '../TopUpRequest'

export type Form = {
    currency: CryptoCurrency
    amount: string | null

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}

export type FormError = {
    balance?: { type: 'insufficient_balance' }
    submit?:
        | { type: 'insufficient_balance' }
        | { type: 'amount_requred' }
        | { type: 'connected_to_unsupported_network' }
}

const validateAmount = ({
    form,
}: {
    form: Form
}): Result<{ type: 'amount_requred' }, unknown> =>
    fromFixedWithFraction(form.amount, form.currency.fraction) === 0n
        ? failure({ type: 'amount_requred' })
        : success(undefined)

const validateBalance = ({
    form,
    portfolio,
}: {
    portfolio: ServerPortfolio2
    form: Form
}): Result<{ type: 'insufficient_balance' }, CryptoMoney> => {
    const money: CryptoMoney = {
        amount: fromFixedWithFraction(form.amount, form.currency.fraction),
        currency: form.currency,
    }

    const portfolioToken = getTokenByCryptoCurrency3({
        serverPortfolio: portfolio,
        currency: money.currency,
    })

    return !portfolioToken || portfolioToken.balance.amount < money.amount
        ? failure({ type: 'insufficient_balance' })
        : success(money)
}

const validateConnectionState = ({
    connectionState,
    form,
}: {
    form: Form
    connectionState: Connected
}): Result<{ type: 'connected_to_unsupported_network' }, PredefinedNetwork> => {
    const connectedNetwork = connectionState.network
    switch (connectedNetwork.type) {
        case 'unsupported_network':
            return failure({ type: 'connected_to_unsupported_network' })
        case 'supported_network':
            return form.currency.networkHexChainId ===
                connectedNetwork.network.hexChainId
                ? success(connectedNetwork.network)
                : failure({ type: 'connected_to_unsupported_network' })
        /* istanbul ignore next */
        default:
            return notReachable(connectedNetwork)
    }
}

export const validateAsYouType = ({
    form,
    fromAccount,
    portfolio,
    ratePollable,
    account,
    connectionState,
}: {
    account: Account
    fromAccount: Account
    ratePollable: PollableData<
        FXRate2<CryptoCurrency, FiatCurrency> | null,
        Form
    >
    form: Form
    portfolio: ServerPortfolio2
    connectionState: Extract<
        ConnectionState,
        { type: 'connected' | 'connected_to_unsupported_network' }
    >
}): Result<FormError, { topUpRequest: TopUpRequest }> => {
    return shape({
        balance: validateBalance({ portfolio, form }),
        submit: shape({
            network: validateConnectionState({ connectionState, form }),
            amount: validateAmount({ form }).andThen(() =>
                validateBalance({ portfolio, form })
            ),
        }).mapError((error) => values(error)[0]),
    }).map(({ submit: { amount, network } }) => ({
        topUpRequest: {
            fromAccount,
            account,
            amount,
            network,
            amountInDefaultCurrency: (() => {
                switch (ratePollable.type) {
                    case 'loaded':
                    case 'reloading':
                    case 'subsequent_failed':
                        return ratePollable.data
                            ? applyRate2({
                                  baseAmount: amount,
                                  rate: ratePollable.data,
                              })
                            : null

                    case 'loading':
                    case 'error':
                        return null

                    default:
                        return notReachable(ratePollable)
                }
            })(),
        },
    }))
}
