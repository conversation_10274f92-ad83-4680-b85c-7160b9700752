import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { Connected as ConnectedState } from '@zeal/domains/DApp/domains/ConnectionState'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { fetchServerPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import {
    SendTransaction,
    State,
} from '@zeal/domains/RPCRequest/features/SendTransaction'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { fetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { fetchTransactionResultByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'

type Props = {
    sessionPassword: string

    account: Account
    network: Network

    accounts: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    portfolioMap: PortfolioMap

    currencyHiddenMap: CurrencyHiddenMap

    sendTransactionRequest: EthSendTransaction

    connectionState: ConnectedState
    customCurrencies: CustomCurrencyMap
    state: State
    cardConfig: CardConfig

    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'on_minimize_click'
                  | 'on_expand_request'
                  | 'drag'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_safe_transaction_failure_accepted'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_wrong_network_accepted'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_completed_transaction_close_click'
                  | 'transaction_failure_accepted'
                  | 'on_transaction_cancelled_successfully_close_clicked'
                  | 'transaction_cancel_failure_accepted'
                  | 'cancel_submitted'
                  | 'transaction_submited'
                  | 'on_sign_cancel_button_clicked'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'on_close_transaction_status_not_found_modal'
                  | 'transaction_request_replaced'
                  | 'on_user_operation_bundled'
          }
      >
    | { type: 'close' }

export const DataLoader = ({
    account,
    networkMap,
    networkRPCMap,
    customCurrencies,
    portfolioMap,
    sendTransactionRequest,
    installationId,
    connectionState,
    feePresetMap,
    state,
    sessionPassword,
    network,
    cardConfig,
    keystores,
    accounts,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
    currencyHiddenMap,
    onMsg,
}: Props) => {
    const portfolio = unsafe_GetPortfolioCache2({
        address: account.address,
        portfolioMap,
    })
    const params = {
        address: account.address,
        networkMap,
        networkRPCMap,
        customCurrencies,
        cardConfig,
        forceRefresh: false,
        defaultCurrencyConfig,
        currencyHiddenMap,

        installationId,
    }

    const [loadable, setLoadble] = useLoadableData(
        fetchServerPortfolio2,
        portfolio
            ? {
                  type: 'loaded',
                  params,
                  data: portfolio,
              }
            : {
                  type: 'loading',
                  params,
              }
    )
    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout title={null} actionBar={null} onClose={null} />
            )
        case 'loaded':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={loadable.data}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    fetchSimulationByRequest={fetchSimulationByRequest}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    installationId={installationId}
                    accounts={accounts}
                    keystores={keystores}
                    state={state}
                    account={account}
                    network={network}
                    sendTransactionRequests={[sendTransactionRequest]}
                    sessionPassword={sessionPassword}
                    actionSource={{
                        type: 'external',
                        dAppSiteInfo: connectionState.dApp,
                        transactionEventSource: 'zwidget',
                    }}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_expand_request':
                            case 'drag':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_completed_safe_transaction_close_click':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_wrong_network_accepted':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_completed_transaction_close_click':
                            case 'transaction_failure_accepted':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'transaction_request_replaced':
                            case 'on_user_operation_bundled':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={null}
                        onClose={null}
                    />
                    <AppErrorPopup
                        installationId={installationId}
                        error={parseAppError(loadable.error)}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg({
                                        type: 'close',
                                    })
                                    break
                                case 'try_again_clicked':
                                    setLoadble({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
