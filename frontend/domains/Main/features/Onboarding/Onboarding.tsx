import { useEffect, useState } from 'react'
import { useIntl } from 'react-intl'

import { externalFetch } from '@zeal/api/externalFetch'
import { getApps } from '@zeal/react-native-packages'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import { useVisibility } from '@zeal/toolkit/Window/useVisibility'

import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { Onboarding as OnboardingEntrypoint } from '@zeal/domains/Main'
import {
    ANDROID_ASSET_LINKS_FILE_URL,
    APPLE_APP_SITE_ASSOCIATION_FILE_URL,
} from '@zeal/domains/Main/constants'
import { NetworkMap } from '@zeal/domains/Network'
import { setupReminder } from '@zeal/domains/Notification/domains/Reminder'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { AppSplashScreen } from './AppSplashScreen'
import { Flow } from './Flow'

type Props = {
    entryPoint: OnboardingEntrypoint
    networkMap: NetworkMap
    installationId: string
    referralConfig: ReferralConfig
    installationCampaign: string | null

    onMsg: (msg: Msg) => void
}

type State = { type: 'app_splash_screen' } | { type: 'onboarding_flow' }

type Msg = MsgOf<typeof Flow>

export const Onboarding = ({
    entryPoint: _,
    installationId,
    networkMap,
    referralConfig,
    installationCampaign,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    const [state, setState] = useState<State>(
        (() => {
            switch (ZealPlatform.OS) {
                case 'ios':
                case 'android':
                    return { type: 'onboarding_flow' }
                case 'web':
                    return { type: 'app_splash_screen' }
                default:
                    return notReachable(ZealPlatform)
            }
        })()
    )

    const { current: currentVisibility } = useVisibility()

    useEffect(() => {
        switch (currentVisibility) {
            case 'visible':
                setupReminder({
                    formatMessage,
                    reminder: 'onboarding',
                }).catch(captureError)
                break

            case 'hidden':
                break
            default:
                notReachable(currentVisibility)
        }
    }, [currentVisibility, installationId, formatMessage])

    useEffect(() => {
        switch (ZealPlatform.OS) {
            case 'ios':
            case 'android':
                // Pre-fetch these files to ensure they are cached before the user creates their first passkey
                Promise.all([
                    externalFetch(APPLE_APP_SITE_ASSOCIATION_FILE_URL),
                    externalFetch(ANDROID_ASSET_LINKS_FILE_URL),
                ]).catch(() =>
                    captureError(
                        new ImperativeError(
                            'Failed to prefetch app association files'
                        )
                    )
                )

                getApps()
                    .then((apps) => {
                        postUserEvent({
                            type: 'AppsFetchedEvent',
                            apps,
                            installationId,
                            location: 'onboarding',
                        })
                    })
                    .catch(captureError)

                break
            case 'web':
                break
            /* istanbul ignore next */
            default:
                return notReachable(ZealPlatform)
        }
    }, [installationId])

    switch (state.type) {
        case 'app_splash_screen':
            return (
                <AppSplashScreen
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_get_started':
                                setState({ type: 'onboarding_flow' })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg.type)
                        }
                    }}
                />
            )

        case 'onboarding_flow':
            return (
                <Flow
                    referralConfig={referralConfig}
                    installationId={installationId}
                    installationCampaign={installationCampaign}
                    networkMap={networkMap}
                    onMsg={onMsg}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
