import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { OnLine } from '@zeal/uikit/Icon/OnLine'
import { Operator } from '@zeal/uikit/Icon/Operator'
import { IconButton } from '@zeal/uikit/IconButton'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { getIntercomState } from '@zeal/domains/Intercom/api/getIntercomState'
import { open } from '@zeal/domains/Intercom/api/open'
import { subscribeForUnreadConversationsCount } from '@zeal/domains/Intercom/api/subscribeForUnreadConversationsCount'
import { fetchDiagnostics } from '@zeal/domains/Storage/api/fetchDiagnostics'
import { HelpButtonClickedEvent } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Variant = 'icon_button' | 'secondary_button'

type Props = {
    installationId: string
    variant: Variant
    location: HelpButtonClickedEvent['location']
}

export const IntercomButton = ({
    installationId,
    location,
    variant,
}: Props) => {
    switch (ZealPlatform.OS) {
        case 'web':
            return null
        case 'ios':
        case 'android':
            return (
                <Layout
                    variant={variant}
                    location={location}
                    installationId={installationId}
                />
            )

        default:
            notReachable(ZealPlatform)
    }
}

const Layout = ({ installationId, location, variant }: Props) => {
    const [loadable, setLoadable] = useLoadableData(getIntercomState, {
        type: 'loading',
        params: {
            installationId,
        },
    })

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                return subscribeForUnreadConversationsCount({
                    callback: (count) => {
                        setLoadable((old) => {
                            switch (old.type) {
                                case 'loaded':
                                    return count !==
                                        old.data.unreadConversations
                                        ? {
                                              ...old,
                                              data: {
                                                  ...old.data,
                                                  unreadConversations: count,
                                              },
                                          }
                                        : old
                                case 'loading':
                                case 'error':
                                    return old
                                default:
                                    return notReachable(old)
                            }
                        })
                    },
                })

            case 'loading':
                return noop
            case 'error':
                captureError(loadable.error)
                return noop

            default:
                return notReachable(loadable)
        }
    }, [loadable, setLoadable])

    const onClick = async () => {
        postUserEvent({
            type: 'HelpButtonClickedEvent',
            installationId,
            location,
        })
        open()

        try {
            const diagnostics = await fetchDiagnostics()
            postUserEvent({
                type: 'DiagnosticsSentEvent',
                installationId,
                diagnostics,
            })
        } catch (error) {
            captureError(error)
        }
    }

    switch (loadable.type) {
        case 'loaded': {
            const unreadConversations = loadable.data.unreadConversations
            const hasUnread = unreadConversations > 0

            switch (variant) {
                case 'icon_button':
                    return (
                        <IconButton variant="on_light_bold" onClick={onClick}>
                            {({ color }) => (
                                <Avatar
                                    size={24}
                                    leftBadge={
                                        hasUnread
                                            ? ({ size }) => (
                                                  <Avatar
                                                      backgroundColor="red40"
                                                      border="borderSecondary"
                                                      size={size}
                                                  >
                                                      {null}
                                                  </Avatar>
                                              )
                                            : undefined
                                    }
                                >
                                    <Operator size={24} color={color} />
                                </Avatar>
                            )}
                        </IconButton>
                    )

                case 'secondary_button':
                    return (
                        <Button
                            variant="secondary"
                            size="regular"
                            onClick={onClick}
                            leftIcon={({ color, size }) => (
                                <Operator size={size} color={color} />
                            )}
                            rightIcon={({ size }) =>
                                hasUnread ? (
                                    <Avatar backgroundColor="red40" size={size}>
                                        <Text
                                            color="gray100"
                                            variant="caption1"
                                            weight="regular"
                                        >
                                            {unreadConversations}
                                        </Text>
                                    </Avatar>
                                ) : (
                                    <OnLine size={size} />
                                )
                            }
                        >
                            <FormattedMessage
                                id="intercom.getHelp"
                                defaultMessage="Get help"
                            />
                        </Button>
                    )
                default:
                    return notReachable(variant)
            }
        }

        case 'loading':
        case 'error':
            return null

        default:
            return notReachable(loadable)
    }
}
