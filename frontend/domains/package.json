{"name": "@zeal/domains", "version": "0.0.1", "license": "UNLICENSED", "private": true, "devDependencies": {"@safe-global/safe-deployments": "1.33.0", "@types/big.js": "6.1.5", "@types/elliptic": "6.4.17", "@types/lodash.clonedeepwith": "4", "@types/lodash.once": "4.1.7", "@zeal/eslint-config": "workspace:*", "eslint": "8.3.0", "lint-staged": "15.2.2", "typescript": "5.5.3"}, "scripts": {"lint": "yarn eslint --cache --cache-location ../../.eslintcache/domains.cache --max-warnings 0 ."}, "lint-staged": {"*.{js,jsx,ts,tsx,mdx}": ["eslint --cache --cache-location ../../.eslintcache/domains.cache --max-warnings 0"]}, "dependencies": {"@ethereumjs/common": "2.6.5", "@intercom/intercom-react-native": "8.3.0", "@metamask/browser-passworder": "3.0.0", "@noble/curves": "1.2.0", "@react-native-firebase/app": "20.4.0", "@react-native-firebase/messaging": "20.4.0", "@scure/bip39": "1.1.1", "@sentry/node": "8.35.0", "@sentry/react-native": "5.35.0", "@sumsub/react-native-mobilesdk-module": "1.33.1", "@tanstack/react-query": "5.51.15", "@walletconnect/types": "2.12.2", "@walletconnect/web3wallet": "1.11.2", "@web3modal/wagmi-react-native": "2.0.0", "@zeal/api": "workspace:*", "@zeal/passkeys": "workspace:*", "@zeal/toolkit": "workspace:*", "big.js": "6.2.1", "cbor": "9.0.1", "cbor-rn-prereqs": "9.0.0", "chrome-types": "0.1.254", "date-fns": "2.29.3", "elliptic": "6.5.4", "expo-application": "5.9.1", "expo-clipboard": "6.0.3", "expo-crypto": "13.0.2", "expo-haptics": "13.0.1", "expo-local-authentication": "14.0.1", "expo-notifications": "0.28.19", "fast-deep-equal": "3.1.3", "lodash.clonedeepwith": "4.5.0", "lodash.once": "4.1.1", "memoize-one": "6.0.0", "promise": "8.3.0", "react-intl": "6.2.10", "react-native-appsflyer": "6.16.2", "react-native-in-app-review": "4.3.5", "react-native-play-install-referrer": "1.1.9", "react-native-svg": "15.2.0", "react-native-test-flight": "1.1.0", "react-native-webview": "13.8.6", "slate": "0.88.1", "viem": "2.18.5", "wagmi": "2.12.1", "web3": "4.1.2", "web3-core": "4.1.1"}, "peerDependencies": {"react": "*", "react-native": "*"}}