import { Fragment, useState } from 'react'

import { Column } from '@zeal/uikit/Column'
import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { Address } from '@zeal/domains/Address'
import {
    State as AppWidgetState,
    Widget as AppWidget,
} from '@zeal/domains/App/components/Widget'
import {
    CardBalance,
    CardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { CardWidget } from '@zeal/domains/Card/features/CardWidget'
import { GnosisPayOnboardingWidget } from '@zeal/domains/Card/features/GnosisPayOnboardingWidget'
import { cardConfigToUserEventCardOnboardedStatus } from '@zeal/domains/Card/helpers/cardConfigToUserEventCardOnboardedStatus'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SubmittedOfframpTransaction } from '@zeal/domains/Currency/domains/BankTransfer'
import { DepositMonitorWidget } from '@zeal/domains/Currency/domains/BankTransfer/features/DepositMonitorWidget'
import { KYCStatusWidget } from '@zeal/domains/Currency/domains/BankTransfer/features/KYCStatusWidget'
import { WithdrawalMonitorWidget } from '@zeal/domains/Currency/domains/BankTransfer/features/WithdrawalMonitorWidget'
import {
    BridgeSubmitted,
    SubmitedBridgesMap,
} from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { BridgeWidget } from '@zeal/domains/Currency/features/BridgeWidget'
import {
    ConfiguredEarn,
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { ConfiguredEarnWidget } from '@zeal/domains/Earn/features/ConfiguredEarnWidget'
import { AppRating } from '@zeal/domains/Feedback'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { MarketingCarousel } from '@zeal/domains/Main/features/MarketingCarousel'
import {
    calculateMarketingCarouselState,
    MarketingCarouselItem,
} from '@zeal/domains/Main/helpers/calculateMarketingCarouselState'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    State as NFTWidgetState,
    Widget as NFTWidget,
} from '@zeal/domains/NFTCollection/components/Widget'
import { Portfolio2, PortfolioMap } from '@zeal/domains/Portfolio'
import { QuickActionsWidget } from '@zeal/domains/Portfolio/components/QuickActionsWidget'
import {
    BankTransferInfo,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import {
    calculateState as tokenWidgetCalculateState,
    State as TokenWidgetState,
    Widget as TokenWidget,
} from '@zeal/domains/Token/components/Widget'
import { Submited } from '@zeal/domains/TransactionRequest'
import { List as TransactionRequestList } from '@zeal/domains/TransactionRequest/features/List'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { RecentTransactionActivityWidget } from '@zeal/domains/Transactions/features/RecentTransactionActivityWidget'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { SecretPhraseWalletBackupBanner } from './SecretPhraseWalletBackupBanner'

type Msg =
    | MsgOf<typeof AppWidget>
    | MsgOf<typeof BridgeWidget>
    | MsgOf<typeof DepositMonitorWidget>
    | MsgOf<typeof KYCStatusWidget>
    | MsgOf<typeof NFTWidget>
    | MsgOf<typeof TokenWidget>
    | MsgOf<typeof TransactionRequestList>
    | MsgOf<typeof WithdrawalMonitorWidget>
    | MsgOf<typeof ConfiguredEarnWidget>
    | MsgOf<typeof MarketingCarousel>
    | MsgOf<typeof SecretPhraseWalletBackupBanner>
    | MsgOf<typeof QuickActionsWidget>
    | MsgOf<typeof CardWidget>
    | MsgOf<typeof GnosisPayOnboardingWidget>
    | MsgOf<typeof RecentTransactionActivityWidget>
    | { type: 'reload_button_click' }

type Props = {
    account: Account
    portfolio: Portfolio2
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    isEthereumNetworkFeeWarningSeen: boolean
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    cardConfig: CardConfig
    networkMap: NetworkMap
    installationCampaign: string | null
    earnTakerMetrics: EarnTakerMetrics
    networkRPCMap: NetworkRPCMap
    submitedBridgesMap: SubmitedBridgesMap
    submittedOffRampTransactions: SubmittedOfframpTransaction[]
    transactionRequests: Record<Address, Submited[]>
    bankTransferInfo: BankTransferInfo
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    customCurrencies: CustomCurrencyMap
    installationId: string
    sessionPassword: string
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    refreshContainerState: RefreshContainerState
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    encryptedPassword: string
    referralConfig: ReferralConfig
    onMsg: (msg: Msg) => void
}

const getNonDismissedBridges = (
    submitedBridgesMap: SubmitedBridgesMap,
    account: Account
): BridgeSubmitted[] => {
    const bridges =
        submitedBridgesMap[account.address as Web3.address.Address] || []

    return bridges
        .filter((bridge) => !bridge.dismissed)
        .map(({ submittedBridge }) => submittedBridge)
}

export const View = ({
    submitedBridgesMap,
    transactionRequests,
    submittedOffRampTransactions,
    account,
    onMsg,
    accountsMap,
    keyStoreMap,
    portfolio,
    celebrationConfig,
    appRating,
    networkMap,
    cardConfig,
    installationCampaign,
    totalEarningsInDefaultCurrencyMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    transactionActivitiesCacheMap,
    networkRPCMap,
    bankTransferInfo,
    currencyHiddenMap,
    currencyPinMap,
    isEthereumNetworkFeeWarningSeen,
    earnTakerMetrics,
    customCurrencies,
    installationId,
    gasCurrencyPresetMap,
    sessionPassword,
    feePresetMap,
    portfolioMap,
    defaultCurrencyConfig,
    encryptedPassword,
    refreshContainerState,
    referralConfig,
}: Props) => {
    const [cachedTransactionRequests] = useState<Submited[]>(
        transactionRequests[account.address] || []
    )
    const [bridges, setBridges] = useState<BridgeSubmitted[]>(() =>
        getNonDismissedBridges(submitedBridgesMap, account)
    )
    const [pendingOfframpTransactions] = useState<
        SubmittedOfframpTransaction[]
    >(submittedOffRampTransactions)

    const widgets = calculateSortedWidgets({
        cardConfig,
        currencyHiddenMap,
        defaultCurrencyConfig,
        owner: account.address,
        portfolio,
    })

    return (
        <Column spacing={8}>
            <QuickActionsWidget
                onMsg={onMsg}
                address={account.address as Web3.address.Address}
                installationId={installationId}
            />

            <Column spacing={8}>
                {widgets.map((widget) => {
                    const key = `${account.address}_${widget.type}`

                    switch (widget.type) {
                        case 'info_widgets':
                            return (
                                <Fragment key={key}>
                                    {bridges.length > 0 && (
                                        <Column spacing={12}>
                                            {bridges.map((bridge) => (
                                                <BridgeWidget
                                                    lastDismissedOnboardingBannerState
                                                    key={
                                                        bridge.sourceTransactionHash
                                                    }
                                                    bridgeSubmitted={bridge}
                                                    onMsg={(msg) => {
                                                        switch (msg.type) {
                                                            case 'on_bridge_submitted_click':
                                                            case 'bridge_completed':
                                                                onMsg(msg)
                                                                break
                                                            case 'on_dismiss_bridge_widget_click':
                                                                setBridges(
                                                                    (prev) =>
                                                                        prev.filter(
                                                                            (
                                                                                bridge
                                                                            ) =>
                                                                                bridge.sourceTransactionHash !==
                                                                                msg
                                                                                    .bridgeSubmitted
                                                                                    .sourceTransactionHash
                                                                        )
                                                                )
                                                                onMsg(msg)
                                                                break
                                                            /* istanbul ignore next */
                                                            default:
                                                                return notReachable(
                                                                    msg
                                                                )
                                                        }
                                                    }}
                                                />
                                            ))}
                                        </Column>
                                    )}

                                    {cachedTransactionRequests.length > 0 && (
                                        <TransactionRequestList
                                            defaultCurrencyConfig={
                                                defaultCurrencyConfig
                                            }
                                            accountsMap={accountsMap}
                                            keyStoreMap={keyStoreMap}
                                            networkMap={networkMap}
                                            networkRPCMap={networkRPCMap}
                                            transactionRequests={
                                                cachedTransactionRequests
                                            }
                                            onMsg={onMsg}
                                        />
                                    )}

                                    <KYCStatusWidget
                                        bankTransferInfo={bankTransferInfo}
                                        networkMap={networkMap}
                                        onMsg={onMsg}
                                    />

                                    <GnosisPayOnboardingWidget
                                        installationId={installationId}
                                        cardConfig={cardConfig}
                                        keyStoreMap={keyStoreMap}
                                        networkRPCMap={networkRPCMap}
                                        networkMap={networkMap}
                                        defaultCurrencyConfig={
                                            defaultCurrencyConfig
                                        }
                                        sessionPassword={sessionPassword}
                                        onMsg={onMsg}
                                        accountsMap={accountsMap}
                                    />

                                    <DepositMonitorWidget
                                        bankTransferInfo={bankTransferInfo}
                                        networkMap={networkMap}
                                        onMsg={onMsg}
                                    />

                                    {pendingOfframpTransactions.length > 0 && (
                                        <Column spacing={12}>
                                            {pendingOfframpTransactions.map(
                                                (submittedTransaction) => (
                                                    <WithdrawalMonitorWidget
                                                        key={
                                                            submittedTransaction.transactionHash
                                                        }
                                                        bankTransferInfo={
                                                            bankTransferInfo
                                                        }
                                                        submittedTransaction={
                                                            submittedTransaction
                                                        }
                                                        networkMap={networkMap}
                                                        onMsg={onMsg}
                                                    />
                                                )
                                            )}
                                        </Column>
                                    )}

                                    <SecretPhraseWalletBackupBanner
                                        defaultCurrencyConfig={
                                            defaultCurrencyConfig
                                        }
                                        account={account}
                                        keyStore={getKeyStore({
                                            keyStoreMap,
                                            address: account.address,
                                        })}
                                        bankTransferInfo={bankTransferInfo}
                                        cardConfig={cardConfig}
                                        portfolio={portfolio}
                                        currencyHiddenMap={currencyHiddenMap}
                                        onMsg={onMsg}
                                    />
                                </Fragment>
                            )

                        case 'token':
                            return (
                                <TokenWidget
                                    key={key}
                                    state={widget.state}
                                    currencyHiddenMap={currencyHiddenMap}
                                    currencyPinMap={currencyPinMap}
                                    networkMap={networkMap}
                                    onMsg={onMsg}
                                />
                            )
                        case 'dApps':
                            return (
                                <AppWidget
                                    key={key}
                                    state={widget.state}
                                    networkMap={networkMap}
                                    onMsg={onMsg}
                                />
                            )
                        case 'collectibles':
                            return (
                                <NFTWidget
                                    key={key}
                                    state={widget.state}
                                    onMsg={onMsg}
                                />
                            )

                        case 'card': {
                            switch (widget.state.type) {
                                case 'hidden':
                                    return null
                                case 'populated':
                                    return (
                                        <CardWidget
                                            referralConfig={referralConfig}
                                            installationCampaign={
                                                installationCampaign
                                            }
                                            encryptedPassword={
                                                encryptedPassword
                                            }
                                            isEthereumNetworkFeeWarningSeen={
                                                isEthereumNetworkFeeWarningSeen
                                            }
                                            key={key}
                                            appRating={appRating}
                                            celebrationConfig={
                                                celebrationConfig
                                            }
                                            refreshContainerState={
                                                refreshContainerState
                                            }
                                            portfolioMap={portfolioMap}
                                            currencyHiddenMap={
                                                currencyHiddenMap
                                            }
                                            takerPortfolioMap={
                                                portfolio.earn.takerPortfolioMap
                                            }
                                            currencyPinMap={currencyPinMap}
                                            customCurrencyMap={customCurrencies}
                                            feePresetMap={feePresetMap}
                                            gasCurrencyPresetMap={
                                                gasCurrencyPresetMap
                                            }
                                            installationId={installationId}
                                            accountsMap={accountsMap}
                                            defaultCurrencyConfig={
                                                defaultCurrencyConfig
                                            }
                                            networkMap={networkMap}
                                            keyStoreMap={keyStoreMap}
                                            networkRPCMap={networkRPCMap}
                                            sessionPassword={sessionPassword}
                                            cachedCardBalance={
                                                widget.state.cardBalance
                                            }
                                            cardConfig={widget.state.card}
                                            onMsg={(msg) => {
                                                switch (msg.type) {
                                                    case 'on_cashback_widget_clicked':
                                                    case 'on_card_widget_clicked':
                                                        postUserEvent({
                                                            type: 'CardEnteredEvent',
                                                            location:
                                                                'portfolio_screen',
                                                            cardOnboardedStatus:
                                                                cardConfigToUserEventCardOnboardedStatus(
                                                                    cardConfig
                                                                ),
                                                            keystoreType:
                                                                keystoreToUserEventType(
                                                                    getKeyStore(
                                                                        {
                                                                            keyStoreMap,
                                                                            address:
                                                                                account.address,
                                                                        }
                                                                    )
                                                                ),
                                                            installationId,
                                                        })
                                                        onMsg(msg)
                                                        break

                                                    case 'on_cashback_loaded':
                                                    case 'on_select_rpc_click':
                                                    case 'on_rpc_change_confirmed':
                                                    case 'on_4337_auto_gas_token_selection_clicked':
                                                    case 'on_4337_gas_currency_selected':
                                                    case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                                    case 'import_keys_button_clicked':
                                                    case 'on_predefined_fee_preset_selected':
                                                    case 'cancel_submitted':
                                                    case 'on_transaction_completed_splash_animation_screen_competed':
                                                    case 'transaction_request_replaced':
                                                    case 'transaction_submited':
                                                    case 'track_wallet_clicked':
                                                    case 'on_account_create_request':
                                                    case 'add_wallet_clicked':
                                                    case 'hardware_wallet_clicked':
                                                    case 'safe_wallet_clicked':
                                                    case 'recover_safe_wallet_clicked':
                                                    case 'on_get_cashback_currency_clicked':
                                                    case 'import_card_owner_clicked':
                                                    case 'on_silent_gnosis_pay_login_failed':
                                                    case 'on_app_rating_submitted':
                                                    case 'on_cashback_celebration_triggered':
                                                    case 'on_accounts_create_success_animation_finished':
                                                    case 'on_bank_transfer_selected':
                                                    case 'on_address_scanned':
                                                    case 'on_address_scanned_and_add_label':
                                                    case 'on_ethereum_network_fee_warning_understand_clicked':
                                                    case 'on_add_label_to_track_only_account_during_send':
                                                    case 'on_top_up_transaction_complete_close':
                                                    case 'on_swaps_io_swap_request_created':
                                                    case 'on_dismiss_add_to_wallet_banner_clicked':
                                                    case 'on_dissmiss_card_kyc_onboarded_widget_clicked':
                                                    case 'on_card_b_reward_dissmiss_clicked':
                                                    case 'card_breward_claimed':
                                                    case 'card_brewards_updated':
                                                        onMsg(msg)
                                                        break
                                                    /* istanbul ignore next */
                                                    default:
                                                        notReachable(msg)
                                                }
                                            }}
                                        />
                                    )
                                default:
                                    return notReachable(widget.state)
                            }
                        }
                        case 'activity':
                            return (
                                <RecentTransactionActivityWidget
                                    key={key}
                                    earn={portfolio.earn}
                                    address={
                                        account.address as Web3.address.Address
                                    }
                                    refreshContainerState={
                                        refreshContainerState
                                    }
                                    cardConfig={cardConfig}
                                    keyStoreMap={keyStoreMap}
                                    sessionPassword={sessionPassword}
                                    portfolio={portfolio}
                                    accountsMap={accountsMap}
                                    networkMap={networkMap}
                                    networkRPCMap={networkRPCMap}
                                    defaultCurrencyConfig={
                                        defaultCurrencyConfig
                                    }
                                    transactionActivitiesCacheMap={
                                        transactionActivitiesCacheMap
                                    }
                                    swapsIOSwapRequestsMap={
                                        swapsIOSwapRequestsMap
                                    }
                                    earnHistoricalTakerUserCurrencyRateMap={
                                        earnHistoricalTakerUserCurrencyRateMap
                                    }
                                    installationId={installationId}
                                    onMsg={onMsg}
                                />
                            )

                        case 'earn':
                            switch (widget.state.type) {
                                case 'hidden':
                                    return null
                                case 'populated':
                                    return (
                                        <ConfiguredEarnWidget
                                            appRating={appRating}
                                            celebrationConfig={
                                                celebrationConfig
                                            }
                                            customCurrencies={customCurrencies}
                                            earnTakerMetrics={earnTakerMetrics}
                                            defaultCurrencyConfig={
                                                defaultCurrencyConfig
                                            }
                                            key={key}
                                            totalEarningsInDefaultCurrencyMap={
                                                totalEarningsInDefaultCurrencyMap
                                            }
                                            earnHistoricalTakerUserCurrencyRateMap={
                                                earnHistoricalTakerUserCurrencyRateMap
                                            }
                                            owner={account}
                                            isEthereumNetworkFeeWarningSeen={
                                                isEthereumNetworkFeeWarningSeen
                                            }
                                            cardConfig={cardConfig}
                                            networkMap={networkMap}
                                            accounts={accountsMap}
                                            feePresetMap={feePresetMap}
                                            gasCurrencyPresetMap={
                                                gasCurrencyPresetMap
                                            }
                                            networkRPCMap={networkRPCMap}
                                            sessionPassword={sessionPassword}
                                            installationId={installationId}
                                            portfolioMap={portfolioMap}
                                            keystores={keyStoreMap}
                                            currencyPinMap={currencyPinMap}
                                            currencyHiddenMap={
                                                currencyHiddenMap
                                            }
                                            earn={widget.state.earn}
                                            location="portfolio_screen"
                                            onMsg={onMsg}
                                        />
                                    )

                                /* istanbul ignore next */
                                default:
                                    return notReachable(widget.state)
                            }

                        case 'marketing_carousel':
                            switch (widget.state.type) {
                                case 'hidden':
                                    return null
                                case 'populated':
                                    return (
                                        <MarketingCarousel
                                            items={widget.state.items}
                                            installationCampaign={
                                                installationCampaign
                                            }
                                            customCurrencies={customCurrencies}
                                            earnTakerMetrics={earnTakerMetrics}
                                            defaultCurrencyConfig={
                                                defaultCurrencyConfig
                                            }
                                            key={key}
                                            earnOwner={account}
                                            isEthereumNetworkFeeWarningSeen={
                                                isEthereumNetworkFeeWarningSeen
                                            }
                                            cardConfig={cardConfig}
                                            networkMap={networkMap}
                                            accounts={accountsMap}
                                            feePresetMap={feePresetMap}
                                            gasCurrencyPresetMap={
                                                gasCurrencyPresetMap
                                            }
                                            networkRPCMap={networkRPCMap}
                                            sessionPassword={sessionPassword}
                                            installationId={installationId}
                                            portfolioMap={portfolioMap}
                                            keystores={keyStoreMap}
                                            currencyPinMap={currencyPinMap}
                                            currencyHiddenMap={
                                                currencyHiddenMap
                                            }
                                            earn={portfolio.earn}
                                            onMsg={onMsg}
                                        />
                                    )
                                default:
                                    return notReachable(widget.state)
                            }

                        default:
                            return notReachable(widget)
                    }
                })}
            </Column>
        </Column>
    )
}

type WidgetState =
    | { type: 'info_widgets'; state: { type: 'populated' } }
    | {
          type: 'card'
          state:
              | {
                    type: 'hidden'
                }
              | {
                    type: 'populated'
                    cardBalance: CardBalance
                    card: ReadonlySignerSelectedOnboardedCardConfig
                }
      }
    | {
          type: 'earn'
          state:
              | {
                    type: 'populated'
                    earn: ConfiguredEarn
                }
              | {
                    type: 'hidden'
                }
      }
    | {
          type: 'activity'
          state: {
              type: 'populated'
          }
      }
    | {
          type: 'token'
          state: TokenWidgetState
      }
    | {
          type: 'dApps'
          state: AppWidgetState
      }
    | {
          type: 'collectibles'
          state: NFTWidgetState
      }
    | {
          type: 'marketing_carousel'
          state:
              | { type: 'hidden' }
              | { type: 'populated'; items: MarketingCarouselItem[] }
      }

const DEFAULT_WIDGET_PRIORITY: WidgetState['type'][] = [
    'earn',
    'card',
    'info_widgets',
    'activity',
    'token',
    'dApps',
    'collectibles',
    'marketing_carousel',
]

const calculateWidgetsState = ({
    cardConfig,
    currencyHiddenMap,
    owner,
    portfolio,
    widgetPriority,
    defaultCurrencyConfig,
}: {
    widgetPriority: WidgetState['type'][]
    portfolio: Portfolio2
    cardConfig: CardConfig
    currencyHiddenMap: CurrencyHiddenMap
    owner: Address
    defaultCurrencyConfig: DefaultCurrencyConfig
}): WidgetState[] => {
    return widgetPriority.map((widgetType) => {
        switch (widgetType) {
            case 'info_widgets':
                return { type: 'info_widgets', state: { type: 'populated' } }

            case 'card': {
                const { cardBalance } = portfolio

                switch (cardConfig.type) {
                    case 'card_readonly_signer_address_is_not_selected':
                    case 'card_readonly_signer_address_is_selected': {
                        return { type: 'card', state: { type: 'hidden' } }
                    }

                    case 'card_readonly_signer_address_is_selected_fully_onboarded': {
                        if (
                            owner === cardConfig.readonlySignerAddress &&
                            cardBalance
                        ) {
                            return {
                                type: 'card',
                                state: {
                                    type: 'populated',
                                    card: cardConfig,
                                    cardBalance,
                                },
                            }
                        }

                        return {
                            type: 'card',
                            state: { type: 'hidden' },
                        }
                    }

                    /* istanbul ignore next */
                    default:
                        return notReachable(cardConfig)
                }
            }

            case 'earn':
                switch (portfolio.earn.type) {
                    case 'not_configured':
                        return { type: 'earn', state: { type: 'hidden' } }
                    case 'configured': {
                        const earn = portfolio.earn

                        return {
                            type: 'earn',
                            state: { type: 'populated', earn },
                        }
                    }
                    default:
                        return notReachable(portfolio.earn)
                }
            case 'token': {
                return {
                    type: 'token',
                    state: tokenWidgetCalculateState({
                        currencyHiddenMap,
                        defaultCurrencyConfig,
                        tokens: portfolio.tokens,
                        cardBalance: portfolio.cardBalance,
                    }),
                }
            }

            case 'dApps': {
                return portfolio.apps.length
                    ? {
                          type: 'dApps',
                          state: {
                              type: 'populated',
                              apps: portfolio.apps,
                          },
                      }
                    : { type: 'dApps', state: { type: 'hidden' } }
            }

            case 'collectibles':
                return portfolio.nftCollections.length
                    ? {
                          type: 'collectibles',
                          state: {
                              type: 'populated',
                              nfts: portfolio.nftCollections,
                          },
                      }
                    : { type: 'collectibles', state: { type: 'hidden' } }
            case 'activity':
                return {
                    type: 'activity',
                    state: {
                        type: 'populated',
                    },
                }

            case 'marketing_carousel':
                const state = calculateMarketingCarouselState({
                    earn: portfolio.earn,
                    cardConfig,
                    defaultCurrencyConfig,
                })

                return {
                    type: 'marketing_carousel',
                    state,
                }

            /* istanbul ignore next */
            default:
                return notReachable(widgetType)
        }
    })
}

const calculateSortedWidgets = ({
    cardConfig,
    currencyHiddenMap,
    owner,
    defaultCurrencyConfig,
    portfolio,
}: {
    portfolio: Portfolio2
    cardConfig: CardConfig
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    owner: Address
}): WidgetState[] => {
    const widgetsState = calculateWidgetsState({
        cardConfig,
        currencyHiddenMap,
        defaultCurrencyConfig,
        owner,
        portfolio,
        widgetPriority: DEFAULT_WIDGET_PRIORITY,
    })

    return widgetsState.toSorted((a, b) => {
        return getWidgetStatePriority(b) - getWidgetStatePriority(a)
    })
}

// TODO :: @Nicvaniek if we're no longer going to have a 'zero' state for widgets, we can remove all this complex widget state calculations and just show / hide in static order
const getWidgetStatePriority = (widgetState: WidgetState): number => {
    switch (widgetState.state.type) {
        case 'populated':
            return 1
        case 'hidden':
            return -1
        /* istanbul ignore next */
        default:
            return notReachable(widgetState.state)
    }
}
