import { unsafe_fromNumberWithFraction } from '@zeal/toolkit/BigInt'
import { parseHexAsBigInt } from '@zeal/toolkit/Hexadecimal'
import {
    boolean,
    failure,
    nullableOf,
    number,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
    ValidObject,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    CryptoCurrency,
    currencyId,
    FiatCurrency,
    PriceChange24H,
} from '@zeal/domains/Currency'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { getCryptoCurrencyIcon } from '@zeal/domains/Currency/helpers/getCryptoCurrencyIcon'
import { FXRate2 } from '@zeal/domains/FXRate'
import { CryptoMoney } from '@zeal/domains/Money'
import { PredefinedNetwork } from '@zeal/domains/Network'
import { Token2 } from '@zeal/domains/Token'
import { DeBankNetwork } from '@zeal/domains/Transactions/domains/DeBank'
import { DEBANK_NETWORK_TO_NETWORK_MAP } from '@zeal/domains/Transactions/domains/DeBank/constants'
const parseDeBankNetwork = (
    input: unknown
): Result<unknown, PredefinedNetwork> =>
    string(input).andThen((network) => {
        if (network in DEBANK_NETWORK_TO_NETWORK_MAP) {
            return success(
                DEBANK_NETWORK_TO_NETWORK_MAP[network as DeBankNetwork]
            )
        }
        return failure({ type: 'invalid_network', network })
    })
const parseDeBankTokenAddress = (
    tokenId: unknown
): Result<unknown, Web3.address.Address> => {
    return oneOf(tokenId, [
        Web3.address.parse(tokenId),
        parseDeBankNetwork(tokenId).map(
            (network) => network.nativeCurrency.address as Web3.address.Address
        ),
    ])
}
export const parseDebankCurrency = (
    obj: ValidObject
): Result<unknown, CryptoCurrency> => {
    return shape({
        address: parseDeBankTokenAddress(obj.id),
        network: parseDeBankNetwork(obj.chain),
        name: nullableOf(obj.name, string),
        fraction: number(obj.decimals),
        symbol: string(obj.symbol),
    }).map(({ address, fraction, symbol, name, network }) => {
        return {
            type: 'CryptoCurrency',
            id: currencyId({ network: network.hexChainId, address }),
            address,
            symbol,
            code: symbol,
            fraction,
            icon: getCryptoCurrencyIcon(network.name, address),
            marketCapRank: null,
            name: name || symbol,
            rateFraction: fraction,
            networkHexChainId: network.hexChainId,
        }
    })
}

export const parseDebankCryptoMoney = (
    obj: ValidObject
): Result<unknown, CryptoMoney> => {
    return parseDebankCurrency(obj).andThen((currency) =>
        shape({
            amount: oneOf(obj, [
                parseHexAsBigInt(obj.raw_amount_hex_str),
                nullableOf(obj.amount, number)
                    .map((amount) => amount || 0)
                    .map((amount) =>
                        unsafe_fromNumberWithFraction(amount, currency.fraction)
                    ),
            ]),
            currency: success(currency),
        })
    )
}

const paseMarketData = (
    obj: ValidObject
): Result<unknown, null | { priceChange24h: PriceChange24H }> => {
    return nullableOf(obj.price_24h_change, number).map((price24hChange) => {
        if (price24hChange) {
            const absPriceChange = Math.abs(price24hChange)
            switch (true) {
                case Math.trunc(absPriceChange * 1000) / 1000 === 0: // should change at least 0.1 of a percent (fraction 1)
                    return {
                        priceChange24h: {
                            direction: 'Unchanged' as const,
                        },
                    }
                case price24hChange > 0:
                    return {
                        priceChange24h: {
                            direction: 'Up' as const,
                            percentage: price24hChange,
                        },
                    }
                default:
                    return {
                        priceChange24h: {
                            direction: 'Down' as const,
                            percentage: Math.abs(price24hChange),
                        },
                    }
            }
        }
        return null
    })
}

export const parseDebankRate = (
    obj: ValidObject,
    currency: CryptoCurrency
): Result<unknown, FXRate2<CryptoCurrency, FiatCurrency> | null> => {
    return number(obj.price).map((price) => {
        return price > 0
            ? {
                  base: currency,
                  quote: FIAT_CURRENCIES.USD,
                  rate: unsafe_fromNumberWithFraction(
                      price,
                      FIAT_CURRENCIES.USD.rateFraction
                  ),
              }
            : null
    })
}

export const parseDebankToken = (input: unknown): Result<unknown, Token2> => {
    return object(input).andThen((obj) =>
        parseDebankCryptoMoney(obj).andThen((balance) => {
            return shape({
                balance: success(balance),
                rate: parseDebankRate(obj, balance.currency),
                priceInDefaultCurrency: success(null),
                marketData: paseMarketData(obj),
                scam: boolean(obj.is_core).map((scam) => !scam),
            })
        })
    )
}
