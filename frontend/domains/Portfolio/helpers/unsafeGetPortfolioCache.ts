import { Address } from '@zeal/domains/Address'
import { Portfolio, Portfolio2, PortfolioMap } from '@zeal/domains/Portfolio'

import { portfolioToPortfolio2 } from './portfolioToPortfolio2'

/**
 * portfolio map is just a cache that may not contain all tokens eg at a time of writing scam token is filtered, hence unsafe
 */
export const unsafe_GetPortfolioCache2 = ({
    address,
    portfolioMap,
}: {
    portfolioMap: PortfolioMap
    address: Address
}): Portfolio2 | null => {
    const portfolioCache =
        (portfolioMap as Record<Address, Portfolio>)[address] || null
    return portfolioCache
        ? portfolioToPortfolio2({
              portfolio: portfolioCache,
          })
        : null
}
