import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { sumPortfolio2 } from './sum'

import { Portfolio2 } from '..'

export const isFunded = ({
    portfolio,
    currencyHiddenMap,
    defaultCurrencyConfig,
}: {
    portfolio: Portfolio2
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}): boolean =>
    sumPortfolio2({ currencyHiddenMap, defaultCurrencyConfig, portfolio })
        .amount > 0n
