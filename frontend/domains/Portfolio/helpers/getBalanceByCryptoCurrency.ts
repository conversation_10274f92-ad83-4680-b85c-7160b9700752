import { CryptoCurrency } from '@zeal/domains/Currency'
import { CryptoMoney } from '@zeal/domains/Money'

import { ServerPortfolio2 } from '../index'

export const getBalanceByCryptoCurrency2 = ({
    currency,
    serverPortfolio,
}: {
    currency: CryptoCurrency
    serverPortfolio: ServerPortfolio2
}): CryptoMoney => {
    const portfolioToken =
        serverPortfolio.tokens.find(
            (token) => token.balance.currency.id === currency.id
        ) || null

    return portfolioToken
        ? {
              amount: portfolioToken.balance.amount,
              currency,
          }
        : {
              amount: 0n,
              currency,
          }
}
