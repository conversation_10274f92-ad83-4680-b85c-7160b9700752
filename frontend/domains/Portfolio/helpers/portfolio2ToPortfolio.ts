import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { token2ToToken } from '@zeal/domains/Token/helpers/token2ToToken'

import { Portfolio, Portfolio2, ServerPortfolio, ServerPortfolio2 } from '..'

export const serverPortfolio2ToServerPortfolio = ({
    serverPortfolio,
    defaultCurrencyConfig,
}: {
    serverPortfolio: ServerPortfolio2
    defaultCurrencyConfig: DefaultCurrencyConfig
}): ServerPortfolio => {
    const currencies = Object.fromEntries(
        serverPortfolio.tokens.map((t) => [
            t.balance.currency.id,
            t.balance.currency,
        ])
    )
    const knownCurrencies = {
        ...currencies,
        [defaultCurrencyConfig.defaultCurrency.id]:
            defaultCurrencyConfig.defaultCurrency,
    }

    return {
        tokens: serverPortfolio.tokens.map((token) =>
            token2ToToken({
                token,
            })
        ),
        apps: serverPortfolio.apps,
        nftCollections: serverPortfolio.nftCollections,
        currencies: knownCurrencies,
    }
}

export const portfolio2ToPortfolio = ({
    portfolio,
    defaultCurrencyConfig,
}: {
    portfolio: Portfolio2
    defaultCurrencyConfig: DefaultCurrencyConfig
}): Portfolio => ({
    ...serverPortfolio2ToServerPortfolio({
        defaultCurrencyConfig,
        serverPortfolio: portfolio,
    }),
    earn: portfolio.earn,
    cardBalance: portfolio.cardBalance,
})
