import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'

import { App2 } from '@zeal/domains/App'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { sumEarnWithEarningsPerMS } from '@zeal/domains/Earn/helpers/sumEarn'
import { FiatMoney, Money2 } from '@zeal/domains/Money'
import { sum } from '@zeal/domains/Money/helpers/sum'
import { PortfolioNFTCollection } from '@zeal/domains/NFTCollection'
import { Portfolio2, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { Token2 } from '@zeal/domains/Token'
import { filterByHideMap } from '@zeal/domains/Token/helpers/filterByHideMap'

export const sumTokensInDefaultCurrency = ({
    tokens,
}: {
    tokens: Token2[]
}): FiatMoney | null =>
    sum(
        tokens
            .map(({ priceInDefaultCurrency }) => priceInDefaultCurrency)
            .filter(excludeNullValues)
    )

export const sumAppsInDefaultCurrency = (apps: App2[]): Money2 | null =>
    sum(
        apps
            .map(({ priceInDefaultCurrency }) => priceInDefaultCurrency)
            .filter(excludeNullValues)
    )

export const sumNFTSInDefaultCurrency = (
    ntfs: PortfolioNFTCollection[]
): Money2 | null =>
    sum(
        ntfs
            .map(({ priceInDefaultCurrency }) => priceInDefaultCurrency)
            .filter(excludeNullValues)
    )

const sumPortfolioWithEarningsPerMs2 = ({
    portfolio,
    currencyHiddenMap,
    defaultCurrencyConfig,
}: {
    portfolio: Portfolio2
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}): {
    sum: FiatMoney
    earningsPerMs: FiatMoney
    // TODO @resetko-zeal consider some type for this "sum with diff per ms",
    // since we have it in earn, and hence we need same in portfolio
    // when we want to calculate how many decimals we want to show
} => {
    const defaultPrice = {
        amount: 0n,
        currency: defaultCurrencyConfig.defaultCurrency,
    }

    const serverPortfolioSum = sumServerPortfolio({
        portfolio,
        currencyHiddenMap,
        defaultCurrencyConfig,
    })

    const earnWithEarnings = sumEarnWithEarningsPerMS({
        takerPortfolioMap: portfolio.earn.takerPortfolioMap,
    })

    const earnSum = earnWithEarnings?.totalBalance || defaultPrice
    const earnEarnings = earnWithEarnings?.earningsPerMs || defaultPrice

    const cardBalanceSum = portfolio.cardBalance?.totalInDefaultCurrency || null
    const cashbackBalanceSum =
        portfolio.cardBalance?.cashbackTokenBalanceInDefaultCurrency || null

    return {
        sum:
            sum(
                [
                    serverPortfolioSum,
                    earnSum,
                    cardBalanceSum,
                    cashbackBalanceSum,
                ].filter(excludeNullValues)
            ) || defaultPrice,
        earningsPerMs: earnEarnings,
    }
}

export const sumPortfolio2 = ({
    portfolio,
    currencyHiddenMap,
    defaultCurrencyConfig,
}: {
    portfolio: Portfolio2
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}): FiatMoney =>
    sumPortfolioWithEarningsPerMs2({
        currencyHiddenMap,
        defaultCurrencyConfig,
        portfolio,
    }).sum

export const sumServerPortfolio = ({
    portfolio,
    currencyHiddenMap,
    defaultCurrencyConfig,
}: {
    portfolio: ServerPortfolio2
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}): FiatMoney => {
    const tokensSum = sumTokensInDefaultCurrency({
        tokens: portfolio.tokens.filter(filterByHideMap(currencyHiddenMap)),
    })

    const appsSum = sumAppsInDefaultCurrency(portfolio.apps)

    const totalBalance = sum(
        [tokensSum, appsSum].filter(excludeNullValues)
    ) || {
        currency: defaultCurrencyConfig.defaultCurrency,
        amount: 0n,
    }

    return totalBalance as FiatMoney
}
