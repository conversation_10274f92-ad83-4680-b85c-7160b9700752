import { notReachable } from '@zeal/toolkit'

import { AppProtocol2 } from '@zeal/domains/App'
import {
    CurrencyId,
    DefaultCurrency,
    FiatCurrency,
} from '@zeal/domains/Currency'
import { PricesMap } from '@zeal/domains/Currency/api/fetchPriceChange'
import { FXRate2, RatesMap } from '@zeal/domains/FXRate'
import {
    applyNullableRate,
    applyRate2,
    mergeRates,
} from '@zeal/domains/FXRate/helpers/applyRate'
import { PortfolioNFT } from '@zeal/domains/NFTCollection'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { Token2 } from '@zeal/domains/Token'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

const sortByMoneyAmount =
    <T>(toMoneyAmount: (a: T) => bigint) =>
    (a: T, b: T): number => {
        const aAmount = toMoneyAmount(a)
        const bAmount = toMoneyAmount(b)
        return aAmount > bAmount ? -1 : aAmount < bAmount ? 1 : 0
    }

export const updateServerPortfolioUsingDefaultCurrency = ({
    portfolio,
    rates,
    rateFromUsd,
    historyPrices,
    installationId,
}: {
    portfolio: ServerPortfolio2
    defaultCurrencyConfig: DefaultCurrencyConfig
    rates: RatesMap
    rateFromUsd: FXRate2<FiatCurrency, DefaultCurrency> | null
    historyPrices: PricesMap
    installationId: string
}): ServerPortfolio2 => {
    const currenciesWithNullableRates: CurrencyId[] = []

    const tokens = updateTokens({
        tokens: portfolio.tokens,
        historyPrices,
        rateFromUsd,
        rates,
    })

    const apps = portfolio.apps
        .map((item) => {
            return {
                ...item,
                protocols: updateAppProtocols({
                    protocols: item.protocols,
                    rateFromUsd,
                    historyPrices,
                    rates,
                }),
                priceInDefaultCurrency: applyNullableRate({
                    baseAmount: item.priceInUsd,
                    rate: rateFromUsd,
                }),
            }
        })
        .sort(
            sortByMoneyAmount((app) => app.priceInDefaultCurrency?.amount || 0n)
        )
    const nftCollections = portfolio.nftCollections
        .map((item) => {
            return {
                ...item,
                nfts: updateNfts(item.nfts, rateFromUsd),
                priceInDefaultCurrency: rateFromUsd
                    ? applyRate2({
                          baseAmount: item.priceInUsd,
                          rate: rateFromUsd,
                      })
                    : null,
            }
        })
        .sort(
            sortByMoneyAmount((nft) => nft.priceInDefaultCurrency?.amount || 0n)
        )

    if (currenciesWithNullableRates.length > 0) {
        postUserEvent({
            type: 'PortfolioLoadedCurrenciesWithNullableRates',
            currenciesIds: currenciesWithNullableRates,
            installationId,
        })
    }

    return {
        nftCollections,
        apps,
        tokens,
    }
}

const updateNfts = (
    nfts: PortfolioNFT[],
    rate: FXRate2<FiatCurrency, DefaultCurrency> | null
): PortfolioNFT[] => {
    return nfts.map((nft) => {
        return {
            ...nft,
            priceInDefaultCurrency: applyNullableRate({
                baseAmount: nft.priceInUsd,
                rate,
            }),
        }
    })
}

const updateAppProtocols = ({
    protocols,
    rateFromUsd,
    historyPrices,
    rates,
}: {
    protocols: AppProtocol2[]
    rates: RatesMap
    historyPrices: PricesMap
    rateFromUsd: FXRate2<FiatCurrency, DefaultCurrency> | null
}): AppProtocol2[] =>
    protocols.map((protocol) => {
        switch (protocol.type) {
            case 'CommonAppProtocol':
            case 'LendingAppProtocol': {
                return {
                    ...protocol,
                    suppliedTokens: updateTokens({
                        tokens: protocol.suppliedTokens,
                        rateFromUsd,
                        historyPrices,
                        rates,
                    }),
                    rewardTokens: updateTokens({
                        tokens: protocol.rewardTokens,
                        rateFromUsd,
                        historyPrices,
                        rates,
                    }),
                    borrowedTokens: updateTokens({
                        tokens: protocol.borrowedTokens,
                        rateFromUsd,
                        historyPrices,
                        rates,
                    }),
                    priceInDefaultCurrency: applyNullableRate({
                        baseAmount: protocol.priceInUsd,
                        rate: rateFromUsd,
                    }),
                }
            }
            case 'LockedTokenAppProtocol':
                return {
                    ...protocol,
                    rewardTokens: updateTokens({
                        tokens: protocol.rewardTokens,
                        rateFromUsd,
                        historyPrices,
                        rates,
                    }),
                    lockedTokens: updateTokens({
                        tokens: protocol.lockedTokens,
                        rateFromUsd,
                        historyPrices,
                        rates,
                    }),
                    priceInDefaultCurrency: applyNullableRate({
                        baseAmount: protocol.priceInUsd,
                        rate: rateFromUsd,
                    }),
                }

            case 'UnknownAppProtocol':
                return {
                    ...protocol,
                    tokens: updateTokens({
                        tokens: protocol.tokens,
                        rateFromUsd,
                        historyPrices,
                        rates,
                    }),
                    priceInDefaultCurrency: applyNullableRate({
                        baseAmount: protocol.priceInUsd,
                        rate: rateFromUsd,
                    }),
                }

            case 'VestingAppProtocol':
                return {
                    ...protocol,
                    priceInDefaultCurrency: applyNullableRate({
                        baseAmount: protocol.priceInUsd,
                        rate: rateFromUsd,
                    }),
                    vestedToken: updateToken({
                        token2: protocol.vestedToken,
                        rateFromUsd,
                        historyPrices,
                        rates,
                    }),
                    claimableToken: updateToken({
                        token2: protocol.claimableToken,
                        rateFromUsd,
                        historyPrices,
                        rates,
                    }),
                }

            default:
                return notReachable(protocol)
        }
    })

const updateToken = ({
    token2,
    rateFromUsd,
    historyPrices,
    rates,
}: {
    token2: Token2
    rates: RatesMap
    historyPrices: PricesMap
    rateFromUsd: FXRate2<FiatCurrency, DefaultCurrency> | null
}): Token2 => {
    const rate = rates[token2.balance.currency.id]
    const finalRate =
        rate ||
        (rateFromUsd &&
            token2.rate &&
            mergeRates({ rateA: token2.rate, rateB: rateFromUsd }))

    const priceInDefaultCurrency =
        finalRate &&
        applyRate2({
            baseAmount: token2.balance,
            rate: finalRate,
        })
    const priceChange24h = historyPrices[token2.balance.currency.id]
    return {
        ...token2,
        rate: finalRate,
        priceInDefaultCurrency,
        marketData: priceChange24h ? { priceChange24h } : null,
    }
}

const updateTokens = ({
    tokens,
    rateFromUsd,
    historyPrices,
    rates,
}: {
    tokens: Token2[]
    rates: RatesMap
    historyPrices: PricesMap
    rateFromUsd: FXRate2<FiatCurrency, DefaultCurrency> | null
}): Token2[] => {
    return tokens
        .map((token2) => {
            return updateToken({ token2, rateFromUsd, historyPrices, rates })
        })
        .sort(
            sortByMoneyAmount(
                (token2) => token2.priceInDefaultCurrency?.amount || 0n
            )
        )
}
