import { CryptoCurrency } from '@zeal/domains/Currency'
import { Token2 } from '@zeal/domains/Token'

import { ServerPortfolio2 } from '../index'

export const getTokenByCryptoCurrency3 = ({
    currency,
    serverPortfolio,
}: {
    currency: CryptoCurrency
    serverPortfolio: ServerPortfolio2 | null
}): Token2 => {
    const tokens = serverPortfolio?.tokens || []
    const portfolioToken =
        tokens.find((token) => token.balance.currency.id === currency.id) ||
        null

    if (portfolioToken) {
        return portfolioToken
    }

    return portfolioToken
        ? portfolioToken
        : {
              balance: {
                  amount: 0n,
                  currency,
              },
              priceInDefaultCurrency: null,
              rate: null,
              marketData: null,
              scam: false,
          }
}
