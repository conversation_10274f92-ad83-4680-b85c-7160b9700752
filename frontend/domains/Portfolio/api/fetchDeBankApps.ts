import { get } from '@zeal/api/requestBackend'

import { unsafe_fromNumberWithFraction } from '@zeal/toolkit/BigInt'
import { keys } from '@zeal/toolkit/Object'
import {
    array,
    arrayOf,
    failure,
    groupByType,
    match,
    nullable,
    nullableOf,
    number,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
    UnexpectedResultFailureError,
    ValidObject,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    App2,
    AppNft,
    AppProtocol2,
    CommonProtocol2,
    Lending2,
    LockedToken2,
    UnknownProtocol2,
    Vesting2,
} from '@zeal/domains/App'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { FiatMoney } from '@zeal/domains/Money'
import { sum } from '@zeal/domains/Money/helpers/sum'
import { PredefinedNetwork } from '@zeal/domains/Network'
import { Token2 } from '@zeal/domains/Token'
import { DeBankNetwork } from '@zeal/domains/Transactions/domains/DeBank'
import { DEBANK_NETWORK_TO_NETWORK_MAP } from '@zeal/domains/Transactions/domains/DeBank/constants'

import { parseDebankToken } from '../helpers/parseDeBankToken'

const parseDeBankNetwork = (
    input: unknown
): Result<unknown, PredefinedNetwork> =>
    string(input).andThen((network) => {
        if (network in DEBANK_NETWORK_TO_NETWORK_MAP) {
            return success(
                DEBANK_NETWORK_TO_NETWORK_MAP[network as DeBankNetwork]
            )
        }
        return failure({ type: 'invalid_network', network })
    })

const nullableTokenList = (input: unknown): Result<unknown, Token2[]> => {
    return oneOf(input, [
        nullable(input).map(() => []),
        arrayOf(input, parseDebankToken),
    ])
}
const nullableNFTList = (input: unknown): Result<unknown, AppNft[]> => {
    return oneOf(input, [
        nullable(input).map(() => []),
        arrayOf(input, parseNFT),
    ])
}

const parseNetValue = (input: ValidObject): Result<unknown, FiatMoney> => {
    return object(input.stats)
        .andThen((statsObj) => number(statsObj.net_usd_value))
        .map((amount) => {
            return {
                amount: unsafe_fromNumberWithFraction(
                    amount,
                    FIAT_CURRENCIES.USD.fraction
                ),
                currency: FIAT_CURRENCIES.USD,
            }
        })
}

const parseDetailType = (obj: ValidObject): Result<unknown, string> => {
    return array(obj.detail_types)
        .map((details) => details[details.length - 1])
        .andThen(string)
}
const parseNFT = (input: unknown): Result<unknown, AppNft> => {
    return object(input).andThen((obj) =>
        shape({
            tokenId: string(obj.inner_id),
            name: nullableOf(obj.name, string),
            uri: nullableOf(obj.content_url, string),
            amount: number(obj.amount).map((n) => n.toString()),
            decimals: success(18),
            priceInDefaultCurrency: success(null),
        })
    )
}

export const parseLending2 = (obj: ValidObject): Result<unknown, Lending2> => {
    return object(obj.detail).andThen((details) => {
        return shape({
            priceInUsd: parseNetValue(obj),
            type: parseDetailType(obj).andThen((detailsType) =>
                match(detailsType, 'lending').map(
                    () => 'LendingAppProtocol' as const
                )
            ),
            priceInDefaultCurrency: success(null),
            suppliedTokens: nullableTokenList(details.supply_token_list),
            borrowedTokens: nullableTokenList(details.borrow_token_list),
            rewardTokens: nullableTokenList(details.reward_token_list),
            category: string(obj.name),
            healthFactor: number(obj.health_rate).andThen((healthRate) =>
                healthRate > 0 && healthRate % 1 !== 0
                    ? success(Math.round(healthRate * 100) / 100)
                    : failure({
                          type: 'health rate is not decimal var or negative',
                          value: healthRate,
                      })
            ),
        })
    })
}

const parseCommonLockedProtocol = (
    obj: ValidObject
): Result<unknown, LockedToken2> => {
    return shape({
        type: parseDetailType(obj).andThen((detailsType) =>
            match(detailsType, 'locked' as const).map(
                () => 'LockedTokenAppProtocol' as const
            )
        ),
        priceInUsd: parseNetValue(obj),
        priceInDefaultCurrency: success(null),
        category: string(obj.name),
        unlockAt: nullableOf(obj.unlock_at, number).map(
            (unlockAt) => unlockAt || 0
        ),
        description: nullableOf(obj.description, string),
        lockedTokens: nullableTokenList(obj.supply_token_list),
        rewardTokens: nullableTokenList(obj.reward_token_list),
    })
}

const parseCommonAppProtocol = (
    obj: ValidObject
): Result<unknown, CommonProtocol2> => {
    return object(obj.detail).andThen((details) => {
        return shape({
            type: parseDetailType(obj).andThen((lastDetailType) =>
                oneOf(lastDetailType, [
                    match(lastDetailType, 'common'),
                    match(lastDetailType, 'leveraged_farming'),
                    match(lastDetailType, 'lending'),
                ]).map(() => 'CommonAppProtocol' as const)
            ),

            priceInUsd: parseNetValue(obj),
            priceInDefaultCurrency: success(null),
            category: string(obj.name),
            description: nullableOf(obj.description, string),
            suppliedTokens: nullableTokenList(details.supply_token_list),
            borrowedTokens: nullableTokenList(details.borrow_token_list),
            rewardTokens: nullableTokenList(details.reward_token_list),
        })
    })
}

const parseUnknownProtocol = (
    obj: ValidObject
): Result<unknown, UnknownProtocol2> => {
    return object(obj.detail).andThen((details) => {
        return shape({
            suppliedTokens: nullableTokenList(details.supply_token_list),
            borrowedTokens: nullableTokenList(details.borrow_token_list),
            rewardTokens: nullableTokenList(details.reward_token_list),
            supplyNftList: nullableNFTList(details.supply_nft_list),
            nftList: nullableNFTList(obj.nft_list),
        }).andThen(
            ({
                suppliedTokens,
                nftList,
                supplyNftList,
                borrowedTokens,
                rewardTokens,
            }) => {
                return shape({
                    type: success('UnknownAppProtocol' as const),
                    priceInDefaultCurrency: success(null),
                    category: string(obj.name),

                    priceInUsd: parseNetValue(obj),
                    tokens: success([
                        ...suppliedTokens,
                        ...borrowedTokens,
                        ...rewardTokens,
                    ]),
                    nfts: success([...nftList, ...supplyNftList]),
                })
            }
        )
    })
}

const parseVesting2 = (obj: ValidObject): Result<unknown, Vesting2> => {
    return object(obj.detail)
        .andThen((details) => parseDebankToken(details.token))
        .andThen((token) => {
            return shape({
                type: parseDetailType(obj).andThen((detailsType) =>
                    match(detailsType, 'locked' as const).map(
                        () => 'VestingAppProtocol' as const
                    )
                ),
                priceInDefaultCurrency: success(null),
                priceInUsd: parseNetValue(obj),
                category: string(obj.name),
                vestedToken: success(token),
                claimableToken: number(obj.claimable_amount).map((amount) => {
                    return {
                        ...token,
                        balance: {
                            ...token.balance,
                            amount: unsafe_fromNumberWithFraction(
                                amount,
                                token.balance.currency.fraction
                            ),
                        },
                    }
                }),
            })
        })
}

export const parseAppProtocol = (
    input: unknown
): Result<unknown, AppProtocol2> => {
    return object(input).andThen((input) => {
        return oneOf(input, [
            parseLending2(input),
            parseVesting2(input),
            parseCommonLockedProtocol(input),
            parseCommonAppProtocol(input),
            parseUnknownProtocol(input),
        ])
    })
}

export const parseApp2 = (input: unknown): Result<unknown, App2> => {
    return object(input)
        .andThen((obj) => {
            return shape({
                name: string(obj.name),
                icon: nullableOf(obj.logo_url, string),
                networkHexId: parseDeBankNetwork(obj.chain).map(
                    (network) => network.hexChainId
                ),
                priceInDefaultCurrency: success(null),
                priceInUsd: success({
                    amount: 0n,
                    currency: FIAT_CURRENCIES.USD,
                }),
                url: nullableOf(obj.site_url, string),
                protocols: arrayOf(obj.portfolio_item_list, parseAppProtocol),
            })
        })
        .map((app) => {
            return {
                ...app,
                priceInUsd: sum([
                    app.priceInUsd,
                    ...app.protocols.map((p) => p.priceInUsd),
                ]),
            }
        })
}

export const fetchDeBankApps = async ({
    address,
    signal,
}: {
    address: Web3.address.Address
    signal?: AbortSignal
}): Promise<App2[]> => {
    const response = await get(
        `/proxy/dbk/user/all_complex_protocol_list`,
        {
            query: {
                id: address,
                chain_ids: keys(DEBANK_NETWORK_TO_NETWORK_MAP),
            },
        },
        signal
    )

    const appArray = array(response).getSuccessResultOrThrow(
        'Failed to parse DeBank apps array'
    )

    const [debankAppErrors, parsedApps] = groupByType(
        appArray.map((app) => parseApp2(app))
    )

    if (debankAppErrors.length) {
        captureError(
            new UnexpectedResultFailureError(
                'Failed to parse DeBank apps',
                debankAppErrors
            )
        )
    }

    return parsedApps
}
