import React from 'react'

import { ListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Text } from '@zeal/uikit/Text'
import { Toggle } from '@zeal/uikit/Toggle'

import { Account } from '@zeal/domains/Account'
import { Avatar } from '@zeal/domains/Account/components/Avatar'
import { CopyAddress } from '@zeal/domains/Address/components/CopyAddress'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStore } from '@zeal/domains/KeyStore'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { sumPortfolio2 } from '@zeal/domains/Portfolio/helpers/sum'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    account: Account
    keyStore: KeyStore
    portfolioMap: PortfolioMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    notificationsEnabled: boolean
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_account_notification_toggle' }

export const AccountNotificationListItem = ({
    account,
    onMsg,
    portfolioMap,
    installationId,
    currencyHiddenMap,
    notificationsEnabled,
    keyStore,
    defaultCurrencyConfig,
}: Props) => {
    const portfolio = unsafe_GetPortfolioCache2({
        address: account.address,
        portfolioMap,
    })
    const sum =
        portfolio &&
        sumPortfolio2({ portfolio, currencyHiddenMap, defaultCurrencyConfig })

    return (
        <ListItem
            key={account.address}
            size="regular"
            avatar={({ size }) => (
                <Avatar account={account} keystore={keyStore} size={size} />
            )}
            primaryText={account.label}
            shortText={
                <Row spacing={16}>
                    <CopyAddress
                        location="wallet_list"
                        installationId={installationId}
                        size="small"
                        color="on_light"
                        address={account.address}
                    />
                    <Text>
                        {sum && (
                            <FormattedMoneyPrecise
                                withSymbol
                                sign={null}
                                money={sum}
                            />
                        )}
                    </Text>
                </Row>
            }
            aria-current={false}
            side={{
                rightIcon: () => {
                    return (
                        <Toggle
                            variant="default"
                            size="regular"
                            title={null}
                            checked={notificationsEnabled}
                            onClick={() =>
                                onMsg({
                                    type: 'on_account_notification_toggle',
                                })
                            }
                        />
                    )
                },
            }}
        />
    )
}
