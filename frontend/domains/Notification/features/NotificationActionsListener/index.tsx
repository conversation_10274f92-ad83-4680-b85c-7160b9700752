import { useEffect, useState } from 'react'

import Messaging from '@react-native-firebase/messaging'

import { Modal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import { useLiveRef } from '@zeal/toolkit/React'
import { Address } from '@zeal/toolkit/Web3/address'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardConfig,
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    ReferralConfig,
    UserAConfiguredReferralConfig,
    UserAReferralConfig,
} from '@zeal/domains/Card/domains/Reward'
import { DEFAULT_AREWARD_AMOUNT_IN_FIAT } from '@zeal/domains/Card/domains/Reward/constants'
import { ClaimARewards } from '@zeal/domains/Card/domains/Reward/features/ClaimARewards'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { fetchCryptoCurrency2 } from '@zeal/domains/Currency/api/fetchCryptoCurrency2'
import { STABLE_COIN_TO_FIAT_CURRENCY_MAP } from '@zeal/domains/Currency/constants'
import { getCryptoCurrency } from '@zeal/domains/Currency/helpers/getCryptoCurrency'
import {
    ConfiguredEarn,
    DeployedTaker,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { AppRating } from '@zeal/domains/Feedback'
import { open as openIntercome } from '@zeal/domains/Intercom/api/open'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    AddressReceiveMoneriumBankTransferNotification,
    AddressReceiveTokenNotification,
} from '@zeal/domains/Notification'
import { parseNotification } from '@zeal/domains/Notification/parsers/parseNotification'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import {
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { CardTransactionDetails } from './CardTransactionDetails'
import { CashbackFeature } from './CashbackFeature'
import { RedirectMoneyToCard } from './RedirectMoneyToCard'

type Props = {
    customCurrencies: CustomCurrencyMap
    currencyPinMap: CurrencyPinMap
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    portfolioMap: PortfolioMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    accountsMap: AccountsMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    networkRpcMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    referralConfig: ReferralConfig
    cardConfig: CardConfig
    networkRPCMap: NetworkRPCMap
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof CashbackFeature>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_get_cashback_currency_clicked'
                  | 'import_card_owner_clicked'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
          }
      >
    | Extract<
          MsgOf<typeof ClaimARewards>,
          { type: 'on_a_reward_claimed_successfully' }
      >
    | Extract<
          MsgOf<typeof RedirectMoneyToCard>,
          {
              type:
                  | 'cancel_submitted'
                  | 'transaction_request_replaced'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_rpc_change_confirmed'
                  | 'on_select_rpc_click'
                  | 'safe_wallet_clicked'
                  | 'on_earn_configured'
                  | 'transaction_submited'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'on_swap_success_clicked'
                  | 'on_swaps_io_swap_request_created'
                  | 'import_keys_button_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_account_create_request'
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_earn_deposit_success'
          }
      >

type ModalState =
    | { type: 'closed' }
    | {
          type: 'card_transaction_details'
          transactionHash: Hexadecimal.Hexadecimal
          blockTimestemp: number
      }
    | { type: 'cashback_feature' }
    | {
          type: 'redirect_money_to_card'
          recievedMoney: CryptoMoney
          toAddress: Address
          cardConfig: ReadonlySignerSelectedOnboardedCardConfig
          cardWalletKeySore: CardSlientSignKeyStore
          sendTo:
              | {
                    type: 'earn'
                    earn: ConfiguredEarn
                    taker: DeployedTaker
                }
              | {
                    type: 'card'
                }
      }
    | {
          type: 'user_a_claim_celebration'
          userAReferralConfig: UserAConfiguredReferralConfig
      }

const handleRecieveTokensNotification = async ({
    notification,
    cardConfig,
    keyStoreMap,
    networkRPCMap,
    portfolioMap,
}: {
    notification:
        | AddressReceiveMoneriumBankTransferNotification
        | AddressReceiveTokenNotification
    cardConfig: CardConfig
    networkRPCMap: NetworkRPCMap
    portfolioMap: PortfolioMap
    keyStoreMap: KeyStoreMap
}): Promise<ModalState> => {
    switch (notification.type) {
        case 'address_receive_monerium_bank_transfer_notification':
        case 'address_receive_token_notification':
            const cryptoCurrencyId = notification.currencyId
            if (!STABLE_COIN_TO_FIAT_CURRENCY_MAP[cryptoCurrencyId]) {
                return { type: 'closed' }
            }

            switch (cardConfig.type) {
                case 'card_readonly_signer_address_is_not_selected':
                case 'card_readonly_signer_address_is_selected':
                    return { type: 'closed' }
                case 'card_readonly_signer_address_is_selected_fully_onboarded': {
                    const cardWalletKeySore = getKeyStore({
                        address: cardConfig.readonlySignerAddress,
                        keyStoreMap,
                    })
                    switch (cardWalletKeySore.type) {
                        case 'private_key_store':
                        case 'secret_phrase_key':
                        case 'safe_4337': {
                            const portfolio = unsafe_GetPortfolioCache2({
                                address: cardConfig.readonlySignerAddress,
                                portfolioMap,
                            })
                            const knownCurrencies = await fetchCryptoCurrency2({
                                currencies: [cryptoCurrencyId],
                                networkRPCMap,
                            })
                            const currency = getCryptoCurrency({
                                cryptoCurrencyId,
                                knownCurrencies,
                            })

                            if (!portfolio || !currency) {
                                return { type: 'closed' }
                            }

                            const recievedMoney = {
                                amount: notification.amount,
                                currency,
                            }
                            switch (portfolio.earn.type) {
                                case 'not_configured':
                                    return {
                                        type: 'redirect_money_to_card',
                                        cardConfig,
                                        recievedMoney,
                                        cardWalletKeySore,
                                        toAddress: notification.address,

                                        sendTo: { type: 'card' },
                                    }

                                case 'configured': {
                                    switch (portfolio.earn.cardRecharge.type) {
                                        case 'recharge_disabled':
                                            return {
                                                type: 'redirect_money_to_card',
                                                cardConfig,
                                                recievedMoney,
                                                cardWalletKeySore,
                                                toAddress: notification.address,
                                                sendTo: {
                                                    type: 'card',
                                                },
                                            }

                                        case 'recharge_enabled': {
                                            if (
                                                portfolio.earn.cardRecharge
                                                    .cardSafeAddress !==
                                                cardConfig.lastSeenSafeAddress
                                            ) {
                                                captureError(
                                                    new ImperativeError(
                                                        'handleRemoteNotifications portfolio.earn.cardRecharge.recipient !== cardConfig.readonlySignerAddress'
                                                    )
                                                )
                                                return {
                                                    type: 'closed',
                                                }
                                            }
                                            const taker =
                                                portfolio.earn.cardRecharge
                                                    .rebalancers[0]

                                            if (!taker) {
                                                captureError(
                                                    new ImperativeError(
                                                        'Earn configured but 0 deployed takers'
                                                    )
                                                )
                                                return {
                                                    type: 'closed',
                                                }
                                            }
                                            return {
                                                type: 'redirect_money_to_card',
                                                cardConfig,
                                                recievedMoney,
                                                cardWalletKeySore,
                                                toAddress: notification.address,
                                                sendTo: {
                                                    type: 'earn',
                                                    taker,
                                                    earn: portfolio.earn,
                                                },
                                            }
                                        }

                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(
                                                portfolio.earn.cardRecharge
                                            )
                                    }
                                }

                                /* istanbul ignore next */
                                default:
                                    return notReachable(portfolio.earn)
                            }
                        }

                        case 'track_only':
                        case 'ledger':
                        case 'trezor':
                            return { type: 'closed' }
                        default:
                            return notReachable(cardWalletKeySore)
                    }
                }

                /* istanbul ignore next */
                default:
                    return notReachable(cardConfig)
            }
        default:
            return notReachable(notification)
    }
}

const handleRemoteNotifications = async ({
    installationId,
    userAReferralConfig,
    remoteNotificationData,
    cardConfig,
    portfolioMap,
    networkRPCMap,
    keyStoreMap,
}: {
    remoteNotificationData: unknown
    installationId: string
    cardConfig: CardConfig
    networkRPCMap: NetworkRPCMap
    portfolioMap: PortfolioMap
    keyStoreMap: KeyStoreMap
    userAReferralConfig: UserAReferralConfig
}): Promise<ModalState> => {
    const notification = parseNotification(remoteNotificationData)
    switch (notification.type) {
        case 'Failure':
            return { type: 'closed' }
        case 'Success':
            postUserEvent({
                type: 'NotificationReceivedEvent',
                installationId,
                notificationType: notification.data.type,
            })
            switch (notification.data.type) {
                case 'card_refund':
                case 'address_send_token_notification':
                case 'card_topup':
                    return { type: 'closed' }
                case 'address_receive_monerium_bank_transfer_notification':
                case 'address_receive_token_notification':
                    return await handleRecieveTokensNotification({
                        cardConfig,
                        keyStoreMap,
                        networkRPCMap,
                        notification: notification.data,
                        portfolioMap,
                    })

                case 'card_spend':
                    return {
                        type: 'card_transaction_details',
                        blockTimestemp: notification.data.blockTimestamp,
                        transactionHash: notification.data.transactionHash,
                    }

                case 'card_cashback_reward':
                    return { type: 'cashback_feature' }

                case 'intercom_reply_notification':
                    openIntercome()
                    return { type: 'closed' }

                case 'user_a_claimable_reward_notification':
                    switch (userAReferralConfig.type) {
                        case 'not_configured':
                            return { type: 'closed' }
                        case 'configured':
                            return {
                                type: 'user_a_claim_celebration',
                                userAReferralConfig,
                            }
                        default:
                            return notReachable(userAReferralConfig)
                    }

                default:
                    return notReachable(notification.data)
            }
        default:
            return notReachable(notification)
    }
}

export const NotificationActionsListener = ({
    customCurrencies,
    currencyPinMap,
    celebrationConfig,
    appRating,
    portfolioMap,
    gasCurrencyPresetMap,
    keyStoreMap,
    sessionPassword,
    referralConfig,
    networkRpcMap,
    networkMap,
    defaultCurrencyConfig,
    cardConfig,
    currencyHiddenMap,
    onMsg,
    installationId,
    accountsMap,
    feePresetMap,
    networkRPCMap,
    historicalTakerUserCurrencyRateMap,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    const cardConfigLive = useLiveRef(cardConfig)
    const networkRPCMapLive = useLiveRef(networkRPCMap)
    const portfolioMapLive = useLiveRef(portfolioMap)
    const keyStoreMapLive = useLiveRef(keyStoreMap)
    const referralConfigLive = useLiveRef(referralConfig)
    useEffect(() => {
        switch (ZealPlatform.OS) {
            case 'web':
                break
            case 'android':
            case 'ios':
                Messaging()
                    .getInitialNotification()
                    .then(async (remoteNotification) => {
                        setModal(
                            await handleRemoteNotifications({
                                remoteNotificationData:
                                    remoteNotification?.data?.notification,
                                installationId,
                                userAReferralConfig:
                                    referralConfigLive.current.userA,
                                networkRPCMap: networkRPCMapLive.current,
                                keyStoreMap: keyStoreMapLive.current,
                                cardConfig: cardConfigLive.current,
                                portfolioMap: portfolioMapLive.current,
                            })
                        )
                    })
                    .catch(captureError)

                const unsubscribeOnOpenApp =
                    Messaging().onNotificationOpenedApp(
                        async (remoteNotification) => {
                            setModal(
                                await handleRemoteNotifications({
                                    remoteNotificationData:
                                        remoteNotification?.data?.notification,
                                    installationId,
                                    userAReferralConfig:
                                        referralConfigLive.current.userA,
                                    networkRPCMap: networkRPCMapLive.current,
                                    keyStoreMap: keyStoreMapLive.current,
                                    cardConfig: cardConfigLive.current,
                                    portfolioMap: portfolioMapLive.current,
                                })
                            )
                        }
                    )
                const unsubscribeMessage = Messaging().onMessage(
                    async (remoteNotification) => {
                        parseNotification(
                            remoteNotification?.data?.notification
                        ).tap(async (notification) => {
                            switch (notification.type) {
                                case 'address_receive_token_notification':
                                case 'address_receive_monerium_bank_transfer_notification':
                                    const modalState =
                                        await handleRecieveTokensNotification({
                                            networkRPCMap:
                                                networkRPCMapLive.current,
                                            keyStoreMap:
                                                keyStoreMapLive.current,
                                            cardConfig: cardConfigLive.current,
                                            portfolioMap:
                                                portfolioMapLive.current,

                                            notification,
                                        })
                                    setModal(modalState)
                                    break
                                case 'address_send_token_notification':
                                case 'card_spend':
                                case 'card_topup':
                                case 'card_refund':
                                case 'card_cashback_reward':
                                case 'intercom_reply_notification':
                                case 'user_a_claimable_reward_notification':
                                    break
                                default:
                                    notReachable(notification)
                            }
                        })
                    }
                )
                return () => {
                    unsubscribeOnOpenApp()
                    unsubscribeMessage()
                }

            default:
                return notReachable(ZealPlatform)
        }
    }, [
        cardConfigLive,
        installationId,
        keyStoreMapLive,
        networkRPCMapLive,
        portfolioMapLive,
        referralConfigLive,
    ])

    switch (modal.type) {
        case 'closed':
            return null
        case 'card_transaction_details':
            return (
                <CardTransactionDetails
                    historicalTakerUserCurrencyRateMap={
                        historicalTakerUserCurrencyRateMap
                    }
                    installationId={installationId}
                    cardConfig={cardConfig}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networkMap={networkMap}
                    networkRpcMap={networkRpcMap}
                    sessionPassword={sessionPassword}
                    blockTimestemp={modal.blockTimestemp}
                    transactionHash={modal.transactionHash}
                    keystoreMap={keyStoreMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setModal({ type: 'closed' })
                                break
                            default:
                                notReachable(msg.type)
                        }
                    }}
                />
            )
        case 'redirect_money_to_card':
            return (
                <Modal>
                    <RedirectMoneyToCard
                        cardWalletKeySore={modal.cardWalletKeySore}
                        toAddress={modal.toAddress}
                        sessionPassword={sessionPassword}
                        sendTo={modal.sendTo}
                        recievedMoney={modal.recievedMoney}
                        portfolioMap={portfolioMap}
                        customCurrencies={customCurrencies}
                        currencyPinMap={currencyPinMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        currencyHiddenMap={currencyHiddenMap}
                        installationId={installationId}
                        cardConfig={modal.cardConfig}
                        keyStoreMap={keyStoreMap}
                        accountsMap={accountsMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRpcMap}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                case 'on_transaction_cancelled_successfully_close_clicked':
                                case 'on_swap_created_close_clicked':
                                case 'on_completed_transaction_close_click':
                                case 'on_swap_cancelled_close_clicked':
                                    setModal({ type: 'closed' })
                                    break
                                case 'cancel_submitted':
                                case 'transaction_request_replaced':
                                case 'on_predefined_fee_preset_selected':
                                case 'on_4337_auto_gas_token_selection_clicked':
                                case 'on_4337_gas_currency_selected':
                                case 'on_rpc_change_confirmed':
                                case 'on_select_rpc_click':
                                case 'safe_wallet_clicked':
                                case 'on_earn_configured':
                                case 'transaction_submited':
                                    onMsg(msg)
                                    break

                                case 'on_transaction_completed_splash_animation_screen_competed':
                                case 'on_swap_success_clicked':
                                case 'on_swaps_io_swap_request_created':
                                case 'import_keys_button_clicked':
                                case 'recover_safe_wallet_clicked':
                                case 'on_account_create_request':
                                case 'track_wallet_clicked':
                                case 'add_wallet_clicked':
                                case 'hardware_wallet_clicked':
                                case 'on_completed_safe_transaction_close_click':
                                case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                case 'on_earn_deposit_success':
                                    setModal({ type: 'closed' })
                                    onMsg(msg)
                                    break
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </Modal>
            )
        case 'cashback_feature':
            const data = narrowKeystoreAndCardConfig({
                cardConfig,
                keyStoreMap,
            })
            return data ? (
                <Modal>
                    <CashbackFeature
                        cardConfig={data.cardConfig}
                        currencyHiddenMap={currencyHiddenMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        keyStore={data.keyStore}
                        keystoreMap={keyStoreMap}
                        networkMap={networkMap}
                        networkRpcMap={networkRpcMap}
                        sessionPassword={sessionPassword}
                        accountsMap={accountsMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        portfolioMap={portfolioMap}
                        appRating={appRating}
                        celebrationConfig={celebrationConfig}
                        currencyPinMap={currencyPinMap}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    setModal({ type: 'closed' })
                                    break
                                case 'on_4337_auto_gas_token_selection_clicked':
                                case 'on_4337_gas_currency_selected':
                                case 'import_keys_button_clicked':
                                case 'on_predefined_fee_preset_selected':
                                case 'on_get_cashback_currency_clicked':
                                case 'import_card_owner_clicked':
                                case 'on_app_rating_submitted':
                                case 'on_cashback_celebration_triggered':
                                    onMsg(msg)
                                    break
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </Modal>
            ) : null
        case 'user_a_claim_celebration':
            return (
                <Modal>
                    <ClaimARewards
                        location="push_notification"
                        appRating={appRating}
                        installationId={installationId}
                        networkRPCMap={networkRpcMap}
                        minimumClaimableAmountInFiatCurrency={
                            DEFAULT_AREWARD_AMOUNT_IN_FIAT
                        }
                        userAReferralConfig={modal.userAReferralConfig}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    setModal({ type: 'closed' })
                                    break
                                case 'on_a_reward_claimed_successfully':
                                    onMsg(msg)
                                    break
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </Modal>
            )
        default:
            return notReachable(modal)
    }
}

const narrowKeystoreAndCardConfig = ({
    cardConfig,
    keyStoreMap,
}: {
    cardConfig: CardConfig
    keyStoreMap: KeyStoreMap
}): null | {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    keyStore: CardSlientSignKeyStore
} => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
        case 'card_readonly_signer_address_is_selected':
            return null
        case 'card_readonly_signer_address_is_selected_fully_onboarded':
            const keyStore = getKeyStore({
                address: cardConfig.readonlySignerAddress,
                keyStoreMap,
            })
            switch (keyStore.type) {
                case 'private_key_store':
                case 'safe_4337':
                case 'secret_phrase_key':
                    return {
                        cardConfig,
                        keyStore,
                    }

                case 'trezor':
                case 'ledger':
                case 'track_only':
                    return null
                default:
                    return notReachable(keyStore)
            }
        default:
            return notReachable(cardConfig)
    }
}
