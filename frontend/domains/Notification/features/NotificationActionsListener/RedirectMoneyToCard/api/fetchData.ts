import { Account } from '@zeal/domains/Account'
import {
    CardBalance,
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { fetchBalanceOfCardOnChain } from '@zeal/domains/Card/api/fetchBalance'
import { fetchGnosisPayAccountOnboardedStateResult } from '@zeal/domains/Card/api/fetchGnosisPayAccountOnboardedStateResult'
import { CryptoCurrency, CurrencyHiddenMap } from '@zeal/domains/Currency'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { fetchContractsWithCache } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchContractAddresses'
import { fetchSupportedTopupCurrenciesWithCache } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchSupportedTopupCurrencies'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { fetchPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'

export type Data = {
    portfolio: Portfolio2
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    supportedTopupCurrencies: CryptoCurrency[]
    contractsMap: SwapsIOContractsMap
    cardBalance: CardBalance
}

export type Params = {
    account: Account
    customCurrencies: CustomCurrencyMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    sessionPassword: string
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    cardWalletKeySore: CardSlientSignKeyStore
    cacheKey: string
    signal?: AbortSignal
}

export const fetchData = async ({
    account,
    customCurrencies,
    cardConfig,
    currencyHiddenMap,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    installationId,
    cardWalletKeySore,
    sessionPassword,
    cacheKey,
    signal,
}: Params): Promise<Data> => {
    const [gnosisPayAccountStateResult, portfolio, contractsMap] =
        await Promise.all([
            fetchGnosisPayAccountOnboardedStateResult({
                signal,
                networkMap,
                defaultCurrencyConfig,
                networkRPCMap,
                keyStore: cardWalletKeySore,
                sessionPassword,
                cardConfig,
            }),
            fetchPortfolio2({
                address: account.address,
                customCurrencies,
                currencyHiddenMap,
                defaultCurrencyConfig,
                cardConfig,
                signal,
                networkRPCMap,
                networkMap,
                installationId,
            }),

            fetchContractsWithCache({
                cacheKey,
            }),
        ])

    const gnosisPayAccountStateResultSuccess =
        gnosisPayAccountStateResult.getSuccessResultOrThrow(
            'gnosisPayAccountStateResult in wrong state'
        )

    const gnosisPayAccountOnboardedState =
        gnosisPayAccountStateResultSuccess.gnosisPayAccountOnboardedState

    const [cardBalance, supportedTopupCurrencies] = await Promise.all([
        gnosisPayAccountOnboardedState.balance
            ? gnosisPayAccountOnboardedState.balance
            : fetchBalanceOfCardOnChain({
                  cardAddress: gnosisPayAccountOnboardedState.cardSafe.address,
                  cardCryptoCurrency:
                      gnosisPayAccountOnboardedState.cardSafe.cryptoCurrency,
                  networkRPCMap,
                  defaultCurrencyConfig,
                  networkMap,
                  signal,
              }),
        fetchSupportedTopupCurrenciesWithCache({
            networkRPCMap,
            gnosisPayAccountOnboardedState,
            cacheKey,
        }),
    ])

    return {
        portfolio,
        cardBalance,
        supportedTopupCurrencies,
        contractsMap,
        gnosisPayAccountOnboardedState,
    }
}
