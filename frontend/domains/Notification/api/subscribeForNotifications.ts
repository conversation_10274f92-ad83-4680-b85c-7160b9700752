import Messaging from '@react-native-firebase/messaging'
import * as requestBackend from '@zeal/api/requestBackend'
import { SubscribeToNotificationConfig } from '@zeal/api/types'

import { notReachable } from '@zeal/toolkit'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { Address } from '@zeal/domains/Address'
import { CardConfig } from '@zeal/domains/Card'
import { fetchNotificationPermissions } from '@zeal/domains/Notification/api/fetchNotificationPermissions'
import { BankTransferInfo, NotificationsConfig } from '@zeal/domains/Storage'

export const subscribeForNotifications = async ({
    bankTransferInfo,
    cardConfig,
    notificationsConfig,
    installationId,
    signal,
}: {
    notificationsConfig: NotificationsConfig
    cardConfig: CardConfig
    bankTransferInfo: BankTransferInfo
    installationId: string
    signal?: AbortSignal
}): Promise<void> => {
    switch (ZealPlatform.OS) {
        case 'ios':
        case 'android': {
            const permissions = await fetchNotificationPermissions()

            switch (permissions) {
                case 'granted':
                    const bankTransferAddress = ((): Address | null => {
                        switch (bankTransferInfo.type) {
                            case 'not_started':
                                return null
                            case 'unblock_user_created':
                                return bankTransferInfo.connectedWalletAddress

                            default:
                                return notReachable(bankTransferInfo)
                        }
                    })()

                    const cardSafe = ((): Address | null => {
                        switch (cardConfig.type) {
                            case 'card_readonly_signer_address_is_not_selected':
                            case 'card_readonly_signer_address_is_selected':
                                return null
                            case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                return cardConfig.lastSeenSafeAddress
                            default:
                                return notReachable(cardConfig)
                        }
                    })()

                    await Messaging().registerDeviceForRemoteMessages()

                    const token = await Messaging().getToken()

                    await requestBackend.post(
                        '/api/notifications/subscribe',
                        {
                            body: {
                                installationId,
                                token,
                                config: getNotificationConfig({
                                    config: notificationsConfig,
                                    bankTransferAddress,
                                    cardSafe,
                                }),
                            },
                        },
                        signal
                    )

                    return

                case 'denied':
                    return

                default:
                    return notReachable(permissions)
            }
        }

        case 'web':
            return

        default:
            return notReachable(ZealPlatform)
    }
}

const getNotificationConfig = ({
    config,
    cardSafe,
    bankTransferAddress,
}: {
    config: NotificationsConfig
    cardSafe: Address | null
    bankTransferAddress: Address | null
}): SubscribeToNotificationConfig => {
    const cardPaymentConfig = cardSafe
        ? { [cardSafe]: config.cardPayments }
        : {}
    const bankTransfer = bankTransferAddress
        ? { [bankTransferAddress]: config.bankTransfers }
        : {}
    return {
        token_transfer: {
            ...config.wallets,
        },
        card_payment: cardPaymentConfig,
        bank_transfer: bankTransfer,
    }
}
