import { useIntl } from 'react-intl'

import * as Notifications from 'expo-notifications'

import { notReachable } from '@zeal/toolkit'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

export type Reminder = 'onboarding' | 'fund_wallet'

const MINUTE = 60
const HOUR = 60 * MINUTE

const scheduleNotification = async ({
    body,
    title,
    triggerInSeconds,
}: {
    title: string
    body: string
    triggerInSeconds: number
}): Promise<void> => {
    switch (ZealPlatform.OS) {
        case 'web':
            return

        case 'ios':
        case 'android':
            await Notifications.scheduleNotificationAsync({
                content: {
                    title,
                    body,
                    priority: Notifications.AndroidNotificationPriority.HIGH,
                    interruptionLevel: 'critical',
                },
                trigger: { seconds: triggerInSeconds },
            })
            return

        default:
            return notReachable(ZealPlatform)
    }
}

export const clearSheduledNotifications = async (): Promise<void> => {
    switch (ZealPlatform.OS) {
        case 'web':
            return

        case 'ios':
        case 'android':
            await Notifications.cancelAllScheduledNotificationsAsync()
            return

        default:
            return notReachable(ZealPlatform)
    }
}

export const setupReminder = async ({
    formatMessage,
    reminder,
}: {
    reminder: Reminder
    formatMessage: ReturnType<typeof useIntl>['formatMessage']
}): Promise<void> => {
    await clearAllReminders()

    switch (reminder) {
        case 'onboarding': {
            const title = 'Zeal'
            const body = formatMessage({
                id: 'reminder.onboarding',
                defaultMessage: '🏁 Finish setup — earn 6% on your deposits',
            })

            await Promise.all([
                scheduleNotification({
                    body,
                    title,
                    triggerInSeconds: 15 * MINUTE,
                }),
                scheduleNotification({
                    body,
                    title,
                    triggerInSeconds: 24 * HOUR,
                }),
            ])
            break
        }

        case 'fund_wallet': {
            const title = 'Zeal'
            const body = formatMessage({
                id: 'reminder.fund',
                defaultMessage: '💸 Add funds — start earning 6% instantly',
            })

            await Promise.all([
                scheduleNotification({
                    body,
                    title,
                    triggerInSeconds: 15 * MINUTE,
                }),
                scheduleNotification({
                    body,
                    title,
                    triggerInSeconds: 24 * HOUR,
                }),
            ])
            break
        }

        default:
            notReachable(reminder)
    }
}

export const clearAllReminders = async (): Promise<void> => {
    await clearSheduledNotifications()
}
