import { notReachable } from '@zeal/toolkit'
import { delay } from '@zeal/toolkit/Function'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import {
    arrayOf,
    bigint,
    match,
    nullable,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    Network,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import { ParsedLog } from '@zeal/domains/RPCRequest'
import {
    fetchRPCResponseWithRetry,
    Request,
} from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { GasInfo } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction'
import { parseGasInfo } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/parsers/parseSubmitedTransaction'
import { parseLog } from '@zeal/domains/Transactions/helpers/parseLog'

// TODO @resetko-zeal Move into Receipt2
type Receipt =
    | { status: 'not_found' }
    | {
          status: 'success' | 'failed'
          blockNumber: string
          gasInfo: ReceiptGasInfo
          dto: {
              logs: {
                  topics: string[]
              }[]
          }
      }

type Receipt2 =
    | {
          status: 'success'
          logs: ParsedLog[]
          to: Web3.address.Address
      }
    | {
          status: 'failed'
          to: Web3.address.Address
          reason: string | null
      }

const parseFailedReceipt = ({
    input,
}: {
    input: unknown
}): Result<unknown, Receipt2> =>
    object(input).andThen((obj) =>
        shape({
            status: match(obj.status, '0x0').map(() => 'failed' as const),
            to: Web3.address.parse(obj.to),
            reason: success(null), // FIXME @resetko-zeal can we parse reason from receipt?
        })
    )

const parseSuccessReceipt = ({
    input,
    network,
}: {
    input: unknown
    network: PredefinedNetwork
}): Result<unknown, Receipt2> =>
    object(input).andThen((obj) =>
        shape({
            status: match(obj.status, '0x1').map(() => 'success' as const),
            to: Web3.address.parse(obj.to),
            logs: arrayOf(obj.logs, (rawLog) =>
                object(rawLog)
                    .andThen((rawLogObj) =>
                        shape({
                            address: Web3.address.parse(rawLogObj.address),
                            data: Hexadecimal.nullableParse(rawLogObj.data),
                            topics: arrayOf(
                                rawLogObj.topics,
                                Hexadecimal.parse
                            ),
                            logIndex: Hexadecimal.parse(rawLogObj.logIndex).map(
                                (index) => Number(index)
                            ),
                        })
                    )
                    .andThen((log) => parseLog(log, network))
            ),
        })
    )

const parseReceipt2 = ({
    input,
    network,
}: {
    network: PredefinedNetwork
    input: unknown
}): Result<unknown, Receipt2> =>
    oneOf(input, [
        parseFailedReceipt({ input }),
        parseSuccessReceipt({ input, network }),
    ])

export type ReceiptGasInfo = { type: 'no_gas_price'; gasUsed: bigint } | GasInfo

const parseReceiptGasInfo = (input: unknown): Result<unknown, ReceiptGasInfo> =>
    oneOf(input, [
        parseGasInfo(input),
        object(input).andThen((dto) =>
            shape({
                type: success('no_gas_price' as const),
                gasUsed: bigint(dto.gasUsed),
            })
        ),
    ])

const parseReceipt = (input: unknown): Result<unknown, Receipt> =>
    oneOf(input, [
        nullable(input).map(() => ({
            status: 'not_found' as const,
        })),
        object(input).andThen((dto) =>
            oneOf(dto, [
                shape({
                    status: match(dto.status, '0x1').map(
                        () => 'success' as const
                    ),
                    blockNumber: string(dto.blockNumber),
                    gasInfo: parseReceiptGasInfo(dto),
                    dto: success(input as any),
                }),
                shape({
                    status: match(dto.status, '0x0').map(
                        () => 'failed' as const
                    ),
                    blockNumber: string(dto.blockNumber),
                    gasInfo: parseReceiptGasInfo(dto),
                    dto: success(input as any),
                }),
            ])
        ),
    ])

const FETCH_TRANSACTION_RECEIPT_RETRY_DELAY_MS = 500
const FETCH_TRANSACTION_RECEIPT_RETRY_MAX_COUNT = 10

export const createGetTransactionReceiptRequest = ({
    network,
    transactionHash,
}: {
    transactionHash: Hexadecimal.Hexadecimal
    network: PredefinedNetwork
}): Request<Receipt2> => ({
    request: {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'eth_getTransactionReceipt',
        params: [transactionHash],
    },
    parser: (input) =>
        parseReceipt2({ input, network }).getSuccessResultOrThrow(
            'Failed to parse transaction receipt'
        ),
})

/**
 * @deprecated use request
 */
export const fetchTransactionReceiptWithRetry = async ({
    network,
    networkRPCMap,
    transactionHash,
    retriesDone,
}: {
    network: Network
    networkRPCMap: NetworkRPCMap
    transactionHash: string
    retriesDone: number
}): Promise<Receipt> => {
    const receiptResponse = await fetchRPCResponseWithRetry({
        network,
        networkRPCMap,
        request: {
            id: generateRandomNumber(),
            jsonrpc: '2.0',
            method: 'eth_getTransactionReceipt',
            params: [transactionHash],
        },
    })

    const receipt = await parseReceipt(receiptResponse).getSuccessResultOrThrow(
        'failed to parse eth_getTransactionReceipt'
    )

    switch (receipt.status) {
        case 'not_found':
            if (retriesDone >= FETCH_TRANSACTION_RECEIPT_RETRY_MAX_COUNT) {
                return receipt
            }

            await delay(FETCH_TRANSACTION_RECEIPT_RETRY_DELAY_MS)

            return fetchTransactionReceiptWithRetry({
                network,
                networkRPCMap,
                transactionHash,
                retriesDone: retriesDone + 1,
            })

        case 'success':
        case 'failed':
            return receipt

        default:
            return notReachable(receipt)
    }
}

export const getConfirmationBlockCount = (network: Network): bigint => {
    switch (network.type) {
        case 'predefined':
        case 'testnet':
            switch (network.name) {
                case 'EthereumSepolia':
                case 'Ethereum':
                case 'Arbitrum':
                case 'zkSync':
                case 'BSC':
                case 'Fantom':
                case 'Optimism':
                case 'Base':
                case 'Blast':
                case 'OPBNB':
                case 'Gnosis':
                case 'Celo':
                case 'Avalanche':
                case 'Cronos':
                case 'Mantle':
                case 'Manta':
                case 'Aurora':
                case 'BscTestnet':
                case 'AvalancheFuji':
                case 'FantomTestnet':
                case 'Linea': // TODO @kitty-the-kat check
                    return 0n // initial confirmation not included in the confirmation count. i.e. 0=1 and 1=2
                case 'Polygon':
                case 'PolygonZkevm':
                    return 1n
                default:
                    return notReachable(network)
            }
        case 'custom':
            return 1n
        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}
