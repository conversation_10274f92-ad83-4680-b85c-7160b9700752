import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'

import {
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { captureErrorOnce } from '@zeal/domains/Error/helpers/captureError'

export const getOnboardedCardConfig = (
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig,
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
): ReadonlySignerSelectedOnboardedCardConfig => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_selected':
            captureErrorOnce(
                new ImperativeError(
                    'got card_readonly_signer_address_is_selected in getOnboardedCardConfig',
                    {
                        userId: gnosisPayAccountOnboardedState.userId,
                    }
                )
            )
            return {
                type: 'card_readonly_signer_address_is_selected_fully_onboarded',
                readonlySignerAddress: cardConfig.readonlySignerAddress,
                selectedCardId: null,
                cardTransactionsCache: null,
                lastRechargeTransactionHash: null,
                lastSeenSafeAddress:
                    gnosisPayAccountOnboardedState.cardSafe.address,
                currency:
                    gnosisPayAccountOnboardedState.cardSafe.cryptoCurrency,
                dissmissedAddToWalletBanner: false,
                lastDismissedKycBannerState: null,
                country:
                    gnosisPayAccountOnboardedState.residentialAddress
                        ?.country || null,
                cashback: {
                    type: 'not_eligible_for_cashback',
                },
                isCreatedViaZeal:
                    gnosisPayAccountOnboardedState.isCreatedViaZeal,
                userId: gnosisPayAccountOnboardedState.userId,
                rewards: { type: 'not_eligible' },
            }
        case 'card_readonly_signer_address_is_selected_fully_onboarded':
            return {
                ...cardConfig,
                type: 'card_readonly_signer_address_is_selected_fully_onboarded',
                readonlySignerAddress: cardConfig.readonlySignerAddress,
                cardTransactionsCache: cardConfig.cardTransactionsCache,
                lastRechargeTransactionHash:
                    cardConfig.lastRechargeTransactionHash,
                lastSeenSafeAddress:
                    gnosisPayAccountOnboardedState.cardSafe.address,
                currency:
                    gnosisPayAccountOnboardedState.cardSafe.cryptoCurrency,
            }
        default:
            return notReachable(cardConfig)
    }
}
