import { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import {
    CardBalance,
    CardSlientSignKeyStore,
    CardTransaction,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { BReward } from '@zeal/domains/Card/domains/Reward'
import { calculateBRewardState } from '@zeal/domains/Card/domains/Reward/helpers/calculateBRewardState'
import { CountryISOCode } from '@zeal/domains/Country'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { SuccessLayoutWithNotificationsPrompt } from '@zeal/domains/Notification/features/SuccessLayoutWithNotificationsPrompt'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { SelectCard } from './SelectCard'

type Props = {
    cardReadonlySigner: Account
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    isBRewardClaimed: boolean
    cardTransactions: CardTransaction[]
    installationId: string
    keyStore: CardSlientSignKeyStore
    sessionPassword: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof SelectCard>, { type: 'close' }>
    | {
          type: 'on_onboarded_card_imported_success_animation_complete'
          cardReadonlySigner: Account
          currency: CryptoCurrency
          lastSeenSafeAddress: Web3.address.Address
          balance: CardBalance
          selectedCardId: string
          country: CountryISOCode | null

          reward: BReward
          userId: string | null
          isCreatedViaZeal: boolean
      }

type State =
    | {
          type: 'card_imported_success_animation'
          cardId: string
      }
    | {
          type: 'select_card'
      }

const calculateState = (
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
): State => {
    return gnosisPayAccountOnboardedState.activatedCards.length === 1
        ? {
              type: 'card_imported_success_animation',
              cardId: gnosisPayAccountOnboardedState.selectedCard.id,
          }
        : { type: 'select_card' }
}

export const SeveralCardsChecker = ({
    cardReadonlySigner,
    gnosisPayAccountOnboardedState,
    installationId,
    keyStore,
    sessionPassword,
    cardTransactions,
    isBRewardClaimed,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>(
        calculateState(gnosisPayAccountOnboardedState)
    )

    switch (state.type) {
        case 'select_card':
            return (
                <SelectCard
                    keyStore={keyStore}
                    cardReadonlySigner={cardReadonlySigner}
                    sessionPassword={sessionPassword}
                    gnosisPayAccountOnboardedState={
                        gnosisPayAccountOnboardedState
                    }
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break

                            case 'on_card_selected':
                                setState({
                                    type: 'card_imported_success_animation',
                                    cardId: msg.cardId,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'card_imported_success_animation':
            return (
                <SuccessLayoutWithNotificationsPrompt
                    location="card_onboarding"
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_success_layout_animation_completed':
                                postUserEvent({
                                    installationId: installationId,
                                    type: 'CardImportSuccessEvent',
                                    keystoreId: keyStore.id,
                                    keystoreType:
                                        keystoreToUserEventType(keyStore),
                                })
                                onMsg({
                                    type: 'on_onboarded_card_imported_success_animation_complete',
                                    cardReadonlySigner: cardReadonlySigner,
                                    currency:
                                        gnosisPayAccountOnboardedState.cardSafe
                                            .cryptoCurrency,
                                    balance:
                                        gnosisPayAccountOnboardedState.balance,
                                    lastSeenSafeAddress:
                                        gnosisPayAccountOnboardedState.cardSafe
                                            .address,
                                    selectedCardId: state.cardId,
                                    country:
                                        gnosisPayAccountOnboardedState
                                            .residentialAddress?.country ||
                                        null,
                                    isCreatedViaZeal:
                                        gnosisPayAccountOnboardedState.isCreatedViaZeal,
                                    userId: gnosisPayAccountOnboardedState.userId,
                                    reward: calculateBRewardState({
                                        gnosisAccountState:
                                            gnosisPayAccountOnboardedState,
                                        cardTransactions,
                                        isBRewardClaimed,
                                    }),
                                })

                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg.type)
                        }
                    }}
                    installationId={installationId}
                    title={
                        <FormattedMessage
                            id="gnosisPayAccountStatus.success.title"
                            defaultMessage="Card imported"
                        />
                    }
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
