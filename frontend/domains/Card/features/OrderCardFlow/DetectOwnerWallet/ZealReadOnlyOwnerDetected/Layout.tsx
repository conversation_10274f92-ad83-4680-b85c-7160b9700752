import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Avatar, Badge } from '@zeal/uikit/Avatar'
import { Column } from '@zeal/uikit/Column'
import { Group, GroupHeader } from '@zeal/uikit/Group'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldAdd } from '@zeal/uikit/Icon/BoldAdd'
import { FaceIdLogo } from '@zeal/uikit/Icon/FaceIdLogo'
import { LightDownload } from '@zeal/uikit/Icon/LightDownload'
import { IconButton } from '@zeal/uikit/IconButton'
import { ListItem, ListItemButton } from '@zeal/uikit/ListItem'
import { SubtextListItem } from '@zeal/uikit/ListItem/SubtextListItem'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { values } from '@zeal/toolkit/Object'
import { Address, format } from '@zeal/toolkit/Web3/address'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ListItem as AccountListItem } from '@zeal/domains/Account/components/ListItem'
import { CardSlientSignKeyStore } from '@zeal/domains/Card'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap, TrackOnly } from '@zeal/domains/KeyStore'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { filterAccountsByCardSlientSigningKeyStore } from '../../filterAccountsByCardSlientSigningKeyStore'

type Props = {
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    installationId: string
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig

    detectedReadOnlyAccountWithKeyStore: {
        account: Account
        keyStore: TrackOnly
    }
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_create_smart_wallet_clicked' }
    | {
          type: 'on_active_wallet_selected'
          account: Account
          keyStore: CardSlientSignKeyStore
      }
    | { type: 'on_card_import_on_import_keys_clicked' }

const hasSafeKeyStore = (keyStoreMap: KeyStoreMap): boolean =>
    values(keyStoreMap).some((keyStore) => {
        switch (keyStore.type) {
            case 'safe_4337':
                return true
            case 'track_only':
            case 'private_key_store':
            case 'ledger':
            case 'secret_phrase_key':
            case 'trezor':
                return false
            /* istanbul ignore next */
            default:
                return notReachable(keyStore)
        }
    })

export const Layout = ({
    onMsg,
    accountsMap,
    keyStoreMap,
    detectedReadOnlyAccountWithKeyStore,
    installationId,
    defaultCurrencyConfig,
    currencyHiddenMap,
    portfolioMap,
}: Props) => {
    const suitableWallets = filterAccountsByCardSlientSigningKeyStore(
        accountsMap,
        keyStoreMap
    )

    const hasSmartWallet = hasSafeKeyStore(keyStoreMap)

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={24} fill shrink alignY="stretch">
                <Header
                    title={
                        <FormattedMessage
                            id="card.read-only-detected.title"
                            defaultMessage="Card detected on read-only wallet. Select wallet to manage card"
                        />
                    }
                />
                <Column spacing={12} shrink fill>
                    <ScrollContainer withFloatingActions={false}>
                        {suitableWallets.length ? (
                            <Group variant="widget">
                                <GroupHeader
                                    right={null}
                                    left={({
                                        color,
                                        textVariant,
                                        textWeight,
                                    }) => (
                                        <Text
                                            color={color}
                                            variant={textVariant}
                                            weight={textWeight}
                                        >
                                            <FormattedMessage
                                                id="add-another-card-owner"
                                                defaultMessage="Add another card owner"
                                            />
                                        </Text>
                                    )}
                                />

                                {suitableWallets.map((item) => (
                                    <AccountListItem
                                        keystore={item.keystore}
                                        defaultCurrencyConfig={
                                            defaultCurrencyConfig
                                        }
                                        installationId={installationId}
                                        currencyHiddenMap={currencyHiddenMap}
                                        key={item.account.address}
                                        portfolio={unsafe_GetPortfolioCache2({
                                            address: item.account.address,
                                            portfolioMap,
                                        })}
                                        selected={false}
                                        account={item.account}
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'account_item_clicked':
                                                    onMsg({
                                                        type: 'on_active_wallet_selected',
                                                        account: msg.account,
                                                        keyStore: item.keystore,
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        msg.type
                                                    )
                                            }
                                        }}
                                    />
                                ))}
                                {!hasSmartWallet && (
                                    <ListItem
                                        aria-current={false}
                                        size="large"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_create_smart_wallet_clicked',
                                            })
                                        }
                                        primaryText={
                                            <FormattedMessage
                                                id="create-smart-wallet"
                                                defaultMessage="Create Smart wallet"
                                            />
                                        }
                                        avatar={({ size }) => (
                                            <Avatar
                                                size={size}
                                                leftBadge={({
                                                    size: badgeSize,
                                                }) => (
                                                    <Badge
                                                        size={badgeSize}
                                                        backgroundColor="teal40"
                                                        outlineColor="surfaceDefault"
                                                    >
                                                        <BoldAdd
                                                            size={badgeSize}
                                                            color="surfaceLight"
                                                        />
                                                    </Badge>
                                                )}
                                            >
                                                <FaceIdLogo
                                                    size={size}
                                                    color="teal40"
                                                />
                                            </Avatar>
                                        )}
                                    />
                                )}
                            </Group>
                        ) : (
                            <ListItemButton
                                variant="default"
                                aria-current={false}
                                background="surface"
                                onClick={() =>
                                    onMsg({
                                        type: 'on_create_smart_wallet_clicked',
                                    })
                                }
                                disabled={false}
                                primaryText={
                                    <FormattedMessage
                                        id="card.read-only-detected.create-new"
                                        defaultMessage="Add a new wallet as owner"
                                    />
                                }
                                avatar={({ size }) => (
                                    <Avatar
                                        size={size}
                                        leftBadge={({ size: badgeSize }) => (
                                            <Badge
                                                size={badgeSize}
                                                backgroundColor="teal40"
                                                outlineColor="surfaceDefault"
                                            >
                                                <BoldAdd
                                                    size={badgeSize}
                                                    color="surfaceLight"
                                                />
                                            </Badge>
                                        )}
                                    >
                                        <FaceIdLogo
                                            size={size}
                                            color="teal40"
                                        />
                                    </Avatar>
                                )}
                            />
                        )}
                    </ScrollContainer>
                    <SubtextListItem
                        onClick={() =>
                            onMsg({
                                type: 'on_card_import_on_import_keys_clicked',
                            })
                        }
                        subItems={
                            <Text>
                                <FormattedMessage
                                    id="card.read-only-detected.import-current-owner.sub-text"
                                    defaultMessage="Import private keys or seed phrase of wallet {address}"
                                    values={{
                                        address: format(
                                            detectedReadOnlyAccountWithKeyStore
                                                .account.address as Address
                                        ),
                                    }}
                                />
                            </Text>
                        }
                        size="large"
                        primaryText={
                            <FormattedMessage
                                id="card.read-only-detected.import-current-owner"
                                defaultMessage="Import keys for {wallet}"
                                values={{
                                    wallet: detectedReadOnlyAccountWithKeyStore
                                        .account.label,
                                }}
                            />
                        }
                        avatar={({ size }) => (
                            <LightDownload size={size} color="teal40" />
                        )}
                    />
                </Column>
            </Column>
        </Screen>
    )
}
