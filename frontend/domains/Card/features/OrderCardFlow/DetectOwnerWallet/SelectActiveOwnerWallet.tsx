import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { Header } from '@zeal/uikit/Header'
import { Close<PERSON>ross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'

import { notReachable } from '@zeal/toolkit'

import { Account } from '@zeal/domains/Account'
import { UnlockedListItem } from '@zeal/domains/Account/components/UnlockedListItem'
import { CardSlientSignKeyStore } from '@zeal/domains/Card'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    owners: { account: Account; keyStore: CardSlientSignKeyStore }[]
    portfolioMap: PortfolioMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_zeal_active_account_selected'
          account: Account
          keyStore: CardSlientSignKeyStore
      }

export const SelectActiveOwnerWallet = ({
    owners,
    onMsg,
    installationId,
    defaultCurrencyConfig,
    currencyHiddenMap,
    portfolioMap,
}: Props) => {
    return (
        <Popup.Layout onMsg={onMsg}>
            <ActionBar
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() => {
                            onMsg({ type: 'close' })
                        }}
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Header
                title={
                    <FormattedMessage
                        id="select-active-owner.title"
                        defaultMessage="Select wallet"
                    />
                }
                subtitle={
                    <FormattedMessage
                        id="select-active-owner.subtitle"
                        defaultMessage="You have multiple wallets linked to your card. Select one to connect to Zeal. You can switch anytime."
                    />
                }
            />

            <Popup.Content>
                <Column spacing={8}>
                    {owners.map((owner) => (
                        <Group variant="default" key={owner.account.address}>
                            <UnlockedListItem
                                account={owner.account}
                                currencyHiddenMap={currencyHiddenMap}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                installationId={installationId}
                                keyStore={owner.keyStore}
                                portfolio={unsafe_GetPortfolioCache2({
                                    address: owner.account.address,
                                    portfolioMap,
                                })}
                                selected={false}
                                selectionVariant="background_color"
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'account_item_clicked':
                                            onMsg({
                                                type: 'on_zeal_active_account_selected',
                                                account: owner.account,
                                                keyStore: owner.keyStore,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg.type)
                                    }
                                }}
                            />
                        </Group>
                    ))}
                </Column>
            </Popup.Content>
        </Popup.Layout>
    )
}
