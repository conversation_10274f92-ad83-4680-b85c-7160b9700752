import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Avatar, Badge } from '@zeal/uikit/Avatar'
import { Column } from '@zeal/uikit/Column'
import { Group, GroupHeader } from '@zeal/uikit/Group'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldAdd } from '@zeal/uikit/Icon/BoldAdd'
import { FaceIdLogo } from '@zeal/uikit/Icon/FaceIdLogo'
import { LightDownload } from '@zeal/uikit/Icon/LightDownload'
import { IconButton } from '@zeal/uikit/IconButton'
import { ListItem, ListItemButton } from '@zeal/uikit/ListItem'
import { SubtextListItem } from '@zeal/uikit/ListItem/SubtextListItem'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { values } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ListItem as AccountListItem } from '@zeal/domains/Account/components/ListItem'
import { CardSlientSignKeyStore } from '@zeal/domains/Card'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    installationId: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_zeal_active_account_selected'
          account: Account
          keyStore: CardSlientSignKeyStore
      }
    | { type: 'on_create_smart_wallet_clicked' }
    | { type: 'on_card_import_on_import_keys_clicked' }

const filterByCardSilentSignKeyStore = (
    accountsMap: AccountsMap,
    keyStoreMap: KeyStoreMap
): { account: Account; keystore: CardSlientSignKeyStore }[] =>
    values(accountsMap)
        .map((account) => {
            return {
                account: account,
                keystore: getKeyStore({
                    address: account.address,
                    keyStoreMap,
                }),
            }
        })
        .filter(
            (
                accountKeystorePair
            ): accountKeystorePair is {
                account: Account
                keystore: CardSlientSignKeyStore
            } => {
                const { keystore } = accountKeystorePair
                switch (keystore.type) {
                    case 'track_only':
                    case 'trezor':
                    case 'ledger':
                        return false
                    case 'private_key_store':
                    case 'secret_phrase_key':
                    case 'safe_4337':
                        return true
                    /* istanbul ignore next */
                    default:
                        return notReachable(keystore)
                }
            }
        )

const hasSafeKeyStore = (keyStoreMap: KeyStoreMap): boolean =>
    values(keyStoreMap).some((keyStore) => {
        switch (keyStore.type) {
            case 'safe_4337':
                return true
            case 'track_only':
            case 'private_key_store':
            case 'ledger':
            case 'secret_phrase_key':
            case 'trezor':
                return false
            /* istanbul ignore next */
            default:
                return notReachable(keyStore)
        }
    })

export const ImportFlowChooseWallet = ({
    keystoreMap,
    accountsMap,
    portfolioMap,
    currencyHiddenMap,
    onMsg,
    installationId,
    defaultCurrencyConfig,
}: Props) => {
    const suitableWallets = filterByCardSilentSignKeyStore(
        accountsMap,
        keystoreMap
    )

    const hasSmartWallet = hasSafeKeyStore(keystoreMap)

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={24} fill shrink alignY="stretch">
                <Header
                    title={
                        <FormattedMessage
                            id="card.choose-wallet.title"
                            defaultMessage="Select wallet to manage your card"
                        />
                    }
                />

                <Column spacing={12} shrink fill>
                    <ScrollContainer withFloatingActions={false}>
                        {suitableWallets.length ? (
                            <Group variant="widget">
                                <GroupHeader
                                    right={null}
                                    left={({
                                        color,
                                        textVariant,
                                        textWeight,
                                    }) => (
                                        <Text
                                            color={color}
                                            variant={textVariant}
                                            weight={textWeight}
                                        >
                                            <FormattedMessage
                                                id="add-another-card-owner"
                                                defaultMessage="Add another card owner"
                                            />
                                        </Text>
                                    )}
                                />

                                {suitableWallets.map((item) => (
                                    <AccountListItem
                                        keystore={item.keystore}
                                        defaultCurrencyConfig={
                                            defaultCurrencyConfig
                                        }
                                        installationId={installationId}
                                        currencyHiddenMap={currencyHiddenMap}
                                        key={item.account.address}
                                        portfolio={unsafe_GetPortfolioCache2({
                                            address: item.account.address,
                                            portfolioMap,
                                        })}
                                        selected={false}
                                        account={item.account}
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'account_item_clicked':
                                                    onMsg({
                                                        type: 'on_zeal_active_account_selected',
                                                        account: msg.account,
                                                        keyStore: item.keystore,
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        msg.type
                                                    )
                                            }
                                        }}
                                    />
                                ))}
                                {!hasSmartWallet && (
                                    <ListItem
                                        aria-current={false}
                                        size="large"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_create_smart_wallet_clicked',
                                            })
                                        }
                                        primaryText={
                                            <FormattedMessage
                                                id="create-smart-wallet"
                                                defaultMessage="Create Smart wallet"
                                            />
                                        }
                                        avatar={({ size }) => (
                                            <Avatar
                                                size={size}
                                                leftBadge={({
                                                    size: badgeSize,
                                                }) => (
                                                    <Badge
                                                        size={badgeSize}
                                                        backgroundColor="teal40"
                                                        outlineColor="surfaceDefault"
                                                    >
                                                        <BoldAdd
                                                            size={badgeSize}
                                                            color="surfaceLight"
                                                        />
                                                    </Badge>
                                                )}
                                            >
                                                <FaceIdLogo
                                                    size={size}
                                                    color="teal40"
                                                />
                                            </Avatar>
                                        )}
                                    />
                                )}
                            </Group>
                        ) : (
                            <ListItemButton
                                variant="default"
                                aria-current={false}
                                background="surface"
                                onClick={() =>
                                    onMsg({
                                        type: 'on_create_smart_wallet_clicked',
                                    })
                                }
                                disabled={false}
                                primaryText={
                                    <FormattedMessage
                                        id="card.choose-wallet.create-new"
                                        defaultMessage="Add a new wallet as owner"
                                    />
                                }
                                avatar={({ size }) => (
                                    <Avatar
                                        size={size}
                                        leftBadge={({ size: badgeSize }) => (
                                            <Badge
                                                size={badgeSize}
                                                backgroundColor="teal40"
                                                outlineColor="surfaceDefault"
                                            >
                                                <BoldAdd
                                                    size={badgeSize}
                                                    color="surfaceLight"
                                                />
                                            </Badge>
                                        )}
                                    >
                                        <FaceIdLogo
                                            size={size}
                                            color="teal40"
                                        />
                                    </Avatar>
                                )}
                            />
                        )}
                    </ScrollContainer>
                    <SubtextListItem
                        onClick={() =>
                            onMsg({
                                type: 'on_card_import_on_import_keys_clicked',
                            })
                        }
                        subItems={
                            <Text>
                                <FormattedMessage
                                    id="card.choose-wallet.import-current-owner.sub-text"
                                    defaultMessage="Import private keys or seed phrase that owns your Gnosis Pay card"
                                />
                            </Text>
                        }
                        size="large"
                        primaryText={
                            <FormattedMessage
                                id="card.choose-wallet.import-current-owner"
                                defaultMessage="Import current card owner"
                            />
                        }
                        avatar={({ size }) => (
                            <LightDownload size={size} color="teal40" />
                        )}
                    />
                </Column>
            </Column>
        </Screen>
    )
}
