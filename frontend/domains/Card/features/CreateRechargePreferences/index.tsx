import { useEffect } from 'react'

import { StepWizard } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { CardBalance, CardConfig } from '@zeal/domains/Card'
import { NotConfiguredEarn, TakerType } from '@zeal/domains/Earn'
import { unsafeGetNotConfiguredEarn } from '@zeal/domains/Earn/helpers/unsafeGetNotConfiguredEarn'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'

import { InfoScreen } from './InfoScreen'
import { SelectEarnAccount } from './SelectEarnAccount'
import { SelectRechargeThreshold } from './SelectRechargeThreshold'

type Props = {
    cardReadonlySignerAddress: Web3.address.Address
    portfolioMap: PortfolioMap
    installationId: string
    cardConfig: CardConfig
    variant: 'order_card' | 'onboarding'
    cardBalance: CardBalance | null
    onMsg: (msg: Msg) => void
}
type Msg =
    | Extract<
          MsgOf<typeof InfoScreen>,
          {
              type: 'on_dont_link_account_clicked' | 'close'
          }
      >
    | Extract<
          MsgOf<typeof SelectEarnAccount>,
          {
              type: 'on_zero_percent_per_year_selected'
          }
      >
    | Extract<
          MsgOf<typeof SelectRechargeThreshold>,
          {
              type: 'recharge_preferences_created'
          }
      >

type State =
    | {
          type: 'info_screen'
      }
    | {
          type: 'select_earn_account'
          earn: NotConfiguredEarn
      }
    | {
          type: 'select_recharge_threshold'
          takerType: TakerType
      }

export const CreateRechargePreferences = ({
    cardConfig,
    cardBalance,
    cardReadonlySignerAddress,
    installationId,
    variant,
    onMsg,
    portfolioMap,
}: Props) => {
    const captureErrorOnce = useCaptureErrorOnce()

    const cachedEarn = unsafe_GetPortfolioCache2({
        portfolioMap,
        address: cardReadonlySignerAddress,
    })?.earn

    useEffect(() => {
        if (!cachedEarn) {
            captureErrorOnce(
                new ImperativeError(
                    '[Create recharge preferences] Earn not found in cache'
                )
            )
        }
    }, [cachedEarn, captureErrorOnce])

    const earn =
        cachedEarn ||
        unsafeGetNotConfiguredEarn({
            address: cardReadonlySignerAddress,
        })

    return (
        <StepWizard<State>
            initialStep={{
                type: 'info_screen',
            }}
        >
            {({ step, forwardTo, backTo }) => {
                switch (step.type) {
                    case 'info_screen':
                        return (
                            <InfoScreen
                                earn={earn}
                                variant={variant}
                                cardConfig={cardConfig}
                                cardBalance={cardBalance}
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_continue_clicked':
                                            switch (msg.selectedTaker.type) {
                                                case 'preselected_taker':
                                                    forwardTo({
                                                        type: 'select_recharge_threshold',
                                                        takerType:
                                                            msg.selectedTaker
                                                                .taker.type,
                                                    })
                                                    break
                                                case 'no_preselected_taker':
                                                    forwardTo({
                                                        type: 'select_earn_account',
                                                        earn: msg.selectedTaker
                                                            .earn,
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        msg.selectedTaker
                                                    )
                                            }
                                            break
                                        case 'on_dont_link_account_clicked':
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'select_earn_account':
                        return (
                            <SelectEarnAccount
                                earn={step.earn}
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_zero_percent_per_year_selected':
                                            onMsg(msg)
                                            break
                                        case 'close':
                                            backTo({ type: 'info_screen' })
                                            break
                                        case 'on_earn_account_selected':
                                            forwardTo({
                                                type: 'select_recharge_threshold',
                                                takerType: msg.takerType,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'select_recharge_threshold':
                        return (
                            <SelectRechargeThreshold
                                takerType={step.takerType}
                                cardConfig={cardConfig}
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'recharge_preferences_created':
                                            onMsg(msg)
                                            break
                                        case 'close': {
                                            switch (earn.type) {
                                                case 'configured':
                                                    backTo({
                                                        type: 'info_screen',
                                                    })
                                                    break
                                                case 'not_configured':
                                                    backTo({
                                                        type: 'select_earn_account',
                                                        earn,
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(earn)
                                            }
                                            break
                                        }
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
