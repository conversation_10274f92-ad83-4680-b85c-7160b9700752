import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { NULL_ADDRESS } from '@zeal/domains/Address/constants'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { FAKE_GNOSIS_PAY_DAPP } from '@zeal/domains/DApp/constants'
import { KeyStoreMap, Safe4337 } from '@zeal/domains/KeyStore'
import { ConstructSafeTransactionHash } from '@zeal/domains/KeyStore/domains/Safe/features/ConstructSafeTransactionHash'
import { SignSafeTransactionHash } from '@zeal/domains/KeyStore/domains/Safe/features/SignSafeTransactionHash'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { MetaTransactionData, OperationType } from '@zeal/domains/UserOperation'
import { SAFE_ABI } from '@zeal/domains/UserOperation/constants'

import { SignRelayTransactionMessage } from '../SignRelayTransactionMessage'

type Props = {
    delayModuleTransaction: MetaTransactionData
    cardOwner: Account
    keyStore: Safe4337

    installationId: string
    network: PredefinedNetwork
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof ConstructSafeTransactionHash>, { type: 'close' }>
    | Extract<
          MsgOf<typeof SignSafeTransactionHash>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >
    | MsgOf<typeof SignRelayTransactionMessage>

type State =
    | { type: 'construct_safe_transaction_hash' }
    | {
          type: 'sign_safe_transaction_hash'
          transactionHash: Hexadecimal.Hexadecimal
      }
    | {
          type: 'sign_safe_relay_transaction_message'
          transactionHashSignature: Hexadecimal.Hexadecimal
      }

export const SignSafeRelayTransaction = ({
    delayModuleTransaction,
    network,
    networkRPCMap,
    installationId,
    keyStore,
    cardOwner,
    feePresetMap,
    gasCurrencyPresetMap,
    portfolioMap,
    networkMap,
    keyStoreMap,
    accountsMap,
    sessionPassword,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'construct_safe_transaction_hash',
    })

    switch (state.type) {
        case 'construct_safe_transaction_hash':
            return (
                <ConstructSafeTransactionHash
                    installationId={installationId}
                    network={network}
                    networkRPCMap={networkRPCMap}
                    account={cardOwner}
                    transaction={delayModuleTransaction}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_safe_transaction_hash_constructed':
                                setState({
                                    type: 'sign_safe_transaction_hash',
                                    transactionHash: msg.transactionHash,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'sign_safe_transaction_hash':
            return (
                <SignSafeTransactionHash
                    actionSource={{
                        type: 'internal_sign',
                        dAppSiteInfo: FAKE_GNOSIS_PAY_DAPP,
                        transactionEventSource:
                            'gnosisPaySignSafeRelayTransaction',
                    }}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolio={unsafe_GetPortfolioCache2({
                        portfolioMap,
                        address: cardOwner.address,
                    })}
                    transactionHash={state.transactionHash}
                    account={cardOwner}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStore={keyStore}
                    keyStoreMap={keyStoreMap}
                    network={network}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'safe_transaction_hash_signed':
                                setState({
                                    type: 'sign_safe_relay_transaction_message',
                                    transactionHashSignature: msg.signature,
                                })
                                break
                            case 'drag':
                            case 'on_minimize_click':
                            case 'on_expand_request':
                            case 'on_wrong_network_accepted':
                                throw new ImperativeError(
                                    `Message cannot be fired in SignSafeRelayTransaction flow`,
                                    { msg }
                                )
                            case 'close':
                            case 'on_safe_deployemnt_cancelled':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_safe_deployment_error_popup_cancel_clicked':
                                onMsg({ type: 'close' })
                                break
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'sign_safe_relay_transaction_message':
            const safeTransaction: MetaTransactionData = {
                data: Web3.abi.encodeFunctionData({
                    abi: SAFE_ABI,
                    functionName: 'execTransaction',
                    args: [
                        delayModuleTransaction.to,
                        BigInt(delayModuleTransaction.value),
                        delayModuleTransaction.data,
                        OperationType.Call,
                        0n,
                        0n,
                        0n,
                        NULL_ADDRESS,
                        NULL_ADDRESS,
                        state.transactionHashSignature,
                    ],
                }),
                operation: OperationType.Call,
                to: cardOwner.address as `0x${string}`,
                value: '0x0',
            }

            return (
                <SignRelayTransactionMessage
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    transaction={safeTransaction}
                    network={network}
                    cardOwner={cardOwner}
                    keyStore={keyStore}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
