import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { CryptoCurrency } from '@zeal/domains/Currency'
import { SwapsIOQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import {
    SwapsIOQuoteRequest,
    SwapsIOQuoteValidationsErrors,
} from '@zeal/domains/Currency/domains/SwapsIO/api/fetchQuote'
import { CryptoMoney } from '@zeal/domains/Money'
import { Portfolio2, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

export type MaxBalanceLoadable = LoadableData<
    CryptoMoney,
    {
        swapQuoteRequest: SwapsIOQuoteRequest
        serverPortfolio: ServerPortfolio2
    }
>

export type Pollable = PollableData<
    Result<SwapsIOQuoteValidationsErrors, SwapsIOQuote>,
    SwapsIOQuoteRequest
>

type LimitsError =
    | {
          type: 'above_maximum_limit'
          limit: CryptoMoney | null
      }
    | { type: 'below_minimum_limit'; limit: CryptoMoney | null }

type MaxBalanceStillLoading = { type: 'max_balance_still_loading' }
type PollableNotLoaded = {
    type: 'pollable_not_loaded'
}
type NotEnoughBalance = {
    type: 'not_enough_balance'
}
type AmountRequired = {
    type: 'amount_required'
}
type RoutesNotFoundError = { type: 'routes_not_found' }

export type SubmitErrors =
    | PollableNotLoaded
    | LimitsError
    | NotEnoughBalance
    | AmountRequired
    | MaxBalanceStillLoading
    | RoutesNotFoundError

export type BannerError =
    | PollableNotLoaded
    | AmountRequired
    | LimitsError
    | RoutesNotFoundError

export type Errors = {
    amount?: NotEnoughBalance
    banner?: BannerError
    submit?: SubmitErrors
}

const validateAmountRequired = ({
    pollable,
}: {
    pollable: Pollable
}): Result<AmountRequired, string> => {
    if (!pollable.params.amount) {
        return failure({ type: 'amount_required' })
    }
    const amount = fromFixedWithFraction(
        pollable.params.amount,
        pollable.params.fromCurrency.fraction
    )
    if (amount === 0n) {
        return failure({ type: 'amount_required' })
    }
    return success(pollable.params.amount)
}

const validatePollableState = ({
    pollable,
}: {
    pollable: Pollable
}): Result<
    PollableNotLoaded,
    Result<SwapsIOQuoteValidationsErrors, SwapsIOQuote>
> => {
    switch (pollable.type) {
        case 'loaded':
            return success(pollable.data)
        case 'reloading':
        case 'subsequent_failed':
        case 'loading':
        case 'error':
            return failure({ type: 'pollable_not_loaded' })

        default:
            return notReachable(pollable)
    }
}
const validateAmount = ({
    senderPortfolio,
    maxBalanceLoadable,
    pollable,
}: {
    pollable: Pollable
    maxBalanceLoadable: MaxBalanceLoadable
    senderPortfolio: Portfolio2
}): Result<NotEnoughBalance, unknown> => {
    if (!pollable.params.amount) {
        return success(undefined)
    }

    return validateBalance({
        amount: pollable.params.amount,
        selectedCurrency: pollable.params.fromCurrency,
        senderPortfolio,
        maxBalanceLoadable,
    })
}

export const getMaxBalance = ({
    maxBalanceLoadable,
    senderPortfolio,
}: {
    maxBalanceLoadable: MaxBalanceLoadable
    senderPortfolio: Portfolio2
}) => {
    switch (maxBalanceLoadable.type) {
        case 'loading':
        case 'error':
            return getBalanceByCryptoCurrency2({
                currency:
                    maxBalanceLoadable.params.swapQuoteRequest.fromCurrency,
                serverPortfolio: senderPortfolio,
            })

        case 'loaded':
            return maxBalanceLoadable.data
        /* istanbul ignore next */
        default:
            return notReachable(maxBalanceLoadable)
    }
}

const validateBalance = ({
    amount,
    senderPortfolio,
    selectedCurrency,
    maxBalanceLoadable,
}: {
    amount: string
    maxBalanceLoadable: MaxBalanceLoadable
    selectedCurrency: CryptoCurrency
    senderPortfolio: Portfolio2
}): Result<NotEnoughBalance, unknown> => {
    const balance = getMaxBalance({
        maxBalanceLoadable,
        senderPortfolio,
    })

    return balance.amount <
        fromFixedWithFraction(amount, selectedCurrency.fraction)
        ? failure({ type: 'not_enough_balance' })
        : success(undefined)
}

const validateErrors = (
    data: Result<SwapsIOQuoteValidationsErrors, SwapsIOQuote>
): Result<LimitsError | RoutesNotFoundError, SwapsIOQuote> => {
    return data.mapError((errors) => {
        switch (errors.type) {
            case 'above_maximum':
                return {
                    type: 'above_maximum_limit',
                    limit: errors.max,
                }
            case 'below_minimum':
                return {
                    type: 'below_minimum_limit',
                    limit: errors.min,
                }
            case 'currency_not_supported':
            case 'amount_required':
                return {
                    type: 'routes_not_found',
                }
            default:
                return notReachable(errors)
        }
    })
}

const validateMaxBalanceLoadable = ({
    maxBalanceLoadable,
}: {
    maxBalanceLoadable: MaxBalanceLoadable
}): Result<MaxBalanceStillLoading, unknown> => {
    switch (maxBalanceLoadable.type) {
        case 'loading':
            return failure({ type: 'max_balance_still_loading' })
        case 'error':
        case 'loaded':
            return success(undefined)

        /* istanbul ignore next */
        default:
            return notReachable(maxBalanceLoadable)
    }
}

export const validate = ({
    pollable,
    senderPortfolio,
    maxBalanceLoadable,
}: {
    pollable: Pollable
    senderPortfolio: Portfolio2
    maxBalanceLoadable: MaxBalanceLoadable
}): Result<Errors, SwapsIOQuote> => {
    return shape({
        amount: validateAmount({
            pollable,
            senderPortfolio,
            maxBalanceLoadable,
        }),
        banner: validateAmountRequired({ pollable })
            .andThen(() => validatePollableState({ pollable }))
            .andThen(validateErrors),
        submit: validateAmountRequired({ pollable })
            .andThen((amount) =>
                validateBalance({
                    amount,
                    selectedCurrency: pollable.params.fromCurrency,
                    senderPortfolio,
                    maxBalanceLoadable,
                })
            )
            .andThen(() => validateMaxBalanceLoadable({ maxBalanceLoadable }))
            .andThen(() => validatePollableState({ pollable }))
            .andThen(validateErrors),
    }).map(({ submit }) => {
        return submit
    })
}
