import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SwapsIOQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import { SubmitSwap } from '@zeal/domains/Currency/domains/SwapsIO/features/SubmitSwap'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { SelectFromAsset } from '../../SelectFromAsset'

type Props = {
    state: State
    sender: Account
    senderPortfolio: Portfolio2
    supportedTopupCurrencies: CryptoCurrency[]
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    currencyPinMap: CurrencyPinMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap

    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof SelectFromAsset> | MsgOf<typeof SubmitSwap>

export type State =
    | { type: 'closed' }
    | { type: 'token_selector' }
    | { type: 'submit_swap'; quote: SwapsIOQuote }

export const Modal = ({
    accountsMap,
    currencyHiddenMap,
    currencyPinMap,
    feePresetMap,
    gasCurrencyPresetMap,
    gnosisPayAccountOnboardedState,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    sender,
    senderPortfolio,
    sessionPassword,
    state,
    supportedTopupCurrencies,
    onMsg,
    defaultCurrencyConfig,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'token_selector':
            return (
                <UIModal>
                    <SelectFromAsset
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        sender={sender}
                        supportedTopupCurrencies={supportedTopupCurrencies}
                        portfolio={senderPortfolio}
                        currencyPinMap={currencyPinMap}
                        keyStoreMap={keyStoreMap}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'submit_swap':
            return (
                <UIModal>
                    <SubmitSwap
                        source="card"
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        simulation={{
                            type: 'OrderCardTopupSignMessage',
                            from: state.quote.from,
                            to: state.quote.to,
                            card: gnosisPayAccountOnboardedState.selectedCard,
                            fromInDefaultCurrency:
                                state.quote.fromInDefaultCurrency,
                            toInDefaultCurrency:
                                state.quote.toInDefaultCurrency,
                        }}
                        installationId={installationId}
                        quote={state.quote}
                        keyStoreMap={keyStoreMap}
                        onMsg={onMsg}
                        networkMap={networkMap}
                        accountsMap={accountsMap}
                        networkRPCMap={networkRPCMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        sessionPassword={sessionPassword}
                        senderPortfolio={senderPortfolio}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
