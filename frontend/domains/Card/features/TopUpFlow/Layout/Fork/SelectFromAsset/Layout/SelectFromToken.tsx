import { useState } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'
import { SectionListData } from 'react-native'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { EmptyStateWidget } from '@zeal/uikit/EmptyStateWidget'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Tokens } from '@zeal/uikit/Icon/Empty'
import { OutlineSearch } from '@zeal/uikit/Icon/OutlineSearch'
import { Input } from '@zeal/uikit/Input'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { SectionList } from '@zeal/uikit/SectionList'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountSelector } from '@zeal/domains/Account/components/ActionBarAccountSelector'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import {
    CryptoCurrenciesSearchResult,
    searchCryptoCurrencies,
} from '@zeal/domains/Currency/helpers/searchCryptoCurrencies'
import { CurrentNetwork, NetworkMap } from '@zeal/domains/Network'
import { NetworkSelector } from '@zeal/domains/Network/components/NetworkSelector'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { Token2 } from '@zeal/domains/Token'
import { ListItem2 } from '@zeal/domains/Token/components/ListItem2'

type Props = {
    sender: Account
    supportedTopupCurrencies: CryptoCurrency[]
    currentNetwork: CurrentNetwork
    portfolio: Portfolio2
    currencyPinMap: CurrencyPinMap
    networkMap: NetworkMap
    currencyHiddenMap: CurrencyHiddenMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_network_filter_clicked' }
    | { type: 'on_crypto_currency_selected'; currency: CryptoCurrency }

const applySearchAndFilter = ({
    search,
    currencyPinMap,
    portfolio,
    currentNetwork,
    supportedTopupCurrencies,
    currencyHiddenMap,
    networkMap,
}: {
    currentNetwork: CurrentNetwork
    supportedTopupCurrencies: CryptoCurrency[]
    search: string
    portfolio: Portfolio2
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
}): CryptoCurrenciesSearchResult => {
    const networkCurrencies = supportedTopupCurrencies.filter((currency) => {
        switch (currentNetwork.type) {
            case 'all_networks':
                return true
            case 'specific_network':
                return (
                    currency.networkHexChainId ===
                    currentNetwork.network.hexChainId
                )
            /* istanbul ignore next */
            default:
                return notReachable(currentNetwork)
        }
    })

    return searchCryptoCurrencies({
        currencies: networkCurrencies,
        searchTerm: search,
        portfolio,
        currencyPinMap,
        currencyHiddenMap,
        networkMap,
    })
}

export const SelectFromToken = ({
    currentNetwork,
    onMsg,
    sender,
    networkMap,
    supportedTopupCurrencies,
    currencyPinMap,
    currencyHiddenMap,
    portfolio,
}: Props) => {
    const { formatMessage } = useIntl()

    const [search, setSearch] = useState<string>('')

    const searchResult = applySearchAndFilter({
        search,
        currencyPinMap,
        portfolio,
        currentNetwork,
        supportedTopupCurrencies,
        currencyHiddenMap,
        networkMap,
    })

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} shrink>
                <Column spacing={12}>
                    <ActionBar
                        top={
                            <ActionBarAccountSelector
                                account={sender}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'account_filter_click':
                                            onMsg({ type: 'close' })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg.type)
                                    }
                                }}
                            />
                        }
                        left={
                            <Clickable onClick={() => onMsg({ type: 'close' })}>
                                <Row spacing={4}>
                                    <BackIcon size={24} color="iconDefault" />
                                    <Text
                                        variant="title3"
                                        weight="semi_bold"
                                        color="textPrimary"
                                        id="select-card-from-token-title"
                                    >
                                        <FormattedMessage
                                            id="card-select-from-token-title"
                                            defaultMessage="Select From token"
                                        />
                                    </Text>
                                </Row>
                            </Clickable>
                        }
                        right={
                            <NetworkSelector
                                variant="on_light"
                                size={24}
                                currentNetwork={currentNetwork}
                                onClick={() => {
                                    onMsg({ type: 'on_network_filter_clicked' })
                                }}
                            />
                        }
                    />
                    <Input
                        keyboardType="default"
                        placeholder={formatMessage({
                            id: 'card-topup.select-token.searchPlaceholder',
                            defaultMessage: 'Search',
                        })}
                        leftIcon={
                            <OutlineSearch size={24} color="iconDefault" />
                        }
                        state="normal"
                        variant="regular"
                        onChange={(e) => setSearch(e.nativeEvent.text)}
                        value={search}
                        onSubmitEditing={noop}
                    />
                </Column>
                <Column spacing={8} shrink>
                    {(() => {
                        switch (searchResult.type) {
                            case 'no_currencies_found':
                                return (
                                    <EmptyStateWidget
                                        icon={({ size }) => (
                                            <Tokens
                                                size={size}
                                                color="backgroundLight"
                                            />
                                        )}
                                        size="regular"
                                        title={
                                            <FormattedMessage
                                                id="card-topup.select-token.emptyState"
                                                defaultMessage="We found no tokens"
                                            />
                                        }
                                    />
                                )
                            case 'grouped_results':
                                const sections = getTokenSections({
                                    portfolioCurrencies:
                                        searchResult.portfolioCurrencies,
                                    nonPortfolioCurrencies:
                                        searchResult.nonPortfolioCurrencies,
                                    portfolio,
                                })

                                return (
                                    <SectionList
                                        keyboardShouldPersistTaps="handled"
                                        variant="grouped"
                                        itemSpacing={8}
                                        sectionSpacing={8}
                                        sections={sections}
                                        renderItem={({ item: token }) => (
                                            <ListItem2
                                                variant="search"
                                                currencyHiddenMap={
                                                    currencyHiddenMap
                                                }
                                                currencyPinMap={currencyPinMap}
                                                key={token.balance.currency.id}
                                                networkMap={networkMap}
                                                aria-current={false}
                                                token={token}
                                                onClick={() =>
                                                    onMsg({
                                                        type: 'on_crypto_currency_selected',
                                                        currency:
                                                            token.balance
                                                                .currency,
                                                    })
                                                }
                                            />
                                        )}
                                    />
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(searchResult)
                        }
                    })()}
                </Column>
            </Column>
        </Screen>
    )
}

const getTokenSections = ({
    portfolioCurrencies,
    nonPortfolioCurrencies,
    portfolio,
}: {
    portfolioCurrencies: CryptoCurrency[]
    nonPortfolioCurrencies: CryptoCurrency[]
    portfolio: Portfolio2
}): SectionListData<Token2>[] => {
    const mapCurrencies = (currencies: CryptoCurrency[]) =>
        currencies.map((currency) =>
            getTokenByCryptoCurrency3({ currency, serverPortfolio: portfolio })
        )

    const portfolioTokens = mapCurrencies(portfolioCurrencies)
    const nonPortfolioTokens = mapCurrencies(nonPortfolioCurrencies)

    return [{ data: portfolioTokens }, { data: nonPortfolioTokens }]
}
