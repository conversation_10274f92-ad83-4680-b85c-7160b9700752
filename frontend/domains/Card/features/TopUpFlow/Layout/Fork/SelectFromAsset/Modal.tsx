import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import {
    CurrentNetwork,
    NetworkMap,
    NetworkRPCMap,
} from '@zeal/domains/Network'
import { NetworkFilter } from '@zeal/domains/Network/features/Fillter'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    state: State
    portfolio: Portfolio2 | null
    account: Account
    keyStoreMap: KeyStoreMap
    installationId: string
    networks: CurrentNetwork[]
    currentNetwork: CurrentNetwork
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof NetworkFilter>

export type State = { type: 'closed' } | { type: 'network_filter' }

export const Modal = ({
    state,
    onMsg,
    portfolio,
    currencyHiddenMap,
    networkMap,
    networkRPCMap,
    keyStoreMap,
    networks,
    currentNetwork,
    account,
    installationId,
    defaultCurrencyConfig,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'network_filter':
            return (
                <UIModal>
                    <NetworkFilter
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        networkMap={networkMap}
                        currencyHiddenMap={currencyHiddenMap}
                        account={account}
                        currentNetwork={currentNetwork}
                        networkRPCMap={networkRPCMap}
                        keyStoreMap={keyStoreMap}
                        networks={networks}
                        portfolio={portfolio}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
