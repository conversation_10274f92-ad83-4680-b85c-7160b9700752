import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { CurrentNetwork, NetworkMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'

import { SelectFromAsset } from './SelectFromAsset'
import { SelectFromToken } from './SelectFromToken'

type Props = {
    sender: Account
    supportedTopupCurrencies: CryptoCurrency[]
    currentNetwork: CurrentNetwork
    portfolio: Portfolio2
    currencyPinMap: CurrencyPinMap
    networkMap: NetworkMap
    currencyHiddenMap: CurrencyHiddenMap
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof SelectFromToken> | MsgOf<typeof SelectFromAsset>

export const Layout = ({
    currentNetwork,
    onMsg,
    sender,
    networkMap,
    supportedTopupCurrencies,
    currencyPinMap,
    currencyHiddenMap,
    portfolio,
}: Props) => {
    switch (portfolio.earn.type) {
        case 'not_configured':
            return (
                <SelectFromToken
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    sender={sender}
                    supportedTopupCurrencies={supportedTopupCurrencies}
                    currentNetwork={currentNetwork}
                    portfolio={portfolio}
                    currencyPinMap={currencyPinMap}
                    onMsg={onMsg}
                />
            )
        case 'configured':
            return (
                <SelectFromAsset
                    earn={portfolio.earn}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    sender={sender}
                    supportedTopupCurrencies={supportedTopupCurrencies}
                    currentNetwork={currentNetwork}
                    portfolio={portfolio}
                    currencyPinMap={currencyPinMap}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(portfolio.earn)
    }
}
