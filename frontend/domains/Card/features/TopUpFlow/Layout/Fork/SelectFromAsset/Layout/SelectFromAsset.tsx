import { useState } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'
import { SectionListData } from 'react-native'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { EmptyStateWidget } from '@zeal/uikit/EmptyStateWidget'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Tokens } from '@zeal/uikit/Icon/Empty'
import { OutlineSearch } from '@zeal/uikit/Icon/OutlineSearch'
import { Input } from '@zeal/uikit/Input'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { SectionList } from '@zeal/uikit/SectionList'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountSelector } from '@zeal/domains/Account/components/ActionBarAccountSelector'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { searchCryptoCurrencies } from '@zeal/domains/Currency/helpers/searchCryptoCurrencies'
import { ConfiguredEarn, DeployedTaker, Taker } from '@zeal/domains/Earn'
import { TakerListItem } from '@zeal/domains/Earn/components/TakerListItem'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { CurrentNetwork, NetworkMap } from '@zeal/domains/Network'
import { NetworkSelector } from '@zeal/domains/Network/components/NetworkSelector'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { Token2 } from '@zeal/domains/Token'
import { ListItem2 } from '@zeal/domains/Token/components/ListItem2'

type Props = {
    earn: ConfiguredEarn

    sender: Account
    supportedTopupCurrencies: CryptoCurrency[]
    currentNetwork: CurrentNetwork
    portfolio: Portfolio2
    currencyPinMap: CurrencyPinMap
    networkMap: NetworkMap
    currencyHiddenMap: CurrencyHiddenMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_network_filter_clicked' }
    | { type: 'on_crypto_currency_selected'; currency: CryptoCurrency }
    | {
          type: 'on_earn_account_selected'
          taker: DeployedTaker
          earn: ConfiguredEarn
      }

type SearchResult =
    | {
          type: 'grouped_results'
          earnAccounts: DeployedTaker[]
          portfolioCurrencies: CryptoCurrency[]
          nonPortfolioCurrencies: CryptoCurrency[]
      }
    | { type: 'no_results_found' }

const getDeployedTakers = (takers: Taker[]): DeployedTaker[] =>
    takers.filter((taker): taker is DeployedTaker => {
        switch (taker.state) {
            case 'not_deployed':
                return false
            case 'deployed':
                return true
            /* istanbul ignore next */
            default:
                return notReachable(taker)
        }
    })

const searchEarnAccounts = ({
    earn,
    search,
    currentNetwork,
}: {
    search: string
    earn: ConfiguredEarn
    currentNetwork: CurrentNetwork
}): DeployedTaker[] => {
    const deployedTakers = (() => {
        switch (currentNetwork.type) {
            case 'all_networks':
                return getDeployedTakers(earn.takers)
            case 'specific_network':
                return currentNetwork.network.hexChainId ===
                    EARN_NETWORK.hexChainId
                    ? getDeployedTakers(earn.takers)
                    : []
            /* istanbul ignore next */
            default:
                return notReachable(currentNetwork)
        }
    })()

    return deployedTakers.filter((taker) => {
        const portfolio = earn.takerPortfolioMap[taker.type]
        const userCurrency = portfolio.userCurrencyRate.quote

        const searchFields = [
            userCurrency.name,
            userCurrency.code,
            userCurrency.symbol,
            EARN_NETWORK.name,
        ]

        return searchFields.reduce(
            (result, field) =>
                result ||
                field.toLocaleLowerCase().includes(search.toLocaleLowerCase()),
            false
        )
    })
}

const applySearchAndFilter = ({
    search,
    currencyPinMap,
    portfolio,
    currentNetwork,
    supportedTopupCurrencies,
    currencyHiddenMap,
    earn,
    networkMap,
}: {
    currentNetwork: CurrentNetwork
    supportedTopupCurrencies: CryptoCurrency[]
    search: string
    portfolio: Portfolio2
    earn: ConfiguredEarn
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
}): SearchResult => {
    const networkCurrencies = supportedTopupCurrencies.filter((currency) => {
        switch (currentNetwork.type) {
            case 'all_networks':
                return true
            case 'specific_network':
                return (
                    currency.networkHexChainId ===
                    currentNetwork.network.hexChainId
                )
            /* istanbul ignore next */
            default:
                return notReachable(currentNetwork)
        }
    })

    const currenciesResult = searchCryptoCurrencies({
        currencies: networkCurrencies,
        searchTerm: search,
        portfolio,
        currencyPinMap,
        currencyHiddenMap,
        networkMap,
    })

    const earnAccounts = searchEarnAccounts({
        earn,
        currentNetwork,
        search,
    })

    switch (currenciesResult.type) {
        case 'grouped_results':
            return {
                type: 'grouped_results',
                portfolioCurrencies: currenciesResult.portfolioCurrencies,
                nonPortfolioCurrencies: currenciesResult.nonPortfolioCurrencies,
                earnAccounts,
            }
        case 'no_currencies_found':
            return earnAccounts.length
                ? {
                      type: 'grouped_results',
                      earnAccounts,
                      portfolioCurrencies: [],
                      nonPortfolioCurrencies: [],
                  }
                : { type: 'no_results_found' }
        /* istanbul ignore next */
        default:
            return notReachable(currenciesResult)
    }
}

export const SelectFromAsset = ({
    currentNetwork,
    onMsg,
    earn,
    sender,
    networkMap,
    supportedTopupCurrencies,
    currencyPinMap,
    currencyHiddenMap,
    portfolio,
}: Props) => {
    const { formatMessage } = useIntl()

    const [search, setSearch] = useState<string>('')

    const searchResult = applySearchAndFilter({
        search,
        currencyPinMap,
        portfolio,
        currentNetwork,
        supportedTopupCurrencies,
        currencyHiddenMap,
        networkMap,
        earn,
    })

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} shrink>
                <Column spacing={12}>
                    <ActionBar
                        top={
                            <ActionBarAccountSelector
                                account={sender}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'account_filter_click':
                                            onMsg({ type: 'close' })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg.type)
                                    }
                                }}
                            />
                        }
                        left={
                            <Clickable onClick={() => onMsg({ type: 'close' })}>
                                <Row spacing={4}>
                                    <BackIcon size={24} color="iconDefault" />
                                    <Text
                                        variant="title3"
                                        weight="semi_bold"
                                        color="textPrimary"
                                        id="select-card-from-token-title"
                                    >
                                        <FormattedMessage
                                            id="card-select-from-token-title"
                                            defaultMessage="Select From token"
                                        />
                                    </Text>
                                </Row>
                            </Clickable>
                        }
                        right={
                            <NetworkSelector
                                variant="on_light"
                                size={24}
                                currentNetwork={currentNetwork}
                                onClick={() => {
                                    onMsg({ type: 'on_network_filter_clicked' })
                                }}
                            />
                        }
                    />
                    <Input
                        keyboardType="default"
                        placeholder={formatMessage({
                            id: 'card-topup.select-token.searchPlaceholder',
                            defaultMessage: 'Search',
                        })}
                        leftIcon={
                            <OutlineSearch size={24} color="iconDefault" />
                        }
                        state="normal"
                        variant="regular"
                        onChange={(e) => setSearch(e.nativeEvent.text)}
                        value={search}
                        onSubmitEditing={noop}
                    />
                </Column>
                <Column spacing={8} shrink>
                    {(() => {
                        switch (searchResult.type) {
                            case 'no_results_found':
                                return (
                                    <EmptyStateWidget
                                        icon={({ size }) => (
                                            <Tokens
                                                size={size}
                                                color="backgroundLight"
                                            />
                                        )}
                                        size="regular"
                                        title={
                                            <FormattedMessage
                                                id="card-topup.select-token.emptyState"
                                                defaultMessage="We found no tokens"
                                            />
                                        }
                                    />
                                )
                            case 'grouped_results':
                                const sections = getTokenSections({
                                    portfolioCurrencies:
                                        searchResult.portfolioCurrencies,
                                    nonPortfolioCurrencies:
                                        searchResult.nonPortfolioCurrencies,
                                    portfolio,
                                    earnAccounts: searchResult.earnAccounts,
                                })

                                return (
                                    <SectionList
                                        keyboardShouldPersistTaps="handled"
                                        variant="grouped"
                                        itemSpacing={8}
                                        sectionSpacing={8}
                                        sections={sections}
                                        renderItem={({ item }) => {
                                            switch (item.type) {
                                                case 'earn_account':
                                                    return (
                                                        <TakerListItem
                                                            aria-current={false}
                                                            taker={item.taker}
                                                            takerPortfolio={
                                                                earn
                                                                    .takerPortfolioMap[
                                                                    item.taker
                                                                        .type
                                                                ]
                                                            }
                                                            onClick={() =>
                                                                onMsg({
                                                                    type: 'on_earn_account_selected',
                                                                    taker: item.taker,
                                                                    earn,
                                                                })
                                                            }
                                                        />
                                                    )
                                                case 'token':
                                                    const { token } = item
                                                    return (
                                                        <ListItem2
                                                            variant="search"
                                                            currencyHiddenMap={
                                                                currencyHiddenMap
                                                            }
                                                            currencyPinMap={
                                                                currencyPinMap
                                                            }
                                                            key={
                                                                token.balance
                                                                    .currency.id
                                                            }
                                                            networkMap={
                                                                networkMap
                                                            }
                                                            aria-current={false}
                                                            token={token}
                                                            onClick={() =>
                                                                onMsg({
                                                                    type: 'on_crypto_currency_selected',
                                                                    currency:
                                                                        token
                                                                            .balance
                                                                            .currency,
                                                                })
                                                            }
                                                        />
                                                    )
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(item)
                                            }
                                        }}
                                    />
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(searchResult)
                        }
                    })()}
                </Column>
            </Column>
        </Screen>
    )
}

type TopUpAsset =
    | {
          type: 'earn_account'
          taker: DeployedTaker
      }
    | { type: 'token'; token: Token2 }

const getTokenSections = ({
    portfolioCurrencies,
    nonPortfolioCurrencies,
    portfolio,
    earnAccounts,
}: {
    portfolioCurrencies: CryptoCurrency[]
    nonPortfolioCurrencies: CryptoCurrency[]
    earnAccounts: DeployedTaker[]
    portfolio: Portfolio2
}): SectionListData<TopUpAsset>[] => {
    const mapCurrencies = (currencies: CryptoCurrency[]) =>
        currencies.map((currency) => ({
            type: 'token' as const,
            token: getTokenByCryptoCurrency3({
                currency,
                serverPortfolio: portfolio,
            }),
        }))

    const portfolioTokens = mapCurrencies(portfolioCurrencies)
    const nonPortfolioTokens = mapCurrencies(nonPortfolioCurrencies)
    const earnAccountAssets = earnAccounts.map((taker) => ({
        type: 'earn_account' as const,
        taker,
    }))

    return [
        { data: earnAccountAssets },
        { data: portfolioTokens },
        { data: nonPortfolioTokens },
    ]
}
