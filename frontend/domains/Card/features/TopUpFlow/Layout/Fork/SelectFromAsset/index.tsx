import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import {
    CurrentNetwork,
    NetworkHexId,
    NetworkMap,
    NetworkRPCMap,
} from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    sender: Account
    supportedTopupCurrencies: CryptoCurrency[]
    portfolio: Portfolio2
    currencyPinMap: CurrencyPinMap
    keyStoreMap: KeyStoreMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_crypto_currency_selected'
                  | 'on_earn_account_selected'
                  | 'close'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          { type: 'on_rpc_change_confirmed' | 'on_select_rpc_click' }
      >

const getSupportedNetworks = ({
    supportedTopupCurrencies,
    networkMap,
}: {
    supportedTopupCurrencies: CryptoCurrency[]
    networkMap: NetworkMap
}): CurrentNetwork[] => {
    const networkHexIds = new Set<NetworkHexId>(
        supportedTopupCurrencies.map((currency) => currency.networkHexChainId)
    )

    return [
        { type: 'all_networks' },
        ...Array.from(networkHexIds).map((networkHexId) => ({
            type: 'specific_network' as const,
            network: findNetworkByHexChainId(networkHexId, networkMap),
        })),
    ]
}

export const SelectFromAsset = ({
    portfolio,
    currencyPinMap,
    supportedTopupCurrencies,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    defaultCurrencyConfig,
    onMsg,
    sender,
}: Props) => {
    const [selectedNetwork, setSelectedNetwork] = useState<CurrentNetwork>({
        type: 'all_networks',
    })

    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Layout
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                sender={sender}
                supportedTopupCurrencies={supportedTopupCurrencies}
                currentNetwork={selectedNetwork}
                portfolio={portfolio}
                currencyPinMap={currencyPinMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_network_filter_clicked':
                            setModal({ type: 'network_filter' })
                            break
                        case 'close':
                        case 'on_crypto_currency_selected':
                        case 'on_earn_account_selected':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                state={modal}
                portfolio={portfolio}
                account={sender}
                keyStoreMap={keyStoreMap}
                installationId={installationId}
                networks={getSupportedNetworks({
                    networkMap,
                    supportedTopupCurrencies,
                })}
                currentNetwork={selectedNetwork}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_network_item_click':
                            setSelectedNetwork(msg.network)
                            setModal({ type: 'closed' })
                            break
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
