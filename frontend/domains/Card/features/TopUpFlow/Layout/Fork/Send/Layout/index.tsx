import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardTopUpRequest,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Form } from './Form'
import { Form as SendForm } from './Form/Layout'
import { SubmitTransaction } from './SubmitTransaction'

type Props = {
    installationId: string
    portfolio: Portfolio2
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    sender: Account
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    feePresetMap: FeePresetMap
    sessionPassword: string
    gasCurrencyPresetMap: GasCurrencyPresetMap

    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Form>,
          {
              type:
                  | 'close'
                  | 'on_from_currency_clicked'
                  | 'on_from_account_click'
          }
      >
    | Extract<
          MsgOf<typeof SubmitTransaction>,
          {
              type:
                  | 'cancel_submitted'
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_completed_transaction_close_click'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_transaction_cancelled_successfully_close_clicked'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
          }
      >

type State =
    | { type: 'form'; initialForm: SendForm }
    | {
          type: 'submit_transaction'
          topUpRequest: CardTopUpRequest
          form: SendForm
      }

export const Layout = ({
    gnosisPayAccountOnboardedState,
    installationId,
    onMsg,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    sender,
    gasCurrencyPresetMap,
    feePresetMap,
    sessionPassword,
    accountsMap,
    portfolio,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'form',
        initialForm: {
            type: 'amount_in_tokens',
            amount: null,
        },
    })

    switch (state.type) {
        case 'form':
            return (
                <Form
                    installationId={installationId}
                    networkRPCMap={networkRPCMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    senderPortfolio={portfolio}
                    initialForm={state.initialForm}
                    networkMap={networkMap}
                    sender={sender}
                    gnosisPayAccountOnboardedState={
                        gnosisPayAccountOnboardedState
                    }
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_from_currency_clicked':
                            case 'on_from_account_click':
                                onMsg(msg)
                                break
                            case 'on_submit_form':
                                setState({
                                    type: 'submit_transaction',
                                    form: msg.form,
                                    topUpRequest: msg.request,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit_transaction':
            return (
                <SubmitTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    topUpRequest={state.topUpRequest}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={portfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_sign_cancel_button_clicked':
                            case 'transaction_failure_accepted':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_minimize_click':
                            case 'on_close_transaction_status_not_found_modal':
                                setState({
                                    type: 'form',
                                    initialForm: state.form,
                                })
                                break
                            case 'on_expand_request':
                            case 'on_user_operation_bundled':
                            case 'drag':
                                break

                            case 'on_completed_transaction_close_click':
                            case 'on_completed_safe_transaction_close_click':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'import_keys_button_clicked':
                            case 'transaction_submited':
                            case 'cancel_submitted':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'transaction_request_replaced':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
