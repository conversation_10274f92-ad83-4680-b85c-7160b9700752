import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { SelectFromAsset } from '../SelectFromAsset'

type Props = {
    state: State
    sender: Account
    senderPortfolio: Portfolio2
    supportedTopupCurrencies: CryptoCurrency[]
    currencyPinMap: CurrencyPinMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap

    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof SelectFromAsset>

export type State = { type: 'closed' } | { type: 'token_selector' }

export const Modal = ({
    currencyHiddenMap,
    currencyPinMap,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    sender,
    senderPortfolio,
    state,
    supportedTopupCurrencies,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'token_selector':
            return (
                <UIModal>
                    <SelectFromAsset
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        sender={sender}
                        supportedTopupCurrencies={supportedTopupCurrencies}
                        portfolio={senderPortfolio}
                        currencyPinMap={currencyPinMap}
                        keyStoreMap={keyStoreMap}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
