import { notReachable } from '@zeal/toolkit'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import { GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Form as SendForm, Layout } from './Layout'

type Props = {
    initialForm: SendForm
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    sender: Account
    senderPortfolio: Portfolio2
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = Extract<
    MsgOf<typeof Layout>,
    {
        type:
            | 'on_submit_form'
            | 'close'
            | 'on_from_currency_clicked'
            | 'on_from_account_click'
    }
>

export const Form = ({
    networkMap,
    networkRPCMap,
    sender,
    initialForm,
    gnosisPayAccountOnboardedState,
    senderPortfolio,
    installationId,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const cardToken = getTokenByCryptoCurrency3({
        currency: gnosisPayAccountOnboardedState.cardSafe.cryptoCurrency,
        serverPortfolio: senderPortfolio,
    })

    const cardCurrency = cardToken.balance.currency

    const [pollable, setPollable] = useLoadedPollableData(
        fetchRate,
        {
            type: 'loaded',
            params: {
                cryptoCurrency: cardCurrency,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
            },
            data: cardToken.rate,
        },
        { stopIf: () => false, pollIntervalMilliseconds: 2000 }
    )

    return (
        <Layout
            installationId={installationId}
            senderPortfolio={senderPortfolio}
            initialForm={initialForm}
            fxRate={pollable.data}
            networkMap={networkMap}
            sender={sender}
            gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
            onMsg={(msg) => {
                switch (msg.type) {
                    case 'close':
                    case 'on_submit_form':
                    case 'on_from_currency_clicked':
                    case 'on_from_account_click':
                        onMsg(msg)
                        break
                    case 'on_form_change':
                        /**
                         * TODO @resetko-zeal
                         * this should be reactive if we need to update the rate, but TBH currently this flow is not using current rate for transaction creation,
                         * so since rate is "captured" in the form state, even if it updates in this pollable, it won't be updated in the form automatically, we need a hook for that.
                         * This brings us to the problem of 2way sync between this parent and the form state held in Layout component.
                         * The proper fix for this bug should be to hold form state within pollable, and hence keep rate always up to date, so when user press submit - it's correct rate.
                         */
                        setPollable({
                            type: 'loaded',
                            data: pollable.data,
                            params: {
                                ...pollable.params,
                            },
                        })
                        break
                    /* istanbul ignore next */
                    default:
                        return notReachable(msg)
                }
            }}
        />
    )
}
