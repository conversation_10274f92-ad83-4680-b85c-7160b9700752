import { useMemo } from 'react'

import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { CardTopUpRequest } from '@zeal/domains/Card'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { SimulationResult } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'

type Props = {
    topUpRequest: CardTopUpRequest
    installationId: string
    portfolio: Portfolio2
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    sessionPassword: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof SendTransaction>

const fetchCardTopUpSimulation = async ({
    topUpRequest,
}: {
    topUpRequest: CardTopUpRequest
}): Promise<SimulationResult> => ({
    type: 'simulated',
    simulation: {
        transaction: {
            type: 'CardTopUpTrx' as const,
            topUpRequest,
        },
        checks: [],
        currencies: {
            [topUpRequest.amount.currency.id]: topUpRequest.amount.currency,
        },
    },
})

export const SubmitTransaction = ({
    topUpRequest,
    installationId,
    onMsg,
    portfolio,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    gasCurrencyPresetMap,
    feePresetMap,
    sessionPassword,
    accountsMap,
    defaultCurrencyConfig,
}: Props) => {
    const ethSendTransaction = useMemo(
        () =>
            createTransferEthSendTransaction({
                amount: topUpRequest.amount,
                from: topUpRequest.sender.address,
                network: CARD_NETWORK,
                to: topUpRequest.cardSafeAddress,
            }),
        [topUpRequest]
    )

    return (
        <SendTransaction
            defaultCurrencyConfig={defaultCurrencyConfig}
            fetchSimulationByRequest={() =>
                fetchCardTopUpSimulation({ topUpRequest })
            }
            fetchTransactionResultByRequest={async () => ({
                transaction: {
                    type: 'CardTopUpTrx' as const,
                    topUpRequest,
                },
                currencies: {},
            })}
            sendTransactionRequests={[ethSendTransaction]}
            gasCurrencyPresetMap={gasCurrencyPresetMap}
            portfolio={portfolio}
            feePresetMap={feePresetMap}
            networkMap={networkMap}
            installationId={installationId}
            accounts={accountsMap}
            keystores={keyStoreMap}
            state={{ type: 'maximised' }}
            network={CARD_NETWORK}
            networkRPCMap={networkRPCMap}
            account={topUpRequest.sender}
            sessionPassword={sessionPassword}
            actionSource={{
                type: 'internal',
                transactionEventSource: 'cardAddCash',
            }}
            onMsg={onMsg}
        />
    )
}
