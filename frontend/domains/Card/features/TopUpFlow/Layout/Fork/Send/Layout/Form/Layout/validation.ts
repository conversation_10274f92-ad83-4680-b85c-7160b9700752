import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { CryptoCurrency } from '@zeal/domains/Currency'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { revert2 } from '@zeal/domains/FXRate/helpers/revert'
import { CryptoMoney } from '@zeal/domains/Money'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

import { Form } from './index'

type BalanceError = {
    type: 'not_enough_tokens'
}

type FormErrors = {
    amount?: BalanceError
    submit?: BalanceError | { type: 'zero_amount' }
}

const validateTokenBalance = ({
    form,
    portfolio,
    cardCurrency,
}: {
    form: Form
    cardCurrency: CryptoCurrency
    portfolio: Portfolio2
}): Result<BalanceError, CryptoMoney> => {
    const cardCurrencyBalance = getBalanceByCryptoCurrency2({
        currency: cardCurrency,
        serverPortfolio: portfolio,
    })

    switch (form.type) {
        case 'amount_in_tokens': {
            const amount = fromFixedWithFraction(
                form.amount,
                cardCurrency.fraction
            )

            return amount <= cardCurrencyBalance.amount
                ? success({
                      amount: amount,
                      currency: cardCurrency,
                  })
                : failure({ type: 'not_enough_tokens' })
        }

        case 'amount_in_default_currency': {
            const defaultCurrency = form.fxRate.quote

            const inputAmount = fromFixedWithFraction(
                form.amount,
                defaultCurrency.fraction
            )

            const revertedRate = revert2({
                rate: form.fxRate,
                extraPrecision: 0,
            })

            const cryptoAmount = applyRate2({
                baseAmount: { amount: inputAmount, currency: defaultCurrency },
                rate: revertedRate,
            })

            return cryptoAmount.amount <= cardCurrencyBalance.amount
                ? success(cryptoAmount)
                : failure({ type: 'not_enough_tokens' })
        }
        /* istanbul ignore next */
        default:
            return notReachable(form)
    }
}

const validateZeroAmount = (
    tokenAmount: CryptoMoney
): Result<{ type: 'zero_amount' }, CryptoMoney> =>
    tokenAmount.amount > 0n
        ? success(tokenAmount)
        : failure({ type: 'zero_amount' })

export const validate = ({
    form,
    cardCurrency,
    portfolio,
}: {
    form: Form
    cardCurrency: CryptoCurrency
    portfolio: Portfolio2
}): Result<FormErrors, CryptoMoney> =>
    shape({
        amount: validateTokenBalance({ cardCurrency, form, portfolio }),
        submit: validateTokenBalance({ cardCurrency, form, portfolio }).andThen(
            (tokenAmount) => validateZeroAmount(tokenAmount)
        ),
    }).map(({ submit }) => submit)
