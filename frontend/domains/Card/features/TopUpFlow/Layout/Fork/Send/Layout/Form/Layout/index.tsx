import { useState } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Divider } from '@zeal/uikit/Divider'
import { FancyButton } from '@zeal/uikit/FancyButton'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'

import { Account } from '@zeal/domains/Account'
import { AvatarWithoutBadge as AccountAvatar } from '@zeal/domains/Account/components/Avatar'
import {
    CardTopUpRequest,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { CardListItem } from '@zeal/domains/Card/components/CardListItem'
import { CryptoCurrency, FiatCurrency } from '@zeal/domains/Currency'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { FXRate2 } from '@zeal/domains/FXRate'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

import { SecondaryAmountButton } from './SecondaryAmountButton'
import { validate } from './validation'

type Props = {
    initialForm: Form
    fxRate: FXRate2<CryptoCurrency, FiatCurrency> | null
    senderPortfolio: Portfolio2
    networkMap: NetworkMap
    installationId: string
    sender: Account
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_from_currency_clicked' }
    | {
          type: 'on_submit_form'
          form: Form
          request: CardTopUpRequest
      }
    | { type: 'on_form_change' }
    | { type: 'on_from_account_click' }

export type Form =
    | {
          type: 'amount_in_tokens'
          amount: string | null
      }
    | {
          type: 'amount_in_default_currency'
          amount: string | null
          fxRate: FXRate2<CryptoCurrency, FiatCurrency>
      }

const getPrefix = (form: Form): string => {
    switch (form.type) {
        case 'amount_in_tokens':
            return ''
        case 'amount_in_default_currency':
            return form.fxRate.quote.symbol
        /* istanbul ignore next */
        default:
            return notReachable(form)
    }
}

export const Layout = ({
    onMsg,
    networkMap,
    fxRate,
    initialForm,
    gnosisPayAccountOnboardedState,
    sender,
    installationId,
    senderPortfolio,
}: Props) => {
    const { formatMessage } = useIntl()
    const [form, setForm] = useState<Form>(initialForm)

    const cardCurrency = gnosisPayAccountOnboardedState.cardSafe.cryptoCurrency

    const validationResult = validate({
        form,
        cardCurrency,
        portfolio: senderPortfolio,
    })

    const errors = validationResult.getFailureReason() || {}

    const prefix = getPrefix(form)

    const onSubmit = () =>
        validationResult.tap((result) =>
            onMsg({
                type: 'on_submit_form',
                form,
                request: {
                    amount: result,
                    card: gnosisPayAccountOnboardedState.selectedCard,
                    cardSafeAddress:
                        gnosisPayAccountOnboardedState.cardSafe.address,
                    sender,
                },
            })
        )

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} fill>
                <UIActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <UIActionBar.Header>
                                    <FormattedMessage
                                        id="send-card-token.form.title"
                                        defaultMessage="Add cash to Card"
                                    />
                                </UIActionBar.Header>
                            </Row>
                        </Clickable>
                    }
                />

                <Column spacing={16} shrink alignY="stretch">
                    <ScrollContainer withFloatingActions={false}>
                        <Column spacing={4}>
                            <AmountInput
                                top={
                                    <Column spacing={0}>
                                        <FancyButton
                                            color="secondary"
                                            rounded
                                            left={() => (
                                                <Row grow shrink spacing={4}>
                                                    <AccountAvatar
                                                        size={12}
                                                        account={sender}
                                                    />
                                                    <Text
                                                        variant="caption1"
                                                        color="gray40"
                                                        weight="medium"
                                                        ellipsis
                                                    >
                                                        {sender.label}
                                                    </Text>
                                                </Row>
                                            )}
                                            right={({ color }) => (
                                                <LightArrowDown2
                                                    size={16}
                                                    color={color}
                                                />
                                            )}
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_from_account_click',
                                                })
                                            }}
                                        />
                                        <Divider variant="secondary" />
                                    </Column>
                                }
                                content={{
                                    topLeft: (
                                        <IconButton
                                            variant="on_light"
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_from_currency_clicked',
                                                })
                                            }}
                                        >
                                            {({ color }) => (
                                                <Row spacing={4}>
                                                    <CurrencyAvatar
                                                        currency={cardCurrency}
                                                        rightBadge={({
                                                            size,
                                                        }) => (
                                                            <Badge
                                                                size={size}
                                                                network={findNetworkByHexChainId(
                                                                    cardCurrency.networkHexChainId,
                                                                    networkMap
                                                                )}
                                                            />
                                                        )}
                                                        size={24}
                                                    />
                                                    <Text
                                                        variant="title3"
                                                        color="textPrimary"
                                                        weight="medium"
                                                    >
                                                        {cardCurrency.code}
                                                    </Text>

                                                    <LightArrowDown2
                                                        size={18}
                                                        color={color}
                                                    />
                                                </Row>
                                            )}
                                        </IconButton>
                                    ),
                                    topRight: ({ onBlur, onFocus }) => (
                                        <AmountInput.Input
                                            onFocus={onFocus}
                                            onBlur={onBlur}
                                            label={formatMessage({
                                                id: 'send-card-token.form.send-amount',
                                                defaultMessage: 'Top up amount',
                                            })}
                                            fraction={cardCurrency.fraction}
                                            autoFocus
                                            readOnly={false}
                                            prefix={prefix}
                                            amount={form.amount}
                                            onChange={(value) => {
                                                setForm({
                                                    ...form,
                                                    amount: value,
                                                })
                                                onMsg({
                                                    type: 'on_form_change',
                                                })
                                            }}
                                            onSubmitEditing={onSubmit}
                                        />
                                    ),
                                    bottomLeft: (
                                        <MaxButton
                                            installationId={installationId}
                                            location="card_add_cash"
                                            balance={getBalanceByCryptoCurrency2(
                                                {
                                                    currency: cardCurrency,
                                                    serverPortfolio:
                                                        senderPortfolio,
                                                }
                                            )}
                                            onMsg={(msg) => {
                                                switch (msg.type) {
                                                    case 'on_amount_change':
                                                        // TODO @resetko-zeal why would we need to set state and do onMsg at the same time?
                                                        setForm({
                                                            type: 'amount_in_tokens',
                                                            amount: msg.amount,
                                                        })
                                                        onMsg({
                                                            type: 'on_form_change',
                                                        })
                                                        break

                                                    /* istanbul ignore next */
                                                    default:
                                                        notReachable(msg.type)
                                                }
                                            }}
                                            state={
                                                errors.amount
                                                    ? 'error'
                                                    : 'normal'
                                            }
                                        />
                                    ),
                                    bottomRight: fxRate && (
                                        <SecondaryAmountButton
                                            cardCurrency={cardCurrency}
                                            form={form}
                                            fxRate={fxRate}
                                            onClick={(amount) => {
                                                switch (form.type) {
                                                    case 'amount_in_tokens':
                                                        setForm({
                                                            type: 'amount_in_default_currency',
                                                            amount,
                                                            fxRate,
                                                        })
                                                        break
                                                    case 'amount_in_default_currency':
                                                        setForm({
                                                            type: 'amount_in_tokens',
                                                            amount,
                                                        })
                                                        break
                                                    /* istanbul ignore next */
                                                    default:
                                                        return notReachable(
                                                            form
                                                        )
                                                }
                                                onMsg({
                                                    type: 'on_form_change',
                                                })
                                            }}
                                        />
                                    ),
                                }}
                                state={errors.amount ? 'error' : 'normal'}
                            />

                            <NextStepSeparator />

                            <Group variant="default">
                                <CardListItem
                                    card={
                                        gnosisPayAccountOnboardedState.selectedCard
                                    }
                                />
                            </Group>
                        </Column>
                    </ScrollContainer>

                    <Actions variant="default">
                        <Button
                            size="regular"
                            variant="primary"
                            disabled={!!errors.submit}
                            onClick={onSubmit}
                        >
                            <FormattedMessage
                                id="send-card-token.form.send"
                                defaultMessage="Send"
                            />
                        </Button>
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}
