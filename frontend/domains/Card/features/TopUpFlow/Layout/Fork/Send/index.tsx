import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    sender: Account
    supportedTopupCurrencies: CryptoCurrency[]
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    senderPortfolio: Portfolio2
    currencyPinMap: CurrencyPinMap
    keyStoreMap: KeyStoreMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap

    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    sessionPassword: string
    gasCurrencyPresetMap: GasCurrencyPresetMap

    defaultCurrencyConfig: DefaultCurrencyConfig

    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_crypto_currency_selected'
                  | 'on_earn_account_selected'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
          }
      >
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_completed_transaction_close_click'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_transaction_cancelled_successfully_close_clicked'
                  | 'cancel_submitted'
                  | 'transaction_submited'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'on_from_account_click'
          }
      >

export const Send = ({
    currencyHiddenMap,
    currencyPinMap,
    gnosisPayAccountOnboardedState,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    senderPortfolio,
    sender,
    supportedTopupCurrencies,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    sessionPassword,
    onMsg,
    defaultCurrencyConfig,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    return (
        <>
            <Layout
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationId={installationId}
                portfolio={senderPortfolio}
                accountsMap={accountsMap}
                keyStoreMap={keyStoreMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                sender={sender}
                gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
                feePresetMap={feePresetMap}
                sessionPassword={sessionPassword}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_from_currency_clicked':
                            setModal({ type: 'token_selector' })
                            break
                        case 'close':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'on_completed_safe_transaction_close_click':
                        case 'on_completed_transaction_close_click':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_transaction_cancelled_successfully_close_clicked':
                        case 'cancel_submitted':
                        case 'transaction_submited':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                        case 'on_from_account_click':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />

            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                state={modal}
                currencyHiddenMap={currencyHiddenMap}
                currencyPinMap={currencyPinMap}
                installationId={installationId}
                keyStoreMap={keyStoreMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                sender={sender}
                senderPortfolio={senderPortfolio}
                supportedTopupCurrencies={supportedTopupCurrencies}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break

                        case 'on_crypto_currency_selected':
                        case 'on_earn_account_selected':
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                            setModal({ type: 'closed' })
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
