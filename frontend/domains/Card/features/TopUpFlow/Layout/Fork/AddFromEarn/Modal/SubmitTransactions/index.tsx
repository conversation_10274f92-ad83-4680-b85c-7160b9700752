import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { TopUpCardFromEarnRequest } from '@zeal/domains/Card'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { SubmitEOATransactions } from './SubmitEOATransactions'

type Props = {
    topUpRequest: TopUpCardFromEarnRequest
    earnOwner: Account

    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    ownerPortfolio: ServerPortfolio2
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_completed_transaction_close_click'
          }
      >

const TOPUP_CARD_FROM_EARN_ACTION_SOURCE: ActionSource2 = {
    type: 'internal',
    transactionEventSource: 'topupCardFromEarn',
}

export const SubmitTransactions = ({
    earnOwner,
    topUpRequest,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keystoreMap,
    networkMap,
    networkRPCMap,
    onMsg,
    ownerPortfolio,
    sessionPassword,
    defaultCurrencyConfig,
}: Props) => {
    const keyStore = getKeyStore({
        keyStoreMap: keystoreMap,
        address: earnOwner.address,
    })

    switch (keyStore.type) {
        case 'safe_4337':
            const transactionsToBundle: NonEmptyArray<EthSendTransaction> =
                topUpRequest.swapRoute.approvalTransaction
                    ? [
                          topUpRequest.earnWithdrawalTransaction,
                          topUpRequest.swapRoute.approvalTransaction,
                          topUpRequest.swapRoute.swapTransaction,
                      ]
                    : [
                          topUpRequest.earnWithdrawalTransaction,
                          topUpRequest.swapRoute.swapTransaction,
                      ]
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    feePresetMap={feePresetMap}
                    fetchSimulationByRequest={async () => ({
                        type: 'simulated',
                        simulation: {
                            transaction: {
                                type: 'card_top_up_from_earn',
                                topUpRequest,
                                state: 'swap',
                            },
                            currencies: {},
                            checks: [],
                        },
                    })}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'card_top_up_from_earn',
                            topUpRequest,
                            state: 'swap',
                        },
                        currencies: {},
                    })}
                    network={topUpRequest.swapRoute.network}
                    networkRPCMap={networkRPCMap}
                    account={earnOwner}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    networkMap={networkMap}
                    sessionPassword={sessionPassword}
                    sendTransactionRequests={transactionsToBundle}
                    portfolio={ownerPortfolio}
                    state={{ type: 'maximised' }}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    actionSource={TOPUP_CARD_FROM_EARN_ACTION_SOURCE}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_safe_transaction_failure_accepted':
                                onMsg({ type: 'close' })
                                break

                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in card topup from Earn $${msg.type}`
                                    )
                                )
                                break

                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_completed_safe_transaction_close_click':
                            case 'on_completed_transaction_close_click':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
        case 'track_only':
            return (
                <SubmitEOATransactions
                    actionSource={TOPUP_CARD_FROM_EARN_ACTION_SOURCE}
                    earnOwner={earnOwner}
                    topUpRequest={topUpRequest}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    ownerPortfolio={ownerPortfolio}
                    sessionPassword={sessionPassword}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}
