import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { TopUpCardFromEarnRequest } from '@zeal/domains/Card'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    topUpRequest: TopUpCardFromEarnRequest
    earnOwner: Account

    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    ownerPortfolio: ServerPortfolio2
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig

    actionSource: ActionSource2
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_completed_transaction_close_click'
          }
      >

type State =
    | { type: 'submit_withdrawal_transaction' }
    | {
          type: 'submit_approval_transaction'
          approvalTransaction: EthSendTransaction
      }
    | { type: 'submit_swap_transaction' }

export const SubmitEOATransactions = ({
    earnOwner,
    topUpRequest,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keystoreMap,
    networkMap,
    networkRPCMap,
    onMsg,
    ownerPortfolio,
    sessionPassword,
    defaultCurrencyConfig,
    actionSource,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'submit_withdrawal_transaction',
    })

    switch (state.type) {
        case 'submit_withdrawal_transaction':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    sendTransactionRequests={[
                        topUpRequest.earnWithdrawalTransaction,
                    ]}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={ownerPortfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    fetchSimulationByRequest={async () => ({
                        type: 'simulated',
                        simulation: {
                            transaction: {
                                type: 'card_top_up_from_earn',
                                topUpRequest,
                                state: 'withdraw',
                            },
                            currencies: {},
                            checks: [],
                        },
                    })}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'card_top_up_from_earn',
                            topUpRequest,
                            state: 'withdraw',
                        },
                        currencies: {},
                    })}
                    key={state.type}
                    sessionPassword={sessionPassword}
                    account={earnOwner}
                    network={topUpRequest.swapRoute.network}
                    networkRPCMap={networkRPCMap}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    installationId={installationId}
                    state={{ type: 'maximised' }}
                    actionSource={actionSource}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_safe_transaction_failure_accepted':
                                onMsg({ type: 'close' })
                                break

                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'transaction_request_replaced':
                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in card topup from earn $${msg.type}`
                                    )
                                )
                                break

                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break

                            case 'on_completed_safe_transaction_close_click':
                            case 'on_completed_transaction_close_click':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                                setState(
                                    topUpRequest.swapRoute.approvalTransaction
                                        ? {
                                              type: 'submit_approval_transaction',
                                              approvalTransaction:
                                                  topUpRequest.swapRoute
                                                      .approvalTransaction,
                                          }
                                        : { type: 'submit_swap_transaction' }
                                )
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit_approval_transaction':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={ownerPortfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    fetchSimulationByRequest={async () => ({
                        type: 'simulated',
                        simulation: {
                            transaction: {
                                type: 'card_top_up_from_earn',
                                topUpRequest,
                                state: 'approval',
                            },
                            currencies: {},
                            checks: [],
                        },
                    })}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'card_top_up_from_earn',
                            topUpRequest,
                            state: 'approval',
                        },
                        currencies: {},
                    })}
                    key={state.type}
                    sendTransactionRequests={[state.approvalTransaction]}
                    sessionPassword={sessionPassword}
                    account={earnOwner}
                    network={topUpRequest.swapRoute.network}
                    networkRPCMap={networkRPCMap}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    installationId={installationId}
                    state={{ type: 'maximised' }}
                    actionSource={actionSource}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_safe_transaction_failure_accepted':
                                onMsg({ type: 'close' })
                                break

                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'transaction_request_replaced':
                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in card topup from earn $${msg.type}`
                                    )
                                )
                                break

                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break

                            case 'on_completed_safe_transaction_close_click':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_completed_transaction_close_click':
                                setState({ type: 'submit_swap_transaction' })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit_swap_transaction':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={ownerPortfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    fetchSimulationByRequest={async () => ({
                        type: 'simulated',
                        simulation: {
                            transaction: {
                                type: 'card_top_up_from_earn',
                                topUpRequest,
                                state: 'swap',
                            },
                            currencies: {},
                            checks: [],
                        },
                    })}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'card_top_up_from_earn',
                            topUpRequest,
                            state: 'swap',
                        },
                        currencies: {},
                    })}
                    key={state.type}
                    sendTransactionRequests={[
                        topUpRequest.swapRoute.swapTransaction,
                    ]}
                    sessionPassword={sessionPassword}
                    account={earnOwner}
                    network={topUpRequest.swapRoute.network}
                    networkRPCMap={networkRPCMap}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    installationId={installationId}
                    state={{ type: 'maximised' }}
                    actionSource={actionSource}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_safe_transaction_failure_accepted':
                                onMsg({ type: 'close' })
                                break

                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in card topup from earn $${msg.type}`
                                    )
                                )
                                break

                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_completed_safe_transaction_close_click':
                            case 'on_completed_transaction_close_click':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
