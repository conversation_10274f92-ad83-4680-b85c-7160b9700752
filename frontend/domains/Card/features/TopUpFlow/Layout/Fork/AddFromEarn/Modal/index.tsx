import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { TopUpCardFromEarnRequest } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { SubmitTransactions } from './SubmitTransactions'

import { SelectFromAsset } from '../../SelectFromAsset'

type Props = {
    state: State

    earnOwner: Account
    supportedTopupCurrencies: CryptoCurrency[]
    portfolio: Portfolio2
    currencyPinMap: CurrencyPinMap
    keyStoreMap: KeyStoreMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof SelectFromAsset> | MsgOf<typeof SubmitTransactions>

export type State =
    | { type: 'closed' }
    | { type: 'select_from_asset' }
    | { type: 'submit_transactions'; topUpRequest: TopUpCardFromEarnRequest }

export const Modal = ({
    state,
    onMsg,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    currencyPinMap,
    supportedTopupCurrencies,
    installationId,
    earnOwner,
    defaultCurrencyConfig,
    gasCurrencyPresetMap,
    feePresetMap,
    keystoreMap,
    sessionPassword,
    accountsMap,
    portfolio,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'select_from_asset':
            return (
                <UIModal>
                    <SelectFromAsset
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        sender={earnOwner}
                        supportedTopupCurrencies={supportedTopupCurrencies}
                        portfolio={portfolio}
                        currencyPinMap={currencyPinMap}
                        keyStoreMap={keyStoreMap}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'submit_transactions':
            return (
                <UIModal>
                    <SubmitTransactions
                        earnOwner={earnOwner}
                        topUpRequest={state.topUpRequest}
                        accountsMap={accountsMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        installationId={installationId}
                        keystoreMap={keystoreMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        ownerPortfolio={portfolio}
                        sessionPassword={sessionPassword}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
