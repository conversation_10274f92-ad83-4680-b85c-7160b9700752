import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import {
    fromFixedWithFraction,
    toFixedWithFraction,
} from '@zeal/toolkit/BigInt'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardBalance, GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { fetchSwapQuote } from '@zeal/domains/Currency/domains/SwapQuote/api/fetchSwapQuote'
import { ConfiguredEarn, DeployedTaker } from '@zeal/domains/Earn'
import { fetchTakerPortfolio } from '@zeal/domains/Earn/api/fetchTakerPortfolio'
import { EARN_SLIPPAGE_PERCENT_MAP } from '@zeal/domains/Earn/constants'
import { correctMaxWithdrawalAmount } from '@zeal/domains/Earn/helpers/correctMaxWithdrawalAmount'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { revert2 } from '@zeal/domains/FXRate/helpers/revert'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Money2 } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Data, Form } from './Layout/validation'
import { Modal, State as ModalState } from './Modal'

type Props = {
    taker: DeployedTaker
    earnOwner: Account

    earn: ConfiguredEarn
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    cardBalance: CardBalance
    defaultCurrencyConfig: DefaultCurrencyConfig
    supportedTopupCurrencies: CryptoCurrency[]
    portfolio: Portfolio2
    currencyPinMap: CurrencyPinMap
    keyStoreMap: KeyStoreMap
    currencyHiddenMap: CurrencyHiddenMap
    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof Layout>, { type: 'close' | 'on_from_account_click' }>
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_crypto_currency_selected'
                  | 'on_rpc_change_confirmed'
                  | 'on_select_rpc_click'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_completed_transaction_close_click'
          }
      >

const REVERT_RATE_EXTRA_PRECISION = 36

const fetch = async ({
    taker,
    amount,
    earnOwner,
    cardSafeAddress,
    networkRPCMap,
    networkMap,
    cardCryptoCurrency,
    defaultCurrencyConfig,
    keyStoreMap,
    signal,
}: Form & { signal?: AbortSignal }): Promise<Data> => {
    const takerPortfolio = await fetchTakerPortfolio({
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        taker,
        signal,
    })

    const takerUserCurrencyRate = takerPortfolio.userCurrencyRate
    const userCurrency = takerUserCurrencyRate.quote

    const userAmount = {
        amount: amount
            ? fromFixedWithFraction(amount, userCurrency.fraction)
            : 0n,
        currency: userCurrency,
    } as Money2

    const reverseRate = revert2({
        rate: takerUserCurrencyRate,
        extraPrecision: REVERT_RATE_EXTRA_PRECISION,
    })

    const investmentAssetAmount = applyRate2({
        baseAmount: userAmount,
        rate: reverseRate,
        extraRatePrecision: REVERT_RATE_EXTRA_PRECISION,
    })

    const correctedInvestmentAssetAmount = correctMaxWithdrawalAmount({
        investmentAssetAmount,
        investmentAssetBalance: takerPortfolio.assetBalance,
    })

    const swapQuote = await fetchSwapQuote({
        amount: toFixedWithFraction(
            correctedInvestmentAssetAmount.amount,
            correctedInvestmentAssetAmount.currency.fraction
        ),
        fromAccount: earnOwner,
        fromCurrency: taker.cryptoCurrency,
        networkMap,
        recipient: cardSafeAddress,
        swapSlippagePercent: EARN_SLIPPAGE_PERCENT_MAP[taker.type],
        toCurrency: cardCryptoCurrency,
        networkRPCMap,
        usedDexName: null,
        defaultCurrencyConfig,
        keyStoreMap,
    })

    return {
        swapQuote,
        takerPortfolio,
        investmentAssetAmount: correctedInvestmentAssetAmount.amount,
    }
}

const POLL_INTERVAL_MS = 60_000

export const AddFromEarn = ({
    earn,
    networkRPCMap,
    networkMap,
    onMsg,
    earnOwner,
    defaultCurrencyConfig,
    supportedTopupCurrencies,
    currencyPinMap,
    currencyHiddenMap,
    keyStoreMap,
    portfolio,
    installationId,
    gnosisPayAccountOnboardedState,
    sessionPassword,
    gasCurrencyPresetMap,
    feePresetMap,
    keystoreMap,
    accountsMap,
    cardBalance,
    taker,
}: Props) => {
    const captureErrorOnce = useCaptureErrorOnce()

    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    const [pollable, setPollable] = useLoadedPollableData<Data, Form>(
        fetch,
        {
            type: 'reloading',
            params: {
                taker,
                amount: '0',
                earn,
                earnOwner,
                networkRPCMap,
                networkMap,
                cardCryptoCurrency:
                    gnosisPayAccountOnboardedState.cardSafe.cryptoCurrency,
                cardSafeAddress:
                    gnosisPayAccountOnboardedState.cardSafe.address,
                defaultCurrencyConfig,
                keyStoreMap: keystoreMap,
            },
            data: {
                swapQuote: {
                    routes: [],
                    bestReturnRoute: null,
                },
                investmentAssetAmount: 0n,
                takerPortfolio: earn.takerPortfolioMap[taker.type],
            },
        },
        {
            pollIntervalMilliseconds: POLL_INTERVAL_MS,
        }
    )

    useEffect(() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
                break
            case 'subsequent_failed':
                captureErrorOnce(pollable.error)
                break
            /* istanbul ignore next */
            default:
                return notReachable(pollable)
        }
    }, [captureErrorOnce, pollable])

    return (
        <>
            <Layout
                pollable={pollable}
                installationId={installationId}
                cardBalance={cardBalance}
                card={gnosisPayAccountOnboardedState.selectedCard}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_from_account_click':
                            onMsg(msg)
                            break
                        case 'on_from_asset_clicked':
                            setModal({ type: 'select_from_asset' })
                            break
                        case 'on_amount_changed':
                            setPollable((old) => ({
                                type: 'reloading',
                                params: {
                                    ...old.params,
                                    amount: msg.amount,
                                },
                                data: old.data,
                            }))
                            break
                        case 'on_form_submit':
                            setModal({
                                type: 'submit_transactions',
                                topUpRequest: msg.request,
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                supportedTopupCurrencies={supportedTopupCurrencies}
                portfolio={portfolio}
                currencyPinMap={currencyPinMap}
                keyStoreMap={keyStoreMap}
                installationId={installationId}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                earnOwner={earnOwner}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                sessionPassword={sessionPassword}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                keystoreMap={keystoreMap}
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_completed_safe_transaction_close_click':
                        case 'on_completed_transaction_close_click':
                        case 'on_crypto_currency_selected':
                            setModal({ type: 'closed' })
                            onMsg(msg)
                            break
                        case 'on_rpc_change_confirmed':
                        case 'on_select_rpc_click':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                            onMsg(msg)
                            break
                        case 'on_earn_account_selected':
                            setModal({ type: 'closed' })
                            setPollable((old) => ({
                                type: 'reloading',
                                params: {
                                    ...old.params,
                                    taker: msg.taker,
                                    amount: null,
                                },
                                data: {
                                    ...old.data,
                                    takerPortfolio:
                                        earn.takerPortfolioMap[msg.taker.type],
                                },
                            }))
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
