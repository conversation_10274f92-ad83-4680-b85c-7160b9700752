import { useState } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldAddWallet } from '@zeal/uikit/Icon/BoldAddWallet'
import { OutlineSearch } from '@zeal/uikit/Icon/OutlineSearch'
import { Plus } from '@zeal/uikit/Icon/Plus'
import { QrCode } from '@zeal/uikit/Icon/QrCode'
import { IconButton } from '@zeal/uikit/IconButton'
import { Input } from '@zeal/uikit/Input'
import { ListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { keys } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ActiveAccountsSection } from '@zeal/domains/Account/components/ActiveAccountsSection'
import { EmptySearch } from '@zeal/domains/Account/components/EmptySearch'
import { EmptySearchForValidAddress } from '@zeal/domains/Account/components/EmptySearchForValidAddress'
import { TrackedAccountsSection } from '@zeal/domains/Account/components/TrackedAccountsSection'
import { UnlockedListItem } from '@zeal/domains/Account/components/UnlockedListItem'
import { validateAccountSearch } from '@zeal/domains/Account/helpers/validateAccountSearch'
import { format } from '@zeal/domains/Address/helpers/format'
import { GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    selectedSender: Account
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_sender_selected'; account: Account }
    | { type: 'on_show_card_address_clicked' }
    | { type: 'on_add_account_clicked' }
    | MsgOf<typeof ActiveAccountsSection>
    | MsgOf<typeof TrackedAccountsSection>

const MIN_ACCOUNTS_TO_SHOW_SEARCH = 5
export const Layout = ({
    gnosisPayAccountOnboardedState,
    selectedSender,
    accountsMap,
    portfolioMap,
    currencyHiddenMap,
    keyStoreMap,
    installationId,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [search, setSearch] = useState<string>('')
    const { formatMessage } = useIntl()
    const accountsCount = keys(accountsMap).length
    const searchResults = validateAccountSearch({
        accountsMap,
        keystoreMap: keyStoreMap,
        portfolioMap,
        currencyHiddenMap,
        search,
        defaultCurrencyConfig,
        selectedAccountAddress: selectedSender.address as Web3.address.Address,
    })

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={16} fill>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="textPrimary"
                                >
                                    <FormattedMessage
                                        id="send_to_card.select_sender.header"
                                        defaultMessage="Select sender"
                                    />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                    right={
                        <IconButton
                            onClick={() => {
                                onMsg({ type: 'on_add_account_clicked' })
                            }}
                            size="small"
                            variant="on_light"
                        >
                            {({ color }) => <Plus size={24} color={color} />}
                        </IconButton>
                    }
                />

                {accountsCount > MIN_ACCOUNTS_TO_SHOW_SEARCH && (
                    <Input
                        keyboardType="default"
                        onSubmitEditing={noop}
                        leftIcon={
                            <OutlineSearch size={24} color="iconDefault" />
                        }
                        variant="regular"
                        value={search}
                        onChange={(e) => {
                            setSearch(e.nativeEvent.text)
                        }}
                        state="normal"
                        placeholder={formatMessage({
                            id: 'send_to_card.select_sender.search.default_placeholder',
                            defaultMessage: 'Search address or ENS',
                        })}
                    />
                )}

                <ScrollContainer withFloatingActions={false}>
                    <Column spacing={16}>
                        {(() => {
                            switch (searchResults.type) {
                                case 'accounts_not_found':
                                    return <EmptySearch />
                                case 'accounts_not_found_search_valid_address':
                                    return <EmptySearchForValidAddress />
                                case 'grouped_accounts':
                                    return (
                                        <>
                                            {searchResults.selectedAccount && (
                                                <Group variant="default">
                                                    <UnlockedListItem
                                                        defaultCurrencyConfig={
                                                            defaultCurrencyConfig
                                                        }
                                                        installationId={
                                                            installationId
                                                        }
                                                        currencyHiddenMap={
                                                            currencyHiddenMap
                                                        }
                                                        selectionVariant="background_color"
                                                        key={
                                                            searchResults
                                                                .selectedAccount
                                                                .address
                                                        }
                                                        portfolio={unsafe_GetPortfolioCache2(
                                                            {
                                                                address:
                                                                    searchResults
                                                                        .selectedAccount
                                                                        .address,
                                                                portfolioMap,
                                                            }
                                                        )}
                                                        keyStore={getKeyStore({
                                                            address:
                                                                searchResults
                                                                    .selectedAccount
                                                                    .address,
                                                            keyStoreMap,
                                                        })}
                                                        selected
                                                        account={
                                                            searchResults.selectedAccount
                                                        }
                                                        onMsg={(msg) => {
                                                            switch (msg.type) {
                                                                case 'account_item_clicked':
                                                                    onMsg({
                                                                        type: 'on_sender_selected',
                                                                        account:
                                                                            msg.account,
                                                                    })
                                                                    break
                                                                /* istanbul ignore next */
                                                                default:
                                                                    return notReachable(
                                                                        msg.type
                                                                    )
                                                            }
                                                        }}
                                                    />
                                                </Group>
                                            )}
                                            <ActiveAccountsSection
                                                onMsg={onMsg}
                                                accounts={searchResults.active}
                                                listItem={({ account }) => (
                                                    <UnlockedListItem
                                                        defaultCurrencyConfig={
                                                            defaultCurrencyConfig
                                                        }
                                                        installationId={
                                                            installationId
                                                        }
                                                        currencyHiddenMap={
                                                            currencyHiddenMap
                                                        }
                                                        selectionVariant="background_color"
                                                        key={account.address}
                                                        portfolio={unsafe_GetPortfolioCache2(
                                                            {
                                                                address:
                                                                    account.address,
                                                                portfolioMap,
                                                            }
                                                        )}
                                                        keyStore={getKeyStore({
                                                            address:
                                                                account.address,
                                                            keyStoreMap,
                                                        })}
                                                        selected={
                                                            account.address ===
                                                            selectedSender.address
                                                        }
                                                        account={account}
                                                        onMsg={(msg) => {
                                                            switch (msg.type) {
                                                                case 'account_item_clicked':
                                                                    onMsg({
                                                                        type: 'on_sender_selected',
                                                                        account:
                                                                            msg.account,
                                                                    })
                                                                    break
                                                                /* istanbul ignore next */
                                                                default:
                                                                    return notReachable(
                                                                        msg.type
                                                                    )
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />

                                            {searchResults.active.length >
                                                0 && (
                                                <Group variant="default">
                                                    <ListItem
                                                        onClick={() => {
                                                            onMsg({
                                                                type: 'on_add_account_clicked',
                                                            })
                                                        }}
                                                        aria-current={false}
                                                        size="regular"
                                                        avatar={({ size }) => (
                                                            <BoldAddWallet
                                                                size={size}
                                                                color="textAccent2"
                                                            />
                                                        )}
                                                        primaryText={
                                                            <FormattedMessage
                                                                id="send_to_card.select_sender.add_wallet"
                                                                defaultMessage="Add wallet"
                                                            />
                                                        }
                                                    />
                                                </Group>
                                            )}

                                            <TrackedAccountsSection
                                                onMsg={onMsg}
                                                accounts={searchResults.tracked}
                                                listItem={({ account }) => (
                                                    <UnlockedListItem
                                                        defaultCurrencyConfig={
                                                            defaultCurrencyConfig
                                                        }
                                                        installationId={
                                                            installationId
                                                        }
                                                        currencyHiddenMap={
                                                            currencyHiddenMap
                                                        }
                                                        selectionVariant="background_color"
                                                        key={account.address}
                                                        portfolio={unsafe_GetPortfolioCache2(
                                                            {
                                                                address:
                                                                    account.address,
                                                                portfolioMap,
                                                            }
                                                        )}
                                                        keyStore={getKeyStore({
                                                            address:
                                                                account.address,
                                                            keyStoreMap,
                                                        })}
                                                        selected={
                                                            account.address ===
                                                            selectedSender?.address
                                                        }
                                                        account={account}
                                                        onMsg={(msg) => {
                                                            switch (msg.type) {
                                                                case 'account_item_clicked':
                                                                    onMsg({
                                                                        type: 'on_sender_selected',
                                                                        account:
                                                                            msg.account,
                                                                    })
                                                                    break
                                                                /* istanbul ignore next */
                                                                default:
                                                                    return notReachable(
                                                                        msg.type
                                                                    )
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />
                                        </>
                                    )
                                default:
                                    return notReachable(searchResults)
                            }
                        })()}
                        <Group variant="default">
                            <ListItem
                                onClick={() => {
                                    onMsg({
                                        type: 'on_show_card_address_clicked',
                                    })
                                }}
                                aria-current={false}
                                size="regular"
                                shortText={format(
                                    gnosisPayAccountOnboardedState.cardSafe
                                        .address
                                )}
                                avatar={({ size }) => (
                                    <QrCode size={size} color="textAccent2" />
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="send_to_card.select_sender.show_card_address_button_description"
                                        defaultMessage="Show card address"
                                    />
                                }
                            />
                        </Group>
                    </Column>
                </ScrollContainer>
            </Column>
        </Screen>
    )
}
