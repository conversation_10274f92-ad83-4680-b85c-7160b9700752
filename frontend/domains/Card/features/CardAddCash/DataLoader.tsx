import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { uuid } from '@zeal/toolkit/Crypto'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    CardConfig,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { fetchBalanceOfCardOnChainWithCache } from '@zeal/domains/Card/api/fetchBalance'
import { TopUpFlow } from '@zeal/domains/Card/features/TopUpFlow'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { fetchContractsWithCache } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchContractAddresses'
import { fetchSupportedTopupCurrenciesWithCache } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchSupportedTopupCurrencies'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2, PortfolioMap } from '@zeal/domains/Portfolio'
import { fetchPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    customCurrencies: CustomCurrencyMap
    installationId: string
    initialSender: Account
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    networkMap: NetworkMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg = Extract<
    MsgOf<typeof TopUpFlow>,
    {
        type:
            | 'close'
            | 'on_select_rpc_click'
            | 'on_rpc_change_confirmed'
            | 'on_4337_auto_gas_token_selection_clicked'
            | 'on_4337_gas_currency_selected'
            | 'on_completed_safe_transaction_close_click'
            | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
            | 'import_keys_button_clicked'
            | 'on_predefined_fee_preset_selected'
            | 'on_transaction_cancelled_successfully_close_clicked'
            | 'cancel_submitted'
            | 'on_completed_transaction_close_click'
            | 'on_transaction_completed_splash_animation_screen_competed'
            | 'transaction_request_replaced'
            | 'transaction_submited'
            | 'on_swap_cancelled_close_clicked'
            | 'on_swap_success_clicked'
            | 'on_swap_created_close_clicked'
            | 'on_swaps_io_swap_request_created'
            | 'track_wallet_clicked'
            | 'on_account_create_request'
            | 'add_wallet_clicked'
            | 'hardware_wallet_clicked'
            | 'safe_wallet_clicked'
            | 'recover_safe_wallet_clicked'
    }
>

const fetch = async ({
    defaultCurrencyConfig,
    gnosisPayAccountOnboardedState,
    networkMap,
    networkRPCMap,
    account,
    cardConfig,
    cacheKey,
    currencyHiddenMap,
    customCurrencies,
    installationId,
    signal,
}: {
    networkMap: NetworkMap
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    account: Account
    customCurrencies: CustomCurrencyMap
    cardConfig: CardConfig

    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    cacheKey: string

    signal?: AbortSignal
}): Promise<{
    cardBalance: CardBalance
    supportedTopupCurrencies: CryptoCurrency[]
    contractsMap: SwapsIOContractsMap
    portfolio: Portfolio2
}> => {
    const [cardBalance, supportedTopupCurrencies, contractsMap, portfolio] =
        await Promise.all([
            fetchBalanceOfCardOnChainWithCache({
                cacheKey,
                cardAddress: gnosisPayAccountOnboardedState.cardSafe.address,
                cardCryptoCurrency:
                    gnosisPayAccountOnboardedState.cardSafe.cryptoCurrency,
                networkRPCMap,
                defaultCurrencyConfig,
                networkMap,
                signal,
            }),
            fetchSupportedTopupCurrenciesWithCache({
                cacheKey,
                networkRPCMap,
                gnosisPayAccountOnboardedState,
                signal,
            }),
            fetchContractsWithCache({ cacheKey }),
            fetchPortfolio2({
                cardConfig,
                customCurrencies,
                address: account.address,
                currencyHiddenMap,
                defaultCurrencyConfig,
                installationId,
                networkMap,
                networkRPCMap,
            }),
        ])
    return { cardBalance, supportedTopupCurrencies, contractsMap, portfolio }
}

export const DataLoader = ({
    gnosisPayAccountOnboardedState,
    installationId,
    onMsg,
    currencyHiddenMap,
    customCurrencyMap,
    gasCurrencyPresetMap,
    feePresetMap,
    currencyPinMap,
    keyStoreMap,
    initialSender,
    portfolioMap,
    accountsMap,
    sessionPassword,
    networkRPCMap,
    cardConfig,
    networkMap,
    defaultCurrencyConfig,
    customCurrencies,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            networkMap,
            gnosisPayAccountOnboardedState,
            defaultCurrencyConfig,
            networkRPCMap,
            account: initialSender,
            cacheKey: uuid(),
            cardConfig,
            currencyHiddenMap,
            customCurrencies,
            installationId,
        },
    })

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={null}
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'loaded':
            return (
                <TopUpFlow
                    initialAmountToTopup={null}
                    portfolio={loadable.data.portfolio}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    sender={loadable.params.account}
                    gnosisPayAccountOnboardedState={
                        gnosisPayAccountOnboardedState
                    }
                    cardConfig={cardConfig}
                    supportedTopupCurrencies={
                        loadable.data.supportedTopupCurrencies
                    }
                    cardBalance={loadable.data.cardBalance}
                    contractsMap={loadable.data.contractsMap}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    customCurrencies={customCurrencyMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolioMap={portfolioMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_select_rpc_click':
                            case 'on_rpc_change_confirmed':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_completed_safe_transaction_close_click':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'cancel_submitted':
                            case 'on_completed_transaction_close_click':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'transaction_submited':
                            case 'on_swap_cancelled_close_clicked':
                            case 'on_swap_success_clicked':
                            case 'on_swap_created_close_clicked':
                            case 'on_swaps_io_swap_request_created':
                            case 'track_wallet_clicked':
                            case 'on_account_create_request':
                            case 'add_wallet_clicked':
                            case 'hardware_wallet_clicked':
                            case 'safe_wallet_clicked':
                            case 'recover_safe_wallet_clicked':
                                onMsg(msg)
                                break
                            case 'on_sender_selected':
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        ...loadable.params,
                                        account: msg.account,
                                    },
                                })
                                break
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={null}
                        onClose={() => onMsg({ type: 'close' })}
                    />
                    <AppErrorPopup
                        installationId={installationId}
                        error={parseAppError(loadable.error)}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
