import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { FAKE_GNOSIS_PAY_DAPP } from '@zeal/domains/DApp/constants'
import {
    KeyStoreMap,
    LEDGER,
    Safe4337,
    TrackOnly,
    Trezor,
} from '@zeal/domains/KeyStore'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { EthSignTypedDataV4, PersonalSign } from '@zeal/domains/RPCRequest'
import { fetchSimulatedSignMessage } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation/api/fetchSimulatedSignMessage'
import { Sign } from '@zeal/domains/RPCRequest/features/Sign'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    cardOwner: Account
    keyStore: LEDGER | Trezor | Safe4337 | TrackOnly
    sessionPassword: string
    messageToSign: EthSignTypedDataV4 | PersonalSign

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    network: PredefinedNetwork
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_message_signed'; signature: string }
    | Extract<
          MsgOf<typeof Sign>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >
    | { type: 'close' }

export const SignMessageWithUserInteraction = ({
    keyStore,
    sessionPassword,
    cardOwner,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    network,
    messageToSign,
    defaultCurrencyConfig,
    onMsg,
}: Props) => (
    <Sign
        defaultCurrencyConfig={defaultCurrencyConfig}
        state={{ type: 'maximised' }}
        actionSource={{
            type: 'internal_sign',
            transactionEventSource: 'gnosisPaySignSafeRelayTransaction',
            dAppSiteInfo: FAKE_GNOSIS_PAY_DAPP,
        }}
        account={cardOwner}
        accountsMap={accountsMap}
        feePresetMap={feePresetMap}
        gasCurrencyPresetMap={gasCurrencyPresetMap}
        installationId={installationId}
        keyStore={keyStore}
        keyStoreMap={keyStoreMap}
        network={network}
        networkMap={networkMap}
        networkRPCMap={networkRPCMap}
        fetchSimulatedSignMessage={fetchSimulatedSignMessage}
        portfolio={unsafe_GetPortfolioCache2({
            address: cardOwner.address,
            portfolioMap,
        })}
        sessionPassword={sessionPassword}
        request={messageToSign}
        onMsg={(msg) => {
            switch (msg.type) {
                case 'drag':
                case 'on_minimize_click':
                case 'on_expand_request':
                case 'import_keys_button_clicked':
                case 'on_wrong_network_accepted':
                    throw new ImperativeError(
                        `Message cannot be fired in SignMessageWithUserInteraction flow`,
                        { msg }
                    )

                case 'on_4337_auto_gas_token_selection_clicked':
                case 'on_4337_gas_currency_selected':
                    onMsg(msg)
                    break

                case 'on_safe_deployment_error_popup_cancel_clicked':
                case 'cancel_button_click':
                case 'on_cancel_confirm_transaction_clicked':
                case 'on_safe_transaction_failure_accepted':
                case 'on_safe_deployemnt_cancelled':
                case 'close':
                    onMsg({ type: 'close' })
                    break

                case 'message_signed':
                    onMsg({
                        type: 'on_message_signed',
                        signature: msg.signature,
                    })
                    break

                /* istanbul ignore next */
                default:
                    notReachable(msg)
            }
        }}
    />
)
