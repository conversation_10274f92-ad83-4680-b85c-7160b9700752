import { useState } from 'react'

import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { AppRating } from '@zeal/domains/Feedback'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    cardConfig: CardConfig

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    isEthereumNetworkFeeWarningSeen: boolean
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    portfolioMap: PortfolioMap
    sessionPassword: string
    encryptedPassword: string
    earnTakerMetrics: EarnTakerMetrics
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    notificationsConfig: NotificationsConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    bankTransferInfo: BankTransferInfo
    refreshContainerState: RefreshContainerState
    celebrationConfig: CelebrationConfig
    installationCampaign: string | null
    appRating: AppRating
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_submit_id_clicked'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_create_smart_wallet_clicked'
                  | 'close'
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_predefined_fee_preset_selected'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_activate_existing_monerium_account_click'
                  | 'on_switch_bank_transfer_provider_clicked'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'import_card_owner_clicked'
                  | 'add_wallet_clicked'
                  | 'on_notifications_config_changed'
                  | 'on_card_disconnected'
                  | 'on_switch_card_new_card_selected'
                  | 'on_bank_transfer_selected'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_card_freeze_toggle_failed'
                  | 'on_fallback_freeze_card_click'
                  | 'on_card_onboarded_state_refresh_pulled'
                  | 'on_card_transactions_fetch_success'
                  | 'on_cashback_loaded'
                  | 'on_earn_last_recharge_transaction_hash_loaded'
                  | 'on_card_onboarded_account_state_received'
                  | 'on_card_order_pending_payment_clicked'
                  | 'on_earn_updated'
                  | 'on_do_bank_transfer_clicked'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_dismiss_add_to_wallet_banner_clicked'
                  | 'on_virtual_card_order_created_animation_completed'
                  | 'on_reacharege_configured_with_user_preferences'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_card_order_redirect_to_gnosis_pay_clicked'
                  | 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_gnosis_pay_kyc_submitted_animation_complete'
                  | 'on_gnosis_pay_account_created'
          }
      >

export const CardTab = ({
    onMsg,
    cardConfig,
    accountsMap,
    keyStoreMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    appRating,
    celebrationConfig,
    networkMap,
    networkRPCMap,
    portfolioMap,
    installationCampaign,
    sessionPassword,
    encryptedPassword,
    currencyHiddenMap,
    currencyPinMap,
    customCurrencyMap,
    isEthereumNetworkFeeWarningSeen,
    notificationsConfig,
    defaultCurrencyConfig,
    earnTakerMetrics,
    bankTransferInfo,
    refreshContainerState,
    historicalTakerUserCurrencyRateMap,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    return (
        <>
            <Layout
                historicalTakerUserCurrencyRateMap={
                    historicalTakerUserCurrencyRateMap
                }
                installationCampaign={installationCampaign}
                appRating={appRating}
                celebrationConfig={celebrationConfig}
                refreshContainerState={refreshContainerState}
                earnTakerMetrics={earnTakerMetrics}
                defaultCurrencyConfig={defaultCurrencyConfig}
                notificationsConfig={notificationsConfig}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                customCurrencyMap={customCurrencyMap}
                currencyPinMap={currencyPinMap}
                currencyHiddenMap={currencyHiddenMap}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                installationId={installationId}
                keyStoreMap={keyStoreMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                portfolioMap={portfolioMap}
                sessionPassword={sessionPassword}
                encryptedPassword={encryptedPassword}
                cardConfig={cardConfig}
                bankTransferInfo={bankTransferInfo}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_order_new_card_clicked':
                            setModal({
                                type: 'order_card_flow',
                                userSelected: 'create',
                            })
                            break
                        case 'on_order_import_card_clicked':
                            setModal({
                                type: 'order_card_flow',
                                userSelected: 'import',
                            })
                            break

                        case 'on_create_smart_wallet_clicked':
                        case 'close':
                        case 'import_keys_button_clicked':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'on_predefined_fee_preset_selected':
                        case 'cancel_submitted':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                        case 'transaction_submited':
                        case 'on_activate_existing_monerium_account_click':
                        case 'on_switch_bank_transfer_provider_clicked':
                        case 'on_monerium_deposit_success_go_to_wallet_clicked':
                        case 'import_card_owner_clicked':
                        case 'add_wallet_clicked':
                        case 'on_notifications_config_changed':
                        case 'on_card_disconnected':
                        case 'on_switch_card_new_card_selected':
                        case 'on_bank_transfer_selected':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'track_wallet_clicked':
                        case 'on_account_create_request':
                        case 'on_accounts_create_success_animation_finished':
                        case 'hardware_wallet_clicked':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_get_cashback_currency_clicked':
                        case 'on_card_freeze_toggle_failed':
                        case 'on_fallback_freeze_card_click':
                        case 'on_card_onboarded_state_refresh_pulled':
                        case 'on_card_transactions_fetch_success':
                        case 'on_cashback_loaded':
                        case 'on_earn_last_recharge_transaction_hash_loaded':
                        case 'on_card_onboarded_account_state_received':
                        case 'on_card_order_pending_payment_clicked':
                        case 'on_earn_updated':
                        case 'on_do_bank_transfer_clicked':
                        case 'on_app_rating_submitted':
                        case 'on_cashback_celebration_triggered':
                        case 'on_top_up_transaction_complete_close':
                        case 'on_swaps_io_swap_request_created':
                        case 'on_new_virtual_card_created_successfully':
                        case 'on_dismiss_add_to_wallet_banner_clicked':
                        case 'on_virtual_card_order_created_animation_completed':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                        case 'on_physical_card_activated_info_screen_closed':
                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                        case 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                            break
                    }
                }}
            />
            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationId={installationId}
                accountsMap={accountsMap}
                keystoreMap={keyStoreMap}
                portfolioMap={portfolioMap}
                installationCampaign={installationCampaign}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                sessionPassword={sessionPassword}
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_gnosis_pay_onboarding_flow_closed':
                        case 'on_gnosis_pay_not_available_accepted':
                            setModal({ type: 'closed' })
                            break
                        case 'on_gnosis_pay_account_created':
                            onMsg(msg)
                            break

                        case 'on_card_imported_success_animation_complete':
                        case 'on_gnosis_pay_kyc_submitted_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_create_smart_wallet_clicked':
                            setModal({ type: 'closed' })
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
