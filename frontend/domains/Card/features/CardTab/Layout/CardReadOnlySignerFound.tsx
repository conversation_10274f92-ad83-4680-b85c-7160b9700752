import { useEffect } from 'react'

import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { AccountsMap } from '@zeal/domains/Account'
import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { CreateOrImportCard } from '@zeal/domains/Card/features/CreateOrImportCard'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { AppRating } from '@zeal/domains/Feedback'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { DataLoader } from './DataLoader'

type Msg =
    | MsgOf<typeof DataLoader>
    | {
          type: 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found'
      }

export const CardReadOnlySignerFound = ({
    cardConfig,
    customCurrencyMap,
    networkMap,
    networkRPCMap,
    accountsMap,
    earnTakerMetrics,
    keyStoreMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    isEthereumNetworkFeeWarningSeen,
    portfolioMap,
    sessionPassword,
    encryptedPassword,
    currencyHiddenMap,
    currencyPinMap,
    notificationsConfig,
    celebrationConfig,
    appRating,
    defaultCurrencyConfig,
    bankTransferInfo,
    installationCampaign,
    refreshContainerState,
    historicalTakerUserCurrencyRateMap,
    onMsg,
}: {
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    isEthereumNetworkFeeWarningSeen: boolean
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    portfolioMap: PortfolioMap
    sessionPassword: string
    encryptedPassword: string
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    notificationsConfig: NotificationsConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    bankTransferInfo: BankTransferInfo
    earnTakerMetrics: EarnTakerMetrics
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    refreshContainerState: RefreshContainerState
    installationCampaign: string | null
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    onMsg: (msg: Msg) => void
}) => {
    const cardReadOwnerSigner = accountsMap[cardConfig.readonlySignerAddress]
    const keyStore = getKeyStore({
        keyStoreMap,
        address: cardConfig.readonlySignerAddress,
    })

    const onMsgLive = useLiveRef(onMsg)

    useEffect(() => {
        if (!cardReadOwnerSigner) {
            onMsgLive.current({
                type: 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found',
            })
            captureError(
                new ImperativeError(
                    '[Card Tab] cardReadOwnerSigner not found in accounts map'
                )
            )
        }

        switch (keyStore.type) {
            case 'private_key_store':
            case 'ledger':
            case 'secret_phrase_key':
            case 'trezor':
            case 'safe_4337':
                return
            case 'track_only':
                onMsgLive.current({
                    type: 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found',
                })
                captureError(
                    new ImperativeError(
                        '[Card Tab]cardReadOwnerSigner has track_only keystore or keystore not found in keyStoreMap'
                    )
                )
                return

            /* istanbul ignore next */
            default:
                return notReachable(keyStore)
        }
    }, [cardReadOwnerSigner, installationId, keyStore, onMsgLive])

    if (!cardReadOwnerSigner) {
        return (
            <CreateOrImportCard installationId={installationId} onMsg={onMsg} />
        )
    }

    switch (keyStore.type) {
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
        case 'safe_4337':
            return (
                <DataLoader
                    keyStore={keyStore}
                    cardReadOwnerSigner={cardReadOwnerSigner}
                    historicalTakerUserCurrencyRateMap={
                        historicalTakerUserCurrencyRateMap
                    }
                    installationCampaign={installationCampaign}
                    appRating={appRating}
                    celebrationConfig={celebrationConfig}
                    refreshContainerState={refreshContainerState}
                    earnTakerMetrics={earnTakerMetrics}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    notificationsConfig={notificationsConfig}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    cardConfig={cardConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    encryptedPassword={encryptedPassword}
                    currencyPinMap={currencyPinMap}
                    customCurrencyMap={customCurrencyMap}
                    bankTransferInfo={bankTransferInfo}
                    onMsg={onMsg}
                />
            )
        case 'track_only':
            return (
                <CreateOrImportCard
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )

        /* istanbul ignore next */
        default:
            notReachable(keyStore)
    }
}
