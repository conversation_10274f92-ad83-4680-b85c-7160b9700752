import { FormattedMessage } from 'react-intl'

import { But<PERSON> } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { GaugeMeterSpeedLimit } from '@zeal/uikit/Icon/GaugeMeterSpeedLimit'
import { Modal as UIModal } from '@zeal/uikit/Modal'
import { Popup } from '@zeal/uikit/Popup'

import { notReachable } from '@zeal/toolkit'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { values } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { DelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import {
    CardCashback,
    EligibleForCashback,
} from '@zeal/domains/Card/domains/Cashback'
import { Withdraw as CashbackWithdraw } from '@zeal/domains/Card/domains/Cashback/features/Withdraw'
import { SendDelayRelayTransaction } from '@zeal/domains/Card/features/SendDelayRelayTransaction'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { MetaTransactionData } from '@zeal/domains/UserOperation'

import { SelectCurrencyToWithdraw } from './SelectCurrencyToWithdraw'
import { SelectWallet } from './SelectWallet'
import { FormType } from './validate'

type Props = {
    state: State
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    cardOwner: Account
    delayQueueStatePollable: PollableData<DelayQueueState, unknown>
    cardCashback: CardCashback
    portfolio: ServerPortfolio2
    pollable: PollableData<unknown, FormType>

    networkMap: NetworkMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    accountsMap: AccountsMap
    portfolioMap: PortfolioMap
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type State =
    | {
          type: 'closed'
      }
    | {
          type: 'currency_selector'
      }
    | {
          type: 'wallet_selector'
      }
    | {
          type: 'submitter'
          transaction: MetaTransactionData
      }
    | {
          type: 'transaction_submitted'
      }
    | { type: 'cashback_withdraw'; cardCashBack: EligibleForCashback }

type Msg =
    | MsgOf<typeof SelectWallet>
    | MsgOf<typeof SendDelayRelayTransaction>
    | MsgOf<typeof SelectCurrencyToWithdraw>
    | MsgOf<typeof CashbackWithdraw>
    | { type: 'on_card_withdraw_successfully_close_clicked' }

export const Modal = ({
    state,
    currencyHiddenMap,
    installationId,
    pollable,
    accountsMap,
    portfolioMap,
    gnosisPayAccountOnboardedState,
    keyStore,
    delayQueueStatePollable,
    networkMap,
    cardOwner,
    networkRPCMap,
    cardCashback,
    feePresetMap,
    gasCurrencyPresetMap,
    sessionPassword,
    keyStoreMap,
    portfolio,
    currencyPinMap,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'currency_selector':
            return (
                <UIModal>
                    <SelectCurrencyToWithdraw
                        cardCashback={cardCashback}
                        portfolio={portfolio}
                        networkMap={networkMap}
                        selectedCurrency={pollable.params.currency}
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'wallet_selector':
            return (
                <UIModal>
                    <SelectWallet
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        accounts={getActiveAccounts(accountsMap, keyStoreMap)}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        portfolioMap={portfolioMap}
                        selectedAccount={pollable.params.to}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'submitter':
            return (
                <UIModal>
                    <SendDelayRelayTransaction
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        transaction={state.transaction}
                        cardSafeAddress={
                            gnosisPayAccountOnboardedState.cardSafe.address
                        }
                        cardOwner={cardOwner}
                        keyStore={keyStore}
                        network={CARD_NETWORK}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        accountsMap={accountsMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        installationId={installationId}
                        keyStoreMap={keyStoreMap}
                        portfolioMap={portfolioMap}
                        sessionPassword={sessionPassword}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'transaction_submitted':
            return (
                <Popup.Layout onMsg={onMsg} background="surfaceDefault">
                    <Column spacing={24}>
                        <Header
                            icon={({ size }) => (
                                <GaugeMeterSpeedLimit
                                    size={size}
                                    color="iconAccent2"
                                />
                            )}
                            title={
                                <FormattedMessage
                                    id="cardWithdraw.success.title"
                                    defaultMessage="This change will take 3 minutes"
                                />
                            }
                            subtitle={
                                <FormattedMessage
                                    id="cardWithdraw.success.subtitle"
                                    defaultMessage="For security, all withdrawals from the Gnosis Pay card take 3 minutes to process"
                                />
                            }
                        />
                        <Popup.Actions>
                            <Button
                                variant="primary"
                                onClick={() =>
                                    onMsg({
                                        type: 'on_card_withdraw_successfully_close_clicked',
                                    })
                                }
                                size="regular"
                            >
                                <FormattedMessage
                                    id="cardWithdraw.success.cta"
                                    defaultMessage="Close"
                                />
                            </Button>
                        </Popup.Actions>
                    </Column>
                </Popup.Layout>
            )
        case 'cashback_withdraw':
            return (
                <UIModal>
                    <CashbackWithdraw
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        keyStore={keyStore}
                        currencyHiddenMap={currencyHiddenMap}
                        accountsMap={accountsMap}
                        delayQueueStatePollable={delayQueueStatePollable}
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        cardCashback={state.cardCashBack}
                        cardReadonlySigner={cardOwner}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        installationId={installationId}
                        keyStoreMap={keyStoreMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        portfolioMap={portfolioMap}
                        sessionPassword={sessionPassword}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        default:
            return notReachable(state)
    }
}

const getActiveAccounts = (
    accountsMap: AccountsMap,
    keyStoreMap: KeyStoreMap
): Account[] => {
    return values(accountsMap).filter((account) => {
        const keyStore = getKeyStore({
            address: account.address,
            keyStoreMap,
        })

        switch (keyStore.type) {
            case 'track_only':
                return false
            case 'private_key_store':
            case 'ledger':
            case 'secret_phrase_key':
            case 'trezor':
            case 'safe_4337':
                return true
            /* istanbul ignore next */
            default:
                return notReachable(keyStore)
        }
    })
}
