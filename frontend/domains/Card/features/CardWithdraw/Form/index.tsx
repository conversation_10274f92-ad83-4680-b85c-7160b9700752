import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import {
    PollableData,
    usePollableData,
} from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { DelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { CardCashback } from '@zeal/domains/Card/domains/Cashback'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    FiatCurrency,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { FXRate2 } from '@zeal/domains/FXRate'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State } from './Modal'
import { FormType } from './validate'

type Props = {
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    cardServerPortfolio: ServerPortfolio2
    networkRPCMap: NetworkRPCMap
    cardOwner: Account
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    portfolioMap: PortfolioMap
    keyStore: CardSlientSignKeyStore
    feePresetMap: FeePresetMap
    cardCashback: CardCashback
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    delayQueueStatePollable: PollableData<DelayQueueState, unknown>
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof Layout>, { type: 'close' }>
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_card_withdraw_successfully_close_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_card_owner_clicked'
                  | 'on_cashback_withdraw_queued_successfully'
          }
      >

const fetch = async ({
    currency,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
}: FormType & {
    signal?: AbortSignal
}): Promise<FXRate2<CryptoCurrency, FiatCurrency> | null> =>
    fetchRate({
        cryptoCurrency: currency,
        defaultCurrencyConfig,
        networkRPCMap,
        networkMap,
    })

const POLL_INTERVAL_MS = 20_000

export const Form = ({
    currencyHiddenMap,
    installationId,
    accountsMap,
    portfolioMap,
    gnosisPayAccountOnboardedState,
    cardCashback,
    keyStore,
    networkMap,
    cardOwner,
    networkRPCMap,
    feePresetMap,
    gasCurrencyPresetMap,
    delayQueueStatePollable,
    sessionPassword,
    keyStoreMap,
    currencyPinMap,
    cardServerPortfolio,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'closed' })
    const captureErrorOnce = useCaptureErrorOnce()
    const [pollable, setPollable] = usePollableData<
        FXRate2<CryptoCurrency, FiatCurrency> | null,
        FormType
    >(
        fetch,
        {
            type: 'loading',
            params: {
                currency:
                    gnosisPayAccountOnboardedState.cardSafe.cryptoCurrency,
                defaultCurrencyConfig,
                amount: null,
                to: cardOwner,
                networkMap,
                networkRPCMap,
            },
        },

        { pollIntervalMilliseconds: POLL_INTERVAL_MS }
    )

    useEffect(() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
            case 'loading':
                break

            case 'error':
            case 'subsequent_failed':
                captureErrorOnce(pollable.error)
                break

            default:
                notReachable(pollable)
        }
    }, [pollable, captureErrorOnce])

    return (
        <>
            <Layout
                pollable={pollable}
                delayQueueStatePollable={delayQueueStatePollable}
                portfolio={cardServerPortfolio}
                gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_amount_change':
                            setPollable((old) => {
                                switch (old.type) {
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        return {
                                            type: 'reloading',
                                            params: {
                                                ...pollable.params,
                                                amount: msg.amount,
                                            },
                                            data: old.data,
                                        }
                                    case 'loading':
                                    case 'error':
                                        return {
                                            type: 'loading',
                                            params: {
                                                ...pollable.params,
                                                amount: msg.amount,
                                            },
                                        }
                                    default:
                                        return notReachable(old)
                                }
                            })
                            break
                        case 'on_currency_click':
                            setState({ type: 'currency_selector' })
                            break
                        case 'on_submit_click':
                            setState({
                                type: 'submitter',
                                transaction: msg.transaction,
                            })
                            break
                        case 'on_wallet_click':
                            setState({ type: 'wallet_selector' })
                            break
                        case 'close':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
                installationId={installationId}
                networkMap={networkMap}
            />
            <Modal
                delayQueueStatePollable={delayQueueStatePollable}
                defaultCurrencyConfig={defaultCurrencyConfig}
                state={state}
                cardCashback={cardCashback}
                gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
                pollable={pollable}
                cardOwner={cardOwner}
                keyStore={keyStore}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                installationId={installationId}
                keyStoreMap={keyStoreMap}
                portfolioMap={portfolioMap}
                sessionPassword={sessionPassword}
                currencyPinMap={currencyPinMap}
                currencyHiddenMap={currencyHiddenMap}
                portfolio={cardServerPortfolio}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setState({ type: 'closed' })
                            break
                        case 'new_currency_selected':
                            setState({ type: 'closed' })
                            setPollable((old) => {
                                switch (old.type) {
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        return {
                                            type: 'reloading',
                                            params: {
                                                ...pollable.params,
                                                currency: msg.currency,
                                            },
                                            data: old.data,
                                        }
                                    case 'loading':
                                    case 'error':
                                        return {
                                            type: 'loading',
                                            params: {
                                                ...pollable.params,
                                                currency: msg.currency,
                                            },
                                        }
                                    default:
                                        return notReachable(old)
                                }
                            })

                            break
                        case 'on_account_selected':
                            setState({ type: 'closed' })
                            setPollable((old) => {
                                switch (old.type) {
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        return {
                                            type: 'loaded',
                                            params: {
                                                ...pollable.params,
                                                to: msg.account,
                                            },
                                            data: old.data,
                                        }
                                    case 'loading':
                                    case 'error':
                                        return {
                                            type: 'loading',
                                            params: {
                                                ...pollable.params,
                                                to: msg.account,
                                            },
                                        }
                                    default:
                                        return notReachable(old)
                                }
                            })
                            break
                        case 'on_submit_delay_relay_transaction_success':
                            postUserEvent({
                                type: 'CardWithdrawSuccessfullyRecivedEvent',
                                installationId,
                            })
                            setState({ type: 'transaction_submitted' })
                            break
                        case 'on_card_withdraw_successfully_close_clicked':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_card_owner_clicked':
                        case 'on_cashback_withdraw_queued_successfully':
                            onMsg(msg)
                            break
                        case 'on_cashback_currency_selected':
                            setState({
                                type: 'cashback_withdraw',
                                cardCashBack: msg.cardCashback,
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
