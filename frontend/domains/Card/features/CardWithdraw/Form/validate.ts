import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { Account } from '@zeal/domains/Account'
import { GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import { DelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { CryptoCurrency, FiatCurrency } from '@zeal/domains/Currency'
import { FXRate2 } from '@zeal/domains/FXRate'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { MetaTransactionData } from '@zeal/domains/UserOperation'
import { ethSendTransactionToMetaTransactionData } from '@zeal/domains/UserOperation/helpers/ethSendTransactionToMetaTransactionData'

export type FormType = {
    amount: string | null
    currency: CryptoCurrency
    to: Account

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}
export type Pollable = PollableData<
    FXRate2<CryptoCurrency, FiatCurrency> | null,
    FormType
>

type AmountRequired = { type: 'amount_required' }
type QueueBusyError = { type: 'delay_queue_busy' }
type QueueFailedError = { type: 'delay_queue_failed' }
type PollableNotLoadedError = { type: 'pollable_not_loaded' }
type NotEnoughBalanceError = { type: 'not_enough_balance' }

type SubmitError =
    | AmountRequired
    | QueueBusyError
    | QueueFailedError
    | PollableNotLoadedError
    | NotEnoughBalanceError

export type FormError = {
    banner?: QueueBusyError | QueueFailedError | PollableNotLoadedError
    submit?: SubmitError
    amount?: NotEnoughBalanceError
}

export const getWithdrawAmount = ({
    pollable,
}: {
    pollable: Pollable
}): CryptoMoney => ({
    amount: fromFixedWithFraction(
        pollable.params.amount,
        pollable.params.currency.fraction
    ),
    currency: pollable.params.currency,
})

const validatePollableState = ({
    pollable,
}: {
    pollable: PollableData<DelayQueueState, unknown>
}): Result<PollableNotLoadedError, DelayQueueState> => {
    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed':
            return success(pollable.data)
        case 'loading':
        case 'error':
            return failure({ type: 'pollable_not_loaded' })

        default:
            return notReachable(pollable)
    }
}

const validateQueueState = ({
    delayQueueStatePollable,
}: {
    delayQueueStatePollable: PollableData<DelayQueueState, unknown>
}): Result<
    PollableNotLoadedError | QueueFailedError | QueueBusyError,
    unknown
> =>
    validatePollableState({ pollable: delayQueueStatePollable }).andThen(
        (data): Result<QueueBusyError | QueueFailedError, unknown> => {
            switch (data.type) {
                case 'empty':
                    return success(undefined)
                case 'failed':
                    return failure({ type: 'delay_queue_failed' })
                case 'busy':
                    return failure({ type: 'delay_queue_busy' as const })

                default:
                    return notReachable(data)
            }
        }
    )

const validateAmount = ({
    pollable,
}: {
    pollable: Pollable
}): Result<AmountRequired, unknown> => {
    const amount = getWithdrawAmount({ pollable })

    if (amount.amount === 0n) {
        return failure({ type: 'amount_required' })
    }

    return success(amount)
}

const validateBalance = ({
    portfolio,
    pollable,
}: {
    pollable: Pollable
    portfolio: ServerPortfolio2
}): Result<NotEnoughBalanceError, CryptoMoney> => {
    const amount = getWithdrawAmount({ pollable })
    const balance = getBalanceByCryptoCurrency2({
        currency: pollable.params.currency,
        serverPortfolio: portfolio,
    })

    return balance.amount < amount.amount
        ? failure({ type: 'not_enough_balance' })
        : success(amount)
}

export const validate = ({
    pollable,
    delayQueueStatePollable,
    networkMap,
    portfolio,
    gnosisPayAccountOnboardedState,
}: {
    pollable: Pollable
    delayQueueStatePollable: PollableData<DelayQueueState, unknown>
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    portfolio: ServerPortfolio2
    networkMap: NetworkMap
}): Result<FormError, MetaTransactionData> =>
    shape({
        amount: validateBalance({ pollable, portfolio }),
        submit: validateQueueState({ delayQueueStatePollable })
            .andThen(() => validateAmount({ pollable }))
            .andThen(() => {
                return validateBalance({ pollable, portfolio })
            }),
        banner: validateQueueState({ delayQueueStatePollable }),
    }).map(() => {
        const network = findNetworkByHexChainId(
            pollable.params.currency.networkHexChainId,
            networkMap
        )
        return ethSendTransactionToMetaTransactionData(
            createTransferEthSendTransaction({
                amount: getWithdrawAmount({ pollable }),
                to: pollable.params.to.address,
                from: gnosisPayAccountOnboardedState.cardSafe.address,
                network,
            })
        )
    })
