import { FormattedMessage } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'

import {
    CardCashback,
    EligibleForCashback,
} from '@zeal/domains/Card/domains/Cashback'
import { CASHBACK_CURRENCY } from '@zeal/domains/Card/domains/Cashback/constants'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { SelectToken } from '@zeal/domains/Token/components/SelectToken'

type Props = {
    portfolio: ServerPortfolio2
    networkMap: NetworkMap
    cardCashback: CardCashback
    selectedCurrency: CryptoCurrency | null
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    onMsg: (msg: Msg) => void
}
type Msg =
    | { type: 'close' }
    | { type: 'new_currency_selected'; currency: CryptoCurrency }
    | {
          type: 'on_cashback_currency_selected'
          cardCashback: EligibleForCashback
      }
export const SelectCurrencyToWithdraw = ({
    portfolio,
    selectedCurrency,
    networkMap,
    currencyHiddenMap,
    cardCashback,
    currencyPinMap,
    onMsg,
}: Props) => {
    const cryptoCurrencies: CryptoCurrency[] = portfolio.tokens.map(
        (token) => token.balance.currency
    )

    return (
        <Screen
            background="light"
            padding="form"
            aria-labelledby="choose-tokens-label"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <UIActionBar
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="textPrimary"
                                id="choose-tokens-label"
                            >
                                <FormattedMessage
                                    id="select_currency_to_withdraw.select_token_to_withdraw"
                                    defaultMessage="Select token to withdraw"
                                />
                            </Text>
                        </Row>
                    </Clickable>
                }
            />

            <Column shrink spacing={16} fill>
                <SelectToken
                    cryptoCurrencies={cryptoCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    networkMap={networkMap}
                    serverPortfolio={portfolio}
                    selectedCurrency={selectedCurrency}
                    onCryptoCurrencySelected={(currency) => {
                        if (currency.id === CASHBACK_CURRENCY.id) {
                            switch (cardCashback.type) {
                                case 'eligible_for_cashback_no_cashback':
                                case 'eligible_for_cashback_has_cashback':
                                    onMsg({
                                        type: 'on_cashback_currency_selected',
                                        cardCashback,
                                    })
                                    break
                                case 'not_eligible_for_cashback':
                                    onMsg({
                                        type: 'new_currency_selected',
                                        currency,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(cardCashback)
                            }
                        } else {
                            onMsg({
                                type: 'new_currency_selected',
                                currency,
                            })
                        }
                    }}
                />
            </Column>
        </Screen>
    )
}
