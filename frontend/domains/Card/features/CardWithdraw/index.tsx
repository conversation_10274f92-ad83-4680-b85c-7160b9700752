import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { fetchCheckCardReadonlySignerIsAnOwner } from '@zeal/domains/Card/api/fetchCheckCard'
import { DelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { CardOwnerNotImported } from '@zeal/domains/Card/components/CardOwnerNotImported'
import { CardCashback } from '@zeal/domains/Card/domains/Cashback'
import { CheckCardReadonlySignerIsAnOwnerResponse } from '@zeal/domains/Card/helpers/checkCardReadOnlySignerIsAnOwner'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { fetchServerPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Form } from './Form'

type Props = {
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    networkRPCMap: NetworkRPCMap
    cardReadonlySigner: Account
    accountsMap: AccountsMap
    cardCashback: CardCashback
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    portfolioMap: PortfolioMap
    keyStore: CardSlientSignKeyStore
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    delayQueueStatePollable: PollableData<DelayQueueState, unknown>
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof CardOwnerNotImported>
    | MsgOf<typeof Form>
    | { type: 'close' }

const fetch = async ({
    gnosisPayAccountOnboardedState,
    cardReadonlySigner,
    networkRPCMap,
    keyStoreMap,
    accountsMap,
    defaultCurrencyConfig,
    installationId,
    keyStore,
    sessionPassword,
    currencyHiddenMap,
    networkMap,
}: {
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    networkRPCMap: NetworkRPCMap
    cardReadonlySigner: Account
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    defaultCurrencyConfig: DefaultCurrencyConfig

    currencyHiddenMap: CurrencyHiddenMap
    keyStore: CardSlientSignKeyStore
    sessionPassword: string
    networkMap: NetworkMap
    installationId: string
}): Promise<{
    cardServerPortfolio: ServerPortfolio2
    cardReadonlySignerIsAnOwner: CheckCardReadonlySignerIsAnOwnerResponse
}> => {
    const [cardReadonlySignerIsAnOwner, cardServerPortfolio] =
        await Promise.all([
            fetchCheckCardReadonlySignerIsAnOwner({
                gnosisPayAccountOnboardedState,
                cardReadonlySigner,
                networkRPCMap,
                keyStoreMap,
                accountsMap,
                keyStore,
                sessionPassword,
            }),
            fetchServerPortfolio2({
                address: gnosisPayAccountOnboardedState.cardSafe.address,
                defaultCurrencyConfig,
                currencyHiddenMap,
                networkMap,
                networkRPCMap,
                installationId,
            }),
        ])

    return { cardReadonlySignerIsAnOwner, cardServerPortfolio }
}
export const CardWithdraw = ({
    gnosisPayAccountOnboardedState,
    delayQueueStatePollable,
    cardReadonlySigner,

    keyStore,
    networkRPCMap,
    accountsMap,
    currencyHiddenMap,
    installationId,
    portfolioMap,
    networkMap,
    feePresetMap,
    gasCurrencyPresetMap,
    cardCashback,
    sessionPassword,
    keyStoreMap,
    currencyPinMap,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            gnosisPayAccountOnboardedState,
            currencyHiddenMap,
            cardReadonlySigner,
            networkRPCMap,
            accountsMap,
            keyStoreMap,
            defaultCurrencyConfig,
            networkMap,
            keyStore,
            sessionPassword,
            installationId,
        },
    })
    switch (loadable.type) {
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />

                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break

                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loading':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'loaded': {
            switch (loadable.data.cardReadonlySignerIsAnOwner.type) {
                case 'card_readonly_signer_is_an_owner':
                    return (
                        <Form
                            cardCashback={cardCashback}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            delayQueueStatePollable={delayQueueStatePollable}
                            gnosisPayAccountOnboardedState={
                                gnosisPayAccountOnboardedState
                            }
                            cardOwner={cardReadonlySigner}
                            keyStore={keyStore}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            accountsMap={accountsMap}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            installationId={installationId}
                            keyStoreMap={keyStoreMap}
                            portfolioMap={portfolioMap}
                            sessionPassword={sessionPassword}
                            currencyPinMap={currencyPinMap}
                            currencyHiddenMap={currencyHiddenMap}
                            cardServerPortfolio={
                                loadable.data.cardServerPortfolio
                            }
                            onMsg={onMsg}
                        />
                    )
                case 'card_readonly_signer_is_not_an_owner_and_owner_imported':
                    return (
                        <Form
                            cardCashback={cardCashback}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            delayQueueStatePollable={delayQueueStatePollable}
                            gnosisPayAccountOnboardedState={
                                gnosisPayAccountOnboardedState
                            }
                            cardOwner={
                                loadable.data.cardReadonlySignerIsAnOwner
                                    .cardOwner
                            }
                            keyStore={
                                loadable.data.cardReadonlySignerIsAnOwner
                                    .cardOwnerKeyStore
                            }
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            accountsMap={accountsMap}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            installationId={installationId}
                            keyStoreMap={keyStoreMap}
                            portfolioMap={portfolioMap}
                            sessionPassword={sessionPassword}
                            currencyPinMap={currencyPinMap}
                            currencyHiddenMap={currencyHiddenMap}
                            cardServerPortfolio={
                                loadable.data.cardServerPortfolio
                            }
                            onMsg={onMsg}
                        />
                    )
                case 'card_readonly_signer_is_not_an_owner_and_owner_not_imported':
                    return <CardOwnerNotImported onMsg={onMsg} />
                default:
                    return notReachable(
                        loadable.data.cardReadonlySignerIsAnOwner
                    )
            }
        }
        default:
            return notReachable(loadable)
    }
}
