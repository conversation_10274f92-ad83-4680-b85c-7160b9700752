import { useEffect } from 'react'

import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { noop, notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsWithKeystores } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    CardTransaction,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    checkGnosisPayAccountState,
    CheckGnosisPayAccountStateResponse,
} from '@zeal/domains/Card/api/checkGnosisPayAccountState'
import { fetchCardSafesByOwners } from '@zeal/domains/Card/api/fetchCardSafesByOwners'
import { calculateBRewardState } from '@zeal/domains/Card/domains/Reward/helpers/calculateBRewardState'
import { SwitchCard } from '@zeal/domains/Card/features/SwitchCard'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    readonlySigner: Account
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    installationId: string
    accountsWithKeystores: AccountsWithKeystores
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_card_detected_success_animation_complete'
          cardConfig:
              | ReadonlySignerSelectedOnboardedCardConfig
              | ReadonlySignerSelectedCardConfig
          accountsWithKeystores: AccountsWithKeystores
      }
    | {
          type: 'on_card_detection_failed'
          accountsWithKeystores: AccountsWithKeystores
      }
    | {
          type: 'on_card_not_detected'
          accountsWithKeystores: AccountsWithKeystores
      }

type Response =
    | CheckGnosisPayAccountStateResponse
    | { type: 'card_safe_not_detected' }

const fetch = async ({
    readonlySigner,
    keyStore,
    sessionPassword,
    networkRPCMap,
    networkMap,
    defaultCurrencyConfig,
    signal,
}: {
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    readonlySigner: Account
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    signal?: AbortSignal
}): Promise<Response> => {
    const cardSafesMap = await fetchCardSafesByOwners({
        cardOwnerAddresses: [readonlySigner.address as Web3.address.Address],
        signal,
    })

    const cardSafe =
        cardSafesMap[readonlySigner.address as Web3.address.Address] || null

    if (!cardSafe) {
        return {
            type: 'card_safe_not_detected',
        }
    }

    return checkGnosisPayAccountState({
        readonlySigner,
        keyStore,
        sessionPassword,
        networkRPCMap,
        networkMap,
        defaultCurrencyConfig,
    })
}

export const DetectCardForAccount = ({
    networkRPCMap,
    networkMap,
    defaultCurrencyConfig,
    readonlySigner,
    sessionPassword,
    keyStore,
    accountsWithKeystores,
    installationId,
    onMsg,
}: Props) => {
    const [loadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            networkRPCMap,
            networkMap,
            defaultCurrencyConfig,
            readonlySigner,
            sessionPassword,
            keyStore,
        },
    })

    const onLiveMsg = useLiveRef(onMsg)
    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
                break
            case 'loaded': {
                switch (loadable.data.type) {
                    case 'not_onboarded_account_exists':
                        onLiveMsg.current({
                            type: 'on_card_detected_success_animation_complete',
                            accountsWithKeystores,
                            cardConfig: {
                                type: 'card_readonly_signer_address_is_selected',
                                readonlySignerAddress:
                                    readonlySigner.address as Web3.address.Address,
                                lastDismissedOnboardingBannerState: null,
                                rechargePreferences: null,
                            },
                        })
                        break
                    case 'onboarded_account_exists':
                        if (
                            loadable.data.accountState.activatedCards.length ===
                            1
                        ) {
                            onLiveMsg.current({
                                type: 'on_card_detected_success_animation_complete',
                                accountsWithKeystores,
                                cardConfig:
                                    getReadonlySignerSelectedOnboardedCardConfig(
                                        {
                                            readonlySigner,
                                            gnosisPayAccountOnboardedState:
                                                loadable.data.accountState,
                                            isBRewardClaimed:
                                                loadable.data.isBRewardClaimed,
                                            cardTransactions:
                                                loadable.data.cardTransactions,
                                            selectedCardId:
                                                loadable.data.accountState
                                                    .activatedCards[0].id,
                                        }
                                    ),
                            })
                        }
                        break
                    case 'account_does_not_exist':
                    case 'card_safe_not_detected':
                        onLiveMsg.current({
                            type: 'on_card_not_detected',
                            accountsWithKeystores,
                        })
                        break
                    /* istanbul ignore next */
                    default:
                        return notReachable(loadable.data)
                }
                break
            }
            case 'error':
                captureError(parseAppError(loadable.error))
                onLiveMsg.current({
                    type: 'on_card_detection_failed',
                    accountsWithKeystores,
                })
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, onLiveMsg, readonlySigner, accountsWithKeystores])

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout title={null} actionBar={null} onClose={noop} />
            )
        case 'loaded': {
            switch (loadable.data.type) {
                case 'onboarded_account_exists': {
                    if (loadable.data.accountState.activatedCards.length > 1) {
                        const {
                            accountState: gnosisPayAccountOnboardedState,
                            isBRewardClaimed,
                            cardTransactions,
                        } = loadable.data

                        return (
                            <SwitchCard
                                gnosisPayAccountOnboardedState={
                                    loadable.data.accountState
                                }
                                installationId={installationId}
                                keyStore={keyStore}
                                sessionPassword={sessionPassword}
                                cardReadonlySigner={readonlySigner}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg({
                                                type: 'on_card_detected_success_animation_complete',
                                                accountsWithKeystores,
                                                cardConfig:
                                                    getReadonlySignerSelectedOnboardedCardConfig(
                                                        {
                                                            readonlySigner,
                                                            gnosisPayAccountOnboardedState,
                                                            isBRewardClaimed,
                                                            cardTransactions,
                                                            selectedCardId:
                                                                gnosisPayAccountOnboardedState
                                                                    .activatedCards[0]
                                                                    .id,
                                                        }
                                                    ),
                                            })
                                            break
                                        case 'on_switch_card_new_card_selected':
                                            onMsg({
                                                type: 'on_card_detected_success_animation_complete',
                                                accountsWithKeystores,
                                                cardConfig:
                                                    getReadonlySignerSelectedOnboardedCardConfig(
                                                        {
                                                            readonlySigner,
                                                            gnosisPayAccountOnboardedState,
                                                            isBRewardClaimed,
                                                            cardTransactions,
                                                            selectedCardId:
                                                                msg.cardId,
                                                        }
                                                    ),
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    }

                    return (
                        <LoadingLayout
                            title={null}
                            actionBar={null}
                            onClose={noop}
                        />
                    )
                }

                case 'not_onboarded_account_exists':
                case 'account_does_not_exist':
                case 'card_safe_not_detected':
                    return (
                        <LoadingLayout
                            title={null}
                            actionBar={null}
                            onClose={noop}
                        />
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(loadable.data)
            }
        }
        case 'error':
            return (
                <LoadingLayout title={null} actionBar={null} onClose={noop} />
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}

const getReadonlySignerSelectedOnboardedCardConfig = ({
    readonlySigner,
    selectedCardId,
    gnosisPayAccountOnboardedState,
    isBRewardClaimed,
    cardTransactions,
}: {
    readonlySigner: Account
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    selectedCardId: string
    isBRewardClaimed: boolean
    cardTransactions: CardTransaction[]
}): ReadonlySignerSelectedOnboardedCardConfig => {
    return {
        type: 'card_readonly_signer_address_is_selected_fully_onboarded',
        readonlySignerAddress: readonlySigner.address as Web3.address.Address,
        currency: gnosisPayAccountOnboardedState.cardSafe.cryptoCurrency,

        selectedCardId: selectedCardId,
        lastSeenSafeAddress: gnosisPayAccountOnboardedState.cardSafe.address,
        cardTransactionsCache: cardTransactions,
        lastRechargeTransactionHash: null,
        cashback: {
            type: 'not_eligible_for_cashback',
        },
        dissmissedAddToWalletBanner: false,
        lastDismissedKycBannerState: null,

        isCreatedViaZeal: gnosisPayAccountOnboardedState.isCreatedViaZeal,
        country:
            gnosisPayAccountOnboardedState.residentialAddress?.country || null,
        rewards: calculateBRewardState({
            gnosisAccountState: gnosisPayAccountOnboardedState,
            cardTransactions: cardTransactions,
            isBRewardClaimed: isBRewardClaimed,
        }),
        userId: gnosisPayAccountOnboardedState.userId,
    }
}
