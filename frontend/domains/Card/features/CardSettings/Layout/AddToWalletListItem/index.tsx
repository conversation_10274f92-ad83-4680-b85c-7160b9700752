import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import { APPLE_PAY_NOT_SUPPORTED_COUNTRIES } from '@zeal/domains/Card/constants'

import { DataLoader } from './DataLoader'

type Props = {
    installationId: string
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof DataLoader>

export const AddToWalletListItem = ({
    gnosisPayAccountOnboardedState,
    installationId,
    onMsg,
}: Props) => {
    switch (ZealPlatform.OS) {
        case 'web':
            return null
        case 'android':
            return (
                <DataLoader
                    installationId={installationId}
                    onMsg={onMsg}
                    platform={ZealPlatform}
                />
            )
        case 'ios': {
            if (
                gnosisPayAccountOnboardedState.residentialAddress?.country &&
                APPLE_PAY_NOT_SUPPORTED_COUNTRIES.includes(
                    gnosisPayAccountOnboardedState.residentialAddress.country
                )
            ) {
                return null
            }
            return (
                <DataLoader
                    installationId={installationId}
                    onMsg={onMsg}
                    platform={ZealPlatform}
                />
            )
        }

        /* istanbul ignore next */
        default:
            notReachable(ZealPlatform)
    }
}
