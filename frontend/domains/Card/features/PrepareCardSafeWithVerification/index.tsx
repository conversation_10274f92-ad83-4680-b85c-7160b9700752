import { StepW<PERSON>rd } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    CardSafeCurrencyConfigured,
    CardSafeDeployed,
    CardSafeNotDeployed,
    CardSlientSignKeyStore,
    GnosisPayKYCApprovedState,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { ConfigureCardSafe } from '@zeal/domains/Card/features/ConfigureCardSafe'
import { DeployCardSafe } from '@zeal/domains/Card/features/DeployCardSafe'
import { SetCardSafeCurrency } from '@zeal/domains/Card/features/SetCardSafeCurrecny'
import { SourceOfFundsQuestions } from '@zeal/domains/Card/features/SourceOfFundsQuestions'
import { VerifyPhoneNumber } from '@zeal/domains/Card/features/VerifyPhoneNumber'
import { NetworkRPCMap } from '@zeal/domains/Network'
import { CardOrderFlowLocation } from '@zeal/domains/UserEvents'

type Props = {
    onMsg: (msg: Msg) => void
    installationId: string

    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    gnosisPayState: GnosisPayKYCApprovedState
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap

    cardSafe:
        | CardSafeNotDeployed
        | CardSafeDeployed
        | CardSafeCurrencyConfigured

    sessionPassword: string
    location: CardOrderFlowLocation
}

type Msg =
    | Extract<MsgOf<typeof DeployCardSafe>, { type: 'close' }>
    | Extract<MsgOf<typeof SetCardSafeCurrency>, { type: 'close' }>
    | Extract<MsgOf<typeof VerifyPhoneNumber>, { type: 'close' }>
    | Extract<MsgOf<typeof SourceOfFundsQuestions>, { type: 'close' }>
    | MsgOf<typeof ConfigureCardSafe>

type State =
    | {
          type: 'deploy_card_safe'
      }
    | { type: 'set_currency'; cardSafe: CardSafeDeployed }
    | { type: 'verify_phone_number'; cardSafe: CardSafeCurrencyConfigured }
    | {
          type: 'source_of_funds_questions'
          cardSafe: CardSafeCurrencyConfigured
      }
    | { type: 'configure_card_safe'; cardSafe: CardSafeCurrencyConfigured }

const calculateInitialState = (
    gnosisPayAccountState: GnosisPayKYCApprovedState,
    cardSafe:
        | CardSafeNotDeployed
        | CardSafeDeployed
        | CardSafeCurrencyConfigured
): State => {
    const { hasVerifiedPhoneNumber, hasAnsweredSourceOfFunds } =
        gnosisPayAccountState

    switch (cardSafe.type) {
        case 'not_deployed':
            return { type: 'deploy_card_safe' }
        case 'deployed':
            return { type: 'set_currency', cardSafe }
        case 'currency_configured':
            if (!hasVerifiedPhoneNumber) {
                return { type: 'verify_phone_number', cardSafe }
            }

            if (!hasAnsweredSourceOfFunds) {
                return { type: 'source_of_funds_questions', cardSafe }
            }

            return { type: 'configure_card_safe', cardSafe }

        default:
            return notReachable(cardSafe)
    }
}
export const PrepareCardSafeWithVerification = ({
    gnosisPayState,
    sessionPassword,
    networkRPCMap,
    cardConfig,
    keyStore,
    installationId,
    cardSafe,
    location,
    onMsg,
}: Props) => {
    return (
        <StepWizard<State>
            initialStep={() => calculateInitialState(gnosisPayState, cardSafe)}
        >
            {({ step, forwardTo, moveTo }) => {
                switch (step.type) {
                    case 'deploy_card_safe':
                        return (
                            <DeployCardSafe
                                title={null}
                                location={location}
                                installationId={installationId}
                                readonlySignerAddress={
                                    cardConfig.readonlySignerAddress
                                }
                                keyStore={keyStore}
                                sessionPassword={sessionPassword}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'on_card_safe_deployed':
                                            moveTo({
                                                type: 'set_currency',
                                                cardSafe: msg.cardSafe,
                                            })
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'set_currency':
                        return (
                            <SetCardSafeCurrency
                                location={location}
                                cardSafe={step.cardSafe}
                                installationId={installationId}
                                cardConfig={cardConfig}
                                keyStore={keyStore}
                                sessionPassword={sessionPassword}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'on_card_safe_currency_set':
                                            const {
                                                hasVerifiedPhoneNumber,
                                                hasAnsweredSourceOfFunds,
                                            } = gnosisPayState
                                            if (!hasVerifiedPhoneNumber) {
                                                return forwardTo({
                                                    type: 'verify_phone_number',
                                                    cardSafe: msg.cardSafe,
                                                })
                                            }

                                            if (!hasAnsweredSourceOfFunds) {
                                                return forwardTo({
                                                    type: 'source_of_funds_questions',
                                                    cardSafe: msg.cardSafe,
                                                })
                                            }

                                            return forwardTo({
                                                type: 'configure_card_safe',
                                                cardSafe: msg.cardSafe,
                                            })

                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'verify_phone_number':
                        return (
                            <VerifyPhoneNumber
                                location={location}
                                installationId={installationId}
                                sessionPassword={sessionPassword}
                                keyStore={keyStore}
                                cardConfig={cardConfig}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'on_gnosis_pay_phone_verified':
                                            forwardTo({
                                                type: 'source_of_funds_questions',
                                                cardSafe: step.cardSafe,
                                            })
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'source_of_funds_questions':
                        return (
                            <SourceOfFundsQuestions
                                location={location}
                                installationId={installationId}
                                sessionPassword={sessionPassword}
                                keyStore={keyStore}
                                cardConfig={cardConfig}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'on_all_answers_submitted':
                                            forwardTo({
                                                type: 'configure_card_safe',
                                                cardSafe: step.cardSafe,
                                            })
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'configure_card_safe':
                        return (
                            <ConfigureCardSafe
                                cardSafe={step.cardSafe}
                                location={location}
                                cardConfig={cardConfig}
                                sessionPassword={sessionPassword}
                                installationId={installationId}
                                keyStore={keyStore}
                                networkRPCMap={networkRPCMap}
                                onMsg={onMsg}
                            />
                        )

                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
