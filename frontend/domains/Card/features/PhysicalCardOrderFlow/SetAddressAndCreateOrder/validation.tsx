import { notReachable } from '@zeal/toolkit'
import { parse, PostalCodeValidationError } from '@zeal/toolkit/PostalCode'
import {
    EmptyStringError,
    failure,
    nonEmptyString,
    required,
    Result,
    shape,
    success,
} from '@zeal/toolkit/Result'

import { ResidentialAddress } from '@zeal/domains/Card'
import { CountryISOCode } from '@zeal/domains/Country'

export type Form = {
    shippingAddress: Address
}

export type ValidatedForm = {
    shippingAddress: ResidentialAddress
}

export const MAX_LENGTH = 50

type MaxLength = { type: 'max_length' }

export type Errors = {
    address1?: EmptyStringError | MaxLength
    address2?: MaxLength
    city?: EmptyStringError
    postalCode?: EmptyStringError | PostalCodeValidationError
    submit?: { type: 'form_invalid' }
}

type Address = {
    address1: string | null
    address2: string | null
    city: string | null
    postalCode: string | null
    country: CountryISOCode
}

const validateAddress1 = (
    form: Form
): Result<EmptyStringError | MaxLength, string> => {
    return nonEmptyString(form.shippingAddress.address1).andThen((address) => {
        if (address.length > MAX_LENGTH) {
            return failure({ type: 'max_length' })
        }
        return success(address)
    })
}

const validateAddress2 = (form: Form): Result<MaxLength, string | null> => {
    if (!form.shippingAddress.address2) {
        return success(null)
    }
    if (form.shippingAddress.address2.length > MAX_LENGTH) {
        return failure({ type: 'max_length' })
    }
    return success(form.shippingAddress.address2)
}

const validateCity = (form: Form): Result<EmptyStringError, string> => {
    return nonEmptyString(form.shippingAddress.city)
}

const validatePostalCodeOnSubmit = (
    form: Form
): Result<EmptyStringError | PostalCodeValidationError, string> =>
    nonEmptyString(form.shippingAddress.postalCode).andThen((code) =>
        parse(form.shippingAddress.country, code)
    )

const validatePostalCodeAsType = (
    form: Form
): Result<PostalCodeValidationError, unknown> => {
    if (!form.shippingAddress.postalCode) {
        return success(undefined)
    }

    return parse(form.shippingAddress.country, form.shippingAddress.postalCode)
}

const validateSubmit = (
    form: Form
): Result<{ type: 'form_invalid' }, ValidatedForm> => {
    const result = validatePostalCodeOnSubmit(form).andThen((postalCode) =>
        validateAddress1(form).andThen((address1) =>
            validateAddress2(form).andThen((address2) =>
                validateCity(form).map((city) => {
                    return {
                        shippingAddress: {
                            city,
                            address2,
                            address1,
                            postalCode,
                            country: form.shippingAddress.country,
                        },
                    }
                })
            )
        )
    )

    switch (result.type) {
        case 'Failure':
            return failure({ type: 'form_invalid' })
        case 'Success':
            return success(result.data)

        default:
            return notReachable(result)
    }
}

export const validate = (form: Form): Result<Errors, ValidatedForm> => {
    return shape({
        address1: validateAddress1(form),
        address2: validateAddress2(form),
        city: validateCity(form),
        postalCode: validatePostalCodeOnSubmit(form),
        country: required(form.shippingAddress.country),
        submit: validateSubmit(form),
    }).map(({ submit }) => {
        return submit
    })
}

export const validateAsType = (form: Form): Result<Errors, ValidatedForm> => {
    return shape({
        postalCode: validatePostalCodeAsType(form),
        submit: validateSubmit(form),
    }).map(({ submit }) => {
        return submit
    })
}
