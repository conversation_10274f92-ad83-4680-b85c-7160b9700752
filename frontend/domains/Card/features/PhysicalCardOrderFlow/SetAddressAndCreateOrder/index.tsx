import { StepW<PERSON>rd } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    CardSafeFullyConfigured,
    CardSlientSignKeyStore,
    GnosisPayAccountNotOnboardedStateGnosisPayKYCApprovedState,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { COUNTRIES_MAP } from '@zeal/domains/Country/constants'
import { tryToGetUserCurrentCountry } from '@zeal/domains/Country/helpers/tryToGetUserCurrentCountry'

import { AttachCoupon } from './AttachCoupon'
import { ConfirmPayment } from './ConfirmPayment'
import { CreateCardOrder } from './CreateCardOrder'
import { SetShippingAddress } from './SetShippingAddress'
import { Form, ValidatedForm } from './validation'

type Props = {
    cardSafe: CardSafeFullyConfigured
    installationId: string
    gnosisPayState:
        | GnosisPayAccountNotOnboardedStateGnosisPayKYCApprovedState
        | GnosisPayAccountOnboardedState

    readonlySignerAddress: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    onMsg: (msg: Msg) => void
}

type State =
    | {
          type: 'set_shipping_address'
          initialForm: Form
      }
    | {
          type: 'create_card_order'
          form: ValidatedForm
      }
    | { type: 'attach_coupon'; cardOrderId: string; form: ValidatedForm }
    | { type: 'confirm_payment'; cardOrderId: string; form: ValidatedForm }

type Msg =
    | Extract<MsgOf<typeof SetShippingAddress>, { type: 'close' }>
    | Extract<
          MsgOf<typeof CreateCardOrder>,
          { type: 'close' | 'on_card_order_redirect_to_gnosis_pay_clicked' }
      >
    | Extract<MsgOf<typeof AttachCoupon>, { type: 'close' }>
    | MsgOf<typeof ConfirmPayment>

const getInitialForm = ({
    gnosisPayState,
}: {
    gnosisPayState:
        | GnosisPayAccountNotOnboardedStateGnosisPayKYCApprovedState
        | GnosisPayAccountOnboardedState
}): Form => {
    const defaultForm: Form = {
        shippingAddress: {
            address1: null,
            address2: null,
            city: null,
            postalCode: null,
            country:
                tryToGetUserCurrentCountry().getSuccessResult()?.code ||
                COUNTRIES_MAP.GB.code,
        },
    }
    switch (gnosisPayState.type) {
        case 'not_onboarded':
            return gnosisPayState.state.residentialAddress
                ? { shippingAddress: gnosisPayState.state.residentialAddress }
                : defaultForm
        case 'onboarded':
            return gnosisPayState.residentialAddress
                ? { shippingAddress: gnosisPayState.residentialAddress }
                : defaultForm
        /* istanbul ignore next */
        default:
            return notReachable(gnosisPayState)
    }
}

export const SetAddressAndCreateOrder = ({
    gnosisPayState,
    installationId,
    cardSafe,
    keyStore,
    sessionPassword,
    readonlySignerAddress,
    onMsg,
}: Props) => {
    return (
        <StepWizard<State>
            initialStep={{
                type: 'set_shipping_address',
                initialForm: getInitialForm({ gnosisPayState }),
            }}
        >
            {({ step, moveTo }) => {
                switch (step.type) {
                    case 'set_shipping_address':
                        return (
                            <SetShippingAddress
                                initialForm={step.initialForm}
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'on_valid_form_submitted':
                                            moveTo({
                                                type: 'create_card_order',
                                                form: msg.form,
                                            })
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                                cardSafe={cardSafe}
                            />
                        )
                    case 'create_card_order':
                        return (
                            <CreateCardOrder
                                installationId={installationId}
                                form={step.form}
                                keyStore={keyStore}
                                sessionPassword={sessionPassword}
                                readonlySignerAddress={readonlySignerAddress}
                                cardSafe={cardSafe}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                                            onMsg(msg)
                                            break
                                        case 'card_order_created_successfully':
                                            moveTo({
                                                type: 'attach_coupon',
                                                cardOrderId: msg.cardOrderId,
                                                form: step.form,
                                            })
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'attach_coupon':
                        return (
                            <AttachCoupon
                                installationId={installationId}
                                cardOrderId={step.cardOrderId}
                                form={step.form}
                                keyStore={keyStore}
                                sessionPassword={sessionPassword}
                                readonlySignerAddress={readonlySignerAddress}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break

                                        case 'coupon_successfully_attached':
                                            moveTo({
                                                type: 'confirm_payment',
                                                cardOrderId: step.cardOrderId,
                                                form: step.form,
                                            })
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'confirm_payment':
                        return (
                            <ConfirmPayment
                                installationId={installationId}
                                cardOrderId={step.cardOrderId}
                                form={step.form}
                                keyStore={keyStore}
                                sessionPassword={sessionPassword}
                                readonlySignerAddress={readonlySignerAddress}
                                onMsg={onMsg}
                            />
                        )

                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
