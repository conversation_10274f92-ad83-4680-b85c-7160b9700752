import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import { Add } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty/features/Add'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Order } from './Order'

type Props = {
    installationId: string
    portfolio: ServerPortfolio2
    keyStore: SigningKeyStore
    accountsMap: AccountsMap
    sessionPassword: string
    portfolioMap: PortfolioMap
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    keyStoreMap: KeyStoreMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    initialCounterparty: Counterparty | null
    counterparties: Counterparty[]
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    fromAccount: Account
    onMsg: (msg: Msg) => void
}
type Msg =
    | Extract<
          MsgOf<typeof Add>,
          {
              type: 'close' | 'on_save_counterparty_form_submitted'
          }
      >
    | Extract<
          MsgOf<typeof Order>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_monerium_order_status_changed'
                  | 'on_contact_support_clicked'
                  | 'on_delete_counterparty_submitted'
          }
      >

type State =
    | { type: 'add_counterparty'; counterparty: Counterparty | null }
    | { type: 'submit_order'; counterparty: Counterparty }

export const AddCounterparty = ({
    installationId,
    portfolio,
    keyStore,
    accountsMap,
    sessionPassword,
    portfolioMap,
    networkRPCMap,
    networkMap,
    keyStoreMap,
    gasCurrencyPresetMap,
    feePresetMap,
    defaultCurrencyConfig,
    initialCounterparty,
    fromAccount,
    counterparties,
    cardConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'add_counterparty',
        counterparty: initialCounterparty,
    })
    switch (state.type) {
        case 'add_counterparty':
            return (
                <Add
                    installationId={installationId}
                    initialCounterparty={state.counterparty}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_save_counterparty_form_submitted':
                                onMsg(msg)
                                setState({
                                    type: 'submit_order',
                                    counterparty: msg.counterparty,
                                })
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit_order':
            return (
                <Order
                    fromAccount={fromAccount}
                    counterparties={counterparties}
                    cardConfig={cardConfig}
                    counterparty={state.counterparty}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    accountsMap={accountsMap}
                    keyStore={keyStore}
                    portfolio={portfolio}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                return setState({
                                    type: 'add_counterparty',
                                    counterparty: state.counterparty,
                                })
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_monerium_order_status_changed':
                            case 'on_save_counterparty_form_submitted':
                            case 'on_delete_counterparty_submitted':
                            case 'on_contact_support_clicked':
                                return onMsg(msg)
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        default:
            return notReachable(state)
    }
}
