import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import { Select } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty/features/Select'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Order } from './Order'

type Props = {
    sessionPassword: string
    portfolioMap: PortfolioMap
    portfolio: ServerPortfolio2
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    keyStoreMap: KeyStoreMap
    keyStore: SigningKeyStore
    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    accountsMap: AccountsMap
    installationId: string
    counterparties: Counterparty[]
    fromAccount: Account
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof Order>
    | Extract<
          MsgOf<typeof Select>,
          {
              type:
                  | 'close'
                  | 'on_save_counterparty_form_submitted'
                  | 'on_delete_counterparty_submitted'
          }
      >
    | {
          type: 'on_delete_last_counterparty_submitted'
          counterparty: Counterparty
      }

type State =
    | { type: 'select_counterparty' }
    | { type: 'order'; counterparty: Counterparty }

export const SelectCounterparty = ({
    sessionPassword,
    portfolioMap,
    portfolio,
    networkRPCMap,
    networkMap,
    keyStoreMap,
    keyStore,
    gasCurrencyPresetMap,
    feePresetMap,
    defaultCurrencyConfig,
    accountsMap,
    installationId,
    cardConfig,
    fromAccount,
    onMsg,
    counterparties,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'select_counterparty' })
    switch (state.type) {
        case 'select_counterparty':
            return (
                <Select
                    installationId={installationId}
                    selectCounterparty={null}
                    counterparties={counterparties}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_existing_counterparty_selected':
                                setState({
                                    type: 'order',
                                    counterparty: msg.counterparty,
                                })
                                break
                            case 'on_save_counterparty_form_submitted':
                                onMsg(msg)
                                setState({
                                    type: 'order',
                                    counterparty: msg.counterparty,
                                })
                                break
                            case 'on_delete_counterparty_submitted':
                                const newCounterparties = counterparties.filter(
                                    (counterparty) =>
                                        counterparty.iban !==
                                        msg.counterparty.iban
                                )
                                if (newCounterparties.length === 0) {
                                    onMsg({
                                        type: 'on_delete_last_counterparty_submitted',
                                        counterparty: msg.counterparty,
                                    })
                                    break
                                } else {
                                    onMsg(msg)
                                    break
                                }

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'order':
            return (
                <Order
                    fromAccount={fromAccount}
                    counterparties={counterparties}
                    accountsMap={accountsMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    keyStore={keyStore}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolio={portfolio}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    counterparty={state.counterparty}
                    cardConfig={cardConfig}
                    onMsg={onMsg}
                />
            )
        default:
            return notReachable(state)
    }
}
