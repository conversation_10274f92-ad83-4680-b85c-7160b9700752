import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import {
    MoneriumAuthInfo,
    MoneriumOfframpOrder,
} from '@zeal/domains/Card/domains/MoneriumBankTransfer'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { Money2 } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { CheckOrderStatus } from './CheckOrderStatus'
import { Confirmation } from './Confirmation'
import { CreateOrder } from './CreateOrder'
import { LoginAndCheckCounterparty } from './LoginAndCheckCounterparty'
import { Submit } from './Submit'

type Props = {
    keyStore: SigningKeyStore
    sessionPassword: string
    portfolioMap: PortfolioMap
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    keyStoreMap: KeyStoreMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    accountsMap: AccountsMap
    counterparty: Counterparty
    portfolio: ServerPortfolio2
    counterparties: Counterparty[]
    fromAccount: Account
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    onMsg: (msg: Msg) => void
}
type Msg =
    | Extract<
          MsgOf<typeof CreateOrder>,
          {
              type:
                  | 'close'
                  | 'on_save_counterparty_form_submitted'
                  | 'on_delete_counterparty_submitted'
          }
      >
    | Extract<
          MsgOf<typeof LoginAndCheckCounterparty>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >
    | Extract<
          MsgOf<typeof CheckOrderStatus>,
          {
              type:
                  | 'close'
                  | 'on_monerium_order_status_changed'
                  | 'on_contact_support_clicked'
          }
      >

type State =
    | {
          type: 'create_order'
          counterparty: Counterparty
          initialAmount: Money2 | null
      }
    | {
          type: 'confirmation'
          order: MoneriumOfframpOrder
      }
    | {
          type: 'login_and_check_counterparty'
          order: MoneriumOfframpOrder
      }
    | {
          type: 'submit'
          order: MoneriumOfframpOrder
          authInfo: MoneriumAuthInfo
      }
    | {
          type: 'check_order_status'
          id: string
          order: MoneriumOfframpOrder
          authInfo: MoneriumAuthInfo
      }
export const Order = ({
    accountsMap,
    counterparty,
    portfolio,
    defaultCurrencyConfig,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    sessionPassword,
    keyStore,
    counterparties,
    cardConfig,
    fromAccount,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'create_order',
        counterparty,
        initialAmount: null,
    })
    switch (state.type) {
        case 'create_order':
            return (
                <CreateOrder
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    initialAmount={state.initialAmount}
                    networkMap={networkMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    fromAccount={fromAccount}
                    counterparties={counterparties}
                    counterparty={counterparty}
                    portfolio={portfolio}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_save_counterparty_form_submitted':
                            case 'on_delete_counterparty_submitted':
                                onMsg(msg)
                                break
                            case 'on_submit_clicked':
                                setState({
                                    type: 'confirmation',
                                    order: msg.order,
                                })
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'confirmation':
            return (
                <Confirmation
                    order={state.order}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({
                                    type: 'create_order',
                                    counterparty: state.order.counterparty,
                                    initialAmount: state.order.amount,
                                })
                                break
                            case 'on_continue_clicked':
                                setState({
                                    type: 'login_and_check_counterparty',
                                    order: state.order,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'login_and_check_counterparty':
            return (
                <LoginAndCheckCounterparty
                    cardConfig={cardConfig}
                    accountsMap={accountsMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    keyStore={keyStore}
                    order={state.order}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({
                                    type: 'create_order',
                                    counterparty: state.order.counterparty,
                                    initialAmount: state.order.amount,
                                })
                                break
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break
                            case 'monerium_adress_checked':
                            case 'monerium_adress_linked':
                                setState({
                                    type: 'submit',
                                    order: msg.order,
                                    authInfo: msg.authInfo,
                                })
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit':
            return (
                <Submit
                    order={state.order}
                    network={CARD_NETWORK}
                    authInfo={state.authInfo}
                    accountsMap={accountsMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                return setState({
                                    type: 'create_order',
                                    counterparty: state.order.counterparty,
                                    initialAmount: state.order.amount,
                                })
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break
                            case 'monerium_order_submitted':
                                postUserEvent({
                                    type: 'MoneriumTransferInitiatedEvent',
                                    installationId,
                                })
                                return setState({
                                    type: 'check_order_status',
                                    authInfo: state.authInfo,
                                    order: state.order,
                                    id: msg.id,
                                })
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'check_order_status':
            return (
                <CheckOrderStatus
                    networkMap={networkMap}
                    portfolio={portfolio}
                    networkRPCMap={networkRPCMap}
                    moniriumOfframpOrder={state.order}
                    authInfo={state.authInfo}
                    id={state.id}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_monerium_order_status_changed':
                            case 'on_contact_support_clicked':
                                onMsg(msg)
                                break
                            case 'on_monerium_order_rejected':
                                setState({
                                    type: 'create_order',
                                    counterparty,
                                    initialAmount: state.order.amount,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        default:
            return notReachable(state)
    }
}
