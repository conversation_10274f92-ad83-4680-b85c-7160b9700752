import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { Account } from '@zeal/domains/Account'
import { MoneriumOfframpOrder } from '@zeal/domains/Card/domains/MoneriumBankTransfer'
import { DEFAULT_MONERIUM_CRYPTO_CURRENCY } from '@zeal/domains/Card/domains/MoneriumBankTransfer/constants'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import { CryptoMoney } from '@zeal/domains/Money'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

export type Form = {
    counterparty: Counterparty | null
    amount: string | null
    reference: string | null
}
type AmountRequired = { type: 'amount_required' }
type InsufficientFunds = { type: 'insufficient_funds' }
type MaximumNumberOfCharacters = {
    type: 'maximum_number_of_characters_exceeded'
}
type CounterpartyRequired = { type: 'counterparty_required' }
export type Errors = {
    submit?:
        | AmountRequired
        | InsufficientFunds
        | MaximumNumberOfCharacters
        | CounterpartyRequired
    referenceInput?: MaximumNumberOfCharacters
}

const validateReferenceNumberOfCharacters = ({
    reference,
}: Form): Result<MaximumNumberOfCharacters, string | null> => {
    if (!reference || (reference && reference.length <= 140)) {
        return success(reference)
    }
    return failure({ type: 'maximum_number_of_characters_exceeded' })
}

const validateNonZeroAmount = (
    inputAmount: string | null
): Result<AmountRequired, string> => {
    if (!inputAmount) {
        return failure({ type: 'amount_required' })
    }

    const amount = fromFixedWithFraction(
        inputAmount,
        DEFAULT_MONERIUM_CRYPTO_CURRENCY.fraction
    )

    if (amount === 0n) {
        return failure({ type: 'amount_required' })
    }

    return success(inputAmount)
}

const validateBalance = ({
    inputAmount,
    portfolio,
}: {
    inputAmount: string
    portfolio: ServerPortfolio2
}): Result<InsufficientFunds, CryptoMoney> => {
    const balance = getBalanceByCryptoCurrency2({
        currency: DEFAULT_MONERIUM_CRYPTO_CURRENCY,
        serverPortfolio: portfolio,
    })

    const amount = fromFixedWithFraction(
        inputAmount,
        DEFAULT_MONERIUM_CRYPTO_CURRENCY.fraction
    )

    if (balance.amount < amount) {
        return failure({ type: 'insufficient_funds' })
    }

    return success({ amount, currency: DEFAULT_MONERIUM_CRYPTO_CURRENCY })
}

const validateCounterparty = (
    form: Form
): Result<CounterpartyRequired, Counterparty> => {
    if (form.counterparty) {
        return success(form.counterparty)
    }
    return failure({ type: 'counterparty_required' })
}

export const validate = ({
    form,
    portfolio,
    fromAccount,
}: {
    form: Form
    portfolio: ServerPortfolio2
    fromAccount: Account
}): Result<Errors, MoneriumOfframpOrder> =>
    shape({
        submit: validateCounterparty(form).andThen(() =>
            validateNonZeroAmount(form.amount)
                .andThen((amount) =>
                    validateBalance({ inputAmount: amount, portfolio })
                )
                .andThen((balance) =>
                    validateReferenceNumberOfCharacters(form).map(() => balance)
                )
        ),
        counterparty: validateCounterparty(form),
        referenceInput: validateReferenceNumberOfCharacters(form),
    }).map(({ submit, referenceInput, counterparty }) => {
        return {
            counterparty,
            amount: submit,
            fromAccount,
            reference: referenceInput,
        }
    })
