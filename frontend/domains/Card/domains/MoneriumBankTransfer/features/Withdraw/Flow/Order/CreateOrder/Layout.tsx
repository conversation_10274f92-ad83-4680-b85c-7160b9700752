import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { But<PERSON> } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { FeeInputButton } from '@zeal/uikit/FeeInputButton'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { InfoCircleOutline } from '@zeal/uikit/Icon/InfoCircleOutline'
import { Monerium } from '@zeal/uikit/Icon/Providers/Monerium'
import { Input } from '@zeal/uikit/Input'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { MoneriumOfframpOrder } from '@zeal/domains/Card/domains/MoneriumBankTransfer'
import { DEFAULT_MONERIUM_CRYPTO_CURRENCY } from '@zeal/domains/Card/domains/MoneriumBankTransfer/constants'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { FormattedFeeInDefaultCurrency2 } from '@zeal/domains/Money/components/FormattedFeeInDefaultCurrency'
import { truncateCents } from '@zeal/domains/Money/helpers/truncateCents'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { GNOSIS } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { AmountInDefaultCurrency } from './AmountInDefaultCurrency'
import { CounterpartySelectorListItem } from './CounterpartySelectorListItem'
import { Errors, Form, validate } from './validate'

const MONERIUM_MAX_CENTS = 2

type Props = {
    form: Form
    portfolio: ServerPortfolio2
    fromAccount: Account
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_info_clicked' }
    | {
          type: 'on_amount_change'
          amount: string | null
      }
    | {
          type: 'on_submit_clicked'
          order: MoneriumOfframpOrder
      }
    | {
          type: 'on_reference_messsage_changed'
          reference: string | null
      }
    | MsgOf<typeof CounterpartySelectorListItem>

export const Layout = ({
    form,
    defaultCurrencyConfig,
    onMsg,
    portfolio,
    installationId,
    networkMap,
    fromAccount,
    networkRPCMap,
}: Props) => {
    const validationResult = validate({ form, portfolio, fromAccount })

    const { formatMessage } = useIntl()

    const errors = validationResult.getFailureReason() || {}

    const onSubmit = () => {
        validationResult.tap((order) =>
            onMsg({
                type: 'on_submit_clicked',
                order,
            })
        )
    }

    const cryptoCurrency = DEFAULT_MONERIUM_CRYPTO_CURRENCY
    const balance = truncateCents(
        getBalanceByCryptoCurrency2({
            serverPortfolio: portfolio,
            currency: cryptoCurrency,
        }),
        MONERIUM_MAX_CENTS
    )

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <UIActionBar
                top={<ActionBarAccountIndicator account={fromAccount} />}
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="textPrimary"
                            >
                                <FormattedMessage
                                    id="send_token.form.title"
                                    defaultMessage="Send"
                                />
                            </Text>
                        </Row>
                    </Clickable>
                }
            />
            <Column spacing={16} shrink fill>
                <ScrollContainer withFloatingActions={false}>
                    <Column spacing={12}>
                        <Column spacing={4}>
                            <AmountInput
                                label={cryptoCurrency.code}
                                content={{
                                    topLeft: (
                                        <Row spacing={4}>
                                            <CurrencyAvatar
                                                key={cryptoCurrency.id}
                                                currency={cryptoCurrency}
                                                size={24}
                                                rightBadge={({ size }) => (
                                                    <Badge
                                                        size={size}
                                                        network={GNOSIS}
                                                    />
                                                )}
                                            />
                                            <Text
                                                variant="title3"
                                                color="textPrimary"
                                                weight="medium"
                                            >
                                                {cryptoCurrency.code}
                                            </Text>
                                        </Row>
                                    ),
                                    topRight: ({ onBlur, onFocus }) => (
                                        <AmountInput.Input
                                            prefix=""
                                            onFocus={onFocus}
                                            onBlur={onBlur}
                                            label={formatMessage({
                                                id: 'send_token.form.send-amount',
                                                defaultMessage: 'Send amount',
                                            })}
                                            fraction={cryptoCurrency.fraction}
                                            autoFocus
                                            readOnly={false}
                                            amount={form.amount}
                                            onChange={(value) => {
                                                onMsg({
                                                    type: 'on_amount_change',
                                                    amount: value,
                                                })
                                            }}
                                            onSubmitEditing={onSubmit}
                                        />
                                    ),
                                    bottomLeft: (
                                        <MaxButton
                                            installationId={installationId}
                                            location="monerium_withdrawal"
                                            balance={balance}
                                            onMsg={onMsg}
                                            state="normal"
                                        />
                                    ),
                                    bottomRight: (
                                        <Text>
                                            <AmountInDefaultCurrency
                                                networkRPCMap={networkRPCMap}
                                                cryptoCurrency={cryptoCurrency}
                                                defaultCurrencyConfig={
                                                    defaultCurrencyConfig
                                                }
                                                networkMap={networkMap}
                                                amount={form.amount}
                                            />
                                        </Text>
                                    ),
                                }}
                                state="normal"
                            />
                            <NextStepSeparator />
                            <Group variant="default">
                                <CounterpartySelectorListItem
                                    counterparty={form.counterparty}
                                    onMsg={onMsg}
                                />
                            </Group>
                        </Column>
                        <Column spacing={8}>
                            <Text
                                variant="paragraph"
                                color="gray40"
                                weight="regular"
                                id="reference-input-label"
                            >
                                <FormattedMessage
                                    id="bank_transfer.reference.label"
                                    defaultMessage="Reference (Optional)"
                                />
                            </Text>
                            <Input
                                keyboardType="default"
                                onSubmitEditing={onSubmit}
                                variant="regular"
                                value={form.reference ? form.reference : ''}
                                onChange={(e) => {
                                    const refText =
                                        e.nativeEvent.text !== ''
                                            ? e.nativeEvent.text
                                            : null
                                    onMsg({
                                        type: 'on_reference_messsage_changed',
                                        reference: refText,
                                    })
                                }}
                                state={
                                    !!errors.referenceInput ? 'error' : 'normal'
                                }
                                message={(() => {
                                    if (!errors.referenceInput) {
                                        return null
                                    }
                                    switch (errors.referenceInput.type) {
                                        case 'maximum_number_of_characters_exceeded':
                                            return (
                                                <FormattedMessage
                                                    id="reference.error.maximum_number_of_characters_exceeded"
                                                    defaultMessage="Too many characters"
                                                />
                                            )
                                        default:
                                            return null
                                    }
                                })()}
                                placeholder={formatMessage({
                                    id: 'bank_transfer.reference_message',
                                    defaultMessage: 'Sent from Zeal',
                                })}
                            />
                        </Column>
                    </Column>
                </ScrollContainer>
                <Column spacing={12}>
                    <Text
                        variant="footnote"
                        weight="regular"
                        color="textSecondary"
                    >
                        <FormattedMessage
                            id="transfer_provider"
                            defaultMessage="Transfer provider"
                        />
                    </Text>
                    <FeeInputButton
                        left={
                            <Row spacing={4}>
                                <Avatar variant="squared" size={20}>
                                    <Monerium size={20} />
                                </Avatar>

                                <Text
                                    variant="paragraph"
                                    weight="regular"
                                    color="textPrimary"
                                >
                                    Monerium
                                </Text>
                            </Row>
                        }
                        right={
                            <Row spacing={4}>
                                <Row spacing={4} alignY="center">
                                    <Text
                                        variant="paragraph"
                                        weight="regular"
                                        color="textPrimary"
                                    >
                                        <FormattedMessage
                                            id="bank_transfers.fees"
                                            defaultMessage="Fees"
                                        />
                                    </Text>
                                    <Text
                                        variant="paragraph"
                                        color="textPrimary"
                                    >
                                        <FormattedFeeInDefaultCurrency2
                                            money={{
                                                amount: 0n,
                                                currency:
                                                    defaultCurrencyConfig.defaultCurrency,
                                            }}
                                        />
                                    </Text>
                                </Row>
                                <InfoCircleOutline
                                    size={20}
                                    color="iconDefault"
                                />
                            </Row>
                        }
                        onClick={() => onMsg({ type: 'on_info_clicked' })}
                    />
                </Column>
                <Actions variant="default">
                    <Button
                        size="regular"
                        variant="primary"
                        disabled={!!errors.submit}
                        onClick={onSubmit}
                    >
                        <SubmitActions errors={errors} />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}

const SubmitActions = ({ errors }: { errors: Errors | undefined }) => {
    if (!errors || !errors.submit) {
        return (
            <FormattedMessage id="actions.continue" defaultMessage="Continue" />
        )
    }

    switch (errors.submit.type) {
        case 'amount_required':
            return (
                <FormattedMessage
                    id="setLimit.submit.error.amount_required"
                    defaultMessage="Enter Amount"
                />
            )
        case 'insufficient_funds':
            return (
                <FormattedMessage
                    id="submit.error.not_enough_balance"
                    defaultMessage="Not enough balance"
                />
            )
        case 'maximum_number_of_characters_exceeded':
            return (
                <FormattedMessage
                    id="submit.error.maximum_number_of_characters_exceeded"
                    defaultMessage="Reduce message characters"
                />
            )
        case 'counterparty_required':
            return (
                <FormattedMessage
                    id="submit.error.recipient_required"
                    defaultMessage="Recipient required"
                />
            )

        default:
            return notReachable(errors.submit)
    }
}
