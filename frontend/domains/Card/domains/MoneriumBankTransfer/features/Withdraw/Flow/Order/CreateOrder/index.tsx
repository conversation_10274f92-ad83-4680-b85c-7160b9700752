import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import { Money2 } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'
import { Form } from './validate'

type Props = {
    counterparty: Counterparty
    initialAmount: null | Money2
    counterparties: Counterparty[]
    portfolio: ServerPortfolio2
    fromAccount: Account
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof Layout>, { type: 'close' | 'on_submit_clicked' }>
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_save_counterparty_form_submitted'
                  | 'on_delete_counterparty_submitted'
          }
      >

export const CreateOrder = ({
    counterparty,
    counterparties,
    portfolio,
    fromAccount,
    defaultCurrencyConfig,
    initialAmount,
    networkMap,
    installationId,
    networkRPCMap,
    onMsg,
}: Props) => {
    const [form, setForm] = useState<Form>({
        amount: initialAmount
            ? toFixedWithFraction(
                  initialAmount.amount,
                  initialAmount.currency.fraction
              )
            : null,
        counterparty,
        reference: null,
    })
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    return (
        <>
            <Layout
                installationId={installationId}
                networkRPCMap={networkRPCMap}
                networkMap={networkMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                fromAccount={fromAccount}
                portfolio={portfolio}
                form={form}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_submit_clicked':
                            onMsg(msg)
                            break
                        case 'on_add_counterparty_clicked':
                            if (counterparties.length) {
                                setModal({
                                    type: 'select_counterparty',
                                    counterparty: null,
                                })
                                break
                            }
                            setModal({ type: 'add_counterparty' })
                            break
                        case 'on_existing_counterparty_selected':
                            setModal({
                                type: 'select_counterparty',
                                counterparty: form.counterparty,
                            })
                            break
                        case 'on_info_clicked':
                            setModal({ type: 'monerium_fee_info' })
                            break
                        case 'on_reference_messsage_changed':
                            setForm({
                                ...form,
                                reference: msg.reference,
                            })
                            break
                        case 'on_amount_change':
                            setForm({
                                amount: msg.amount,
                                counterparty: form.counterparty,
                                reference: form.reference,
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
            <Modal
                installationId={installationId}
                counterparties={counterparties}
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_save_counterparty_form_submitted':
                            setModal({ type: 'closed' })
                            onMsg(msg)
                            setForm({ ...form, counterparty: msg.counterparty })
                            break
                        case 'on_existing_counterparty_selected':
                            setModal({ type: 'closed' })
                            setForm({ ...form, counterparty: msg.counterparty })
                            break
                        case 'on_delete_counterparty_submitted':
                            if (
                                form.counterparty?.iban ===
                                msg.counterparty.iban
                            ) {
                                setForm({ ...form, counterparty: null })
                            }
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
