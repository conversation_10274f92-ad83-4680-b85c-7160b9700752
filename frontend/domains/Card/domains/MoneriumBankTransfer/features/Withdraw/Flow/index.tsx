import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { AddCounterparty } from './AddCounterparty'
import { Order } from './Order'
import { SelectCounterparty } from './SelectCounterparty'

type Props = {
    portfolio: ServerPortfolio2
    conterparties: Counterparty[]
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    keyStore: SigningKeyStore
    fromAccount: Account
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof AddCounterparty>
    | MsgOf<typeof SelectCounterparty>
    | MsgOf<typeof Order>

type State =
    | { type: 'add_counterparty'; initialCounterparty: Counterparty | null }
    | { type: 'select_counterparty' }

const calculateState = (conterparties: Counterparty[]): State => {
    if (conterparties.length === 0) {
        return { type: 'add_counterparty', initialCounterparty: null }
    }

    return { type: 'select_counterparty' }
}

export const Flow = ({
    portfolio,
    defaultCurrencyConfig,
    networkMap,
    installationId,
    networkRPCMap,
    keyStoreMap,
    keyStore,
    accountsMap,
    feePresetMap,
    sessionPassword,
    gasCurrencyPresetMap,
    portfolioMap,
    conterparties,
    cardConfig,
    fromAccount,
    onMsg,
}: Props) => {
    const [state] = useState<State>(calculateState(conterparties))
    switch (state.type) {
        case 'add_counterparty':
            return (
                <AddCounterparty
                    fromAccount={fromAccount}
                    counterparties={conterparties}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    accountsMap={accountsMap}
                    keyStore={keyStore}
                    portfolio={portfolio}
                    cardConfig={cardConfig}
                    initialCounterparty={state.initialCounterparty}
                    onMsg={onMsg}
                />
            )
        case 'select_counterparty':
            return (
                <SelectCounterparty
                    fromAccount={fromAccount}
                    sessionPassword={sessionPassword}
                    portfolioMap={portfolioMap}
                    portfolio={portfolio}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    keyStoreMap={keyStoreMap}
                    keyStore={keyStore}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    feePresetMap={feePresetMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    accountsMap={accountsMap}
                    installationId={installationId}
                    counterparties={conterparties}
                    cardConfig={cardConfig}
                    onMsg={onMsg}
                />
            )
        default:
            return notReachable(state)
    }
}
