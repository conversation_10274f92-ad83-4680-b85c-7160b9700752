import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Animation } from '@zeal/uikit/Animation'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldCrossOctagon } from '@zeal/uikit/Icon/BoldCrossOctagon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout as UILoadingLayout } from '@zeal/uikit/LoadingLayout'
import { Screen } from '@zeal/uikit/Screen'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { fetchBalanceOfCryptoCurrency } from '@zeal/domains/Address/api/fetchBalanceOf'
import {
    MoneriumAuthInfo,
    MoneriumOfframpOrder,
} from '@zeal/domains/Card/domains/MoneriumBankTransfer'
import {
    fetchMoneriumOrderStatus,
    MoneriumOrderStatus,
} from '@zeal/domains/Card/domains/MoneriumBankTransfer/api/fetchMoneriumOrderStatus'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    id: string
    authInfo: MoneriumAuthInfo
    installationId: string
    networkRPCMap: NetworkRPCMap
    moniriumOfframpOrder: MoneriumOfframpOrder
    networkMap: NetworkMap
    portfolio: ServerPortfolio2
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof LoadingLayout>
    | MsgOf<typeof WithdrawSuccess>
    | MsgOf<typeof WithdrawRejected>

const fetch = async ({
    networkRPCMap,
    networkMap,
    moniriumOfframpOrder,
    portfolio,
    id,
    authInfo,
    signal,
}: {
    id: string
    authInfo: MoneriumAuthInfo
    networkRPCMap: NetworkRPCMap
    moniriumOfframpOrder: MoneriumOfframpOrder
    networkMap: NetworkMap
    portfolio: ServerPortfolio2
    signal?: AbortSignal
}): Promise<{ status: MoneriumOrderStatus }> => {
    const status = await fetchMoneriumOrderStatus({
        authInfo,
        id,
        signal,
    })

    switch (status.status) {
        case 'placed':
        case 'processed':
        case 'rejected': {
            return status
        }
        case 'pending': {
            const amount = await fetchBalanceOfCryptoCurrency({
                address: moniriumOfframpOrder.fromAccount.address,
                currency: moniriumOfframpOrder.amount.currency,
                networkRPCMap,
                networkMap,
            })
            const balance = getBalanceByCryptoCurrency2({
                serverPortfolio: portfolio,
                currency: moniriumOfframpOrder.amount.currency,
            })
            return {
                status:
                    amount.amount < balance.amount ? 'processed' : 'pending',
            }
        }
        default:
            return notReachable(status.status)
    }
}

export const CheckOrderStatus = ({
    installationId,
    networkRPCMap,
    portfolio,
    moniriumOfframpOrder,
    authInfo,
    networkMap,
    id,
    onMsg,
}: Props) => {
    const [pollable, setPollable] = usePollableData(
        fetch,
        {
            type: 'loading',
            params: {
                networkRPCMap,
                portfolio,
                moniriumOfframpOrder,
                id,
                networkMap,
                authInfo,
            },
        },
        {
            pollIntervalMilliseconds: 1000,
            stopIf: (pollable) => {
                switch (pollable.type) {
                    case 'loaded': {
                        switch (pollable.data.status) {
                            case 'placed':
                            case 'pending':
                                return false
                            case 'processed':
                            case 'rejected':
                                return true

                            /* istanbul ignore next */
                            default:
                                return notReachable(pollable.data.status)
                        }
                    }

                    case 'reloading':
                    case 'subsequent_failed':
                    case 'loading':
                    case 'error':
                        return false
                    /* istanbul ignore next */
                    default:
                        return notReachable(pollable)
                }
            },
        }
    )

    switch (pollable.type) {
        case 'loaded': {
            switch (pollable.data.status) {
                case 'placed':
                case 'pending':
                    return <LoadingLayout onMsg={onMsg} />
                case 'processed':
                    return (
                        <WithdrawSuccess
                            installationId={installationId}
                            status={pollable.data.status}
                            onMsg={onMsg}
                        />
                    )
                case 'rejected':
                    return (
                        <WithdrawRejected
                            installationId={installationId}
                            status={pollable.data.status}
                            onMsg={onMsg}
                        />
                    )

                /* istanbul ignore next */
                default:
                    return notReachable(pollable.data.status)
            }
        }

        case 'reloading':
        case 'loading':
            return <LoadingLayout onMsg={onMsg} />

        case 'subsequent_failed':
        case 'error':
            return (
                <>
                    <LoadingLayout onMsg={onMsg} />
                    <AppErrorPopup
                        error={parseAppError(pollable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setPollable({
                                        type: 'loading',
                                        params: pollable.params,
                                    })
                                    break
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}

const LoadingLayout = ({
    onMsg,
}: {
    onMsg: (msg: { type: 'close' }) => void
}) => {
    return (
        <UILoadingLayout
            actionBar={
                <ActionBar
                    left={
                        <IconButton
                            variant="on_light"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            {({ color }) => (
                                <BackIcon size={24} color={color} />
                            )}
                        </IconButton>
                    }
                />
            }
            title={
                <FormattedMessage
                    id="monerium.check_order_status.sending"
                    defaultMessage="Sending"
                />
            }
            onClose={() => {
                onMsg({ type: 'close' })
            }}
        />
    )
}

export const WithdrawSuccess = ({
    status,
    installationId,
    onMsg,
}: {
    installationId: string
    status: 'processed'
    onMsg: (
        msg:
            | {
                  type: 'on_monerium_order_status_changed'
                  status: 'processed'
              }
            | {
                  type: 'close'
              }
    ) => void
}) => {
    useEffect(() => {
        postUserEvent({
            type: 'MoneriumTransferCompletedEvent',
            installationId,
        })
    }, [installationId])

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => {
                onMsg({
                    type: 'close',
                })
            }}
        >
            <Column spacing={16} fill alignX="center" alignY="center">
                <Animation
                    loop={false}
                    animation="success"
                    onAnimationEvent={noop}
                    size={100}
                />

                <Column spacing={8} alignX="center">
                    <Text variant="title2" weight="bold" color="textPrimary">
                        <FormattedMessage
                            id="moneriumWithdrawSuccess.title"
                            defaultMessage="Sent"
                        />
                    </Text>
                    <Text
                        variant="paragraph"
                        weight="regular"
                        color="textSecondary"
                    >
                        <FormattedMessage
                            id="moneriumWithdrawSuccess.supportText"
                            defaultMessage="It may take 24 hrs for your{br}recipient to receive funds"
                            values={{
                                br: '\n',
                            }}
                        />
                    </Text>
                </Column>
            </Column>
            <Actions variant="default">
                <Button
                    variant="primary"
                    size="regular"
                    onClick={() =>
                        onMsg({
                            type: 'on_monerium_order_status_changed',
                            status,
                        })
                    }
                >
                    <FormattedMessage
                        id="action.close"
                        defaultMessage="Close"
                    />
                </Button>
            </Actions>
        </Screen>
    )
}

export const WithdrawRejected = ({
    status,
    onMsg,
    installationId,
}: {
    installationId: string
    status: 'rejected'
    onMsg: (
        msg:
            | {
                  type: 'on_monerium_order_rejected'
                  status: 'rejected'
              }
            | {
                  type: 'on_contact_support_clicked'
              }
            | {
                  type: 'close'
              }
    ) => void
}) => {
    useEffect(() => {
        postUserEvent({
            type: 'MoneriumTransferRejectedEvent',
            installationId,
        })
    }, [installationId])

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => {
                onMsg({
                    type: 'close',
                })
            }}
        >
            <Column spacing={16} fill alignX="center" alignY="center">
                <BoldCrossOctagon size={72} color="gray40" />

                <Column spacing={8} alignX="center">
                    <Text variant="title2" weight="bold" color="textPrimary">
                        <FormattedMessage
                            id="moneriumWithdrawRejected.title"
                            defaultMessage="Transfer reverted"
                        />
                    </Text>
                    <Text
                        variant="paragraph"
                        weight="regular"
                        color="textSecondary"
                        align="center"
                    >
                        <FormattedMessage
                            id="moneriumWithdrawRejected.supportText"
                            defaultMessage="We couldn’t complete your transfer. Please try again and If it still doesn’t work then <link>contact support.</link>"
                            values={{
                                link: (chunks) => (
                                    <Tertiary
                                        color="on_light"
                                        size="regular"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_contact_support_clicked',
                                            })
                                        }
                                    >
                                        {({
                                            color,
                                            textVariant,
                                            textWeight,
                                        }) => (
                                            <Text
                                                textDecorationLine="underline"
                                                color={color}
                                                variant={textVariant}
                                                weight={textWeight}
                                            >
                                                {chunks}
                                            </Text>
                                        )}
                                    </Tertiary>
                                ),
                            }}
                        />
                    </Text>
                </Column>
            </Column>
            <Actions variant="default">
                <Button
                    variant="primary"
                    size="regular"
                    onClick={() =>
                        onMsg({
                            type: 'on_monerium_order_rejected',
                            status,
                        })
                    }
                >
                    <FormattedMessage
                        id="moneriumWithdrawRejected.tryAgain"
                        defaultMessage="Try again"
                    />
                </Button>
            </Actions>
        </Screen>
    )
}
