import { useState } from 'react'

import { MsgOf } from '@zeal/toolkit/MsgOf'
import { notReachable } from '@zeal/toolkit/notReachable'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { MoneriumDepositAccountDetails } from '@zeal/domains/Card/domains/MoneriumBankTransfer'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import { GnosisPayStateValidator } from '@zeal/domains/Card/domains/MoneriumBankTransfer/features/GnosisPayStateValidator'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { MoneriumSetupFlowEnteredEvent } from '@zeal/domains/UserEvents'

import { Flow } from './Flow'

type Props = {
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    keyStore: SigningKeyStore
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    currencyHiddenMap: CurrencyHiddenMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    counterparties: Counterparty[]
    portfolio: ServerPortfolio2
    selectedAdress: Web3.address.Address
    installationCampaign: string | null
    location: MoneriumSetupFlowEnteredEvent['location']
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof GnosisPayStateValidator>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_activate_existing_monerium_account_click'
                  | 'monerium_deposit_on_enable_card_clicked'
                  | 'on_user_not_eligible_for_monerium'
                  | 'on_save_counterparty_form_submitted'
                  | 'on_monerium_successfully_activated'
                  | 'monerium_on_card_disconnected'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_monerium_sign_delay_relay_success_close_clicked'
                  | 'on_card_disconnected'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
          }
      >
    | MsgOf<typeof Flow>

type State =
    | { type: 'gnosis_pay_state_validator' }
    | {
          type: 'withdraw'
          accountDetails: MoneriumDepositAccountDetails
      }

export const Withdraw = ({
    cardConfig,
    networkMap,
    networkRPCMap,
    portfolioMap,
    accountsMap,
    keyStoreMap,
    gasCurrencyPresetMap,
    feePresetMap,
    sessionPassword,
    installationId,
    defaultCurrencyConfig,
    currencyHiddenMap,
    installationCampaign,
    keyStore,
    counterparties,
    portfolio,
    location,
    selectedAdress,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'gnosis_pay_state_validator',
    })

    const cardReadonlySigner = accountsMap[cardConfig.readonlySignerAddress]

    switch (state.type) {
        case 'gnosis_pay_state_validator':
            return (
                <GnosisPayStateValidator
                    cardReadonlySigner={cardReadonlySigner}
                    installationCampaign={installationCampaign}
                    keyStore={keyStore}
                    currencyHiddenMap={currencyHiddenMap}
                    accountsMap={accountsMap}
                    cardConfig={cardConfig}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    location={location}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_activate_existing_monerium_account_click':
                            case 'monerium_deposit_on_enable_card_clicked':
                            case 'on_user_not_eligible_for_monerium':
                            case 'on_monerium_successfully_activated':
                            case 'monerium_on_card_disconnected':
                            case 'on_create_smart_wallet_clicked':
                            case 'on_monerium_sign_delay_relay_success_close_clicked':
                            case 'on_card_disconnected':
                            case 'on_card_import_on_import_keys_clicked':
                            case 'on_card_imported_success_animation_complete':
                            case 'on_onboarded_card_imported_success_animation_complete':
                                onMsg(msg)
                                break
                            case 'on_monerium_enabled_card_signer_is_receiver':
                                setState({
                                    type: 'withdraw',
                                    accountDetails: msg.accountDetails,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'withdraw':
            return (
                <Flow
                    fromAccount={accountsMap[selectedAdress]}
                    cardConfig={cardConfig}
                    portfolio={portfolio}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    keyStore={keyStore}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    conterparties={counterparties}
                    onMsg={onMsg}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
