import { notReachable } from '@zeal/toolkit'
import {
    failure,
    groupByType,
    oneOf,
    Result,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { GNOSIS } from '@zeal/domains/Network/constants'
import { ERC20TransferLog } from '@zeal/domains/RPCRequest'
import { IndexedTransaction } from '@zeal/domains/Transactions'
import { fetchIndexedTransactions } from '@zeal/domains/Transactions/api/fetchIndexedTransactions'

import {
    CashBackDepositTransaction,
    CashBackRewardTransaction,
    CashBackTransaction,
    CashBackWithdrawTransaction,
} from '..'
import {
    CASHBACK_CURRENCY,
    GNOSIS_PAY_CASHBACK_DISTRIBUTION_ADDRESS,
} from '../constants'

export const fetchCashBackTransactions = async ({
    cardSafeAddress,
    signal,
}: {
    cardSafeAddress: Web3.address.Address
    signal?: AbortSignal
}) => {
    const indexerTransactions = await fetchIndexedTransactions({
        networkHexId: GNOSIS.hexChainId,
        address: cardSafeAddress,
        afterTimestampMs: null,
        beforeTimestampMs: null,
        logsForAddresses: null,
        signal,
    })

    const [_, cashBackTransactions] = groupByType(
        indexerTransactions.map((tx) =>
            parseCardCashbackTransaction(tx, cardSafeAddress)
        )
    )

    return cashBackTransactions.toSorted((a, b) => b.timestamp - a.timestamp)
}

const parseCardCashbackTransaction = (
    indexerTransaction: IndexedTransaction,
    cardSafeAddress: Web3.address.Address
): Result<unknown, CashBackTransaction> =>
    oneOf(indexerTransaction, [
        parseCashbackWithdrawTransaction(indexerTransaction, cardSafeAddress),
        parseCashbackRewardTransaction(indexerTransaction, cardSafeAddress),
        parseCashbackDepositTransaction(indexerTransaction, cardSafeAddress),
    ])

const parseCashbackDepositTransaction = (
    transaction: IndexedTransaction,
    cardSafeAddress: Web3.address.Address
): Result<unknown, CashBackDepositTransaction> => {
    const transferLog = transaction.logs.find(
        (log): log is ERC20TransferLog => {
            switch (log.type) {
                case 'unknown':
                case 'added_owner':
                case 'approval':
                case 'account_deployed':
                case 'threshold_updated':
                case 'set_allowance':
                case 'enable_module':
                case 'disable_module':
                case 'safe_module_transaction':
                case 'safe_received':
                case 'user_operation_event':
                case 'user_operation_revert_reason':
                    return false
                case 'erc20_transfer':
                    return (
                        log.to === cardSafeAddress &&
                        log.from !== GNOSIS_PAY_CASHBACK_DISTRIBUTION_ADDRESS &&
                        log.currencyId === CASHBACK_CURRENCY.id
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(log)
            }
        }
    )

    if (!transferLog) {
        return failure({ type: 'no_erc20_transfer_log' })
    }
    const baseAmount = {
        amount: transferLog.amount,
        currency: CASHBACK_CURRENCY,
    }

    return success({
        type: 'deposit',
        timestamp: transaction.timestamp,
        txHash: transaction.hash,
        blockNumber: transaction.blockNumber,
        fromAddress: transferLog.from,
        amount: baseAmount,
    })
}

const parseCashbackRewardTransaction = (
    transaction: IndexedTransaction,
    cardSafeAddress: Web3.address.Address
): Result<unknown, CashBackRewardTransaction> => {
    const transferLog = transaction.logs.find(
        (log): log is ERC20TransferLog => {
            switch (log.type) {
                case 'unknown':
                case 'added_owner':
                case 'approval':
                case 'account_deployed':
                case 'threshold_updated':
                case 'set_allowance':
                case 'enable_module':
                case 'disable_module':
                case 'safe_module_transaction':
                case 'safe_received':
                case 'user_operation_event':
                case 'user_operation_revert_reason':
                    return false
                case 'erc20_transfer':
                    return (
                        log.to === cardSafeAddress &&
                        log.from === GNOSIS_PAY_CASHBACK_DISTRIBUTION_ADDRESS &&
                        log.currencyId === CASHBACK_CURRENCY.id
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(log)
            }
        }
    )

    if (!transferLog) {
        return failure({ type: 'no_erc20_transfer_log' })
    }
    const baseAmount = {
        amount: transferLog.amount,
        currency: CASHBACK_CURRENCY,
    }
    return success({
        type: 'reward',
        blockNumber: transaction.blockNumber,
        timestamp: transaction.timestamp,
        txHash: transaction.hash,
        amount: baseAmount,
    })
}

const parseCashbackWithdrawTransaction = (
    transaction: IndexedTransaction,
    cardSafeAddress: Web3.address.Address
): Result<unknown, CashBackWithdrawTransaction> => {
    const transferLog = transaction.logs.find(
        (log): log is ERC20TransferLog => {
            switch (log.type) {
                case 'unknown':
                case 'added_owner':
                case 'approval':
                case 'account_deployed':
                case 'threshold_updated':
                case 'set_allowance':
                case 'enable_module':
                case 'disable_module':
                case 'safe_module_transaction':
                case 'safe_received':
                case 'user_operation_event':
                case 'user_operation_revert_reason':
                    return false
                case 'erc20_transfer':
                    return (
                        log.to !== cardSafeAddress &&
                        log.from === cardSafeAddress &&
                        log.currencyId === CASHBACK_CURRENCY.id
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(log)
            }
        }
    )

    if (!transferLog) {
        return failure({ type: 'no_erc20_transfer_log' })
    }

    const baseAmount = {
        amount: transferLog.amount,
        currency: CASHBACK_CURRENCY,
    }
    return success({
        type: 'withdraw',
        blockNumber: transaction.blockNumber,
        timestamp: transaction.timestamp,
        txHash: transaction.hash,
        toAddress: transferLog.to,
        amount: baseAmount,
    })
}
