import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { values } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { EligibleForCashback } from '@zeal/domains/Card/domains/Cashback'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Form } from './Form'
import { PortfolioLoader } from './PortfolioLoader'
import { SelectWallet } from './SelectWallet'

type Props = {
    cardCashback: EligibleForCashback
    accountsMap: AccountsMap
    portfolioMap: PortfolioMap
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState

    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    keyStoreMap: KeyStoreMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardReadonlySigner: Account
    keyStore: CardSlientSignKeyStore
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof SelectWallet>, { type: 'close' }>
    | Extract<
          MsgOf<typeof Form>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_cashback_deposit_success'
                  | 'on_get_cashback_currency_clicked'
          }
      >

type State =
    | { type: 'select_wallet'; selectedAccount: Account | null }
    | { type: 'load_portofolio'; fromAccount: Account }
    | {
          type: 'deposit_form'
          fromAccount: Account
          portfolio: ServerPortfolio2
      }

const filterBySigningKeyStore = (
    accountsMap: AccountsMap,
    keyStoreMap: KeyStoreMap
): { account: Account; keystore: SigningKeyStore }[] =>
    values(accountsMap)
        .map((account) => {
            return {
                account: account,
                keystore: getKeyStore({
                    address: account.address,
                    keyStoreMap,
                }),
            }
        })
        .filter(
            (
                accountKeystorePair
            ): accountKeystorePair is {
                account: Account
                keystore: SigningKeyStore
            } => {
                const { keystore } = accountKeystorePair
                switch (keystore.type) {
                    case 'track_only':
                        return false
                    case 'private_key_store':
                    case 'ledger':
                    case 'secret_phrase_key':
                    case 'trezor':
                    case 'safe_4337':
                        return true
                    /* istanbul ignore next */
                    default:
                        return notReachable(keystore)
                }
            }
        )

export const Deposit = ({
    accountsMap,
    cardCashback,
    portfolioMap,
    gnosisPayAccountOnboardedState,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    networkMap,
    cardReadonlySigner,
    keyStore,
    currencyHiddenMap,
    networkRPCMap,
    sessionPassword,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'select_wallet',
        selectedAccount: null,
    })

    const applicableDepositAccounts = filterBySigningKeyStore(
        accountsMap,
        keyStoreMap
    ).map(({ account }) => account)

    switch (state.type) {
        case 'select_wallet':
            return (
                <SelectWallet
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    accounts={applicableDepositAccounts}
                    installationId={installationId}
                    currencyHiddenMap={currencyHiddenMap}
                    portfolioMap={portfolioMap}
                    selectedAccount={state.selectedAccount}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_account_selected':
                                setState({
                                    type: 'load_portofolio',
                                    fromAccount: msg.account,
                                })
                                break

                            case 'close':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'load_portofolio':
            return (
                <PortfolioLoader
                    currencyHiddenMap={currencyHiddenMap}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationId={installationId}
                    account={state.fromAccount}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_portfolio_loaded':
                                setState({
                                    type: 'deposit_form',
                                    fromAccount: state.fromAccount,
                                    portfolio: msg.portfolio,
                                })
                                break

                            case 'close':
                                setState({
                                    type: 'select_wallet',
                                    selectedAccount: state.fromAccount,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'deposit_form':
            return (
                <Form
                    cardReadonlySigner={cardReadonlySigner}
                    keyStore={keyStore}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    accountsMap={accountsMap}
                    gnosisPayAccountOnboardedState={
                        gnosisPayAccountOnboardedState
                    }
                    cardCashback={cardCashback}
                    feePresetMap={feePresetMap}
                    fromAccount={state.fromAccount}
                    portfolio={state.portfolio}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({
                                    type: 'select_wallet',
                                    selectedAccount: state.fromAccount,
                                })
                                break

                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_cashback_deposit_success':
                            case 'on_get_cashback_currency_clicked':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        default:
            return notReachable(state)
    }
}
