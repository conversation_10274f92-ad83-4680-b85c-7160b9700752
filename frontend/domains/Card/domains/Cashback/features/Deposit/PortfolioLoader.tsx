import { useEffect } from 'react'

import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'

import { Account } from '@zeal/domains/Account'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { fetchServerPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    account: Account
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig

    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_portfolio_loaded'; portfolio: ServerPortfolio2 }
    | { type: 'close' }

export const PortfolioLoader = ({
    account,
    onMsg,
    installationId,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetchServerPortfolio2, {
        type: 'loading',
        params: {
            currencyHiddenMap,
            address: account.address,
            defaultCurrencyConfig,
            forceRefresh: false,
            networkMap,
            networkRPCMap,
            installationId,
        },
    })

    const liveMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'error':
                break
            case 'loaded':
                liveMsg.current({
                    type: 'on_portfolio_loaded',
                    portfolio: loadable.data,
                })
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [liveMsg, loadable])

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={null}
                    onClose={() => {
                        onMsg({ type: 'close' })
                    }}
                />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={null}
                        onClose={() => {
                            onMsg({ type: 'close' })
                        }}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loaded':
            return null
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
