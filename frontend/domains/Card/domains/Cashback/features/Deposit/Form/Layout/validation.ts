import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { Account } from '@zeal/domains/Account'
import { Address } from '@zeal/domains/Address'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import {
    CashbackDepositRequest,
    EligibleForCashback,
} from '@zeal/domains/Card/domains/Cashback'
import { CryptoCurrency, FiatCurrency } from '@zeal/domains/Currency'
import { FXRate2 } from '@zeal/domains/FXRate'
import { CryptoMoney } from '@zeal/domains/Money'
import { sum } from '@zeal/domains/Money/helpers/sum'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'

export type Pollable = PollableData<
    FXRate2<CryptoCurrency, FiatCurrency> | null,
    {
        amount: string | null
        currency: CryptoCurrency
        cardSafe: Address
        fromAccount: Account
    }
>

type NotEnoughBalanceError = { type: 'not_enough_balance' }
type AmountRequired = { type: 'amount_required' }

type SubmitError = NotEnoughBalanceError | AmountRequired

export type FormError = {
    from?: NotEnoughBalanceError
    submit?: SubmitError
}

export const getTotalCashbackTokenAfterDeposit = ({
    cardCashback,
    depositAmount,
}: {
    cardCashback: EligibleForCashback
    depositAmount: CryptoMoney
}): CryptoMoney => {
    switch (cardCashback.type) {
        case 'eligible_for_cashback_no_cashback':
            return depositAmount
        case 'eligible_for_cashback_has_cashback':
            return (
                sum([cardCashback.lockedAmount, depositAmount]) || depositAmount
            )
        /* istanbul ignore next */
        default:
            return notReachable(cardCashback)
    }
}

export const getBalance = ({
    cardCashback,
    pollable,
}: {
    cardCashback: EligibleForCashback
    pollable: Pollable
}): CryptoMoney => {
    const currency = pollable.params.currency
    switch (cardCashback.type) {
        case 'eligible_for_cashback_no_cashback':
            return { amount: 0n, currency }

        case 'eligible_for_cashback_has_cashback':
            return cardCashback.lockedAmount

        default:
            return notReachable(cardCashback)
    }
}

const validateNonZeroAmount = (
    pollable: Pollable
): Result<AmountRequired, unknown> => {
    const { amount: inputAmount, currency } = pollable.params

    const amount = fromFixedWithFraction(inputAmount, currency.fraction)

    if (amount === 0n) {
        return failure({ type: 'amount_required' })
    }

    return success(amount)
}

const validateBalance = (
    pollable: Pollable,
    portfolio: ServerPortfolio2
): Result<NotEnoughBalanceError, CryptoMoney> => {
    const { amount: inputAmount, currency } = pollable.params

    const balance = getBalanceByCryptoCurrency2({
        currency,
        serverPortfolio: portfolio,
    })

    const amount = fromFixedWithFraction(inputAmount, currency.fraction)

    return balance.amount < amount
        ? failure({ type: 'not_enough_balance' })
        : success({ amount, currency })
}

export const validate = ({
    pollable,
    cardCashback,
    portfolio,
}: {
    cardCashback: EligibleForCashback
    portfolio: ServerPortfolio2
    pollable: Pollable
}): Result<FormError, CashbackDepositRequest> =>
    shape({
        from: validateBalance(pollable, portfolio),
        submit: validateNonZeroAmount(pollable).andThen(() =>
            validateBalance(pollable, portfolio)
        ),
    }).map(({ from: depositAmount }) => ({
        transaction: createTransferEthSendTransaction({
            amount: depositAmount,
            to: pollable.params.cardSafe,
            from: pollable.params.fromAccount.address,
            network: CARD_NETWORK,
        }),
        depositAmount,
        totalAmountAfterDeposit: getTotalCashbackTokenAfterDeposit({
            cardCashback,
            depositAmount,
        }),
    }))
