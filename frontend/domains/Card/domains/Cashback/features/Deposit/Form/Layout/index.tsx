import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LightArrowRight2 } from '@zeal/uikit/Icon/LightArrowRight2'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { ListItem } from '@zeal/uikit/ListItem'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Spacer } from '@zeal/uikit/Spacer'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import {
    fromFixedWithFraction,
    toFixedWithFraction,
    unsafe_toNumberWithFraction,
} from '@zeal/toolkit/BigInt'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { ActivatedCard } from '@zeal/domains/Card'
import { CardListItem } from '@zeal/domains/Card/components/CardListItem'
import {
    CashbackDepositRequest,
    EligibleForCashback,
} from '@zeal/domains/Card/domains/Cashback'
import { FormattedCashbackPercentage } from '@zeal/domains/Card/domains/Cashback/components/FormattedCashbackPercentage'
import { getCashbackPercentage } from '@zeal/domains/Card/domains/Cashback/helpers/getCashbackPercentage'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

import {
    getBalance,
    getTotalCashbackTokenAfterDeposit,
    Pollable,
    validate,
} from './validation'

type Props = {
    portfolio: ServerPortfolio2
    pollable: Pollable
    cardCashback: EligibleForCashback
    card: ActivatedCard
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_amount_change'; amount: string | null }
    | { type: 'on_get_cashback_currency_clicked'; fromAccount: Account }
    | {
          type: 'on_form_submitted'
          cashbackDepositRequest: CashbackDepositRequest
      }

export const Layout = ({
    onMsg,
    pollable,
    portfolio,
    networkMap,
    card,
    cardCashback,
}: Props) => {
    const form = pollable.params
    const { formatMessage, formatNumber } = useIntl()

    const validationResult = validate({
        cardCashback,
        pollable,
        portfolio,
    })

    const errors = validationResult.getFailureReason() || {}

    const network = findNetworkByHexChainId(
        form.currency.networkHexChainId,
        networkMap
    )

    const fromPortfolioBalance = getBalanceByCryptoCurrency2({
        serverPortfolio: portfolio,
        currency: form.currency,
    })

    const from: CryptoMoney = {
        amount: fromFixedWithFraction(form.amount, form.currency.fraction),
        currency: form.currency,
    }

    const isMaxAmount = from.amount === fromPortfolioBalance.amount

    const formattedAmount = formatNumber(
        unsafe_toNumberWithFraction(from.amount, form.currency.fraction),
        {
            maximumFractionDigits: 6,
        }
    )

    const cashbackCurrencyBalance = getBalance({ cardCashback, pollable })

    const cashbackBeforeDeposit = getCashbackPercentage({
        amount: cashbackCurrencyBalance,
        earlyAdopter: cardCashback.earlyAdopter,
    })

    const cashbackAfterDeposit = getCashbackPercentage({
        amount: getTotalCashbackTokenAfterDeposit({
            cardCashback,
            depositAmount: {
                amount: fromFixedWithFraction(
                    form.amount,
                    form.currency.fraction
                ),
                currency: form.currency,
            },
        }),
        earlyAdopter: cardCashback.earlyAdopter,
    })

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} fill alignY="stretch">
                <ActionBar
                    top={
                        <ActionBarAccountIndicator account={form.fromAccount} />
                    }
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <ActionBar.Header>
                                    <FormattedMessage
                                        id="cashback.deposit.header"
                                        defaultMessage="Deposit {currency}"
                                        values={{
                                            currency: form.currency.code,
                                        }}
                                    />
                                </ActionBar.Header>
                            </Row>
                        </Clickable>
                    }
                />
                <Column spacing={12} fill>
                    <Column spacing={4}>
                        <AmountInput
                            content={{
                                topLeft: (
                                    <Row spacing={4}>
                                        <CurrencyAvatar
                                            currency={form.currency}
                                            size={24}
                                            rightBadge={({ size }) => (
                                                <Badge
                                                    size={size}
                                                    network={findNetworkByHexChainId(
                                                        form.currency
                                                            .networkHexChainId,
                                                        networkMap
                                                    )}
                                                />
                                            )}
                                        />
                                        <Text
                                            variant="title3"
                                            color="textPrimary"
                                            weight="medium"
                                        >
                                            {form.currency.code}
                                        </Text>
                                    </Row>
                                ),
                                topRight: ({ onBlur, onFocus, isFocused }) => (
                                    <AmountInput.Input
                                        prefix=""
                                        onFocus={onFocus}
                                        onBlur={onBlur}
                                        label={formatMessage({
                                            id: 'cashback.deposit.amount.label',
                                            defaultMessage: 'Deposit amount',
                                        })}
                                        fraction={form.currency.fraction}
                                        autoFocus
                                        readOnly={false}
                                        amount={
                                            !isFocused && isMaxAmount
                                                ? formattedAmount
                                                : pollable.params.amount
                                        }
                                        onChange={(value) => {
                                            onMsg({
                                                type: 'on_amount_change',
                                                amount: value,
                                            })
                                        }}
                                        onSubmitEditing={noop}
                                    />
                                ),

                                bottomLeft: (
                                    <Tertiary
                                        color={
                                            errors.from
                                                ? 'critical'
                                                : 'on_light'
                                        }
                                        size="regular"
                                        onClick={() => {
                                            onMsg({
                                                type: 'on_amount_change',
                                                amount: toFixedWithFraction(
                                                    fromPortfolioBalance.amount,
                                                    form.currency.fraction
                                                ),
                                            })
                                        }}
                                    >
                                        {({
                                            color,
                                            textVariant,
                                            textWeight,
                                        }) => (
                                            <Text
                                                color={color}
                                                variant={textVariant}
                                                weight={textWeight}
                                                textDecorationLine="underline"
                                            >
                                                <FormattedMessage
                                                    id="cashback.deposit.max_label"
                                                    defaultMessage="Max: {amount}"
                                                    values={{
                                                        amount: (
                                                            <FormattedMoneyPrecise
                                                                withSymbol={
                                                                    false
                                                                }
                                                                sign={null}
                                                                money={
                                                                    fromPortfolioBalance
                                                                }
                                                            />
                                                        ),
                                                    }}
                                                />
                                            </Text>
                                        )}
                                    </Tertiary>
                                ),
                                bottomRight: (() => {
                                    switch (pollable.type) {
                                        case 'loaded':
                                        case 'reloading':
                                        case 'subsequent_failed':
                                            return pollable.data ? (
                                                <Text
                                                    variant="footnote"
                                                    color="textSecondary"
                                                    weight="regular"
                                                >
                                                    <FormattedMoneyPrecise
                                                        withSymbol
                                                        sign={null}
                                                        money={applyRate2({
                                                            baseAmount: {
                                                                amount: fromFixedWithFraction(
                                                                    form.amount,
                                                                    form
                                                                        .currency
                                                                        .fraction
                                                                ),
                                                                currency:
                                                                    form.currency,
                                                            },
                                                            rate: pollable.data,
                                                        })}
                                                    />
                                                </Text>
                                            ) : null
                                        case 'loading':
                                            return (
                                                <Skeleton
                                                    width={40}
                                                    height={16}
                                                    variant="default"
                                                />
                                            )
                                        case 'error':
                                            return null

                                        default:
                                            return notReachable(pollable)
                                    }
                                })(),
                            }}
                            state={errors.from ? 'error' : 'normal'}
                        />

                        <NextStepSeparator />

                        <Group variant="default">
                            <CardListItem card={card} />
                        </Group>
                    </Column>
                    <Group variant="default">
                        <Row spacing={4}>
                            <Text
                                variant="callout"
                                weight="medium"
                                color="textPrimary"
                            >
                                <FormattedMessage
                                    id="cashback.deposit.yourcashback"
                                    defaultMessage="Your Cashback"
                                />
                            </Text>

                            <Spacer />

                            <Text
                                variant="callout"
                                weight="medium"
                                color="textPrimary"
                            >
                                {cashbackAfterDeposit ===
                                cashbackBeforeDeposit ? (
                                    <FormattedCashbackPercentage
                                        cashbackAmount={cashbackBeforeDeposit}
                                    />
                                ) : (
                                    <FormattedMessage
                                        id="cashback.deposit.change"
                                        defaultMessage="{from} to {to}"
                                        values={{
                                            from: (
                                                <FormattedCashbackPercentage
                                                    cashbackAmount={
                                                        cashbackBeforeDeposit
                                                    }
                                                />
                                            ),
                                            to: (
                                                <Text color="teal40">
                                                    <FormattedCashbackPercentage
                                                        cashbackAmount={
                                                            cashbackAfterDeposit
                                                        }
                                                    />
                                                </Text>
                                            ),
                                        }}
                                    />
                                )}
                            </Text>
                        </Row>
                    </Group>
                </Column>

                <Column spacing={12}>
                    <Group variant="default">
                        <ListItem
                            aria-current={false}
                            size="regular"
                            onClick={() =>
                                onMsg({
                                    type: 'on_get_cashback_currency_clicked',
                                    fromAccount: form.fromAccount,
                                })
                            }
                            primaryText={
                                <FormattedMessage
                                    id="cashback.deposit.get.tokens.title"
                                    defaultMessage="Get {currency} tokens"
                                    values={{
                                        currency: form.currency.code,
                                    }}
                                />
                            }
                            shortText={
                                <FormattedMessage
                                    id="cashback.deposit.get.tokens.subtitle"
                                    defaultMessage="Swap tokens into {currency} on {network} Chain"
                                    values={{
                                        currency: form.currency.code,
                                        network: network.name,
                                    }}
                                />
                            }
                            side={{
                                rightIcon: ({ size }) => (
                                    <LightArrowRight2
                                        size={size}
                                        color="gray20"
                                    />
                                ),
                            }}
                        />
                    </Group>
                    <Actions variant="default">
                        <Button
                            size="regular"
                            variant="secondary"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            <FormattedMessage
                                id="actions.cancel"
                                defaultMessage="Cancel"
                            />
                        </Button>

                        <Button
                            size="regular"
                            variant="primary"
                            disabled={!!errors.submit}
                            onClick={() =>
                                validationResult.tap((cashbackDepositRequest) =>
                                    onMsg({
                                        type: 'on_form_submitted',
                                        cashbackDepositRequest,
                                    })
                                )
                            }
                        >
                            <FormattedMessage
                                id="actions.deposit"
                                defaultMessage="Deposit"
                            />
                        </Button>
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}
