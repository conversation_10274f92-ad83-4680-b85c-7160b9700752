import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import {
    CardCashback,
    CashbackDepositRequest,
    EligibleForCashback,
} from '@zeal/domains/Card/domains/Cashback'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { FetchCashback } from './FetchCashback'

type Props = {
    cashbackDepositRequest: CashbackDepositRequest
    fromAccount: Account

    cardCashback: EligibleForCashback
    portfolio: ServerPortfolio2
    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    sessionPassword: string
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardReadonlySigner: Account
    keyStore: CardSlientSignKeyStore
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_cashback_deposit_success'; cardCashback: CardCashback }
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >

type State = { type: 'submit_transaction' } | { type: 'fetch_cashback' }

export const SubmitDeposit = ({
    cashbackDepositRequest,
    fromAccount,
    portfolio,
    accountsMap,
    feePresetMap,
    gnosisPayAccountOnboardedState,
    gasCurrencyPresetMap,
    cardCashback,
    installationId,
    keyStore,
    cardReadonlySigner,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    sessionPassword,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'submit_transaction' })

    switch (state.type) {
        case 'submit_transaction':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolio={portfolio}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    sessionPassword={sessionPassword}
                    accounts={accountsMap}
                    keystores={keyStoreMap}
                    installationId={installationId}
                    networkRPCMap={networkRPCMap}
                    fetchSimulationByRequest={async () => ({
                        type: 'simulated',
                        simulation: {
                            transaction: {
                                type: 'card_cashback_deposit',
                                cashbackDepositRequest,
                                card: gnosisPayAccountOnboardedState.selectedCard,
                            },
                            checks: [],
                            currencies: {},
                        },
                    })}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'card_cashback_deposit',
                            cashbackDepositRequest,
                            card: gnosisPayAccountOnboardedState.selectedCard,
                        },
                        currencies: {},
                    })}
                    key={state.type}
                    sendTransactionRequests={[
                        cashbackDepositRequest.transaction,
                    ]}
                    account={fromAccount}
                    network={CARD_NETWORK}
                    state={{ type: 'maximised' }}
                    actionSource={{
                        type: 'internal',
                        transactionEventSource: 'cardCashBackDeposit',
                    }}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_safe_transaction_failure_accepted':
                                onMsg({ type: 'close' })
                                break

                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'transaction_request_replaced':
                            case 'on_user_operation_bundled':
                                noop()
                                break
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                                postUserEvent({
                                    type: 'CashbackDepositCompletedEvent',
                                    asset: 'gno',
                                    installationId,
                                })
                                break

                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in cashback deposit $${msg.type}`
                                    )
                                )
                                break

                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break

                            case 'on_completed_safe_transaction_close_click':
                            case 'on_completed_transaction_close_click':
                                setState({ type: 'fetch_cashback' })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'fetch_cashback':
            return (
                <FetchCashback
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    cardCashback={cardCashback}
                    gnosisPayAccountOnboardedState={
                        gnosisPayAccountOnboardedState
                    }
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    keyStore={keyStore}
                    cardReadonlySigner={cardReadonlySigner}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_cashback_fetched':
                                onMsg({
                                    type: 'on_cashback_deposit_success',
                                    cardCashback: msg.cardCashback,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        default:
            return notReachable(state)
    }
}
