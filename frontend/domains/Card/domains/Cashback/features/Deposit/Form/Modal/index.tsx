import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import {
    CashbackDepositRequest,
    EligibleForCashback,
} from '@zeal/domains/Card/domains/Cashback'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Confirmation } from './Confirmation'
import { SubmitDeposit } from './SubmitDeposit'

type Props = {
    state: State

    portfolio: ServerPortfolio2
    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    sessionPassword: string
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    fromAccount: Account
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    cardCashback: EligibleForCashback
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardReadonlySigner: Account
    keyStore: CardSlientSignKeyStore
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof SubmitDeposit> | MsgOf<typeof Confirmation>

export type State =
    | { type: 'closed' }
    | { type: 'confirmation'; cashbackDepositRequest: CashbackDepositRequest }
    | { type: 'submit_deposit'; cashbackDepositRequest: CashbackDepositRequest }

export const Modal = ({
    state,
    accountsMap,
    cardCashback,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    gnosisPayAccountOnboardedState,
    portfolio,
    sessionPassword,
    fromAccount,
    cardReadonlySigner,
    keyStore,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null

        case 'confirmation':
            return (
                <Confirmation
                    onMsg={onMsg}
                    cardCashback={cardCashback}
                    cashbackDepositRequest={state.cashbackDepositRequest}
                />
            )

        case 'submit_deposit':
            return (
                <UIModal>
                    <SubmitDeposit
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        cardCashback={cardCashback}
                        accountsMap={accountsMap}
                        cashbackDepositRequest={state.cashbackDepositRequest}
                        feePresetMap={feePresetMap}
                        fromAccount={fromAccount}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        installationId={installationId}
                        keyStoreMap={keyStoreMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        portfolio={portfolio}
                        sessionPassword={sessionPassword}
                        cardReadonlySigner={cardReadonlySigner}
                        keyStore={keyStore}
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        default:
            return notReachable(state)
    }
}
