import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { Address } from '@zeal/domains/Address'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
} from '@zeal/domains/Card'
import { EligibleForCashback } from '@zeal/domains/Card/domains/Cashback'
import { CASHBACK_CURRENCY } from '@zeal/domains/Card/domains/Cashback/constants'
import {
    CryptoCurrency,
    FiatCurrency,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { FXRate2 } from '@zeal/domains/FXRate'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    portfolio: ServerPortfolio2
    fromAccount: Account
    cardCashback: EligibleForCashback
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    cardReadonlySigner: Account
    keyStore: CardSlientSignKeyStore

    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_cashback_deposit_success'
          }
      >
    | Extract<
          MsgOf<typeof Layout>,
          { type: 'on_get_cashback_currency_clicked' | 'close' }
      >

const POLL_INTERVAL_MS = 60_000

type Params = {
    amount: string | null
    currency: CryptoCurrency
    cardSafe: Address
    fromAccount: Account
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
}

const fetch = ({
    currency,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
}: Params): Promise<FXRate2<CryptoCurrency, FiatCurrency> | null> =>
    fetchRate({
        cryptoCurrency: currency,
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
    })

export const Form = ({
    gnosisPayAccountOnboardedState,
    fromAccount,
    cardCashback,
    portfolio,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStore,
    cardReadonlySigner,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    sessionPassword,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [pollable, setPollable] = usePollableData<
        FXRate2<CryptoCurrency, FiatCurrency> | null,
        Params
    >(
        fetch,
        {
            type: 'loading',
            params: {
                amount: null,
                defaultCurrencyConfig,
                networkMap,
                currency: CASHBACK_CURRENCY,
                cardSafe: gnosisPayAccountOnboardedState.cardSafe.address,
                fromAccount,
                networkRPCMap,
            },
        },
        { pollIntervalMilliseconds: POLL_INTERVAL_MS }
    )

    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Layout
                networkMap={networkMap}
                card={gnosisPayAccountOnboardedState.selectedCard}
                cardCashback={cardCashback}
                pollable={pollable}
                portfolio={portfolio}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_get_cashback_currency_clicked':
                        case 'close':
                            onMsg(msg)
                            break

                        case 'on_form_submitted':
                            setModal({
                                type: 'confirmation',
                                cashbackDepositRequest:
                                    msg.cashbackDepositRequest,
                            })
                            break

                        case 'on_amount_change':
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    amount: msg.amount,
                                },
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />

            <Modal
                cardReadonlySigner={cardReadonlySigner}
                keyStore={keyStore}
                defaultCurrencyConfig={defaultCurrencyConfig}
                gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
                cardCashback={cardCashback}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                fromAccount={fromAccount}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                installationId={installationId}
                keyStoreMap={keyStoreMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                portfolio={portfolio}
                sessionPassword={sessionPassword}
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_confirm_deposit_clicked':
                            postUserEvent({
                                type: 'CashbackDepositInitiatedEvent',
                                asset: 'gno',
                                installationId,
                            })
                            setModal({
                                type: 'submit_deposit',
                                cashbackDepositRequest:
                                    msg.cashbackDepositRequest,
                            })
                            break

                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_cashback_deposit_success':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
