import { notReachable } from '@zeal/toolkit'

import { CardPayment, CardTransaction } from '@zeal/domains/Card'
import {
    CASHBACK_EXCLUDED_MCC_CODES,
    CURVE_TRANSACTION_REGEXP,
} from '@zeal/domains/Card/domains/Cashback/constants'

import { BREWARD_EXPITY_AFTER_ACTIVATION_TIMESTEMP_MS } from '../constants'

export const isPaymentEligibleForReward = (
    transaction: CardTransaction,
    firstCardActivatedTimestampMS: number
): transaction is CardPayment => {
    const expiryTimestampMS =
        firstCardActivatedTimestampMS +
        BREWARD_EXPITY_AFTER_ACTIVATION_TIMESTEMP_MS
    switch (transaction.kind) {
        case 'Payment': {
            switch (transaction.status) {
                case 'Reversal':
                case 'Declined':
                    return false
                case 'Approved': {
                    return (
                        !CASHBACK_EXCLUDED_MCC_CODES.has(
                            transaction.merchant.mcc
                        ) &&
                        !CURVE_TRANSACTION_REGEXP.test(
                            transaction.merchant.name
                        ) &&
                        firstCardActivatedTimestampMS <=
                            transaction.createdAt &&
                        expiryTimestampMS >= transaction.createdAt
                    )
                }
                default:
                    return notReachable(transaction)
            }
        }

        case 'Reversal':
        case 'Refund':
        case 'Unknown':
            return false
        default:
            return notReachable(transaction)
    }
}
