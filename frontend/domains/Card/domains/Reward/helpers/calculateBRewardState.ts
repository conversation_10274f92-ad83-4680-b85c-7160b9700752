import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'

import { CardTransaction, GnosisPayAccountState2 } from '@zeal/domains/Card'
import { getActivationTimeOfFirstCard } from '@zeal/domains/Card/helpers/getActivationTimeOfFirstCard'
import { captureError } from '@zeal/domains/Error/helpers/captureError'

import { isPaymentEligibleForReward } from './isPaymentEligibleForReward'

import {
    BReward,
    ExpiredBReward,
    InProgressBReward,
    ReadyToClaimBReward,
} from '..'
import {
    BREWARD_CLAIM_LIMIT_AMOUNT_MAP,
    BREWARD_EXPITY_AFTER_ACTIVATION_TIMESTEMP_MS,
} from '../constants'

export const calculateBRewardState = ({
    gnosisAccountState,
    cardTransactions,
    isBRewardClaimed,
}: {
    gnosisAccountState: GnosisPayAccountState2
    cardTransactions: CardTransaction[]
    isBRewardClaimed: boolean
}): BReward => {
    switch (gnosisAccountState.type) {
        case 'not_onboarded':
            return { type: 'not_eligible' }
        case 'onboarded': {
            if (
                !gnosisAccountState.isCreatedViaZeal ||
                !gnosisAccountState.activatedCards.length
            ) {
                return { type: 'not_eligible' }
            }

            if (isBRewardClaimed) {
                return { type: 'claimed' }
            }

            const firstCardActivatedTimestampMS = getActivationTimeOfFirstCard({
                gnosisPayAccountState: gnosisAccountState,
            })

            const filteredCardTransactions = cardTransactions.filter(
                (transaction) => {
                    return isPaymentEligibleForReward(
                        transaction,
                        firstCardActivatedTimestampMS
                    )
                }
            )

            const spent = filteredCardTransactions.reduce(
                (acc, transaction) => {
                    if (
                        acc.currency.id ===
                        transaction.billingAmount.currency.id
                    ) {
                        return {
                            amount:
                                transaction.billingAmount.amount + acc.amount,
                            currency: acc.currency,
                        }
                    } else {
                        captureError(
                            new ImperativeError(
                                `[BRewards] Calculation card transactions broken billingAmount.currency.id !== gnosisAccountState.cardSafe.cryptoCurrency.id ${transaction.billingAmount.currency.id} ${acc.currency.id} `
                            )
                        )
                    }
                    return acc
                },
                {
                    amount: 0n,
                    currency: gnosisAccountState.cardSafe.cryptoCurrency,
                }
            )

            return calculateBRewardStateBasedOnReward({
                reward: {
                    type: 'in_progress',
                    spent,
                    firstCardActivatedTimestampMS,
                },
            })
        }

        default:
            return notReachable(gnosisAccountState)
    }
}

export const calculateBRewardStateBasedOnReward = ({
    reward,
}: {
    reward: InProgressBReward
}): ExpiredBReward | ReadyToClaimBReward | InProgressBReward => {
    const { firstCardActivatedTimestampMS, spent } = reward
    const expiryTimestampMS =
        firstCardActivatedTimestampMS +
        BREWARD_EXPITY_AFTER_ACTIVATION_TIMESTEMP_MS
    const currentTimestampMS = Date.now()
    const limit = BREWARD_CLAIM_LIMIT_AMOUNT_MAP[spent.currency.id]

    if (!limit) {
        captureError(
            new ImperativeError(
                'Missing currency in  BREWARD_CLAIM_LIMIT_AMOUNT_MAP',
                {
                    currencyId: spent.currency.id,
                }
            )
        )
        return reward
    }

    if (spent.amount > limit) {
        return { type: 'ready_to_claim', firstCardActivatedTimestampMS, spent }
    }

    if (currentTimestampMS >= expiryTimestampMS) {
        return {
            type: 'expired',
            dismissed: false,
            firstCardActivatedTimestampMS,
            spent,
        }
    }

    return reward
}
