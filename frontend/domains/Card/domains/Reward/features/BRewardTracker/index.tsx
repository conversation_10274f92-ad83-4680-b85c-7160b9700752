import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import {
    LoadedReloadableData,
    useLoadedReloadableData,
} from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { Account } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    CardTransaction,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { fetchTransactionsWithSilentLogin } from '@zeal/domains/Card/api/fetchTransactions'
import {
    BReward,
    ClaimedBReward,
    ExpiredBReward,
    InProgressBReward,
    ReadyToClaimBReward,
    ReferralConfig,
} from '@zeal/domains/Card/domains/Reward'
import { fetchAreBRewardsClaimed } from '@zeal/domains/Card/domains/Reward/api/fetchAreRewardsClaimed'
import { BREWARD_EXPITY_AFTER_ACTIVATION_TIMESTEMP_MS } from '@zeal/domains/Card/domains/Reward/constants'
import { calculateBRewardState } from '@zeal/domains/Card/domains/Reward/helpers/calculateBRewardState'
import { TakerPortfolioMap2 } from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { AppRating } from '@zeal/domains/Feedback'
import { NetworkRPCMap } from '@zeal/domains/Network'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Expired } from './Expired'
import { InProgress } from './InProgress'
import { ReadyToClaim } from './ReadyToClaim'

type Props = {
    takerPortfolioMap: TakerPortfolioMap2
    keyStore: CardSlientSignKeyStore
    gnosisOnboardedAccountState: GnosisPayAccountOnboardedState
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    owner: Account
    appRating: AppRating
    installationId: string
    installationCampaign: string | null
    referralConfig: ReferralConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof Expired>
    | MsgOf<typeof ReadyToClaim>
    | {
          type: 'card_brewards_updated'
          reward:
              | InProgressBReward
              | ExpiredBReward
              | ReadyToClaimBReward
              | ClaimedBReward
      }

type Params = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    gnosisOnboardedAccountState: GnosisPayAccountOnboardedState
    keyStore: CardSlientSignKeyStore
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
}
const fetch = async ({
    cardConfig,
    networkRPCMap,
    keyStore,
    sessionPassword,
    gnosisOnboardedAccountState,
}: Params): Promise<{
    breward: BReward
    cardTransactions: CardTransaction[] | null
}> => {
    switch (cardConfig.rewards.type) {
        case 'not_eligible':
        case 'expired':
        case 'claimed':
            return { breward: cardConfig.rewards, cardTransactions: null }
        case 'in_progress':
        case 'ready_to_claim': {
            const [cardTransactions, [isBRewardClaimed]] = await Promise.all([
                fetchTransactionsWithSilentLogin({
                    keyStore,
                    readonlySignerAddress: cardConfig.readonlySignerAddress,
                    sessionPassword,
                    afterTimestampMs:
                        cardConfig.rewards.firstCardActivatedTimestampMS,
                    beforeTimestampMs:
                        cardConfig.rewards.firstCardActivatedTimestampMS +
                        BREWARD_EXPITY_AFTER_ACTIVATION_TIMESTEMP_MS,
                }),
                fetchAreBRewardsClaimed({
                    userIds: [gnosisOnboardedAccountState.userId],
                    networkRPCMap,
                }),
            ])
            const breward = calculateBRewardState({
                gnosisAccountState: gnosisOnboardedAccountState,
                cardTransactions,
                isBRewardClaimed,
            })
            return {
                breward,
                cardTransactions,
            }
        }

        default:
            return notReachable(cardConfig.rewards)
    }
}

const calculateType = ({
    cardConfig,
    keyStore,
    gnosisOnboardedAccountState,
    sessionPassword,
    networkRPCMap,
}: {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    keyStore: CardSlientSignKeyStore
    gnosisOnboardedAccountState: GnosisPayAccountOnboardedState
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
}): LoadedReloadableData<
    { breward: BReward; cardTransactions: null },
    Params
> => {
    switch (cardConfig.rewards.type) {
        case 'not_eligible':
        case 'expired':
        case 'claimed':
            return {
                type: 'loaded',
                params: {
                    cardConfig,
                    keyStore,
                    sessionPassword,
                    gnosisOnboardedAccountState,
                    networkRPCMap,
                },
                data: { breward: cardConfig.rewards, cardTransactions: null },
            }
        case 'in_progress':
        case 'ready_to_claim':
            return {
                type: 'reloading',
                params: {
                    cardConfig,
                    keyStore,
                    sessionPassword,
                    gnosisOnboardedAccountState,
                    networkRPCMap,
                },
                data: { breward: cardConfig.rewards, cardTransactions: null },
            }

        /* istanbul ignore next */
        default:
            return notReachable(cardConfig.rewards)
    }
}

export const BRewardTracker = ({
    keyStore,

    appRating,
    takerPortfolioMap,
    cardConfig,
    sessionPassword,
    installationId,
    owner,
    networkRPCMap,
    installationCampaign,
    gnosisOnboardedAccountState,
    referralConfig,
    onMsg,
}: Props) => {
    const [loadable] = useLoadedReloadableData(
        fetch,
        calculateType({
            cardConfig,
            sessionPassword,
            keyStore,
            gnosisOnboardedAccountState,
            networkRPCMap,
        })
    )
    const liveOnMsg = useLiveRef(onMsg)
    const liveCardConfig = useLiveRef(cardConfig)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                switch (loadable.data.breward.type) {
                    case 'claimed':
                    case 'ready_to_claim':
                    case 'expired':
                        if (
                            loadable.data.breward.type !==
                            liveCardConfig.current.rewards.type
                        ) {
                            liveOnMsg.current({
                                type: 'card_brewards_updated',
                                reward: loadable.data.breward,
                            })
                            postUserEvent({
                                type: 'RewardsStateUpdated',
                                transactions: loadable.data.cardTransactions,
                                prevRewardState: liveCardConfig.current.rewards,
                                newRewardState: loadable.data.breward,
                                installationId,
                            })
                        }
                        break
                    case 'in_progress':
                        liveOnMsg.current({
                            type: 'card_brewards_updated',
                            reward: loadable.data.breward,
                        })
                        postUserEvent({
                            type: 'RewardsStateUpdated',
                            transactions: loadable.data.cardTransactions,
                            prevRewardState: liveCardConfig.current.rewards,
                            newRewardState: loadable.data.breward,
                            installationId,
                        })
                        break
                    case 'not_eligible':
                        break
                    default:
                        notReachable(loadable.data.breward)
                }
                break
            case 'reloading':
                break
            case 'subsequent_failed':
                captureError(loadable.error)
                break
            default:
                notReachable(loadable)
        }
    }, [liveCardConfig, liveOnMsg, loadable, installationId])

    switch (loadable.data.breward.type) {
        case 'not_eligible':
        case 'claimed':
            return null
        case 'expired': {
            if (loadable.data.breward.dismissed) {
                return null
            }
            return (
                <Expired
                    onMsg={onMsg}
                    reward={loadable.data.breward}
                    installationCampaign={installationCampaign}
                />
            )
        }
        case 'in_progress':
            return (
                <InProgress
                    reward={loadable.data.breward}
                    installationCampaign={installationCampaign}
                />
            )
        case 'ready_to_claim':
            return (
                <ReadyToClaim
                    appRating={appRating}
                    referralConfig={referralConfig}
                    keyStore={keyStore}
                    onMsg={onMsg}
                    reward={loadable.data.breward}
                    takerPortfolioMap={takerPortfolioMap}
                    owner={owner}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    installationCampaign={installationCampaign}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(loadable.data.breward)
    }
}
