import { get } from '@zeal/api/gnosisApi'

import { notReachable } from '@zeal/toolkit'
import {
    decryptAESGCM,
    encryptRSAOAEP,
    getRandomIntArray,
} from '@zeal/toolkit/Crypto'
import { ImperativeError } from '@zeal/toolkit/Error'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { object, shape, string } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { loginWithCache } from '@zeal/domains/Card/api/login'
import { CountryISOCode } from '@zeal/domains/Country'
import { FiatCurrency } from '@zeal/domains/Currency'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'

import {
    Card,
    CardDetails,
    CardSlientSignKeyStore,
    GnosisPayAccountNotOnboardedStatePostKYCCardCreated<PERSON>rom<PERSON>rder,
    GnosisPayAccountOnboardedState,
} from '..'

const GNOSIS_PUBLIC_KEY = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCPGTymqjTrzIZOO9XwGPepvAYMNCCMIkNhSkSLzWF51LwSatILicxmUTuLkAra55Pttqmm92S+LCRwtmRjAOyb8FD1P0O7K3lvx716xEauf2BqjBW+9/tF0EI0V503qHCI9qcaQerlnckehkdQkPp98pzDzf5RT/X2VxA1tdz+NwIDAQAB`
const GNOSIS_PUBLIC_KEY_GB =
    'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCghtYE8mLq8ebewtBHp5JXErXSGd2iic+XBU9H0j3VpBCw20HATbsHF8uUrqITHC0cNa7+UU8IeDXAcksOsuh3BdIwuwtGMqLAgsKfIYlRpgQ9NeOXqac6mCS87RkNJDDNWSJddUbL9ndDoRGekmcBahopCdyXErblhtOnGqK1EQIDAQAB'

type GnosisPayPublicKey = { type: 'generic_key' | 'gb_key'; key: string }

const PIN_REGEXP = /24(\d{4})FFFFFFFFFF/i

const COUNTRIES_TO_USE_GB_KEY: CountryISOCode[] = ['GB', 'CH', 'BR']

const selectPublicKeyByCountyAndCurrency = ({
    fiatCurrency,
    country,
}: {
    fiatCurrency: FiatCurrency | null
    country: CountryISOCode | null
}): GnosisPayPublicKey => {
    if (fiatCurrency && fiatCurrency.id === FIAT_CURRENCIES.GBP.id) {
        return { type: 'gb_key', key: GNOSIS_PUBLIC_KEY_GB }
    }
    if (country && COUNTRIES_TO_USE_GB_KEY.includes(country)) {
        return { type: 'gb_key', key: GNOSIS_PUBLIC_KEY_GB }
    }

    return { type: 'generic_key', key: GNOSIS_PUBLIC_KEY }
}

const selectPublicKey = ({
    gnosisPayAccountOnboardedState,
}: {
    gnosisPayAccountOnboardedState:
        | GnosisPayAccountOnboardedState
        | GnosisPayAccountNotOnboardedStatePostKYCCardCreatedFromOrder
}): GnosisPayPublicKey => {
    switch (gnosisPayAccountOnboardedState.type) {
        case 'onboarded':
            return selectPublicKeyByCountyAndCurrency({
                fiatCurrency:
                    gnosisPayAccountOnboardedState.cardSafe.fiatCurrency,
                country:
                    gnosisPayAccountOnboardedState.residentialAddress
                        ?.country || null,
            })

        case 'not_onboarded':
            switch (gnosisPayAccountOnboardedState.state.cardSafe.type) {
                case 'not_deployed':
                case 'deployed':
                case 'currency_configured':
                    return selectPublicKeyByCountyAndCurrency({
                        fiatCurrency: null,
                        country: gnosisPayAccountOnboardedState.state.country,
                    })
                case 'fully_configured':
                    return selectPublicKeyByCountyAndCurrency({
                        fiatCurrency:
                            gnosisPayAccountOnboardedState.state.cardSafe
                                .fiatCurrency,
                        country: gnosisPayAccountOnboardedState.state.country,
                    })

                /* istanbul ignore next */
                default:
                    return notReachable(
                        gnosisPayAccountOnboardedState.state.cardSafe
                    )
            }

        /* istanbul ignore next */
        default:
            return notReachable(gnosisPayAccountOnboardedState)
    }
}

export const fetchCardPin = async ({
    gnosisPayAccountOnboardedState,
    readonlySignerAddress,
    sessionPassword,
    keyStore,
    signal,
}: {
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    readonlySignerAddress: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    signal?: AbortSignal
}): Promise<string> => {
    const fetchKey = Buffer.from(
        getRandomIntArray(new Uint8Array(32))
    ).toString('base64')
    const encryptedPinResponse = await fetchPinWithRetry({
        fetchKey,
        gnosisPayAccountOnboardedState,
        readonlySignerAddress,
        sessionPassword,
        keyStore,
        signal,
    })

    const encryptedPinObj = string(encryptedPinResponse)
        .andThen(parseJSON)
        .andThen(object)
        .andThen((obj) =>
            shape({
                encryptedPin: string(obj.encryptedPin),
                iv: string(obj.iv),
            })
        )
        .getSuccessResultOrThrow('Failed to parse encrypted card details')

    try {
        const rawPinString = await decryptAESGCM({
            cipherBase64: encryptedPinObj.encryptedPin,
            keyBase64: fetchKey,
            ivBase64: encryptedPinObj.iv,
        })

        const rawPinExtracted = rawPinString.match(PIN_REGEXP)?.[1]

        if (!rawPinExtracted) {
            throw new ImperativeError('Failed to decrypt card pin', {
                cardId: gnosisPayAccountOnboardedState.selectedCard.id,
            })
        }

        return rawPinExtracted
    } catch {
        throw new ImperativeError('Failed to decrypt card pin', {
            cardId: gnosisPayAccountOnboardedState.selectedCard.id,
        })
    }
}

export const fetchCardDetails = async ({
    gnosisPayAccountOnboardedState,
    card,
    readonlySignerAddress,
    sessionPassword,
    keyStore,
    signal,
}: {
    gnosisPayAccountOnboardedState:
        | GnosisPayAccountOnboardedState
        | GnosisPayAccountNotOnboardedStatePostKYCCardCreatedFromOrder
    card: Card
    readonlySignerAddress: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    signal?: AbortSignal
}): Promise<CardDetails> => {
    const fetchKey = Buffer.from(
        getRandomIntArray(new Uint8Array(32))
    ).toString('base64')

    const encryptedCardDetailsResponse = await fetchCardDetailsWithRetry({
        gnosisPayAccountOnboardedState,
        card,
        readonlySignerAddress,
        sessionPassword,
        keyStore,
        fetchKey,
        signal,
    })

    const encryptedCardDetails = string(encryptedCardDetailsResponse)
        .andThen(parseJSON)
        .andThen(object)
        .andThen((obj) =>
            shape({
                cvv: string(obj.cvv),
                pan: string(obj.secret),
                iv: string(obj.iv),
                exp_date: string(obj.exp_date),
            })
        )
        .getSuccessResultOrThrow('Failed to parse encrypted card details')

    try {
        return {
            cvv: await decryptAESGCM({
                cipherBase64: encryptedCardDetails.cvv,
                keyBase64: fetchKey,
                ivBase64: encryptedCardDetails.iv,
            }),
            pan: await decryptAESGCM({
                cipherBase64: encryptedCardDetails.pan,
                keyBase64: fetchKey,
                ivBase64: encryptedCardDetails.iv,
            }),
            expiryYear: encryptedCardDetails.exp_date.slice(0, 2),
            expiryMonth: encryptedCardDetails.exp_date.slice(2),
        }
    } catch {
        // Swallow original error to not accidentally leak sensitive data
        throw new ImperativeError('Failed to decrypt card details', {
            cardId: card.id,
        })
    }
}

const fetchPinWithRetry = async ({
    gnosisPayAccountOnboardedState,
    fetchKey,
    keyStore,
    sessionPassword,
    readonlySignerAddress,
    signal,
}: {
    fetchKey: string
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    readonlySignerAddress: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    signal?: AbortSignal
}): Promise<unknown> => {
    const publicKeyBase64 = selectPublicKey({ gnosisPayAccountOnboardedState })

    const encryptedKey = await encryptRSAOAEP({
        dataBase64: fetchKey,
        publicKeyBase64: publicKeyBase64.key,
    })

    const gnosisPayLoginInfo = await loginWithCache({
        signal,
        keyStore,
        readonlySignerAddress,
        sessionPassword,
    })

    try {
        return await get(
            `/cards/${gnosisPayAccountOnboardedState.selectedCard.id}/pin`,
            {
                query: {
                    encryptedKey,
                },
                auth: {
                    type: 'bearer_token',
                    token: gnosisPayLoginInfo.token,
                },
            },
            signal
        )
    } catch (error) {
        const newPublicKey = (() => {
            switch (publicKeyBase64.type) {
                case 'generic_key':
                    return { type: 'gb_key', key: GNOSIS_PUBLIC_KEY_GB }
                case 'gb_key':
                    return {
                        type: 'generic_key',
                        key: GNOSIS_PUBLIC_KEY,
                    }
                /* istanbul ignore next */
                default:
                    return notReachable(publicKeyBase64.type)
            }
        })()

        const newEncryptedKey = await encryptRSAOAEP({
            dataBase64: fetchKey,
            publicKeyBase64: newPublicKey.key,
        })

        return await get(
            `/cards/${gnosisPayAccountOnboardedState.selectedCard.id}/pin`,
            {
                query: {
                    encryptedKey: newEncryptedKey,
                },
                auth: {
                    type: 'bearer_token',
                    token: gnosisPayLoginInfo.token,
                },
            },
            signal
        )
    }
}

const fetchCardDetailsWithRetry = async ({
    fetchKey,
    card,
    keyStore,
    sessionPassword,
    readonlySignerAddress,
    gnosisPayAccountOnboardedState,
    signal,
}: {
    fetchKey: string
    card: Card
    gnosisPayAccountOnboardedState:
        | GnosisPayAccountOnboardedState
        | GnosisPayAccountNotOnboardedStatePostKYCCardCreatedFromOrder
    readonlySignerAddress: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    signal?: AbortSignal
}): Promise<unknown> => {
    const publicKeyBase64 = selectPublicKey({ gnosisPayAccountOnboardedState })

    const encryptedKey = await encryptRSAOAEP({
        dataBase64: fetchKey,
        publicKeyBase64: publicKeyBase64.key,
    })

    const gnosisPayLoginInfo = await loginWithCache({
        signal,
        keyStore,
        readonlySignerAddress,
        sessionPassword,
    })

    try {
        return await get(
            `/cards/${card.id}/details`,
            {
                query: {
                    encryptedKey,
                },
                auth: {
                    type: 'bearer_token',
                    token: gnosisPayLoginInfo.token,
                },
            },
            signal
        )
    } catch (error) {
        const parsed = parseAppError(error)

        switch (parsed.type) {
            case 'gnosis_pay_public_key_not_matching_error':
                const newPublicKey = (() => {
                    switch (publicKeyBase64.type) {
                        case 'generic_key':
                            return { type: 'gb_key', key: GNOSIS_PUBLIC_KEY_GB }
                        case 'gb_key':
                            return {
                                type: 'generic_key',
                                key: GNOSIS_PUBLIC_KEY,
                            }
                        /* istanbul ignore next */
                        default:
                            return notReachable(publicKeyBase64.type)
                    }
                })()

                const newEncryptedKey = await encryptRSAOAEP({
                    dataBase64: fetchKey,
                    publicKeyBase64: newPublicKey.key,
                })

                return await get(
                    `/cards/${card.id}/details`,
                    {
                        query: {
                            encryptedKey: newEncryptedKey,
                        },
                        auth: {
                            type: 'bearer_token',
                            token: gnosisPayLoginInfo.token,
                        },
                    },
                    signal
                )
            default:
                throw error
        }
    }
}
