import { post } from '@zeal/api/gnosisApi'

import { parse as parseJSO<PERSON> } from '@zeal/toolkit/JSON'
import { match, object, shape, string } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { loginWithCache } from '@zeal/domains/Card/api/login'

import { CardSlientSignKeyStore, ResidentialAddress } from '..'

export const createPhysicalCardOrder = async ({
    readonlySignerAddress,
    sessionPassword,
    keyStore,
    shippingAddress,
    signal,
}: {
    readonlySignerAddress: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    shippingAddress: ResidentialAddress
    signal?: AbortSignal
}): Promise<{ id: string }> => {
    const gnosisLoginInfo = await loginWithCache({
        sessionPassword,
        keyStore,
        readonlySignerAddress,
        signal,
    })

    const response = await post(
        '/order/create',
        {
            auth: { type: 'bearer_token', token: gnosisLoginInfo.token },
            query: undefined,
            body: {
                personalizationSource: 'KYC',
                virtual: false,
                shippingAddress,
            },
        },
        signal
    )

    return string(response)
        .andThen(parseJSON)
        .andThen(object)
        .andThen((obj) =>
            shape({
                id: string(obj.id),
                virtual: match(obj.virtual, false),
                personalizationSource: match(obj.personalizationSource, 'KYC'),
            })
        )
        .map(({ id }) => {
            return { id }
        })
        .getSuccessResultOrThrow(
            'Failed to parse createPhysicalCardOrder creation response'
        )
}
