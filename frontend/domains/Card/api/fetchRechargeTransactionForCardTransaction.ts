import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { ImperativeError } from '@zeal/toolkit/Error'
import { keys, values } from '@zeal/toolkit/Object'

import { CurrencyId } from '@zeal/domains/Currency'
import {
    HistoricalTakerUserCurrencyRateMap,
    TakerType,
} from '@zeal/domains/Earn'
import { fetchHistoricalTakerUserCurrencyRatesWithCashe } from '@zeal/domains/Earn/api/fetchTakerTransactions'
import { EARN_PRIMARY_INVESTMENT_ASSETS_MAP } from '@zeal/domains/Earn/constants'
import { getHolderPredictedAddress } from '@zeal/domains/Earn/helpers/getHolderPredictedAddress'
import { getTakerPredictedAddress } from '@zeal/domains/Earn/helpers/getTakerPredictedAddress'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney, Money2 } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { GNOSIS } from '@zeal/domains/Network/constants'
import { ERC20TransferLog } from '@zeal/domains/RPCRequest'
import { fetchIndexedTransactions } from '@zeal/domains/Transactions/api/fetchIndexedTransactions'

import { CardTransaction, ReadonlySignerSelectedOnboardedCardConfig } from '..'

type Params = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    cardTransaction: CardTransaction

    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap

    signal?: AbortSignal
}

type IndexedRecharge = {
    hash: string
    blockNumber: bigint
    amountInvestmentCurrency: bigint // TODO @resetko-zeal typo
    investmentCurrencyId: CurrencyId
    amountInCardCurrency: CryptoMoney
}

type RechargeTransaction = {
    hash: string
    takerType: TakerType
    amountInTakerUserCurrency: Money2
    amountInCardCurrency: CryptoMoney
}

const RECHARGE_LOOKUP_TIME_MS = 2 * 60 * 1000

export const fetchLastRechargeTransaction = async ({
    cardTransaction,
    cardConfig,
    historicalTakerUserCurrencyRateMap,
    networkMap,
    networkRPCMap,
    signal,
}: Params): Promise<null | RechargeTransaction> => {
    const trxTimetamp = cardTransaction.createdAt

    const holder = getHolderPredictedAddress({
        earnOwnerAddress: cardConfig.readonlySignerAddress,
    })
    const takerAddresses = new Set(
        keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP).map((takerType) =>
            getTakerPredictedAddress({ holder, takerType })
        )
    )

    const indexedTransactions = await fetchIndexedTransactions({
        address: cardConfig.lastSeenSafeAddress,
        networkHexId: GNOSIS.hexChainId,
        afterTimestampMs: trxTimetamp,
        beforeTimestampMs: trxTimetamp + RECHARGE_LOOKUP_TIME_MS,
        logsForAddresses: Array.from(takerAddresses),
        signal,
    })

    const parsedTx: IndexedRecharge[] = indexedTransactions
        .map((tx) => {
            const erc20Logs = tx.logs.filter((log): log is ERC20TransferLog => {
                switch (log.type) {
                    case 'unknown':
                    case 'added_owner':
                    case 'approval':
                    case 'account_deployed':
                    case 'threshold_updated':
                    case 'set_allowance':
                    case 'enable_module':
                    case 'disable_module':
                    case 'safe_module_transaction':
                    case 'safe_received':
                    case 'user_operation_event':
                    case 'user_operation_revert_reason':
                        return false
                    case 'erc20_transfer':
                        return true
                    /* istanbul ignore next */
                    default:
                        return notReachable(log)
                }
            })

            const erc20LogToCardWithMatchingAmount =
                erc20Logs.find(
                    (log) =>
                        log.to === cardConfig.lastSeenSafeAddress &&
                        log.amount === cardTransaction.billingAmount?.amount &&
                        log.currencyId === cardConfig.currency.id
                ) || null

            const erc20LogFromTaker =
                erc20Logs.find((log) => takerAddresses.has(log.from)) || null

            return erc20LogFromTaker && erc20LogToCardWithMatchingAmount
                ? {
                      hash: tx.hash,
                      blockNumber: tx.blockNumber,
                      amountInvestmentCurrency: erc20LogFromTaker.amount,
                      investmentCurrencyId: erc20LogFromTaker.currencyId,
                      amountInCardCurrency: {
                          amount: erc20LogToCardWithMatchingAmount.amount,
                          currency: cardConfig.currency,
                      },
                  }
                : null
        })
        .filter(excludeNullValues)

    switch (parsedTx.length) {
        case 0:
            return null

        case 1: {
            const recharge = parsedTx[0]
            const blockNumber = Number(recharge.blockNumber)

            const takerType =
                keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP).find(
                    (takerType) =>
                        EARN_PRIMARY_INVESTMENT_ASSETS_MAP[takerType].id ===
                        recharge.investmentCurrencyId
                ) || null

            if (!takerType) {
                throw new ImperativeError(
                    'Failed to find taker type for recharge',
                    { recharge }
                )
            }

            const rates = await fetchHistoricalTakerUserCurrencyRatesWithCashe({
                takerType,
                blocks: [blockNumber],
                cache: historicalTakerUserCurrencyRateMap,
                networkRPCMap,
                networkMap,
                signal,
            })

            const investmentCurrency =
                values(EARN_PRIMARY_INVESTMENT_ASSETS_MAP).find(
                    (currency) => currency.id === recharge.investmentCurrencyId
                ) || null

            if (!investmentCurrency) {
                throw new ImperativeError(
                    'Failed to fetch investment currency',
                    { investmentCurrencyId: recharge.investmentCurrencyId }
                )
            }

            const investmentToUserCurrencyRate = rates[blockNumber]

            return {
                hash: recharge.hash,
                takerType,
                amountInTakerUserCurrency: applyRate2({
                    baseAmount: {
                        amount: recharge.amountInvestmentCurrency,
                        currency: investmentCurrency,
                    },
                    rate: investmentToUserCurrencyRate,
                }),
                amountInCardCurrency: recharge.amountInCardCurrency,
            }
        }

        default:
            captureError(
                new ImperativeError('Multiple recharge transactions found', {
                    parsedTxHashes: parsedTx.map((tx) => tx.hash),
                    billingAmount: cardTransaction.billingAmount,
                })
            )
            return null
    }
}
