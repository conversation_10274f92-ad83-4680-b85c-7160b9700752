import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import { Result } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { CardCashback } from '@zeal/domains/Card/domains/Cashback'
import { BReward } from '@zeal/domains/Card/domains/Reward'
import { CountryISOCode } from '@zeal/domains/Country'
import {
    CryptoCurrency,
    FiatCurrency,
    PriceChange24H,
} from '@zeal/domains/Currency'
import { SwapRoute } from '@zeal/domains/Currency/domains/SwapQuote'
import { DeployedTaker, TakerApyMap, TakerType } from '@zeal/domains/Earn'
import { FXRate2 } from '@zeal/domains/FXRate'
import { PrivateKey, Safe4337, SecretPhrase } from '@zeal/domains/KeyStore'
import { CryptoMoney, FiatMoney, Money2 } from '@zeal/domains/Money'
import { Network } from '@zeal/domains/Network'
import {
    EthSendTransaction,
    EthSignTypedDataV4,
} from '@zeal/domains/RPCRequest'
import { MetaTransactionData } from '@zeal/domains/UserOperation'

export * from './MerchantCategory'

export type CardConfig =
    | ReadonlySignerIsNotSelectedCardConfig
    | ReadonlySignerSelectedCardConfig
    | ReadonlySignerSelectedOnboardedCardConfig

export type ReadonlySignerIsNotSelectedCardConfig = {
    type: 'card_readonly_signer_address_is_not_selected'
}

export type ReadonlySignerSelectedCardConfig = {
    type: 'card_readonly_signer_address_is_selected'
    readonlySignerAddress: Web3.address.Address
    lastDismissedOnboardingBannerState:
        | GnosisPayAccountNotOnboardedState2['state']['type']
        | null
    rechargePreferences: RechargePreferences | null
}

export type RechargePreferences = {
    taker: TakerType
    threshold: bigint
}

export type ReadonlySignerSelectedOnboardedCardConfig = {
    type: 'card_readonly_signer_address_is_selected_fully_onboarded'
    readonlySignerAddress: Web3.address.Address
    currency: CryptoCurrency

    selectedCardId: string | null
    lastSeenSafeAddress: Web3.address.Address
    cardTransactionsCache: CardTransaction[] | null
    lastRechargeTransactionHash: string | null
    cashback: CardCashback
    dissmissedAddToWalletBanner: boolean
    lastDismissedKycBannerState: GnosisPayOnboardedKycStatus | null

    isCreatedViaZeal: boolean
    country: CountryISOCode | null
    rewards: BReward
    userId: string | null
}

export type CardSlientSignKeyStore = PrivateKey | SecretPhrase | Safe4337

export type GnosisPayLoginSignature = {
    type: 'gnosis_pay_login_info'
    address: Web3.address.Address
    message: string
    signature: string
}

export type GnosisPayLoginInfo = {
    type: 'gnosis_pay_login_info'
    token: string // only token so far
    expiresAtMs: number
}

export type MerchantInfo = {
    name: string
    mcc: number
    country: string
    city: string
}

export type CardTransactionState =
    | { type: 'pending' }
    | { type: 'settled'; clearedAt: number }

type CardTransactionCommon = {
    createdAt: number
    transactionAmount: FiatMoney | null // TODO @resetko-zeal make it not nullable once we have fiat currency dictionary in the app
    billingAmount: CryptoMoney
    merchant: MerchantInfo
    state: CardTransactionState
}

export type Reversal = {
    kind: 'Reversal'
    onChainTransactionHash: string
} & CardTransactionCommon

export type CardPayment = {
    kind: 'Payment'
} & (
    | {
          status: 'Approved' | 'Reversal'
          onChainTransactionHash: string
      }
    | {
          status: 'Declined'
          reason: DeclineReason
      }
) &
    CardTransactionCommon

export type DeclineReason =
    | 'InsufficientFunds'
    | 'IncorrectPin'
    | 'InvalidAmount'
    | 'PinEntryTriesExceeded'
    | 'IncorrectSecurityCode'
    | 'Other'

export type Refund = {
    kind: 'Refund'
} & CardTransactionCommon

export type UnknownTransaction = {
    kind: 'Unknown'
    transactionAmount: FiatMoney | null
    billingAmount: CryptoMoney | null
    createdAt: number
    originalTransaction: unknown
}

export type CardTransaction =
    | CardPayment
    | Reversal
    | Refund
    | UnknownTransaction

export type CardSafeNotDeployed = { type: 'not_deployed' }
export type CardSafeDeployed = {
    type: 'deployed'
    address: Web3.address.Address
}

export type CardSafeCurrencyConfigured = {
    type: 'currency_configured'
    address: Web3.address.Address
    fiatCurrency: FiatCurrency
    cryptoCurrency: CryptoCurrency
}

export type CardSafeFullyConfigured = {
    type: 'fully_configured'
    address: Web3.address.Address
    fiatCurrency: FiatCurrency
    cryptoCurrency: CryptoCurrency
}

export type GnosisTermsType =
    | 'general-tos'
    | 'card-monavate-tos'
    | 'cashback-tos'

export type CardTermsType =
    | 'general_terms'
    | 'monavate_terms'
    | 'cashback_terms'

export type CardTermsRequiredForSignupType = Extract<
    CardTermsType,
    'general_terms' | 'monavate_terms'
>

type TermsCommon = { url: string; currentVersion: string; accepted: boolean }

export type CardTerms = {
    type: CardTermsType
} & TermsCommon

export type CardTermsRequiredForSignup = {
    type: CardTermsRequiredForSignupType
} & TermsCommon

export type CardSafeState =
    | CardSafeNotDeployed
    | CardSafeDeployed
    | CardSafeCurrencyConfigured
    | CardSafeFullyConfigured

export type GnosisPayPreKYCApprovedState =
    | {
          type: 'terms_not_accepted'
          missingSignupTerms: CardTermsRequiredForSignup[]
          cardSafe: CardSafeState // FIXME :: @Nicvaniek Should be CardSafeNotDeployed | CardSafeDeployed but need to support all states until re-kyc is done
      }
    | {
          type:
              | 'terms_accepted_kyc_not_started'
              | 'kyc_started_documents_requested'
              | 'kyc_started_verification_in_progress'
              | 'kyc_started_resubmission_requested'
              | 'kyc_failed'
          cardSafe: CardSafeState // FIXME :: @Nicvaniek Should be CardSafeNotDeployed | CardSafeDeployed but need to support all states until re-kyc is done
      }

export type GnosisPayKYCApprovedState = {
    type: 'kyc_approved'
    cardSafe: CardSafeState
    hasVerifiedPhoneNumber: boolean
    hasAnsweredSourceOfFunds: boolean
    residentialAddress: ResidentialAddress | null
}
type GnosisPayPostKYCApprovedStateCardOrderPendingPayment = {
    type: 'card_order_pending_payment'
    cardSafe: CardSafeState
}
type GnosisPayPostKYCApprovedStateCardOrderReady = {
    type: 'card_order_ready'
    cardSafe: CardSafeState // CardSafeNotDeployed, CardSafeDeployed, and CardSafeCurrencyConfigured are still valid types for users who come from the Gnosis portal and start card ordering but don't finish it yet.
}
export type GnosisPayPostKYCApprovedState =
    | GnosisPayPostKYCApprovedStateCardOrderReady
    | GnosisPayPostKYCApprovedStateCardOrderPendingPayment
    | GnosisPayPostKYCApprovedStateCardCreatedFromOrder

export type GnosisPayPostKYCApprovedStateCardCreatedFromOrder = {
    type: 'card_created_from_order'
    cardSafe: CardSafeState // CardSafeNotDeployed, CardSafeDeployed, and CardSafeCurrencyConfigured are still valid types for users who come from the Gnosis portal and start card ordering but don't finish it yet.
    country: CountryISOCode | null
    notActivatedPhysicalCards: NonEmptyArray<NotActivatedPhysicalCard>
}

export type GnosisPayAccountNotOnboardedState2 =
    | {
          type: 'not_onboarded'
          state:
              | GnosisPayPreKYCApprovedState
              | GnosisPayPostKYCApprovedStateCardOrderPendingPayment
              | GnosisPayPostKYCApprovedStateCardOrderReady
      }
    | GnosisPayAccountNotOnboardedStatePostKYCCardCreatedFromOrder
    | GnosisPayAccountNotOnboardedStateGnosisPayKYCApprovedState

export type GnosisPayAccountNotOnboardedStateGnosisPayKYCApprovedState = {
    type: 'not_onboarded'
    state: GnosisPayKYCApprovedState
}

export type GnosisPayAccountNotOnboardedStatePostKYCCardCreatedFromOrder = {
    type: 'not_onboarded'
    state: GnosisPayPostKYCApprovedStateCardCreatedFromOrder
}

export type CardBalance = {
    total: FiatMoney
    totalInDefaultCurrency: FiatMoney | null

    spendable: FiatMoney
    pending: FiatMoney

    cashbackTokenBalance: CryptoMoney
    cashbackTokenBalanceInDefaultCurrency: FiatMoney | null
    cashBackRate: FXRate2<CryptoCurrency, FiatCurrency> | null
    cashBackPriceChange24H: PriceChange24H | null
}
type PhysicalCardType = 'physical'
type VirtualCardType = 'virtual'

export type CardType = PhysicalCardType | 'virtual'

export type CardState = NotActivatedCardState | ActivatedCardState

export type ActivatedCardState = { type: 'card_activated'; status: CardStatus }

export type NotActivatedCardState = { type: 'card_not_activated' }

export type Card = ActivatedCard | NotActivatedPhysicalCard

export type ActivatedCard = ActivatedVirtualCard | ActivatedPhysicalCard

type ActivatedVirtualCard = {
    id: string
    type: VirtualCardType
    state: ActivatedCardState
    lastFourDigits: string
}

export type ActivatedPhysicalCard = {
    id: string
    type: PhysicalCardType
    state: ActivatedCardState
    lastFourDigits: string
}

export type NotActivatedPhysicalCard = {
    id: string
    type: PhysicalCardType
    lastFourDigits: string
    state: NotActivatedCardState
}

export type GnosisPayAccountOnboardedStateResult = Result<
    | { type: 'keystore_not_eligible_for_silent_sign' }
    | { type: 'gnosisPayAccountState_not_in_onboarded_state' },
    {
        gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
        keyStore: CardSlientSignKeyStore
    }
>

export type GnosisPayOnboardedKycStatus =
    | 'not_started'
    | 'documents_requested'
    | 'verification_in_progress'
    | 'resubmission_requested'
    | 'failed'
    | 'approved'

export type GnosisPayAccountOnboardedState = {
    type: 'onboarded'
    selectedCard: ActivatedCard
    cardSafe: CardSafeFullyConfigured
    balance: CardBalance
    residentialAddress: ResidentialAddress | null
    // TODO :: @kate - there should be ActiveCard type instead of union, ask gnosis-pay team to provide it also array should be notNullableArray
    // https://zeal-hzn3129.slack.com/archives/C06R39S06J1/p1750952253099369
    activatedCards: {
        id: string
        type: 'virtual' | 'physical'
        activatedAt: string
        lastFourDigits: string
    }[]
    notActivatedPhysicalCards: NotActivatedPhysicalCard[]
    isCreatedViaZeal: boolean
    userId: string
    acceptedCashbackTerms: boolean
    fullName: string | null
    kycStatus: GnosisPayOnboardedKycStatus
}

export type CardDetails = {
    cvv: string
    pan: string
    expiryYear: string
    expiryMonth: string
}

export type CardsMap = Record<string, ActivatedCard | null>

export type CardStatus = Frozen | Active | Cancelled

export type Frozen = 'frozen'
export type Active = 'active'
export type Cancelled = 'cancelled'

export type GnosisPayAccountState2 =
    | GnosisPayAccountNotOnboardedState2
    | GnosisPayAccountOnboardedState

export type CardTopUpRequest = {
    sender: Account
    amount: CryptoMoney
    cardSafeAddress: Web3.address.Address
    card: ActivatedCard | null
}

export type TopUpCardFromEarnRequest = {
    earnOwner: Account
    taker: DeployedTaker
    takerApyMap: TakerApyMap
    investmentAssetAmount: CryptoMoney
    userCurrencyAmount: Money2
    defaultCurrencyAmount: FiatMoney | null
    card: ActivatedCard | null
    earnWithdrawalTransaction: EthSendTransaction
    swapRoute: SwapRoute
}

export type CardDelayRelayTransactionRequest = {
    cardSafe: Web3.address.Address
    network: Network

    transaction: MetaTransactionData
    relayTransaction: MetaTransactionData
    relayMessage: EthSignTypedDataV4
    relaySalt: Hexadecimal.Hexadecimal
    signature: Hexadecimal.Hexadecimal
}

export type ResidentialAddress = {
    address1: string
    address2: string | null
    city: string
    postalCode: string
    country: CountryISOCode
}
