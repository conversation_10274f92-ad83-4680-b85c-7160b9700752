import { useIntl } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { ThreeDotVertical } from '@zeal/uikit/Icon/ThreeDotVertical'
import { IconButton } from '@zeal/uikit/IconButton'
import { ListItem as UIListItem } from '@zeal/uikit/ListItem'

import { Account } from '@zeal/domains/Account'
import { CopyAddress } from '@zeal/domains/Address/components/CopyAddress'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStore } from '@zeal/domains/KeyStore'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { sumPortfolio2 } from '@zeal/domains/Portfolio/helpers/sum'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { UserEvent } from '@zeal/domains/UserEvents'

import { Avatar } from '../Avatar'

type Props = {
    installationId: string
    account: Account
    selected: boolean
    keystore: KeyStore
    portfolio: Portfolio2 | null // can be not loaded yet
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    copyAddressEventLocation: Extract<
        UserEvent,
        { type: 'CopyAddress' }
    >['location']
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'account_item_clicked'; account: Account }
    | { type: 'account_details_clicked'; account: Account }

export const ListItemWithDetails = ({
    account,
    keystore,
    portfolio,
    selected,
    currencyHiddenMap,
    installationId,
    defaultCurrencyConfig,
    copyAddressEventLocation,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    const sum =
        portfolio &&
        sumPortfolio2({ portfolio, currencyHiddenMap, defaultCurrencyConfig })

    return (
        <UIListItem
            size="regular"
            onClick={() => {
                onMsg({
                    type: 'account_item_clicked',
                    account,
                })
            }}
            avatar={({ size }) => (
                <Avatar account={account} keystore={keystore} size={size} />
            )}
            primaryText={account.label}
            shortText={
                sum ? (
                    <FormattedMoneyCompact money={sum} />
                ) : (
                    '\u00A0' // \u00A0
                )
            }
            aria-current={selected}
            side={{
                title: <></>,
                subtitle: (
                    <Column spacing={0} alignY="end">
                        <CopyAddress
                            location={copyAddressEventLocation}
                            installationId={installationId}
                            size="small"
                            color="on_light"
                            address={account.address}
                        />
                    </Column>
                ),
                rightIcon: ({ size }) => (
                    <IconButton
                        variant="on_light"
                        aria-label={formatMessage({
                            id: 'Account.ListItem.details.label',
                            defaultMessage: 'Details',
                        })}
                        onClick={(e) => {
                            e.stopPropagation()
                            onMsg({
                                type: 'account_details_clicked',
                                account,
                            })
                        }}
                    >
                        {({ color }) => (
                            <ThreeDotVertical size={size} color={color} />
                        )}
                    </IconButton>
                ),
            }}
        />
    )
}
