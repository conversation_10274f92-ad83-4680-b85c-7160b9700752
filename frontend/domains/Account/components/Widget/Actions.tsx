import { useIntl } from 'react-intl'

import { LightSetting } from '@zeal/uikit/Icon/LightSetting'
import { Refresh } from '@zeal/uikit/Icon/Refresh'
import { IconButton } from '@zeal/uikit/IconButton'
import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'
import { Row } from '@zeal/uikit/Row'
import { Spacer } from '@zeal/uikit/Spacer'
import { Spinner } from '@zeal/uikit/Spinner'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CopyAddressIconButton } from '@zeal/domains/Address/components/CopyAddressIconButton'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { WalletConnectInstanceLoadable } from '@zeal/domains/DApp/domains/WalletConnect/api/fetchWalletConnectInstance'
import { ScanButton } from '@zeal/domains/DApp/domains/WalletConnect/features/ScanButton'
import { IntercomButton } from '@zeal/domains/Intercom/features/IntercomButton'
import { KeyStore, KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { isFunded } from '@zeal/domains/Portfolio/helpers/IsFunded'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    currentAccount: Account
    walletConnectInstanceLoadable: WalletConnectInstanceLoadable
    installationId: string
    keystore: KeyStore
    networkMap: NetworkMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    isEthereumNetworkFeeWarningSeen: boolean
    refreshContainerState: RefreshContainerState
    portfolio: Portfolio2
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_settings_clicked' }
    | { type: 'on_refresh_button_clicked' }
    | MsgOf<typeof ScanButton>

export const Actions = ({
    onMsg,
    keystore,
    keyStoreMap,
    networkMap,
    accountsMap,
    currentAccount,
    walletConnectInstanceLoadable,
    isEthereumNetworkFeeWarningSeen,
    installationId,
    refreshContainerState,
    currencyHiddenMap,
    defaultCurrencyConfig,
    portfolio,
}: Props) => {
    const { formatMessage } = useIntl()

    const funded = isFunded({
        currencyHiddenMap,
        defaultCurrencyConfig,
        portfolio,
    })

    return (
        <Row spacing={0} shrink>
            <Spacer />

            {(() => {
                switch (ZealPlatform.OS) {
                    case 'web':
                        return (
                            <>
                                <CopyAddressIconButton
                                    size={24}
                                    variant="on_light_bold"
                                    address={
                                        currentAccount.address as Web3.address.Address
                                    }
                                    installationId={installationId}
                                    isEthereumNetworkFeeWarningSeen={
                                        isEthereumNetworkFeeWarningSeen
                                    }
                                    keyStore={keystore}
                                    onMsg={onMsg}
                                    location="portfolio"
                                />

                                <IconButton
                                    variant="on_light_bold"
                                    aria-label={formatMessage({
                                        id: 'account.widget.refresh',
                                        defaultMessage: 'Refresh',
                                    })}
                                    onClick={(e) => {
                                        e.stopPropagation()
                                        onMsg({
                                            type: 'on_refresh_button_clicked',
                                        })
                                    }}
                                >
                                    {({ color }) => {
                                        switch (refreshContainerState) {
                                            case 'refreshing':
                                                return (
                                                    <Spinner
                                                        variant="regular"
                                                        size={24}
                                                        color={color}
                                                    />
                                                )
                                            case 'refreshed':
                                                return (
                                                    <Refresh
                                                        size={24}
                                                        color={color}
                                                    />
                                                )
                                            default:
                                                return notReachable(
                                                    refreshContainerState
                                                )
                                        }
                                    }}
                                </IconButton>
                            </>
                        )
                    case 'ios':
                    case 'android':
                        return funded ? (
                            <>
                                <IntercomButton
                                    variant="icon_button"
                                    installationId={installationId}
                                    location="home"
                                />

                                <ScanButton
                                    currentAccount={currentAccount}
                                    walletConnectInstanceLoadable={
                                        walletConnectInstanceLoadable
                                    }
                                    isEthereumNetworkFeeWarningSeen={
                                        isEthereumNetworkFeeWarningSeen
                                    }
                                    installationId={installationId}
                                    networkMap={networkMap}
                                    accountsMap={accountsMap}
                                    keyStoreMap={keyStoreMap}
                                    onMsg={onMsg}
                                />
                            </>
                        ) : (
                            <IntercomButton
                                variant="secondary_button"
                                installationId={installationId}
                                location="home"
                            />
                        )

                    default:
                        notReachable(ZealPlatform)
                }
            })()}

            <IconButton
                variant="on_light_bold"
                aria-label={formatMessage({
                    id: 'account.widget.settings',
                    defaultMessage: 'Settings',
                })}
                onClick={(e) => {
                    e.stopPropagation()
                    onMsg({
                        type: 'on_settings_clicked',
                    })
                }}
            >
                {({ color }) => <LightSetting size={24} color={color} />}
            </IconButton>
        </Row>
    )
}
