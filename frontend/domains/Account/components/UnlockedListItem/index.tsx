import { Column } from '@zeal/uikit/Column'
import { NotSelected } from '@zeal/uikit/Icon/NotSelected'
import { Radio } from '@zeal/uikit/Icon/Radio'
import { ListItem } from '@zeal/uikit/ListItem'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'

import { Account } from '@zeal/domains/Account'
import { Avatar } from '@zeal/domains/Account/components/Avatar'
import { CopyAddress } from '@zeal/domains/Address/components/CopyAddress'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStore } from '@zeal/domains/KeyStore'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { sumPortfolio2 } from '@zeal/domains/Portfolio/helpers/sum'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    installationId: string
    account: Account
    selected: boolean
    selectionVariant: 'background_color' | 'radio'
    keyStore: KeyStore
    portfolio: Portfolio2 | null // can be not loaded yet
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg = { type: 'account_item_clicked'; account: Account }

// TODO @max-tern :: rename, this is confusing
export const UnlockedListItem = ({
    account,
    portfolio,
    selected,
    keyStore,
    selectionVariant,
    currencyHiddenMap,
    installationId,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const sum =
        portfolio &&
        sumPortfolio2({ portfolio, currencyHiddenMap, defaultCurrencyConfig })

    return (
        <>
            <ListItem
                size="large"
                onClick={() => {
                    onMsg({
                        type: 'account_item_clicked',
                        account,
                    })
                }}
                avatar={({ size }) => (
                    <Avatar account={account} keystore={keyStore} size={size} />
                )}
                primaryText={account.label}
                shortText={
                    <Text>{sum && <FormattedMoneyCompact money={sum} />}</Text>
                }
                aria-current={(() => {
                    switch (selectionVariant) {
                        case 'background_color':
                            return selected
                        case 'radio':
                            return false
                        /* istanbul ignore next */
                        default:
                            return notReachable(selectionVariant)
                    }
                })()}
                side={{
                    title: <></>,
                    subtitle: (
                        <Column spacing={0} alignY="end">
                            <CopyAddress
                                location="wallet_list"
                                installationId={installationId}
                                size="small"
                                color="on_light"
                                address={account.address}
                            />
                        </Column>
                    ),
                    ...(() => {
                        switch (selectionVariant) {
                            case 'background_color':
                                return {}
                            case 'radio':
                                return {
                                    rightIcon: ({ size }) =>
                                        selected ? (
                                            <Radio
                                                size={size}
                                                color="iconAccent2"
                                            />
                                        ) : (
                                            <NotSelected
                                                size={size}
                                                color="iconDefault"
                                            />
                                        ),
                                }
                            /* istanbul ignore next */
                            default:
                                return notReachable(selectionVariant)
                        }
                    })(),
                }}
            />
        </>
    )
}
