import { Chain } from '@zeal/uikit/Chain'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { ListInputButton } from '@zeal/uikit/ListItem'
import { Text } from '@zeal/uikit/Text'

import { Account } from '@zeal/domains/Account'
import { AvatarWithoutBadge } from '@zeal/domains/Account/components/Avatar'
import { format } from '@zeal/domains/Address/helpers/format'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { sumPortfolio2 } from '@zeal/domains/Portfolio/helpers/sum'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    account: Account
    portfolio: Portfolio2 | null
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onClick: () => void
}

export const InputButton = ({
    account,
    portfolio,
    currencyHiddenMap,
    defaultCurrencyConfig,
    onClick,
}: Props) => {
    const sum =
        portfolio &&
        sumPortfolio2({
            portfolio,
            currencyHiddenMap,
            defaultCurrencyConfig,
        })

    return (
        <ListInputButton
            onClick={onClick}
            avatar={({ size }) => (
                <AvatarWithoutBadge size={size} account={account} />
            )}
            primaryText={account.label}
            shortText={
                <Chain>
                    <Text>{format(account.address)}</Text>
                    {sum && (
                        <Text>
                            <FormattedMoneyCompact money={sum} />
                        </Text>
                    )}
                </Chain>
            }
            side={{
                rightIcon: ({ size }) => (
                    <LightArrowDown2 size={size} color="iconDefault" />
                ),
            }}
        />
    )
}
