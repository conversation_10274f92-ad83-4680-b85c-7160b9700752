import { Clickable } from '@zeal/uikit/Clickable'
import { Row } from '@zeal/uikit/Row'
import { Text, TextStyles } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { formattedMoneyPrecise } from '@zeal/domains/Money/helpers/formattedMoneyPrecise'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { sumPortfolio2 } from '@zeal/domains/Portfolio/helpers/sum'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    portfolio: Portfolio2
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg = { type: 'on_balance_clicked' }

const getTextVariant = (formattedBalance: string): TextStyles['variant'] => {
    const integerPartWithSymbol = formattedBalance.split('.')[0]
    const length = integerPartWithSymbol.replace(/,/g, '').length

    switch (ZealPlatform.OS) {
        case 'ios':
        case 'android':
            switch (true) {
                case length < 8:
                    return 'titleXL'
                case length === 8:
                    return 'largeTitle'
                default:
                    return 'title1'
            }
        case 'web':
            switch (true) {
                case length < 7:
                    return 'titleXL'
                case length === 7:
                    return 'largeTitle'
                case length < 10:
                    return 'title1'
                default:
                    return 'title2'
            }
        /* istanbul ignore next */
        default:
            return notReachable(ZealPlatform)
    }
}

export const ShowBalance = ({
    portfolio,
    currencyHiddenMap,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    if (!portfolio) {
        return <>0</>
    }

    const sum = sumPortfolio2({
        portfolio,
        currencyHiddenMap,
        defaultCurrencyConfig,
    })

    const formatted = formattedMoneyPrecise({
        money: sum,
        sign: null,
        withSymbol: true,
    })

    const textVariant = getTextVariant(formatted)

    const [integerAndSymbol, decimals] = formatted.split('.')

    return (
        <Clickable
            onClick={() => {
                onMsg({ type: 'on_balance_clicked' })
            }}
        >
            <Row spacing={0} alignY="baseline" alignX="start" shrink>
                <Text variant={textVariant} weight="semi_bold" color="gray5">
                    {integerAndSymbol}
                </Text>
                {decimals && (
                    <Text color="gray30" weight="medium" variant="title3">
                        {`.${decimals}`}
                    </Text>
                )}
            </Row>
        </Clickable>
    )
}
