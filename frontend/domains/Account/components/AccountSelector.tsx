import { FormattedMessage } from 'react-intl'

import { EmptyStateWidget } from '@zeal/uikit/EmptyStateWidget'
import { GroupList } from '@zeal/uikit/GroupList'
import { AddressBook } from '@zeal/uikit/Icon/AddressBook'
import { ForwardIcon } from '@zeal/uikit/Icon/ForwardIcon'
import { ListItem } from '@zeal/uikit/ListItem'
import { Text } from '@zeal/uikit/Text'

import { AvatarWithoutBadge } from '@zeal/domains/Account/components/Avatar'
import { sortByBalance } from '@zeal/domains/Account/helpers/sortByBalance'
import { CopyAddress } from '@zeal/domains/Address/components/CopyAddress'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { sumPortfolio2 } from '@zeal/domains/Portfolio/helpers/sum'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Account } from '..'

type Props = {
    accounts: Account[]
    portfolioMap: PortfolioMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    selectedAccount: Account | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_account_selected'; account: Account }

// TODO:: @Nicvaniek add search and grouping based on active/tracked
export const AccountSelector = ({
    selectedAccount,
    accounts,
    onMsg,
    installationId,
    currencyHiddenMap,
    portfolioMap,
    defaultCurrencyConfig,
}: Props) => {
    return (
        <GroupList
            variant="default"
            renderItem={({ item: account }) => {
                const portfolio = unsafe_GetPortfolioCache2({
                    address: account.address,
                    portfolioMap,
                })
                const sum =
                    portfolio &&
                    sumPortfolio2({
                        portfolio,
                        currencyHiddenMap,
                        defaultCurrencyConfig,
                    })
                return (
                    <ListItem
                        size="large"
                        onClick={() =>
                            onMsg({
                                type: 'on_account_selected',
                                account,
                            })
                        }
                        avatar={({ size }) => (
                            <AvatarWithoutBadge account={account} size={size} />
                        )}
                        primaryText={account.label}
                        shortText={
                            <Text>
                                {sum && <FormattedMoneyCompact money={sum} />}
                            </Text>
                        }
                        aria-current={
                            selectedAccount?.address === account.address
                        }
                        side={{
                            title: <></>,
                            subtitle: (
                                <CopyAddress
                                    location="wallet_list"
                                    installationId={installationId}
                                    size="small"
                                    color="on_light"
                                    address={account.address}
                                />
                            ),
                            rightIcon: ({ size }) => (
                                <ForwardIcon size={size} color="iconDefault" />
                            ),
                        }}
                    />
                )
            }}
            data={accounts.toSorted(
                sortByBalance(
                    portfolioMap,
                    currencyHiddenMap,
                    defaultCurrencyConfig
                )
            )}
            emptyState={
                <EmptyStateWidget
                    size="regular"
                    title={
                        <FormattedMessage
                            id="account-selector.empty-state"
                            defaultMessage="No wallets found"
                        />
                    }
                    icon={({ size }) => (
                        <AddressBook size={size} color="iconDefault" />
                    )}
                />
            }
        />
    )
}
