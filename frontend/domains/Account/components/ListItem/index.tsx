import { Column } from '@zeal/uikit/Column'
import { ForwardIcon } from '@zeal/uikit/Icon/ForwardIcon'
import { ListItem as UIListItem } from '@zeal/uikit/ListItem'

import { Account } from '@zeal/domains/Account'
import { <PERSON>pyAddress } from '@zeal/domains/Address/components/CopyAddress'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStore } from '@zeal/domains/KeyStore'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { sumPortfolio2 } from '@zeal/domains/Portfolio/helpers/sum'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Avatar } from '../Avatar'

type Props = {
    installationId: string
    account: Account
    selected: boolean
    keystore: KeyStore
    portfolio: Portfolio2 | null // can be not loaded yet
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg = { type: 'account_item_clicked'; account: Account }

export const ListItem = ({
    account,
    keystore,
    portfolio,
    selected,
    currencyHiddenMap,
    installationId,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const sum =
        portfolio &&
        sumPortfolio2({ portfolio, currencyHiddenMap, defaultCurrencyConfig })

    return (
        <UIListItem
            size="regular"
            onClick={() => {
                onMsg({
                    type: 'account_item_clicked',
                    account,
                })
            }}
            avatar={({ size }) => (
                <Avatar account={account} keystore={keystore} size={size} />
            )}
            primaryText={account.label}
            shortText={
                sum ? (
                    <FormattedMoneyCompact money={sum} />
                ) : (
                    '\u00A0' // \u00A0
                )
            }
            aria-current={selected}
            side={{
                title: <></>,
                subtitle: (
                    <Column spacing={0} alignY="end">
                        <CopyAddress
                            location="wallet_list"
                            installationId={installationId}
                            size="small"
                            color="on_light"
                            address={account.address}
                        />
                    </Column>
                ),
                rightIcon: ({ size }) => (
                    <ForwardIcon size={size} color="iconDefault" />
                ),
            }}
        />
    )
}
