import { StepW<PERSON>rd } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { failure, Result, success } from '@zeal/toolkit/Result'

import {
    Account,
    AccountsMap,
    AccountsWithKeystores,
} from '@zeal/domains/Account'
import { Add4337SafeFromPasskey } from '@zeal/domains/Account/features/Add4337SafeFromPasskey'
import {
    CardConfig,
    CardSlientSignKeyStore,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { DetectCardForAccount } from '@zeal/domains/Card/features/DetectCardForAccount'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { SMART_WALLET_REFERENCE_NETWORK } from '@zeal/domains/Network/constants'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Msg =
    | MsgOf<typeof Add4337SafeFromPasskey>
    | {
          type: 'on_account_create_with_card_detected_success_animation_finished'
          accountsWithKeystores: AccountsWithKeystores
          cardConfig:
              | ReadonlySignerSelectedOnboardedCardConfig
              | ReadonlySignerSelectedCardConfig
      }
    | {
          type: 'on_accounts_create_success_animation_finished'
          accountsWithKeystores: AccountsWithKeystores
      }

type Props = {
    accountsMap: AccountsMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap

    sessionPassword: string
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

type State =
    | {
          type: 'recover_safe'
      }
    | {
          type: 'detect_card'
          accountsWithKeystores: AccountsWithKeystores
          selectedAccount: Account
          selectedKeyStore: CardSlientSignKeyStore
      }

const calculateShouldDetectCard = ({
    cardConfig,
    accountsWithKeystores,
}: {
    cardConfig: CardConfig
    accountsWithKeystores: AccountsWithKeystores<Safe4337>
}): Result<unknown, { account: Account; keystore: CardSlientSignKeyStore }> => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
            const { account, keystore } = accountsWithKeystores[0]
            return success({
                account,
                keystore,
            })
        case 'card_readonly_signer_address_is_selected':
        case 'card_readonly_signer_address_is_selected_fully_onboarded':
            return failure({
                type: 'user_already_has_card',
            })
        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}

export const RecoverSafeWithCardDetection = ({
    accountsMap,
    networkMap,
    networkRPCMap,
    sessionPassword,
    cardConfig,
    defaultCurrencyConfig,
    installationId,
    onMsg,
}: Props) => {
    return (
        <StepWizard<State> initialStep={{ type: 'recover_safe' }}>
            {({ step, backTo, forwardTo }) => {
                switch (step.type) {
                    case 'recover_safe':
                        return (
                            <Add4337SafeFromPasskey
                                network={SMART_WALLET_REFERENCE_NETWORK}
                                installationId={installationId}
                                accountsMap={accountsMap}
                                sessionPassword={sessionPassword}
                                networkRPCMap={networkRPCMap}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                        case 'on_account_create_request':
                                            onMsg(msg)
                                            break
                                        case 'on_accounts_create_success_animation_finished':
                                            const result =
                                                calculateShouldDetectCard({
                                                    cardConfig,
                                                    accountsWithKeystores:
                                                        msg.accountsWithKeystores,
                                                })

                                            switch (result.type) {
                                                case 'Failure':
                                                    onMsg(msg)
                                                    break
                                                case 'Success':
                                                    forwardTo({
                                                        type: 'detect_card',
                                                        accountsWithKeystores:
                                                            msg.accountsWithKeystores,
                                                        selectedAccount:
                                                            result.data.account,
                                                        selectedKeyStore:
                                                            result.data
                                                                .keystore,
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(result)
                                            }
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'detect_card':
                        return (
                            <DetectCardForAccount
                                accountsWithKeystores={
                                    step.accountsWithKeystores
                                }
                                networkRPCMap={networkRPCMap}
                                networkMap={networkMap}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                readonlySigner={step.selectedAccount}
                                sessionPassword={sessionPassword}
                                keyStore={step.selectedKeyStore}
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_card_detected_success_animation_complete':
                                            onMsg({
                                                type: 'on_account_create_with_card_detected_success_animation_finished',
                                                accountsWithKeystores:
                                                    step.accountsWithKeystores,
                                                cardConfig: msg.cardConfig,
                                            })

                                            break
                                        case 'on_card_detection_failed':
                                        case 'on_card_not_detected':
                                            onMsg({
                                                type: 'on_accounts_create_success_animation_finished',
                                                accountsWithKeystores:
                                                    step.accountsWithKeystores,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
