import { Modal as UIModal } from '@zeal/uikit/Modal'
import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    FetchPortfolioRequest,
    FetchPortfolioResponse,
} from '@zeal/domains/Account/api/fetchAccounts'
import { ShowAndScanQRCode } from '@zeal/domains/Account/features/ShowAndScanQRCode'
import { App2 } from '@zeal/domains/App'
import { AppPositionDetails } from '@zeal/domains/App/components/AppPositionDetails'
import { AppsList } from '@zeal/domains/App/components/AppsList'
import { CurrencyHiddenMap, CurrencyPinMap } from '@zeal/domains/Currency'
import { KeyStore, KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    PortfolioNFT,
    PortfolioNFTCollection,
} from '@zeal/domains/NFTCollection'
import { AllNFTsList } from '@zeal/domains/NFTCollection/components/AllNFTsList'
import { DetailsView } from '@zeal/domains/NFTCollection/components/DetailsView'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { SendOrReceiveToken } from '@zeal/domains/RPCRequest/features/SendOrReceiveToken'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { Token2 } from '@zeal/domains/Token'
import { TokenList } from '@zeal/domains/Token/components/TokenList'

type Props = {
    state: State
    account: Account
    portfolioLoadable: LoadedReloadableData<
        FetchPortfolioResponse,
        FetchPortfolioRequest
    >
    portfolioMap: PortfolioMap
    keystore: KeyStore
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    currencyHiddenMap: CurrencyHiddenMap
    isEthereumNetworkFeeWarningSeen: boolean
    currencyPinMap: CurrencyPinMap
    installationId: string
    keystoreMap: KeyStoreMap
    customCurrencyMap: CustomCurrencyMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    accountsMap: AccountsMap
    refreshContainerState: RefreshContainerState
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | MsgOf<typeof TokenList>
    | MsgOf<typeof DetailsView>
    | MsgOf<typeof AppsList>
    | MsgOf<typeof SendOrReceiveToken>
    | MsgOf<typeof AppPositionDetails>
    | MsgOf<typeof ShowAndScanQRCode>

export type State =
    | { type: 'closed' }
    | { type: 'show_all_tokens' }
    | { type: 'show_all_apps' }
    | { type: 'show_all_nfts' }
    | { type: 'receive' }
    | { type: 'send_or_receive'; token: Token2 }
    | {
          type: 'nft_detailed_view'
          nft: PortfolioNFT
          nftCollection: PortfolioNFTCollection
      }
    | { type: 'app_position'; app: App2 }

export const Modal = ({
    state,
    account,
    portfolioLoadable,
    portfolioMap,
    keystore,
    isEthereumNetworkFeeWarningSeen,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    currencyPinMap,
    installationId,
    keystoreMap,
    customCurrencyMap,
    defaultCurrencyConfig,
    accountsMap,
    refreshContainerState,
    onMsg,
}: Props) => {
    const portfolio = portfolioLoadable.data.portfolio

    switch (state.type) {
        case 'receive':
            return (
                <UIModal>
                    <ShowAndScanQRCode
                        keyStore={getKeyStore({
                            address: account.address,
                            keyStoreMap: keystoreMap,
                        })}
                        initialState={{ type: 'show_qr_code' }}
                        accountMap={accountsMap}
                        networkMap={networkMap}
                        installationId={installationId}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        account={account}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'send_or_receive':
            return (
                <SendOrReceiveToken
                    portfolio={portfolioLoadable.data.portfolio}
                    installationId={installationId}
                    token={state.token}
                    networkMap={networkMap}
                    fromAccount={account}
                    networkRPCMap={networkRPCMap}
                    customCurrencies={customCurrencyMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    onMsg={onMsg}
                />
            )
        case 'closed':
            return null
        case 'nft_detailed_view':
            return (
                <UIModal>
                    <DetailsView
                        installationId={installationId}
                        networkMap={networkMap}
                        account={account}
                        onMsg={onMsg}
                        nft={state.nft}
                        nftCollection={state.nftCollection}
                    />
                </UIModal>
            )
        case 'show_all_nfts':
            return (
                <UIModal>
                    <AllNFTsList
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        networkMap={networkMap}
                        portfolioMap={portfolioMap}
                        nftCollections={portfolio.nftCollections}
                        account={account}
                        networkRPCMap={networkRPCMap}
                        keystoreMap={keystoreMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'show_all_apps':
            return (
                <UIModal>
                    <AppsList
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        networkRPCMap={networkRPCMap}
                        keystoreMap={keystoreMap}
                        currencyHiddenMap={currencyHiddenMap}
                        portfolioMap={portfolioMap}
                        installationId={installationId}
                        networkMap={networkMap}
                        keystore={keystore}
                        apps={portfolio.apps}
                        account={account}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'show_all_tokens':
            return (
                <UIModal>
                    <TokenList
                        refreshContainerState={refreshContainerState}
                        accountsMap={accountsMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        customCurrencyMap={customCurrencyMap}
                        currencyPinMap={currencyPinMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        portfolioMap={portfolioMap}
                        portfolioLoadable={portfolioLoadable}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        account={account}
                        keystoreMap={keystoreMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'app_position':
            return (
                <UIModal>
                    <AppPositionDetails
                        account={account}
                        networkMap={networkMap}
                        app={state.app}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
