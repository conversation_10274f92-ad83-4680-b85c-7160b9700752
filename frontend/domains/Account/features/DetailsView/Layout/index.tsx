import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { ListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import { Avatar } from '@zeal/domains/Account/components/Avatar'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStore } from '@zeal/domains/KeyStore'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { sumPortfolio2 } from '@zeal/domains/Portfolio/helpers/sum'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Actions } from './Actions'

type Props = {
    installationId: string
    account: Account
    portfolio: Portfolio2 | null
    keystore: KeyStore
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg = { type: 'close' } | MsgOf<typeof Actions>

export const Layout = ({
    portfolio,
    keystore,
    currencyHiddenMap,
    account,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const sum =
        portfolio &&
        sumPortfolio2({ portfolio, currencyHiddenMap, defaultCurrencyConfig })

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <ActionBar.Header>
                                <FormattedMessage
                                    id="walletSetting.header"
                                    defaultMessage="Wallet Settings"
                                />
                            </ActionBar.Header>
                        </Row>
                    </Clickable>
                }
            />
            <Column spacing={16}>
                <ListItem
                    key={account.address}
                    size="regular"
                    avatar={({ size }) => (
                        <Avatar
                            account={account}
                            keystore={keystore}
                            size={size}
                        />
                    )}
                    primaryText={account.label}
                    shortText={
                        <Text>
                            {sum && <FormattedMoneyCompact money={sum} />}
                        </Text>
                    }
                    aria-current={false}
                />

                <Actions account={account} keystore={keystore} onMsg={onMsg} />
            </Column>
        </Screen>
    )
}
