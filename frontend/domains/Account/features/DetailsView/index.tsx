import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { UserAReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, Msg as ModalMsg, State as ModalState } from './Modal'

type Props = {
    installationId: string
    account: Account
    keystoreMap: KeyStoreMap
    userAReferralConfig: UserAReferralConfig

    encryptedPassword: string
    portfolio: Portfolio2 | null

    currencyHiddenMap: CurrencyHiddenMap

    accounts: AccountsMap
    networkMap: NetworkMap
    isEthereumNetworkFeeWarningSeen: boolean
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | Extract<
          ModalMsg,
          {
              type:
                  | 'on_account_label_change_submit'
                  | 'on_secret_phrase_verified_success'
                  | 'on_google_drive_backup_success'
                  | 'confirm_account_delete_click'
                  | 'on_rewards_warning_confirm_account_delete_click'
                  | 'on_address_scanned_and_add_label'
                  | 'on_address_scanned'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
          }
      >
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_recovery_kit_setup'
                  | 'confirm_account_delete_click'
                  | 'on_rewards_warning_confirm_account_delete_click'
                  | 'on_add_private_key_click'
          }
      >

export const DetailsView = ({
    account,
    portfolio,
    encryptedPassword,
    accounts,
    keystoreMap,
    currencyHiddenMap,
    userAReferralConfig,
    installationId,
    networkMap,
    isEthereumNetworkFeeWarningSeen,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<ModalState>({ type: 'closed' })

    const keystore = getKeyStore({
        keyStoreMap: keystoreMap,
        address: account.address,
    })

    return (
        <>
            <Layout
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationId={installationId}
                currencyHiddenMap={currencyHiddenMap}
                account={account}
                portfolio={portfolio}
                keystore={keystore}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_recovery_kit_setup':
                        case 'on_add_private_key_click':
                            onMsg(msg)
                            break
                        case 'on_edit_label_click':
                            setState({ type: 'edit_label', keystore })
                            break

                        case 'on_show_private_key_click':
                            setState({
                                type: 'show_private_key',
                                keystore: msg.keystore,
                            })
                            break

                        case 'on_show_secret_phrase_click':
                            setState({
                                type: 'show_secret_phrase',
                                keystore: msg.keystore,
                            })
                            break

                        case 'on_see_qr_code_click': {
                            postUserEvent({
                                type: 'ReceiveFlowEnteredEvent',
                                installationId,
                                location: 'wallet_details',
                            })
                            setState({
                                type: 'show_qr_code',
                                keystore: msg.keystore,
                            })
                            break
                        }

                        case 'on_account_delete_click':
                            switch (userAReferralConfig.type) {
                                case 'not_configured':
                                    setState({ type: 'confirm_delete' })
                                    break
                                case 'configured':
                                    if (
                                        account.address ===
                                        userAReferralConfig.ownerAddress
                                    ) {
                                        setState({
                                            type: 'account_delete_rewards_warning',
                                            userAReferralConfig,
                                        })
                                    } else {
                                        setState({ type: 'confirm_delete' })
                                    }
                                    break
                                default:
                                    return notReachable(userAReferralConfig)
                            }
                            break

                        case 'on_zeal_smart_wallets_info_click':
                            setState({ type: 'zeal_smart_wallets_info' })
                            break
                        case 'on_backup_recovery_smart_wallet_clicked':
                            setState({
                                type: 'backup_recovery_smart_wallet',
                                keystore: msg.keystore,
                            })
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />

            <Modal
                networkMap={networkMap}
                installationId={installationId}
                accounts={accounts}
                encryptedPassword={encryptedPassword}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                account={account}
                state={state}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_edit_label_close':
                            setState({ type: 'closed' })
                            break

                        case 'on_account_label_change_submit':
                        case 'confirm_account_delete_click':
                        case 'on_rewards_warning_confirm_account_delete_click':
                            setState({ type: 'closed' })
                            onMsg(msg)
                            break
                        case 'local_private_key_link_clicked':
                            setState({
                                type: 'show_private_key',
                                keystore: msg.keystore,
                            })
                            break
                        case 'on_pairing_uri_scanned':
                            break
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_address_scanned_and_add_label':
                        case 'on_address_scanned':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
