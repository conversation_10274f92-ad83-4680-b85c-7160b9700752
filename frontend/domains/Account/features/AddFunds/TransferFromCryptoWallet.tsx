import { useEffect } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { ExternalWalletsSquare } from '@zeal/uikit/Icon/ExternalWalletsSquare'
import { LightDownload } from '@zeal/uikit/Icon/LightDownload'
import { OutlineQRCode } from '@zeal/uikit/Icon/OutlineQRCode'
import { IconButton } from '@zeal/uikit/IconButton'
import { ListItemButton } from '@zeal/uikit/ListItem'
import { SubtextListItem } from '@zeal/uikit/ListItem/SubtextListItem'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { values } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { UnlockedListItem } from '@zeal/domains/Account/components/UnlockedListItem'
import { format } from '@zeal/domains/Address/helpers/format'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    account: Account
    accountsMap: AccountsMap
    portfolioMap: PortfolioMap
    keystoreMap: KeyStoreMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_transfer_from_crypto_wallet_close' }
    | { type: 'on_show_wallet_address_clicked' }
    | { type: 'on_connect_to_top_up_dapp_clicked' }
    | { type: 'on_active_wallet_clicked'; account: Account }
    | { type: 'add_wallet_clicked' }

export const TransferFromCryptoWallet = ({
    onMsg,
    account,
    accountsMap,
    keystoreMap,
    portfolioMap,
    installationId,
    currencyHiddenMap,
    defaultCurrencyConfig,
}: Props) => {
    const accounts = getActiveAccounts(accountsMap, keystoreMap, account)
    const { formatMessage } = useIntl()

    useEffect(() => {
        postUserEvent({
            installationId,
            type: 'TransferFromExchangeEnteredEvent',
        })
    }, [installationId])

    return (
        <Screen
            padding="form"
            background="default"
            onNavigateBack={() =>
                onMsg({
                    type: 'on_transfer_from_crypto_wallet_close',
                })
            }
        >
            <Column spacing={12}>
                <ActionBar
                    left={
                        <IconButton
                            variant="on_light"
                            aria-label={formatMessage({
                                id: 'actions.close',
                                defaultMessage: 'close',
                            })}
                            onClick={() =>
                                onMsg({
                                    type: 'on_transfer_from_crypto_wallet_close',
                                })
                            }
                        >
                            {({ color }) => (
                                <BackIcon size={24} color={color} />
                            )}
                        </IconButton>
                    }
                />
                <HeaderV2
                    align="left"
                    size="large"
                    title={
                        <FormattedMessage
                            id="add_funds.from_crypto_wallet.header"
                            defaultMessage="From another wallet"
                        />
                    }
                    subtitle={null}
                />
                <ListItemButton
                    variant="outline"
                    background="surface"
                    aria-current={false}
                    avatar={({ size }) => (
                        <OutlineQRCode size={size} color="iconAccent2" />
                    )}
                    onClick={() =>
                        onMsg({
                            type: 'on_show_wallet_address_clicked',
                        })
                    }
                    disabled={false}
                    primaryText={
                        <FormattedMessage
                            id="add_funds.from_crypto_wallet.header.show_wallet_address"
                            defaultMessage="Show your wallet address"
                        />
                    }
                    shortText={format(account.address)}
                />
                <ListItemButton
                    variant="outline"
                    aria-current={false}
                    avatar={({ size }) => (
                        <LightDownload size={size} color="teal40" />
                    )}
                    primaryText={
                        <FormattedMessage
                            id="add_funds.import_wallet"
                            defaultMessage="Import existing crypto wallet"
                        />
                    }
                    onClick={() => {
                        onMsg({
                            type: 'add_wallet_clicked',
                        })
                    }}
                    background="surface"
                    disabled={false}
                />

                <SubtextListItem
                    size="large"
                    variant="outline"
                    primaryText={
                        <FormattedMessage
                            id="add_funds.from_crypto_wallet.connect_to_top_up_dapp"
                            defaultMessage="Connect to topup dApp"
                        />
                    }
                    avatar={({ size }) => <ExternalWalletsSquare size={size} />}
                    onClick={() =>
                        onMsg({
                            type: 'on_connect_to_top_up_dapp_clicked',
                        })
                    }
                    subItems={
                        <Text variant="footnote">
                            <FormattedMessage
                                id="add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle"
                                defaultMessage="Connect any wallet to the Zeal topup dApp and quickly send funds to your wallet"
                            />
                        </Text>
                    }
                />
                {accounts.length ? (
                    <Group variant="widget">
                        {accounts.map((item) => (
                            <UnlockedListItem
                                keyStore={getKeyStore({
                                    address: item.address,
                                    keyStoreMap: keystoreMap,
                                })}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                installationId={installationId}
                                currencyHiddenMap={currencyHiddenMap}
                                key={item.address}
                                portfolio={unsafe_GetPortfolioCache2({
                                    address: item.address,
                                    portfolioMap,
                                })}
                                selected={false}
                                account={item}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'account_item_clicked':
                                            onMsg({
                                                type: 'on_active_wallet_clicked',
                                                account: msg.account,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg.type)
                                    }
                                }}
                                selectionVariant="background_color"
                            />
                        ))}
                    </Group>
                ) : null}
            </Column>
        </Screen>
    )
}

const getActiveAccounts = (
    accountsMap: AccountsMap,
    keyStoreMap: KeyStoreMap,
    accountToExclude: Account
): Account[] => {
    return values(accountsMap).filter((account) => {
        if (account.address === accountToExclude.address) {
            return false
        }

        const keyStore = getKeyStore({
            address: account.address,
            keyStoreMap,
        })

        switch (keyStore.type) {
            case 'track_only':
                return false
            case 'private_key_store':
            case 'ledger':
            case 'secret_phrase_key':
            case 'trezor':
            case 'safe_4337':
                return true
            /* istanbul ignore next */
            default:
                return notReachable(keyStore)
        }
    })
}
