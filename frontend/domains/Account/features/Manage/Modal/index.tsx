import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { SelectTypeOfAccountToAdd } from '@zeal/domains/Account/components/SelectTypeOfAccountToAdd'
import {
    AddFromAddress,
    Msg as AddFromAddressMsg,
} from '@zeal/domains/Account/features/AddFromAddress'
import { AddFromSecretPhrase } from '@zeal/domains/Account/features/AddFromSecretPhrase'
import {
    DetailsView,
    Msg as DetailsViewMsg,
} from '@zeal/domains/Account/features/DetailsView'
import { Address } from '@zeal/domains/Address'
import { CardConfig } from '@zeal/domains/Card'
import { UserAReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SecretPhrase } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    installationId: string
    state: State
    accounts: AccountsMap
    portfolioMap: PortfolioMap
    keystoreMap: KeyStoreMap
    encryptedPassword: string
    currencyHiddenMap: CurrencyHiddenMap
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    cardConfig: CardConfig
    sessionPassword: string
    customCurrencyMap: CustomCurrencyMap
    isEthereumNetworkFeeWarningSeen: boolean
    defaultCurrencyConfig: DefaultCurrencyConfig
    userAReferralConfig: UserAReferralConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | DetailsViewMsg
    | AddFromAddressMsg
    | MsgOf<typeof SelectTypeOfAccountToAdd>
    | MsgOf<typeof AddFromSecretPhrase>

export type State =
    | { type: 'closed' }
    | { type: 'account_details'; address: Address }
    | { type: 'track_account'; address: Address }
    | { type: 'select_type_of_account_to_add' }
    | {
          type: 'add_from_secret_phrase'
          secretPhraseMap: Record<
              string,
              { keystore: SecretPhrase; account: Account }[]
          >
      }

export const Modal = ({
    state,
    keystoreMap,
    portfolioMap,
    accounts,
    installationId,
    encryptedPassword,
    cardConfig,
    currencyHiddenMap,
    networkRPCMap,
    networkMap,
    sessionPassword,
    isEthereumNetworkFeeWarningSeen,
    customCurrencyMap,
    defaultCurrencyConfig,
    userAReferralConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'add_from_secret_phrase':
            return (
                <UIModal>
                    <AddFromSecretPhrase
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        cardConfig={cardConfig}
                        currencyHiddenMap={currencyHiddenMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        accountsMap={accounts}
                        keystoreMap={keystoreMap}
                        sessionPassword={sessionPassword}
                        secretPhraseMap={state.secretPhraseMap}
                        customCurrencies={customCurrencyMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'account_details':
            return (
                <UIModal>
                    <DetailsView
                        userAReferralConfig={userAReferralConfig}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        networkMap={networkMap}
                        installationId={installationId}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        currencyHiddenMap={currencyHiddenMap}
                        keystoreMap={keystoreMap}
                        accounts={accounts}
                        encryptedPassword={encryptedPassword}
                        portfolio={unsafe_GetPortfolioCache2({
                            address: state.address,
                            portfolioMap,
                        })}
                        account={accounts[state.address]}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'select_type_of_account_to_add':
            return <SelectTypeOfAccountToAdd onMsg={onMsg} />

        case 'track_account':
            return (
                <UIModal>
                    <AddFromAddress
                        installationId={installationId}
                        address={state.address}
                        accountMap={accounts}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
