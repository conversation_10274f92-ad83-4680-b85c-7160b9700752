import { FormattedMessage } from 'react-intl'

import { Avatar as UIAvatar } from '@zeal/uikit/Avatar'
import { Checkbox } from '@zeal/uikit/Icon/Checkbox'
import { NotSelected } from '@zeal/uikit/Icon/NotSelected'
import { ListItem } from '@zeal/uikit/ListItem'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Tag } from '@zeal/uikit/Tag'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'

import { Account } from '@zeal/domains/Account'
import { Avatar } from '@zeal/domains/Account/components/Avatar'
import { Address } from '@zeal/domains/Address'
import { format } from '@zeal/domains/Address/helpers/format'
import { CardConfig } from '@zeal/domains/Card'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap, LEDGER } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { FormattedMoneyCompact } from '@zeal/domains/Money/components/FormattedMoneyCompact'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { fetchPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { sumPortfolio2 } from '@zeal/domains/Portfolio/helpers/sum'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    address: Address
    isSelected: boolean
    index: number
    keystoreMap: KeyStoreMap
    accounts: Account[]
    customCurrencies: CustomCurrencyMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    currencyHiddenMap: CurrencyHiddenMap
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onClick: () => void
}

type State =
    | { type: 'not_imported_account' }
    | {
          type: 'imported_with_current_keystore_type'
          account: Account
          keystore: LEDGER
      }
    | {
          type: 'imported_other_keystore_type'
          account: Account
      }

const calculateState = (
    keyStoreMap: KeyStoreMap,
    accounts: Account[],
    address: Address
): State => {
    const account = accounts.find((account) => account.address === address)

    if (!account) {
        return { type: 'not_imported_account' }
    }

    const keystore = getKeyStore({ keyStoreMap, address: account.address })

    switch (keystore.type) {
        case 'private_key_store':
        case 'secret_phrase_key':
        case 'trezor':
        case 'track_only':
        case 'safe_4337':
            return { type: 'imported_other_keystore_type', account }

        case 'ledger':
            return {
                type: 'imported_with_current_keystore_type',
                account,
                keystore,
            }

        /* istanbul ignore next */
        default:
            return notReachable(keystore)
    }
}

/**
 * TODO @resetko-zeal It's almost same as frontend/wallet/src/domains/Account/features/Ledger/SelectAddresses/Layout/Item.tsx
 *                                                             /features/RestoreAccount/AddFromNewSecretPhrase/AccountSelection/Item.tsx
 *                                                             /features/AddFromExistingSecretPhrase/SelectAccount/Item.tsx
 *                                                             /features/AddFromTrezor/SelectAccounts/Item.tsx
 */
export const Item = ({
    address,
    keystoreMap,
    isSelected,
    accounts,
    onClick,
    customCurrencies,
    index,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    cardConfig,
    defaultCurrencyConfig,
    installationId,
}: Props) => {
    const [loadable] = useLoadableData(fetchPortfolio2, {
        type: 'loading',
        params: {
            currencyHiddenMap,
            address,
            customCurrencies,
            forceRefresh: false,
            networkMap,
            networkRPCMap,
            cardConfig,
            defaultCurrencyConfig,
            installationId,
        },
    })

    const state = calculateState(keystoreMap, accounts, address)

    switch (state.type) {
        case 'not_imported_account':
            return (
                <ListItem
                    onClick={onClick}
                    aria-current={false}
                    size="regular"
                    avatar={({ size }) => (
                        <UIAvatar size={size} border="borderSecondary">
                            <Text
                                variant="caption1"
                                weight="medium"
                                color="textPrimary"
                                align="center"
                            >
                                {index + 1}
                            </Text>
                        </UIAvatar>
                    )}
                    primaryText={format(address)}
                    shortText={(() => {
                        switch (loadable.type) {
                            case 'loading':
                                return (
                                    <Skeleton
                                        variant="default"
                                        width={50}
                                        height={15}
                                    />
                                )

                            case 'loaded':
                                return (
                                    <FormattedMoneyCompact
                                        money={sumPortfolio2({
                                            portfolio: loadable.data,
                                            currencyHiddenMap,
                                            defaultCurrencyConfig,
                                        })}
                                    />
                                )

                            case 'error':
                                return null

                            /* istanbul ignore next */
                            default:
                                return notReachable(loadable)
                        }
                    })()}
                    side={{
                        rightIcon: ({ size }) =>
                            isSelected ? (
                                <Checkbox size={size} color="iconAccent2" />
                            ) : (
                                <NotSelected size={size} color="iconDefault" />
                            ),
                    }}
                />
            )

        case 'imported_with_current_keystore_type':
            return (
                <ListItem
                    aria-current={false}
                    size="regular"
                    avatar={({ size }) => (
                        <Avatar
                            account={state.account}
                            size={size}
                            keystore={state.keystore}
                        />
                    )}
                    primaryText={state.account.label}
                    shortText={(() => {
                        switch (loadable.type) {
                            case 'loading':
                                return (
                                    <Skeleton
                                        variant="default"
                                        width={50}
                                        height={15}
                                    />
                                )

                            case 'loaded':
                                return (
                                    <FormattedMoneyCompact
                                        money={sumPortfolio2({
                                            portfolio: loadable.data,
                                            currencyHiddenMap,
                                            defaultCurrencyConfig,
                                        })}
                                    />
                                )

                            case 'error':
                                return null

                            /* istanbul ignore next */
                            default:
                                return notReachable(loadable)
                        }
                    })()}
                    side={{
                        rightIcon: () => (
                            <Tag bg="surfaceHover">
                                <Text
                                    variant="caption1"
                                    weight="regular"
                                    color="textSecondary"
                                >
                                    <FormattedMessage
                                        id="ledger.account_loaded.imported"
                                        defaultMessage="Imported"
                                    />
                                </Text>
                            </Tag>
                        ),
                    }}
                />
            )

        case 'imported_other_keystore_type':
            return (
                <ListItem
                    onClick={onClick}
                    aria-current={false}
                    size="regular"
                    avatar={({ size }) => (
                        <Avatar
                            account={state.account}
                            size={size}
                            keystore={getKeyStore({
                                keyStoreMap: keystoreMap,
                                address,
                            })}
                        />
                    )}
                    primaryText={state.account.label}
                    shortText={(() => {
                        switch (loadable.type) {
                            case 'loading':
                                return (
                                    <Skeleton
                                        variant="default"
                                        width={50}
                                        height={15}
                                    />
                                )

                            case 'loaded':
                                return (
                                    <FormattedMoneyCompact
                                        money={sumPortfolio2({
                                            portfolio: loadable.data,
                                            currencyHiddenMap,
                                            defaultCurrencyConfig,
                                        })}
                                    />
                                )

                            case 'error':
                                return null

                            /* istanbul ignore next */
                            default:
                                return notReachable(loadable)
                        }
                    })()}
                    side={{
                        rightIcon: ({ size }) =>
                            isSelected ? (
                                <Checkbox size={size} color="iconAccent2" />
                            ) : (
                                <NotSelected size={size} color="iconDefault" />
                            ),
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
