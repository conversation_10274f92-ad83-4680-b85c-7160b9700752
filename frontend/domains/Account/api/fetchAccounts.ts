import { Address } from '@zeal/domains/Address'
import { CardConfig } from '@zeal/domains/Card'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { fetchPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'

export type FetchPortfolioRequest = {
    address: Address
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencies: CustomCurrencyMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    cardConfig: CardConfig
    installationId: string
}

export type FetchPortfolioResponse = {
    fetchedAt: Date
    portfolio: Portfolio2
}

export const fetchAccounts = ({
    signal,
    customCurrencies,
    address,
    networkMap,
    cardConfig,
    networkRPCMap,
    currencyHiddenMap,
    defaultCurrencyConfig,
    installationId,
}: FetchPortfolioRequest): Promise<FetchPortfolioResponse> =>
    fetchPortfolio2({
        currencyHiddenMap,
        address,
        customCurrencies,
        signal,
        networkMap,
        networkRPCMap,
        cardConfig,
        defaultCurrencyConfig,
        installationId,
    }).then((portfolio) => ({
        fetchedAt: new Date(),
        portfolio,
    }))
