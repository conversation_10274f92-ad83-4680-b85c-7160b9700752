import { Account } from '@zeal/domains/Account'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { sumPortfolio2 } from '@zeal/domains/Portfolio/helpers/sum'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

export const sortByBalance =
    (
        portfolioMap: PortfolioMap,
        currencyHiddenMap: CurrencyHiddenMap,
        defaultCurrencyConfig: DefaultCurrencyConfig
    ) =>
    (acc1: Account, acc2: Account): number => {
        const portfolio1 = unsafe_GetPortfolioCache2({
            address: acc1.address,
            portfolioMap,
        })
        const portfolio2 = unsafe_GetPortfolioCache2({
            address: acc2.address,
            portfolioMap,
        })
        const amount1 = portfolio1
            ? sumPortfolio2({
                  portfolio: portfolio1,
                  currencyHiddenMap,
                  defaultCurrencyConfig,
              }).amount
            : 0n
        const amount2 = portfolio2
            ? sumPortfolio2({
                  portfolio: portfolio2,
                  currencyHiddenMap,
                  defaultCurrencyConfig,
              }).amount
            : 0n

        if (amount1 < amount2) {
            return 1
        } else if (amount1 > amount2) {
            return -1
        } else {
            return 0
        }
    }
