import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { LoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { Account } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    Currency,
    CurrencyHiddenMap,
} from '@zeal/domains/Currency'
import { SwapQuote, SwapRoute } from '@zeal/domains/Currency/domains/SwapQuote'
import { ConfiguredEarn, EarnWithdrawRequest, Taker } from '@zeal/domains/Earn'
import { FXRate2 } from '@zeal/domains/FXRate'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney, Money2, MoneyByCurrency } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Pollable = LoadedPollableData<Data, Form>

export type Form = {
    currencyHiddenMap: CurrencyHiddenMap
    earn: ConfiguredEarn
    amount: string | null
    taker: Taker
    owner: Account
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    toCurrency: CryptoCurrency
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    keyStoreMap: KeyStoreMap
}

export type Data = {
    takerBalance: CryptoMoney
    ownerPortfolio: ServerPortfolio2
    toCurrencies: CryptoCurrency[]
    takerUserCurrencyRate: FXRate2<CryptoCurrency, Currency>
    investmentAssetAmount: bigint
    swapQuote: SwapQuote
}

type NoRoutesFoundError = { type: 'no_routes_found' }
type NotEnoughBalanceError = { type: 'not_enough_balance' }
type PollableReloading = { type: 'pollable_reloading' }
type PollableSubsequentFailed = { type: 'pollable_subsequent_failed' }
type AmountRequired = { type: 'amount_required' }

export type SubmitError =
    | PollableReloading
    | PollableSubsequentFailed
    | NoRoutesFoundError
    | NotEnoughBalanceError
    | AmountRequired

type FormError = {
    from?: NotEnoughBalanceError
    submit?: SubmitError
}

export const getRoute = (pollable: Pollable): SwapRoute | null =>
    pollable.data.swapQuote.routes[0] || null

export const validateFromBalance = ({
    pollable,
}: {
    pollable: Pollable
}): Result<NotEnoughBalanceError, unknown> => {
    const investmentAssetCurrency = pollable.params.taker.cryptoCurrency
    const inputInInvestmentAssetCurrency: CryptoMoney = {
        amount: pollable.data.investmentAssetAmount,
        currency: investmentAssetCurrency,
    }

    const investmentBalance = pollable.data.takerBalance

    if (investmentBalance.amount < inputInInvestmentAssetCurrency.amount) {
        return failure({ type: 'not_enough_balance' })
    }

    return success(undefined)
}

export const validateFrom = ({
    pollable,
}: {
    pollable: Pollable
}): Result<AmountRequired, Money2> => {
    const inputCurrency = pollable.data.takerUserCurrencyRate.quote
    const inputAmount = pollable.params.amount

    if (!inputAmount) {
        return failure({ type: 'amount_required' })
    }

    const amount = fromFixedWithFraction(inputAmount, inputCurrency.fraction)

    if (amount === 0n) {
        return failure({ type: 'amount_required' })
    }

    return success({ amount, currency: inputCurrency } as MoneyByCurrency<
        typeof inputCurrency
    >)
}

const validateSubmit = ({
    pollable,
    fromAmountInUserCurrency,
}: {
    pollable: Pollable
    fromAmountInUserCurrency: Money2
}): Result<SubmitError, EarnWithdrawRequest> => {
    switch (pollable.type) {
        case 'loaded': {
            const routes = pollable.data.swapQuote.routes
            const selectedRoute = getRoute(pollable)

            if (!routes.length || !selectedRoute) {
                return failure({ type: 'no_routes_found' })
            }

            return success({
                investmentAssetAmount: pollable.data.investmentAssetAmount,
                owner: pollable.params.owner,
                swapRoute: selectedRoute,
                taker: pollable.params.taker,
                earn: pollable.params.earn,
                fromAmountInUserCurrency,
                toAmount: selectedRoute.to,
            })
        }

        case 'reloading':
            return failure({ type: 'pollable_reloading' })

        case 'subsequent_failed':
            return failure({ type: 'pollable_subsequent_failed' })

        default:
            return notReachable(pollable)
    }
}

export const validate = ({
    pollable,
}: {
    pollable: Pollable
}): Result<FormError, EarnWithdrawRequest> =>
    shape({
        from: validateFromBalance({ pollable }),
        submit: validateFrom({ pollable }).andThen((fromAmountInUserCurrency) =>
            validateFromBalance({ pollable }).andThen(() =>
                validateSubmit({ pollable, fromAmountInUserCurrency })
            )
        ),
    }).map(({ submit }) => submit)
