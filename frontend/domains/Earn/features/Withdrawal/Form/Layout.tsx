import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { QuestionCircle } from '@zeal/uikit/Icon/QuestionCircle'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { LoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'

import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { EarnWithdrawRequest } from '@zeal/domains/Earn'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { TakerTitle } from '@zeal/domains/Earn/components/TakerTitle'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { FancyButton } from '@zeal/domains/Network/components/FancyButton'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

import { Data, Form, getRoute, SubmitError, validate } from './validation'

type Props = {
    pollable: LoadedPollableData<Data, Form>
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_to_currency_change_clicked' }
    | { type: 'on_from_amount_changed'; fromAmount: string | null }
    | { type: 'on_form_submit'; earnWithdrawalRequest: EarnWithdrawRequest }
    | { type: 'on_earn_account_selector_click' }
    | { type: 'close' }

export const Layout = ({ pollable, onMsg, installationId }: Props) => {
    const { formatMessage } = useIntl()
    const { taker } = pollable.params
    const validationResult = validate({ pollable })

    const errors = validationResult.getFailureReason() || {}

    const ownerPortfolio = pollable.data.ownerPortfolio

    const investmentBalance = pollable.data.takerBalance

    const investmentBalanceInUserCurrency = applyRate2({
        baseAmount: investmentBalance,
        rate: pollable.data.takerUserCurrencyRate,
    })

    const route = getRoute(pollable)

    const userCurrency = pollable.data.takerUserCurrencyRate.quote
    const toCurrency = pollable.params.toCurrency

    const toCurrencyBalance: CryptoMoney | null = toCurrency
        ? getBalanceByCryptoCurrency2({
              currency: toCurrency,
              serverPortfolio: ownerPortfolio,
          })
        : null

    const to = toCurrency
        ? { amount: route?.to.amount || 0n, currency: toCurrency }
        : null

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={0} fill>
                <ActionBar
                    top={
                        <ActionBarAccountIndicator
                            account={pollable.params.owner}
                        />
                    }
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <ActionBar.Header>
                                    <FormattedMessage
                                        id="earn.withdraw_form.title"
                                        defaultMessage="Withdraw from Earn"
                                    />
                                </ActionBar.Header>
                            </Row>
                        </Clickable>
                    }
                />

                <Column spacing={12} fill alignY="stretch">
                    <Column spacing={4} fill>
                        <AmountInput
                            content={{
                                topRight: ({ onBlur, onFocus }) => (
                                    <AmountInput.Input
                                        onBlur={onBlur}
                                        onFocus={onFocus}
                                        onChange={(value) =>
                                            onMsg({
                                                type: 'on_from_amount_changed',
                                                fromAmount: value,
                                            })
                                        }
                                        label={formatMessage({
                                            id: 'earn.withdraw.amount_to_withdraw',
                                            defaultMessage:
                                                'Amount to withdraw',
                                        })}
                                        prefix=""
                                        fraction={userCurrency.fraction}
                                        autoFocus
                                        amount={pollable.params.amount || null}
                                        onSubmitEditing={noop}
                                    />
                                ),
                                topLeft: (
                                    <IconButton
                                        variant="on_light"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_earn_account_selector_click',
                                            })
                                        }
                                    >
                                        {({ color }) => (
                                            <Row spacing={8}>
                                                <TakerAvatar
                                                    size={32}
                                                    takerType={taker.type}
                                                />
                                                <Row spacing={4}>
                                                    <Text
                                                        variant="title3"
                                                        color="textPrimary"
                                                        weight="medium"
                                                    >
                                                        <TakerTitle
                                                            takerType={
                                                                taker.type
                                                            }
                                                        />
                                                    </Text>
                                                    <LightArrowDown2
                                                        size={20}
                                                        color={color}
                                                    />
                                                </Row>
                                            </Row>
                                        )}
                                    </IconButton>
                                ),

                                bottomLeft: (
                                    <MaxButton
                                        installationId={installationId}
                                        location="earn_withdrawal"
                                        balance={
                                            investmentBalanceInUserCurrency
                                        }
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'on_amount_change':
                                                    onMsg({
                                                        type: 'on_from_amount_changed',
                                                        fromAmount: msg.amount,
                                                    })
                                                    break

                                                /* istanbul ignore next */
                                                default:
                                                    notReachable(msg.type)
                                            }
                                        }}
                                        state={errors.from ? 'error' : 'normal'}
                                    />
                                ),
                            }}
                            state={errors.from ? 'error' : 'normal'}
                        />

                        <NextStepSeparator />

                        <AmountInput
                            top={
                                <FancyButton
                                    fill
                                    rounded={false}
                                    network={EARN_NETWORK}
                                    onClick={null}
                                />
                            }
                            content={{
                                topLeft: (
                                    <IconButton
                                        variant="on_light"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_to_currency_change_clicked',
                                            })
                                        }
                                    >
                                        {({ color }) =>
                                            toCurrency ? (
                                                <Row spacing={8}>
                                                    <CurrencyAvatar
                                                        key={toCurrency.id}
                                                        rightBadge={() => null}
                                                        currency={toCurrency}
                                                        size={32}
                                                    />
                                                    <Row spacing={4}>
                                                        <Text
                                                            variant="title3"
                                                            color="textPrimary"
                                                            weight="medium"
                                                        >
                                                            {toCurrency.code}
                                                        </Text>

                                                        <LightArrowDown2
                                                            size={20}
                                                            color={color}
                                                        />
                                                    </Row>
                                                </Row>
                                            ) : (
                                                <Row spacing={8}>
                                                    <Avatar size={32}>
                                                        <QuestionCircle
                                                            size={32}
                                                            color={color}
                                                        />
                                                    </Avatar>
                                                    <Row spacing={4}>
                                                        <Text
                                                            variant="title3"
                                                            color="textPrimary"
                                                            weight="medium"
                                                        >
                                                            <FormattedMessage
                                                                id="earn.withdraw.select_to_token"
                                                                defaultMessage="Select token"
                                                            />
                                                        </Text>
                                                        <LightArrowDown2
                                                            size={20}
                                                            color={color}
                                                        />
                                                    </Row>
                                                </Row>
                                            )
                                        }
                                    </IconButton>
                                ),
                                topRight: () =>
                                    to
                                        ? (() => {
                                              switch (pollable.type) {
                                                  case 'subsequent_failed':
                                                  case 'loaded':
                                                      return (
                                                          <Row spacing={0}>
                                                              <Spacer />
                                                              <Text
                                                                  variant="title3"
                                                                  weight="medium"
                                                                  color="textPrimary"
                                                              >
                                                                  <FormattedMoneyPrecise
                                                                      withSymbol={
                                                                          false
                                                                      }
                                                                      sign={
                                                                          null
                                                                      }
                                                                      money={to}
                                                                  />
                                                              </Text>
                                                          </Row>
                                                      )
                                                  case 'reloading':
                                                      return (
                                                          <AmountInput.InputSkeleton />
                                                      )

                                                  default:
                                                      return notReachable(
                                                          pollable
                                                      )
                                              }
                                          })()
                                        : null,
                                bottomLeft: toCurrencyBalance ? (
                                    <Text
                                        color="textSecondary"
                                        variant="paragraph"
                                        weight="regular"
                                    >
                                        <FormattedMessage
                                            id="currency.swap.max_label"
                                            defaultMessage="Balance: {amount}"
                                            values={{
                                                amount: (
                                                    <FormattedMoneyPrecise
                                                        withSymbol={false}
                                                        sign={null}
                                                        money={
                                                            toCurrencyBalance
                                                        }
                                                    />
                                                ),
                                            }}
                                        />
                                    </Text>
                                ) : null,
                            }}
                            state="normal"
                        />
                    </Column>

                    <Spacer />

                    <Actions variant="default">
                        <Button
                            variant="primary"
                            size="regular"
                            disabled={!!errors.submit}
                            onClick={() => {
                                validationResult.tap(
                                    (earnWithdrawalRequest) => {
                                        onMsg({
                                            type: 'on_form_submit',
                                            earnWithdrawalRequest,
                                        })
                                    }
                                )
                            }}
                        >
                            <CTAMessage error={errors.submit} />
                        </Button>
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}

const CTAMessage = ({ error }: { error?: SubmitError }) => {
    if (!error) {
        return (
            <FormattedMessage
                id="earn.withdraw.withdraw"
                defaultMessage="Withdraw"
            />
        )
    }

    switch (error.type) {
        case 'pollable_reloading':
        case 'pollable_subsequent_failed':
            return (
                <FormattedMessage
                    id="earn.withdraw.loading"
                    defaultMessage="Loading"
                />
            )
        case 'amount_required':
            return (
                <FormattedMessage
                    id="earn.withdraw.enter_amount"
                    defaultMessage="Enter Amount"
                />
            )
        case 'not_enough_balance':
            return (
                <FormattedMessage
                    id="earn.withdraw.not_enough_balance"
                    defaultMessage="Not enough balance"
                />
            )
        case 'no_routes_found':
            return (
                <FormattedMessage
                    id="earn.withdraw.no_routes_found"
                    defaultMessage="No routes found"
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}
