import { FormattedMessage } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { SelectToken } from '@zeal/domains/Token/components/SelectToken'

type Props = {
    owner: Account
    toCurrencies: CryptoCurrency[]
    ownerPortfolio: ServerPortfolio2
    toCurrency: CryptoCurrency | null
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_to_currency_selected'; toCurrency: CryptoCurrency }

export const SelectToCurrency = ({
    toCurrencies,
    onMsg,
    owner,
    currencyHiddenMap,
    currencyPinMap,
    networkMap,
    ownerPortfolio,
    toCurrency,
}: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            aria-labelledby="select-currency-label"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <UIActionBar
                top={<ActionBarAccountIndicator account={owner} />}
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="textPrimary"
                            >
                                <FormattedMessage
                                    id="earn.withdraw.select-currency.title"
                                    defaultMessage="Select token"
                                />
                            </Text>
                        </Row>
                    </Clickable>
                }
            />

            <Column fill shrink spacing={16}>
                <SelectToken
                    cryptoCurrencies={toCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    networkMap={networkMap}
                    serverPortfolio={ownerPortfolio}
                    selectedCurrency={toCurrency}
                    onCryptoCurrencySelected={(currency) =>
                        onMsg({
                            type: 'on_to_currency_selected',
                            toCurrency: currency,
                        })
                    }
                />
            </Column>
        </Screen>
    )
}
