import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { ConfiguredEarn, EarnWithdrawRequest } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { SelectEarnAccount } from './SelectEarnAccount'
import { SelectToCurrency } from './SelectToCurrency'
import { SubmitWithdrawal } from './SubmitWithdrawal'

type Props = {
    state: State
    owner: Account
    earn: ConfiguredEarn
    toCurrencies: CryptoCurrency[]
    ownerPortfolio: ServerPortfolio2
    toCurrency: CryptoCurrency | null
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type State =
    | { type: 'closed' }
    | { type: 'submit_withdrawal'; earnWithdrawalRequest: EarnWithdrawRequest }
    | { type: 'select_to_currency' }
    | { type: 'select_earn_account' }

type Msg =
    | MsgOf<typeof SelectToCurrency>
    | MsgOf<typeof SubmitWithdrawal>
    | MsgOf<typeof SelectEarnAccount>

export const Modal = ({
    earn,
    owner,
    ownerPortfolio,
    toCurrencies,
    state,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keystoreMap,
    networkMap,
    networkRPCMap,
    sessionPassword,
    currencyHiddenMap,
    currencyPinMap,
    toCurrency,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'select_earn_account':
            return (
                <UIModal>
                    <SelectEarnAccount earn={earn} onMsg={onMsg} />
                </UIModal>
            )

        case 'submit_withdrawal':
            return (
                <UIModal>
                    <SubmitWithdrawal
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        accountsMap={accountsMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        installationId={installationId}
                        keystoreMap={keystoreMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        ownerPortfolio={ownerPortfolio}
                        sessionPassword={sessionPassword}
                        earnWithdrawalRequest={state.earnWithdrawalRequest}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'select_to_currency':
            return (
                <UIModal>
                    <SelectToCurrency
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        networkMap={networkMap}
                        toCurrency={toCurrency}
                        onMsg={onMsg}
                        owner={owner}
                        ownerPortfolio={ownerPortfolio}
                        toCurrencies={toCurrencies}
                    />
                </UIModal>
            )

        default:
            return notReachable(state)
    }
}
