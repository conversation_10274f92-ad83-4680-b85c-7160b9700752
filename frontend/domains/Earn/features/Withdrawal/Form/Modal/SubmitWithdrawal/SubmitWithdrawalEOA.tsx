import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { EarnWithdrawRequest } from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { createWithdrawalTransaction } from '@zeal/domains/Earn/helpers/createWithdrawalTransaction'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    earnWithdrawalRequest: EarnWithdrawRequest

    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolio: ServerPortfolio2
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig

    actionSource: ActionSource2
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_earn_withdrawal_success' }
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_predefined_fee_preset_selected'
          }
      >

type State =
    | { type: 'submit_withdrawal'; withdrawalTransaction: EthSendTransaction }
    | { type: 'submit_approval'; approvalTransaction: EthSendTransaction }
    | { type: 'submit_swap' }

export const SubmitWithdrawalEOA = ({
    accountsMap,
    earnWithdrawalRequest,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keystoreMap,
    networkMap,
    networkRPCMap,
    portfolio,
    sessionPassword,
    defaultCurrencyConfig,
    actionSource,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>(() => ({
        type: 'submit_withdrawal',
        withdrawalTransaction: createWithdrawalTransaction({
            holderAddress: earnWithdrawalRequest.earn
                .holder as Web3.address.Address,
            takerAddress: earnWithdrawalRequest.taker
                .address as Web3.address.Address,
            investmentAssetAmount: earnWithdrawalRequest.investmentAssetAmount,
            ownerAddress: earnWithdrawalRequest.owner
                .address as Web3.address.Address,
        }),
    }))

    switch (state.type) {
        case 'submit_withdrawal':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    sendTransactionRequests={[state.withdrawalTransaction]}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={portfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    fetchSimulationByRequest={async () => ({
                        type: 'simulated',
                        simulation: {
                            transaction: {
                                type: 'earn_withdraw',
                                request: earnWithdrawalRequest,
                                state: 'withdraw',
                            },
                            currencies: {},
                            checks: [],
                        },
                    })}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'earn_withdraw',
                            request: earnWithdrawalRequest,
                            state: 'withdraw',
                        },
                        currencies: {},
                    })}
                    key={state.type}
                    sessionPassword={sessionPassword}
                    account={earnWithdrawalRequest.owner}
                    network={EARN_NETWORK}
                    networkRPCMap={networkRPCMap}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    installationId={installationId}
                    state={{ type: 'maximised' }}
                    actionSource={actionSource}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_safe_transaction_failure_accepted':
                                onMsg({ type: 'close' })
                                break

                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'transaction_request_replaced':
                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in earn withdrawal $${msg.type}`
                                    )
                                )
                                break

                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break

                            case 'on_completed_safe_transaction_close_click':
                            case 'on_completed_transaction_close_click':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                                setState(
                                    earnWithdrawalRequest.swapRoute
                                        .approvalTransaction
                                        ? {
                                              type: 'submit_approval',
                                              approvalTransaction:
                                                  earnWithdrawalRequest
                                                      .swapRoute
                                                      .approvalTransaction,
                                          }
                                        : { type: 'submit_swap' }
                                )
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'submit_approval':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={portfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    fetchSimulationByRequest={async () => ({
                        type: 'simulated',
                        simulation: {
                            transaction: {
                                type: 'earn_withdraw',
                                request: earnWithdrawalRequest,
                                state: 'approval',
                            },
                            currencies: {},
                            checks: [],
                        },
                    })}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'earn_withdraw',
                            request: earnWithdrawalRequest,
                            state: 'withdraw',
                        },
                        currencies: {},
                    })}
                    key={state.type}
                    sendTransactionRequests={[state.approvalTransaction]}
                    sessionPassword={sessionPassword}
                    account={earnWithdrawalRequest.owner}
                    network={EARN_NETWORK}
                    networkRPCMap={networkRPCMap}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    installationId={installationId}
                    state={{ type: 'maximised' }}
                    actionSource={actionSource}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_safe_transaction_failure_accepted':
                                onMsg({ type: 'close' })
                                break

                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'transaction_request_replaced':
                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in earn withdrawal $${msg.type}`
                                    )
                                )
                                break

                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break

                            case 'on_completed_safe_transaction_close_click':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_completed_transaction_close_click':
                                setState({ type: 'submit_swap' })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit_swap':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={portfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    fetchSimulationByRequest={async () => ({
                        type: 'simulated',
                        simulation: {
                            transaction: {
                                type: 'earn_withdraw',
                                request: earnWithdrawalRequest,
                                state: 'swap',
                            },
                            currencies: {},
                            checks: [],
                        },
                    })}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'earn_withdraw',
                            request: earnWithdrawalRequest,
                            state: 'withdraw',
                        },
                        currencies: {},
                    })}
                    key={state.type}
                    sendTransactionRequests={[
                        earnWithdrawalRequest.swapRoute.swapTransaction,
                    ]}
                    sessionPassword={sessionPassword}
                    account={earnWithdrawalRequest.owner}
                    network={EARN_NETWORK}
                    networkRPCMap={networkRPCMap}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    installationId={installationId}
                    state={{ type: 'maximised' }}
                    actionSource={actionSource}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_safe_transaction_failure_accepted':
                                onMsg({ type: 'close' })
                                break

                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in earn withdrawal $${msg.type}`
                                    )
                                )
                                break

                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break

                            case 'on_completed_safe_transaction_close_click':
                            case 'on_completed_transaction_close_click':
                                onMsg({ type: 'on_earn_withdrawal_success' })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        default:
            return notReachable(state)
    }
}
