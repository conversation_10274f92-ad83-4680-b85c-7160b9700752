import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { EarnWithdrawRequest } from '@zeal/domains/Earn'
import { createWithdrawalTransaction } from '@zeal/domains/Earn/helpers/createWithdrawalTransaction'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { SubmitWithdrawalEOA } from './SubmitWithdrawalEOA'

type Props = {
    earnWithdrawalRequest: EarnWithdrawRequest

    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    ownerPortfolio: ServerPortfolio2
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'on_earn_withdrawal_success' }
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'on_4337_gas_currency_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
          }
      >
    | MsgOf<typeof SubmitWithdrawalEOA>

const EARN_WITHDRAWAL_ACTION_SOURCE: ActionSource2 = {
    type: 'internal',
    transactionEventSource: 'earnWithdrawal',
}

export const SubmitWithdrawal = ({
    accountsMap,
    earnWithdrawalRequest,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keystoreMap,
    networkMap,
    networkRPCMap,
    onMsg,
    ownerPortfolio,
    sessionPassword,
    defaultCurrencyConfig,
}: Props) => {
    const owner = earnWithdrawalRequest.owner
    const keyStore = getKeyStore({
        keyStoreMap: keystoreMap,
        address: owner.address,
    })

    switch (keyStore.type) {
        case 'safe_4337':
            const withdrawTransactionRequest = createWithdrawalTransaction({
                holderAddress: earnWithdrawalRequest.earn
                    .holder as Web3.address.Address,
                takerAddress: earnWithdrawalRequest.taker
                    .address as Web3.address.Address,
                investmentAssetAmount:
                    earnWithdrawalRequest.investmentAssetAmount,
                ownerAddress: earnWithdrawalRequest.owner
                    .address as Web3.address.Address,
            })

            const route = earnWithdrawalRequest.swapRoute
            const transactionsToBundle: NonEmptyArray<EthSendTransaction> =
                route.approvalTransaction
                    ? [
                          withdrawTransactionRequest,
                          route.approvalTransaction,
                          route.swapTransaction,
                      ]
                    : [withdrawTransactionRequest, route.swapTransaction]

            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    feePresetMap={feePresetMap}
                    fetchSimulationByRequest={async () => ({
                        type: 'simulated',
                        simulation: {
                            transaction: {
                                type: 'earn_withdraw',
                                request: earnWithdrawalRequest,
                                state: 'swap',
                            },
                            currencies: {},
                            checks: [],
                        },
                    })}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'earn_withdraw',
                            request: earnWithdrawalRequest,
                            state: 'swap',
                        },
                        currencies: {},
                    })}
                    network={route.network}
                    networkRPCMap={networkRPCMap}
                    account={owner}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    networkMap={networkMap}
                    sessionPassword={sessionPassword}
                    sendTransactionRequests={transactionsToBundle}
                    portfolio={ownerPortfolio}
                    state={{ type: 'maximised' }}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    actionSource={EARN_WITHDRAWAL_ACTION_SOURCE}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_safe_transaction_failure_accepted':
                                onMsg({ type: 'close' })
                                break

                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in earn withdrawal $${msg.type}`
                                    )
                                )
                                break

                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break

                            case 'on_completed_safe_transaction_close_click':
                            case 'on_completed_transaction_close_click':
                                onMsg({ type: 'on_earn_withdrawal_success' })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'track_only':
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
            return (
                <SubmitWithdrawalEOA
                    actionSource={EARN_WITHDRAWAL_ACTION_SOURCE}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    earnWithdrawalRequest={earnWithdrawalRequest}
                    sessionPassword={sessionPassword}
                    accountsMap={accountsMap}
                    keystoreMap={keystoreMap}
                    portfolio={ownerPortfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}
