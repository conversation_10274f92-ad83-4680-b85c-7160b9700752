import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Skeleton as UISkeleton } from '@zeal/uikit/Skeleton'
import { Spacer } from '@zeal/uikit/Spacer'

import { noop, notReachable } from '@zeal/toolkit'
import {
    fromFixedWithFraction,
    toFixedWithFraction,
} from '@zeal/toolkit/BigInt'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { CardConfig } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { fetchCurrenciesMatrix } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { fetchSwapQuote } from '@zeal/domains/Currency/domains/SwapQuote/api/fetchSwapQuote'
import { ConfiguredEarn, Taker } from '@zeal/domains/Earn'
import { fetchTakerPortfolio } from '@zeal/domains/Earn/api/fetchTakerPortfolio'
import {
    EARN_BASE_ASSET_MAP,
    EARN_NETWORK,
    EARN_SLIPPAGE_PERCENT_MAP,
} from '@zeal/domains/Earn/constants'
import { correctMaxWithdrawalAmount } from '@zeal/domains/Earn/helpers/correctMaxWithdrawalAmount'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { revert2 } from '@zeal/domains/FXRate/helpers/revert'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Money2 } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { FancyButton } from '@zeal/domains/Network/components/FancyButton'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { fetchServerPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Form } from './Form'
import { Data, Form as PollableForm } from './Form/validation'

type Props = {
    earn: ConfiguredEarn
    taker: Taker
    owner: Account
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap

    sessionPassword: string
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardConfig: CardConfig
    onMsg: (msg: Msg) => void
}

const REVERT_RATE_EXTRA_PRECISION = 36

const fetch = async ({
    taker,
    amount,
    networkRPCMap,
    owner,
    networkMap,
    toCurrency,
    defaultCurrencyConfig,
    installationId,
    keyStoreMap,
    currencyHiddenMap,
    signal,
}: PollableForm & { signal?: AbortSignal }): Promise<Data> => {
    const [ownerPortfolio, takerPortfolio, matrix] = await Promise.all([
        fetchServerPortfolio2({
            address: owner.address,
            defaultCurrencyConfig,
            networkMap,
            currencyHiddenMap,
            installationId,
            networkRPCMap,
            signal,
        }),
        fetchTakerPortfolio({
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            taker,
            signal,
        }),
        fetchCurrenciesMatrix(),
    ])

    const toCurrencyIds =
        matrix.currencies[EARN_NETWORK.hexChainId]?.[EARN_NETWORK.hexChainId]
            ?.to || []

    const toCurrencies = toCurrencyIds
        .map((id): CryptoCurrency | null => {
            const currency = matrix.knownCurrencies[id] || null

            if (!currency) {
                return null
            }

            switch (currency.type) {
                case 'FiatCurrency':
                    return null
                case 'CryptoCurrency':
                    return currency

                default:
                    return notReachable(currency)
            }
        })
        .filter((currency) => currency !== null)

    const takerUserCurrencyRate = takerPortfolio.userCurrencyRate
    const takerBalance = takerPortfolio.assetBalance
    const userCurrency = takerUserCurrencyRate.quote

    const userAmount = {
        amount: amount
            ? fromFixedWithFraction(amount, userCurrency.fraction)
            : 0n,
        currency: userCurrency,
    } as Money2

    const reverseRate = revert2({
        rate: takerUserCurrencyRate,
        extraPrecision: REVERT_RATE_EXTRA_PRECISION,
    })

    const investmentAssetAmount = applyRate2({
        baseAmount: userAmount,
        rate: reverseRate,
        extraRatePrecision: REVERT_RATE_EXTRA_PRECISION,
    })

    const correctedInvestmentAssetAmount = correctMaxWithdrawalAmount({
        investmentAssetAmount,
        investmentAssetBalance: takerBalance,
    })

    const swapQuote = await fetchSwapQuote({
        amount: toFixedWithFraction(
            correctedInvestmentAssetAmount.amount,
            correctedInvestmentAssetAmount.currency.fraction
        ),
        fromAccount: owner,
        fromCurrency: taker.cryptoCurrency,
        networkMap,
        recipient: owner.address as Web3.address.Address,
        swapSlippagePercent: EARN_SLIPPAGE_PERCENT_MAP[taker.type],
        toCurrency,
        networkRPCMap,
        usedDexName: null,
        defaultCurrencyConfig,
        keyStoreMap,
    })

    return {
        ownerPortfolio,
        takerBalance,
        toCurrencies,
        swapQuote,
        takerUserCurrencyRate,
        investmentAssetAmount: correctedInvestmentAssetAmount.amount,
    }
}

type Msg =
    | Extract<
          MsgOf<typeof Form>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_earn_withdrawal_success'
                  | 'on_predefined_fee_preset_selected'
          }
      >
    | { type: 'close' }

const POLL_INTERVAL_MS = 60_000

const getDefaultWithdrawalCurrency = ({
    taker,
    cardConfig,
}: {
    taker: Taker
    cardConfig: CardConfig
}): CryptoCurrency => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
        case 'card_readonly_signer_address_is_selected':
            return EARN_BASE_ASSET_MAP[taker.type]

        case 'card_readonly_signer_address_is_selected_fully_onboarded':
            return cardConfig.currency

        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}

export const Withdrawal = ({
    networkMap,
    networkRPCMap,
    owner,
    taker,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keystoreMap,
    sessionPassword,
    earn,
    currencyHiddenMap,
    currencyPinMap,
    defaultCurrencyConfig,
    cardConfig,
    onMsg,
}: Props) => {
    const captureErrorOnce = useCaptureErrorOnce()

    const defaultCurrency = getDefaultWithdrawalCurrency({
        taker,
        cardConfig,
    })

    const [pollable, setPollable] = usePollableData<Data, PollableForm>(
        fetch,
        {
            type: 'loading',
            params: {
                currencyHiddenMap,
                amount: null,
                toCurrency: defaultCurrency,
                networkMap,
                networkRPCMap,
                owner,
                taker,
                earn,
                installationId,
                defaultCurrencyConfig,
                keyStoreMap: keystoreMap,
            },
        },
        {
            pollIntervalMilliseconds: POLL_INTERVAL_MS,
        }
    )

    useEffect(() => {
        switch (pollable.type) {
            case 'loading':
            case 'loaded':
            case 'reloading':
            case 'error':
                break
            case 'subsequent_failed':
                captureErrorOnce(pollable.error)
                break
            default:
                return notReachable(pollable)
        }
    }, [captureErrorOnce, pollable])

    switch (pollable.type) {
        case 'loading':
            return (
                <Skeleton
                    owner={owner}
                    onClose={() => onMsg({ type: 'close' })}
                />
            )

        case 'error':
            const parsed = parseAppError(pollable.error)
            return (
                <>
                    <Skeleton
                        owner={owner}
                        onClose={() => onMsg({ type: 'close' })}
                    />

                    <AppErrorPopup
                        error={parsed}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg({ type: 'close' })
                                    break
                                case 'try_again_clicked':
                                    setPollable({
                                        type: 'loading',
                                        params: pollable.params,
                                    })
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        case 'loaded':
        case 'reloading':
        case 'subsequent_failed':
            return (
                <Form
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    pollable={pollable}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_earn_account_selected':
                                setPollable({
                                    type: 'reloading',
                                    data: {
                                        ...pollable.data,
                                        swapQuote: {
                                            ...pollable.data.swapQuote,
                                            bestReturnRoute: null,
                                            routes: [],
                                        },
                                    },
                                    params: {
                                        ...pollable.params,
                                        taker: msg.taker,
                                    },
                                })
                                break
                            case 'import_keys_button_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_predefined_fee_preset_selected':
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_earn_withdrawal_success':
                                postUserEvent({
                                    type: 'EarnWithdrawCompletedEvent',
                                    asset: taker.type,
                                    installationId,
                                })
                                onMsg(msg)
                                break

                            case 'on_to_currency_selected':
                                setPollable({
                                    type: 'reloading',
                                    data: pollable.data,
                                    params: {
                                        ...pollable.params,
                                        toCurrency: msg.toCurrency,
                                    },
                                })
                                break

                            case 'on_from_amount_changed':
                                setPollable({
                                    type: 'reloading',
                                    data: pollable.data,
                                    params: {
                                        ...pollable.params,
                                        amount: msg.fromAmount,
                                    },
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        default:
            return notReachable(pollable)
    }
}

export const Skeleton = ({
    onClose,
    owner,
}: {
    owner: Account
    onClose: () => void
}) => {
    return (
        <Screen background="light" padding="form" onNavigateBack={onClose}>
            <ActionBar
                top={<ActionBarAccountIndicator account={owner} />}
                left={
                    <Clickable onClick={onClose}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <ActionBar.Header>
                                <FormattedMessage
                                    id="earn.withdraw_form.title"
                                    defaultMessage="Withdraw from Earn"
                                />
                            </ActionBar.Header>
                        </Row>
                    </Clickable>
                }
            />

            <Column spacing={4} fill>
                <AmountInput
                    content={{
                        topLeft: (
                            <IconButton variant="on_light" onClick={noop}>
                                {() => (
                                    <Row spacing={4}>
                                        <UISkeleton
                                            variant="default"
                                            width={24}
                                            height={24}
                                        />

                                        <UISkeleton
                                            variant="default"
                                            width={75}
                                            height={24}
                                        />
                                    </Row>
                                )}
                            </IconButton>
                        ),
                        topRight: () => <AmountInput.InputSkeleton />,
                        bottomLeft: (
                            <UISkeleton
                                variant="default"
                                width={105}
                                height={18}
                            />
                        ),
                    }}
                    state="normal"
                />

                <NextStepSeparator />

                <AmountInput
                    top={
                        <FancyButton
                            fill
                            rounded={false}
                            network={EARN_NETWORK}
                            onClick={null}
                        />
                    }
                    content={{
                        topLeft: (
                            <IconButton variant="on_light" onClick={noop}>
                                {() => (
                                    <Row spacing={4}>
                                        <UISkeleton
                                            variant="default"
                                            width={24}
                                            height={24}
                                        />

                                        <UISkeleton
                                            variant="default"
                                            width={75}
                                            height={24}
                                        />
                                    </Row>
                                )}
                            </IconButton>
                        ),
                        topRight: () => <AmountInput.InputSkeleton />,
                        bottomLeft: (
                            <UISkeleton
                                variant="default"
                                width={105}
                                height={18}
                            />
                        ),
                    }}
                    state="normal"
                />
            </Column>

            <Spacer />

            <Actions variant="default">
                <UISkeleton variant="default" width="100%" height={42} />
            </Actions>
        </Screen>
    )
}
