import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { Earn, Taker } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { EarnEventLocation } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State } from './Modal'

type Props = {
    initialTaker: Taker
    eventLocation: EarnEventLocation
    fromCurrencies: CryptoCurrency[]
    initialAmount: CryptoMoney
    networkMap: NetworkMap
    earn: Earn
    earnOwner: Account
    portfolioMap: PortfolioMap
    fromAccount: Account

    serverPortfolio: ServerPortfolio2

    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap

    swapsIOContractsMap: SwapsIOContractsMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_deposit_success'
                  | 'on_earn_configured'
                  | 'on_swaps_io_swap_request_created'
          }
      >
    | Extract<MsgOf<typeof Modal>, { type: 'on_account_selected' }>

export const DepositForm = ({
    accountsMap,
    portfolioMap,
    earnOwner,
    initialTaker,
    fromCurrencies,
    initialAmount,
    fromAccount,
    defaultCurrencyConfig,
    feePresetMap,
    networkMap,
    installationId,
    gasCurrencyPresetMap,
    keystoreMap,
    sessionPassword,
    networkRPCMap,
    currencyPinMap,
    currencyHiddenMap,
    swapsIOContractsMap,
    serverPortfolio,

    eventLocation,
    earn,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'closed' })

    return (
        <>
            <Layout
                initialAmount={initialAmount}
                defaultCurrencyConfig={defaultCurrencyConfig}
                earn={earn}
                portfolioMap={portfolioMap}
                earnOwner={earnOwner}
                currencyHiddenMap={currencyHiddenMap}
                currencyPinMap={currencyPinMap}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                installationId={installationId}
                keystoreMap={keystoreMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                sessionPassword={sessionPassword}
                initialTaker={initialTaker}
                fromAccount={fromAccount}
                fromCurrencies={fromCurrencies}
                fromAccountPortfolio={serverPortfolio}
                eventLocation={eventLocation}
                swapsIOContractsMap={swapsIOContractsMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_4337_gas_currency_selected':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_earn_configured':
                        case 'on_swaps_io_swap_request_created':
                            onMsg(msg)
                            break
                        case 'on_earn_deposit_success':
                            postUserEvent({
                                type: 'EarnDepositCompletedEvent',
                                source: 'zeal',
                                asset: initialTaker.type,
                                installationId,
                            })
                            onMsg(msg)
                            break

                        case 'on_from_account_click':
                            setState({ type: 'select_from_account' })
                            break
                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
            <Modal
                installationId={installationId}
                earnOwner={earnOwner}
                state={state}
                fromAccount={fromAccount}
                accountsMap={accountsMap}
                keystoreMap={keystoreMap}
                portfolioMap={portfolioMap}
                currencyHiddenMap={currencyHiddenMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setState({ type: 'closed' })
                            break
                        case 'on_account_selected':
                            postUserEvent({
                                type: 'EarnDepositFromAccountChanged',
                                installationId,
                            })
                            setState({ type: 'closed' })
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
