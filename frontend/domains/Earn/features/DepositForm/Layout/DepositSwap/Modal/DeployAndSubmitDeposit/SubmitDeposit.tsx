import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import {
    ConfiguredEarn,
    DeployedTaker,
    EarnDepositWithSwapRequest,
} from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { SubmitDepositEOA } from './SubmitDepositEOA'

type Props = {
    taker: DeployedTaker
    earnOwner: Account
    earnDepositRequest: EarnDepositWithSwapRequest

    earn: ConfiguredEarn
    sessionPassword: string
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string

    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | MsgOf<typeof SubmitDepositEOA>
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_safe_transaction_failure_accepted'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_wrong_network_accepted'
          }
      >

const EARN_DEPOSIT_ACTION_SOURCE: ActionSource2 = {
    type: 'internal',
    transactionEventSource: 'earnDeposit',
}

export const SubmitDeposit = ({
    taker,
    earnOwner,
    sessionPassword,
    accountsMap,
    earn,
    keyStoreMap,
    installationId,
    earnDepositRequest,
    networkMap,
    networkRPCMap,
    feePresetMap,
    onMsg,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
}: Props) => {
    const keyStore = getKeyStore({
        keyStoreMap: keyStoreMap,
        address: earnDepositRequest.fromAccount.address,
    })

    switch (keyStore.type) {
        case 'safe_4337':
            const route = earnDepositRequest.swapRoute
            const transactionsToBundle: NonEmptyArray<EthSendTransaction> =
                route.approvalTransaction
                    ? [route.approvalTransaction, route.swapTransaction]
                    : [route.swapTransaction]

            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    fetchSimulationByRequest={async () => {
                        return {
                            type: 'simulated',
                            simulation: {
                                currencies: {},
                                transaction: {
                                    type: 'earn_deposit_with_swap',
                                    earnDepositRequest,
                                    takerApyMap: earn.takerApyMap,
                                },
                                checks: [],
                            },
                        }
                    }}
                    fetchTransactionResultByRequest={async () => ({
                        currencies: {},
                        transaction: {
                            type: 'earn_deposit_with_swap',
                            earnDepositRequest,
                            takerApyMap: earn.takerApyMap,
                        },
                    })}
                    network={route.network}
                    networkRPCMap={networkRPCMap}
                    account={earnDepositRequest.fromAccount}
                    accounts={accountsMap}
                    keystores={keyStoreMap}
                    networkMap={networkMap}
                    sessionPassword={sessionPassword}
                    portfolio={earnDepositRequest.fromAccountPortfolio}
                    state={{ type: 'maximised' }}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    actionSource={EARN_DEPOSIT_ACTION_SOURCE}
                    sendTransactionRequests={transactionsToBundle}
                    feePresetMap={feePresetMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'cancel_submitted':
                            case 'on_completed_transaction_close_click':
                            case 'transaction_failure_accepted':
                            case 'transaction_submited':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'transaction_request_replaced':
                                noop() // Not relevant to smart wallet
                                break
                            case 'on_minimize_click':
                                onMsg({ type: 'close' })
                                break
                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in earn deposit 4337 $${msg.type}`
                                    )
                                )
                                break
                            case 'on_user_operation_bundled':
                                noop()
                                break
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_wrong_network_accepted':
                                onMsg(msg)
                                break
                            case 'on_completed_safe_transaction_close_click':
                                onMsg({
                                    type: 'on_earn_deposit_success',
                                    earnOwner,
                                    configuredEarn: earn,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'track_only':
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
            return (
                <SubmitDepositEOA
                    actionSource={EARN_DEPOSIT_ACTION_SOURCE}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    earn={earn}
                    earnOwner={earnOwner}
                    taker={taker}
                    earnDepositRequest={earnDepositRequest}
                    sessionPassword={sessionPassword}
                    accountsMap={accountsMap}
                    keystoreMap={keyStoreMap}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}
