import { useEffect, useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SwapQuoteRequest } from '@zeal/domains/Currency/domains/SwapQuote'
import { fetchMaxBalance } from '@zeal/domains/Currency/domains/SwapQuote/api/fetchMaxBalance'
import { Earn, EarnDepositSwapRouteRequest, Taker } from '@zeal/domains/Earn'
import { fetchEarnDepositSwapRoute } from '@zeal/domains/Earn/api/fetchEarnDepositSwapRoute'
import { EARN_SLIPPAGE_PERCENT_MAP } from '@zeal/domains/Earn/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    taker: Taker
    fromAmount: string | null
    fromAccountPortfolio: ServerPortfolio2
    fromAccount: Account
    fromCurrencies: CryptoCurrency[]
    fromCurrency: CryptoCurrency
    networkMap: NetworkMap
    earn: Earn
    earnOwner: Account
    portfolioMap: PortfolioMap

    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap

    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_earn_deposit_in_same_currancy_as_taker_selected'
          taker: Taker
          amount: string | null
          fromAccount: Account
      }
    | {
          type: 'on_earn_deposit_in_different_currency_on_another_network_selected'
          taker: Taker
          amount: string | null
          fromCurrency: CryptoCurrency
          fromAccount: Account
      }
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_deposit_success'
                  | 'on_earn_configured'
          }
      >
    | Extract<
          MsgOf<typeof Layout>,
          {
              type: 'close' | 'on_from_account_click'
          }
      >

const mapEarnDepositSwapRouteRequestToSwapQouteRequest = ({
    amount,
    fromAccount,
    fromCurrency,
    taker,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    keyStoreMap,
}: EarnDepositSwapRouteRequest): SwapQuoteRequest => ({
    amount,
    fromAccount,
    fromCurrency,
    networkMap,
    networkRPCMap,
    toCurrency: taker.cryptoCurrency,
    recipient: taker.address as Web3.address.Address,
    swapSlippagePercent: EARN_SLIPPAGE_PERCENT_MAP[taker.type],
    usedDexName: null,
    defaultCurrencyConfig,
    keyStoreMap,
})

const POLL_INTERVAL_MS = 60_000

export const DepositSwap = ({
    onMsg,
    earnOwner,
    fromAmount,
    fromAccountPortfolio,
    fromCurrencies,
    fromCurrency,
    portfolioMap,
    taker,
    fromAccount,
    networkMap,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    earn,
    keystoreMap,
    sessionPassword,
    networkRPCMap,
    currencyHiddenMap,
    currencyPinMap,
    defaultCurrencyConfig,
}: Props) => {
    const [pollable, setPollable] = usePollableData(
        fetchEarnDepositSwapRoute,
        {
            type: 'loading',
            params: {
                networkRPCMap,
                networkMap,
                amount: fromAmount,
                fromAccount,
                fromCurrency,
                taker,
                defaultCurrencyConfig,
                keyStoreMap: keystoreMap,
            },
        },
        { pollIntervalMilliseconds: POLL_INTERVAL_MS }
    )

    const captureErrorOnce = useCaptureErrorOnce()

    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    const takerPortfolio = earn.takerPortfolioMap[pollable.params.taker.type]

    const fromCurrencyBalance = getTokenByCryptoCurrency3({
        currency: pollable.params.fromCurrency,
        serverPortfolio: fromAccountPortfolio,
    })

    const swapQuoteRequest = mapEarnDepositSwapRouteRequestToSwapQouteRequest(
        pollable.params
    )

    const [maxBalanceLoadable, setMaxBalanceLoadable] = useLoadableData(
        fetchMaxBalance,
        {
            type: 'loading',
            params: {
                swapQuoteRequest: {
                    ...swapQuoteRequest,
                    amount: toFixedWithFraction(
                        fromCurrencyBalance.balance.amount,
                        fromCurrencyBalance.balance.currency.fraction
                    ),
                    keyStoreMap: keystoreMap,
                },
                portfolio: fromAccountPortfolio,
                gasCurrencyPresetMap,
                keyStore: getKeyStore({
                    address: fromAccount.address,
                    keyStoreMap: keystoreMap,
                }),
            },
        }
    )

    useEffect(() => {
        const fromBalance = getTokenByCryptoCurrency3({
            currency: swapQuoteRequest.fromCurrency,
            serverPortfolio: fromAccountPortfolio,
        })

        setMaxBalanceLoadable((old) => ({
            ...old,
            type: 'loading',
            params: {
                ...old.params,
                swapQuoteRequest: {
                    amount: toFixedWithFraction(
                        fromBalance.balance.amount,
                        fromBalance.balance.currency.fraction
                    ),
                    defaultCurrencyConfig:
                        swapQuoteRequest.defaultCurrencyConfig,
                    fromAccount: swapQuoteRequest.fromAccount,
                    networkMap: swapQuoteRequest.networkMap,
                    networkRPCMap: swapQuoteRequest.networkRPCMap,
                    recipient: swapQuoteRequest.recipient,
                    swapSlippagePercent: swapQuoteRequest.swapSlippagePercent,
                    usedDexName: swapQuoteRequest.usedDexName,
                    toCurrency: swapQuoteRequest.toCurrency,
                    fromCurrency: swapQuoteRequest.fromCurrency,
                    keyStoreMap: keystoreMap,
                },
                portfolio: fromAccountPortfolio,
            },
        }))
    }, [
        swapQuoteRequest.defaultCurrencyConfig,
        swapQuoteRequest.fromAccount,
        swapQuoteRequest.networkMap,
        swapQuoteRequest.networkRPCMap,
        swapQuoteRequest.recipient,
        swapQuoteRequest.swapSlippagePercent,
        swapQuoteRequest.usedDexName,
        swapQuoteRequest.toCurrency,
        swapQuoteRequest.fromCurrency,
        setMaxBalanceLoadable,
        fromAccountPortfolio,
        keystoreMap,
    ])

    useEffect(() => {
        switch (maxBalanceLoadable.type) {
            case 'error':
                captureError(maxBalanceLoadable.error, {
                    extra: { context: 'maxButton balance correction on swap' },
                })
                break

            case 'loaded':
            case 'loading':
                break

            /* istanbul ignore next */
            default:
                notReachable(maxBalanceLoadable)
        }
    }, [maxBalanceLoadable])

    useEffect(() => {
        switch (pollable.type) {
            case 'loading':
            case 'loaded':
            case 'reloading':
                break
            case 'error':
            case 'subsequent_failed':
                captureErrorOnce(pollable.error)
                break
            default:
                return notReachable(pollable)
        }
    }, [captureErrorOnce, pollable])

    return (
        <>
            <Layout
                installationId={installationId}
                networkMap={networkMap}
                takerUserCurrencyRate={
                    earn.takerPortfolioMap[pollable.params.taker.type]
                        .userCurrencyRate
                }
                fromAccountPortfolio={fromAccountPortfolio}
                takerPortfolio={takerPortfolio}
                pollable={pollable}
                maxBalanceLoadable={maxBalanceLoadable}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_from_account_click':
                            onMsg(msg)
                            break

                        case 'on_deposit_submited':
                            postUserEvent({
                                type: 'EarnDepositInitiatedEvent',
                                source: 'zeal',
                                asset: taker.type,
                                installationId,
                            })
                            setModal({
                                type: 'submit_deposit',
                                earnDepositRequest: msg.earnDepositRequest,
                            })
                            break
                        case 'on_earn_account_click':
                            setModal({ type: 'select_earn_account' })
                            break

                        case 'on_select_from_currency_click':
                            setModal({ type: 'select_from_currency' })
                            break

                        case 'on_amount_change':
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    amount: msg.amount,
                                },
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />

            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                fromAccountPortfolio={fromAccountPortfolio}
                earnOwner={earnOwner}
                earn={earn}
                portfolioMap={portfolioMap}
                currencyHiddenMap={currencyHiddenMap}
                currencyPinMap={currencyPinMap}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                installationId={installationId}
                keystoreMap={keystoreMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                sessionPassword={sessionPassword}
                state={modal}
                fromCurrencies={fromCurrencies}
                pollable={pollable}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_cancel_confirm_transaction_clicked':
                        case 'on_safe_transaction_failure_accepted':
                        case 'on_close_transaction_status_not_found_modal':
                        case 'on_transaction_cancelled_successfully_close_clicked':
                        case 'transaction_cancel_failure_accepted':
                        case 'cancel_submitted':
                        case 'transaction_failure_accepted':
                        case 'transaction_request_replaced':
                        case 'on_sign_cancel_button_clicked':
                            setModal({ type: 'closed' })
                            break
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_earn_configured':
                            onMsg(msg)
                            break

                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_submited':
                            noop()
                            break

                        case 'on_wrong_network_accepted':
                            setModal({ type: 'closed' })
                            captureError(
                                new ImperativeError(
                                    `${msg.type} should not appear in earn deposit flow`
                                )
                            )
                            break

                        case 'on_earn_deposit_success':
                            onMsg(msg)
                            setModal({ type: 'closed' })
                            break

                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_earn_account_selected': {
                            setModal({ type: 'closed' })

                            if (
                                msg.taker.cryptoCurrency.id ===
                                pollable.params.fromCurrency.id
                            ) {
                                onMsg({
                                    type: 'on_earn_deposit_in_same_currancy_as_taker_selected',
                                    taker: msg.taker,
                                    amount: pollable.params.amount,
                                    fromAccount: pollable.params.fromAccount,
                                })
                                break
                            }

                            if (
                                msg.taker.cryptoCurrency.networkHexChainId !==
                                pollable.params.fromCurrency.networkHexChainId
                            ) {
                                onMsg({
                                    type: 'on_earn_deposit_in_different_currency_on_another_network_selected',
                                    taker: msg.taker,
                                    amount: pollable.params.amount,
                                    fromCurrency: pollable.params.fromCurrency,
                                    fromAccount: pollable.params.fromAccount,
                                })
                                break
                            }

                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    taker: msg.taker,
                                },
                            })
                            break
                        }

                        case 'on_from_currency_selected': {
                            setModal({ type: 'closed' })

                            if (
                                msg.fromCurrency.id ===
                                pollable.params.taker.cryptoCurrency.id
                            ) {
                                onMsg({
                                    type: 'on_earn_deposit_in_same_currancy_as_taker_selected',
                                    taker: pollable.params.taker,
                                    amount: pollable.params.amount,
                                    fromAccount: pollable.params.fromAccount,
                                })
                                break
                            }

                            if (
                                msg.fromCurrency.networkHexChainId !==
                                pollable.params.taker.cryptoCurrency
                                    .networkHexChainId
                            ) {
                                onMsg({
                                    type: 'on_earn_deposit_in_different_currency_on_another_network_selected',
                                    taker: pollable.params.taker,
                                    amount: pollable.params.amount,
                                    fromCurrency: msg.fromCurrency,
                                    fromAccount: pollable.params.fromAccount,
                                })
                                break
                            }

                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    fromCurrency: msg.fromCurrency,
                                },
                            })
                            break
                        }

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
