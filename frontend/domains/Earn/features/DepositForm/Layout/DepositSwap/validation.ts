import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { values } from '@zeal/toolkit/Object'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import {
    CryptoCurrency,
    Currency,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    SwapQuoteRequest,
    SwapRoute,
} from '@zeal/domains/Currency/domains/SwapQuote'
import {
    EarnDepositSwapRouteRequest,
    EarnDepositWithSwapRequest,
} from '@zeal/domains/Earn'
import { FXRate2 } from '@zeal/domains/FXRate'
import { KeyStore } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

export type Pollable = PollableData<
    SwapRoute | null,
    EarnDepositSwapRouteRequest
>

export type MaxBalanceLoadable = LoadableData<
    CryptoMoney,
    {
        swapQuoteRequest: SwapQuoteRequest
        portfolio: ServerPortfolio2
        keyStore: KeyStore
        gasCurrencyPresetMap: GasCurrencyPresetMap
    }
>

type NoRoutesFoundError = { type: 'no_routes_found' }
type NotEnoughBalanceError = { type: 'not_enough_balance' }
type PollableReloading = { type: 'pollable_reloading' }
type PollableSubsequentFailed = { type: 'pollable_subsequent_failed' }
type AmountRequired = { type: 'amount_required' }

type SubmitError =
    | PollableReloading
    | PollableSubsequentFailed
    | NoRoutesFoundError
    | NotEnoughBalanceError
    | AmountRequired

export type FormError = {
    from?: NotEnoughBalanceError
    submit?: SubmitError
}

export const getMaxBalance = ({
    maxBalanceLoadable,
    portfolio,
}: {
    maxBalanceLoadable: MaxBalanceLoadable
    portfolio: ServerPortfolio2
}) => {
    switch (maxBalanceLoadable.type) {
        case 'loading':
        case 'error':
            return getBalanceByCryptoCurrency2({
                currency:
                    maxBalanceLoadable.params.swapQuoteRequest.fromCurrency,
                serverPortfolio: portfolio,
            })

        case 'loaded':
            return maxBalanceLoadable.data
        /* istanbul ignore next */
        default:
            return notReachable(maxBalanceLoadable)
    }
}

const validateFromBalance = ({
    pollable,
    maxBalanceLoadable,
    fromAccountPortfolio,
}: {
    pollable: Pollable
    maxBalanceLoadable: MaxBalanceLoadable
    fromAccountPortfolio: ServerPortfolio2
}): Result<NotEnoughBalanceError, CryptoMoney> => {
    const balance = getMaxBalance({
        portfolio: fromAccountPortfolio,
        maxBalanceLoadable,
    })

    const amount = fromFixedWithFraction(
        pollable.params.amount,
        pollable.params.fromCurrency.fraction
    )

    if (balance.amount < amount) {
        return failure({ type: 'not_enough_balance' })
    }

    return success({ amount, currency: pollable.params.fromCurrency })
}

const validateFromToken = ({
    pollable,
}: {
    pollable: Pollable
}): Result<AmountRequired, unknown> => {
    const currency = pollable.params.fromCurrency

    if (
        !pollable.params.amount ||
        fromFixedWithFraction(pollable.params.amount, currency.fraction) === 0n
    ) {
        return failure({ type: 'amount_required' })
    }

    return success(pollable.params.amount)
}

const validateRoute = ({
    pollable,
}: {
    pollable: Pollable
}): Result<
    NoRoutesFoundError | PollableReloading | PollableSubsequentFailed,
    SwapRoute
> => {
    switch (pollable.type) {
        case 'loaded': {
            return pollable.data
                ? success(pollable.data)
                : failure({ type: 'no_routes_found' })
        }

        case 'loading':
        case 'reloading':
            return failure({ type: 'pollable_reloading' })

        case 'subsequent_failed':
        case 'error':
            return failure({ type: 'pollable_subsequent_failed' })

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}

export const validate = ({
    pollable,
    maxBalanceLoadable,
    takerUserCurrencyRate,
    fromAccountPortfolio,
}: {
    pollable: Pollable
    maxBalanceLoadable: MaxBalanceLoadable
    takerUserCurrencyRate: FXRate2<CryptoCurrency, Currency>
    fromAccountPortfolio: ServerPortfolio2
}): Result<FormError, EarnDepositWithSwapRequest> =>
    shape({
        from: validateFromBalance({
            pollable,
            maxBalanceLoadable,
            fromAccountPortfolio,
        }),
        submit: shape({
            from: validateFromToken({ pollable }).andThen(() =>
                validateFromBalance({
                    pollable,
                    maxBalanceLoadable,
                    fromAccountPortfolio,
                })
            ),
            swapRoute: validateRoute({ pollable }),
        }).mapError((error) => values(error)[0]),
    }).map(({ submit }) => ({
        swapRoute: submit.swapRoute,
        from: submit.from,
        taker: pollable.params.taker,
        fromAccount: pollable.params.fromAccount,
        fromAccountPortfolio,
        takerUserCurrencyRate,
    }))
