import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { Earn, EarnDepositDirectSendRequest, Taker } from '@zeal/domains/Earn'
import { SelectEarnAccount } from '@zeal/domains/Earn/components/SelectEarnAccount'
import { SelectToken } from '@zeal/domains/Earn/components/SelectToken'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { DeployAndSubmitDeposit } from './DeployAndSubmitDeposit'

type Props = {
    state: State
    portfolio: ServerPortfolio2
    fromCurrencies: CryptoCurrency[]
    fromAccount: Account
    taker: Taker

    earn: Earn
    earnOwner: Account
    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    portfolioMap: PortfolioMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof DeployAndSubmitDeposit>
    | MsgOf<typeof SelectToken>
    | MsgOf<typeof SelectEarnAccount>
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_wrong_network_accepted'
                  | 'transaction_failure_accepted'
                  | 'on_safe_transaction_failure_accepted'
                  | 'on_sign_cancel_button_clicked'
                  | 'transaction_submited'
                  | 'cancel_submitted'
                  | 'on_transaction_cancelled_successfully_close_clicked'
                  | 'transaction_cancel_failure_accepted'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'on_safe_transaction_completed_splash_animation_screen_competed'
                  | 'on_gas_currency_selected'
                  | 'on_close_transaction_status_not_found_modal'
                  | 'transaction_request_replaced'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >

export type State =
    | { type: 'closed' }
    | { type: 'select_from_currency' }
    | {
          type: 'submit_deposit'
          earnDepositDirectSend: EarnDepositDirectSendRequest
      }
    | { type: 'select_earn_account' }

export const Modal = ({
    state,
    taker,
    earnOwner,
    fromAccount,
    fromCurrencies,
    portfolio,
    gasCurrencyPresetMap,
    accountsMap,
    feePresetMap,
    earn,
    installationId,
    keystoreMap,
    networkMap,
    networkRPCMap,
    sessionPassword,
    currencyHiddenMap,
    currencyPinMap,
    portfolioMap,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'select_earn_account':
            return (
                <UIModal>
                    <SelectEarnAccount earn={earn} onMsg={onMsg} />
                </UIModal>
            )

        case 'select_from_currency':
            return (
                <UIModal>
                    <SelectToken
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        fromCurrency={taker.cryptoCurrency}
                        networkMap={networkMap}
                        fromAccount={fromAccount}
                        fromCurrencies={fromCurrencies}
                        fromAccountPortfolio={portfolio}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'submit_deposit':
            return (
                <UIModal>
                    <DeployAndSubmitDeposit
                        taker={taker}
                        earnDepositRequest={state.earnDepositDirectSend}
                        earn={earn}
                        earnOwner={earnOwner}
                        sessionPassword={sessionPassword}
                        accountsMap={accountsMap}
                        keyStoreMap={keystoreMap}
                        feePresetMap={feePresetMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        installationId={installationId}
                        portfolioMap={portfolioMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        default:
            return notReachable(state)
    }
}
