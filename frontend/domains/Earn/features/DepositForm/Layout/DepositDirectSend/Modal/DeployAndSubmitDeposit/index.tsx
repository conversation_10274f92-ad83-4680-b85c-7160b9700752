import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import {
    ConfiguredEarn,
    DeployedTaker,
    Earn,
    EarnDepositDirectSendRequest,
    NotDeployedTaker,
    Taker,
} from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { DeployEarn } from '../../../../DeployEarn'

type State =
    | {
          type: 'deploy_earn_account'
          taker: NotDeployedTaker
      }
    | {
          type: 'deposit'
          configuredEarn: ConfiguredEarn
          taker: DeployedTaker
      }

const calculateState = ({
    earn,
    taker,
}: {
    earn: Earn
    taker: Taker
}): State => {
    switch (earn.type) {
        case 'not_configured': {
            switch (taker.state) {
                case 'not_deployed':
                    return { type: 'deploy_earn_account', taker }
                case 'deployed':
                    throw new ImperativeError(
                        'Impossible state, taker is deployed while earn is not configured',
                        { taker, holder: earn.holder }
                    )

                default:
                    return notReachable(taker)
            }
        }
        case 'configured': {
            switch (taker.state) {
                case 'not_deployed':
                    return { type: 'deploy_earn_account', taker }

                case 'deployed':
                    return { type: 'deposit', configuredEarn: earn, taker }

                default:
                    return notReachable(taker)
            }
        }
        /* istanbul ignore next */
        default:
            return notReachable(earn)
    }
}

type Props = {
    taker: Taker
    earnDepositRequest: EarnDepositDirectSendRequest

    earn: Earn
    earnOwner: Account
    sessionPassword: string
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    portfolioMap: PortfolioMap

    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof DeployEarn>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_configured'
          }
      >
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_wrong_network_accepted'
                  | 'transaction_failure_accepted'
                  | 'on_safe_transaction_failure_accepted'
                  | 'on_sign_cancel_button_clicked'
                  | 'transaction_submited'
                  | 'cancel_submitted'
                  | 'on_transaction_cancelled_successfully_close_clicked'
                  | 'transaction_cancel_failure_accepted'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'on_safe_transaction_completed_splash_animation_screen_competed'
                  | 'on_gas_currency_selected'
                  | 'on_close_transaction_status_not_found_modal'
                  | 'transaction_request_replaced'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >
    | {
          type: 'on_earn_deposit_success'
          earnOwner: Account
          configuredEarn: ConfiguredEarn
      }

export const DeployAndSubmitDeposit = ({
    taker,
    earnOwner,
    earnDepositRequest,
    earn,
    sessionPassword,
    accountsMap,
    keyStoreMap,
    feePresetMap,
    networkMap,
    networkRPCMap,
    installationId,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
    portfolioMap,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>(calculateState({ earn, taker }))

    switch (state.type) {
        case 'deploy_earn_account':
            return (
                <DeployEarn
                    earn={earn}
                    taker={state.taker}
                    account={earnOwner}
                    keyStore={getKeyStore({
                        keyStoreMap,
                        address: earnOwner.address,
                    })}
                    network={EARN_NETWORK}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    feePresetMap={feePresetMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                                onMsg(msg)
                                break
                            case 'on_earn_configured':
                                onMsg(msg)
                                setState({
                                    type: 'deposit',
                                    configuredEarn: msg.configuredEarn,
                                    taker: msg.taker,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'deposit':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={earnDepositRequest.fromAccountPortfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    actionSource={{
                        type: 'internal',
                        transactionEventSource: 'earnDeposit',
                    }}
                    fetchSimulationByRequest={async () => {
                        return {
                            type: 'simulated',
                            simulation: {
                                currencies: {},
                                transaction: {
                                    type: 'earn_deposit_direct_send',
                                    earnDepositRequest,
                                    takerApyMap: earn.takerApyMap,
                                },
                                checks: [],
                            },
                        }
                    }}
                    fetchTransactionResultByRequest={async () => ({
                        currencies: {},
                        transaction: {
                            type: 'earn_deposit_direct_send',
                            earnDepositRequest,
                            takerApyMap: earn.takerApyMap,
                        },
                    })}
                    installationId={installationId}
                    accounts={accountsMap}
                    keystores={keyStoreMap}
                    state={{ type: 'maximised' }}
                    account={earnDepositRequest.fromAccount}
                    network={EARN_NETWORK}
                    sendTransactionRequests={[
                        earnDepositRequest.ethSendTransaction,
                    ]}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                                onMsg({ type: 'close' })
                                break
                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in submit earn direct deposit $${msg.type}`
                                    )
                                )
                                break

                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'import_keys_button_clicked':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'transaction_submited':
                            case 'cancel_submitted':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'transaction_request_replaced':
                                onMsg(msg)
                                break

                            case 'on_completed_safe_transaction_close_click':
                            case 'on_completed_transaction_close_click':
                                onMsg({
                                    type: 'on_earn_deposit_success',
                                    earnOwner,
                                    configuredEarn: state.configuredEarn,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
