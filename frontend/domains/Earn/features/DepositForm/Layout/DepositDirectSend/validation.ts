import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { values } from '@zeal/toolkit/Object'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { Account } from '@zeal/domains/Account'
import { CryptoCurrency, Currency } from '@zeal/domains/Currency'
import { EarnDepositDirectSendRequest, Taker } from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { FXRate2 } from '@zeal/domains/FXRate'
import { CryptoMoney } from '@zeal/domains/Money'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'

type NotEnoughBalanceError = { type: 'not_enough_balance' }
type AmountRequired = { type: 'amount_required' }

type SubmitError = NotEnoughBalanceError | AmountRequired

export type FormError = {
    from?: NotEnoughBalanceError
    submit?: SubmitError
}

const validateFromBalance = ({
    fromAmount,
    fromCurrency,
    fromAccountPortfolio,
}: {
    fromAmount: string | null
    fromCurrency: CryptoCurrency
    fromAccountPortfolio: ServerPortfolio2
}): Result<NotEnoughBalanceError, CryptoMoney> => {
    const balance = getBalanceByCryptoCurrency2({
        currency: fromCurrency,
        serverPortfolio: fromAccountPortfolio,
    })

    const amount = fromFixedWithFraction(fromAmount, fromCurrency.fraction)

    if (balance.amount < amount) {
        return failure({ type: 'not_enough_balance' })
    }

    return success({ amount, currency: fromCurrency })
}

const validateFromToken = ({
    fromAmount,
    fromCurrency,
}: {
    fromAmount: string | null
    fromCurrency: CryptoCurrency
}): Result<AmountRequired, unknown> => {
    if (
        !fromAmount ||
        fromFixedWithFraction(fromAmount, fromCurrency.fraction) === 0n
    ) {
        return failure({ type: 'amount_required' })
    }

    return success(fromAmount)
}

export const validate = ({
    fromAmount,
    fromAccount,
    fromAccountPortfolio,
    taker,
    takerUserCurrencyRate,
}: {
    takerUserCurrencyRate: FXRate2<CryptoCurrency, Currency>
    fromAmount: string | null
    fromAccount: Account
    fromAccountPortfolio: ServerPortfolio2
    taker: Taker
}): Result<FormError, EarnDepositDirectSendRequest> => {
    return shape({
        from: validateFromBalance({
            fromAmount,
            fromCurrency: taker.cryptoCurrency,
            fromAccountPortfolio,
        }),
        submit: shape({
            from: validateFromToken({
                fromAmount,
                fromCurrency: taker.cryptoCurrency,
            }).andThen(() =>
                validateFromBalance({
                    fromAmount,
                    fromCurrency: taker.cryptoCurrency,
                    fromAccountPortfolio,
                })
            ),
        }).mapError((error) => values(error)[0]),
    }).map(({ submit }) => ({
        taker,
        fromAccount,
        fromAccountPortfolio,
        from: submit.from,
        takerUserCurrencyRate,
        ethSendTransaction: createTransferEthSendTransaction({
            amount: submit.from,
            from: fromAccount.address,
            to: taker.address,
            network: EARN_NETWORK,
        }),
    }))
}
