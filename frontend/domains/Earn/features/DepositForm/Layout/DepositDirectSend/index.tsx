import { useEffect, useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    Currency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { Earn, Taker } from '@zeal/domains/Earn'
import { fetchTakerPortfolio } from '@zeal/domains/Earn/api/fetchTakerPortfolio'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { FXRate2 } from '@zeal/domains/FXRate'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

const fetch = async ({
    taker,
    networkRPCMap,
    networkMap,
    defaultCurrencyConfig,
    signal,
}: {
    fromAmount: string | null
    fromAccount: Account
    taker: Taker
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<FXRate2<CryptoCurrency, Currency>> => {
    const portfolio = await fetchTakerPortfolio({
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        taker,
        signal,
    })

    return portfolio.userCurrencyRate
}

type Props = {
    taker: Taker
    fromAmount: string | null
    fromAccountPortfolio: ServerPortfolio2
    fromAccount: Account
    fromCurrencies: CryptoCurrency[]
    networkMap: NetworkMap
    earn: Earn
    earnOwner: Account

    portfolioMap: PortfolioMap
    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap

    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_earn_deposit_in_different_currency_on_same_network_selected'
          fromAmount: string | null
          fromCurrency: CryptoCurrency
          taker: Taker
      }
    | {
          type: 'on_earn_deposit_in_different_currency_on_another_network_selected'
          taker: Taker
          amount: string | null
          fromCurrency: CryptoCurrency
      }
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_deposit_success'
                  | 'on_earn_configured'
          }
      >
    | Extract<
          MsgOf<typeof Layout>,
          {
              type: 'close' | 'on_from_account_click'
          }
      >

export const DepositDirectSend = ({
    onMsg,
    earnOwner,
    fromCurrencies,
    fromAmount,
    fromAccount,
    taker,
    networkMap,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    fromAccountPortfolio,
    installationId,
    earn,
    keystoreMap,
    sessionPassword,
    networkRPCMap,
    currencyHiddenMap,
    currencyPinMap,
    portfolioMap,
    defaultCurrencyConfig,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    const [pollable, setPollable] = useLoadedPollableData(
        fetch,
        {
            type: 'loaded',
            params: {
                fromAmount,
                fromAccount,
                taker,
                networkRPCMap,
                defaultCurrencyConfig,
                networkMap,
            },
            data: earn.takerPortfolioMap[taker.type].userCurrencyRate,
        },
        { stopIf: () => false, pollIntervalMilliseconds: 5000 }
    )

    const captureErrorOnce = useCaptureErrorOnce()

    const takerPortfolio = earn.takerPortfolioMap[pollable.params.taker.type]

    useEffect(() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
                break
            case 'subsequent_failed':
                captureErrorOnce(pollable.error)
                break
            default:
                return notReachable(pollable)
        }
    }, [captureErrorOnce, pollable])

    return (
        <>
            <Layout
                taker={pollable.params.taker}
                fromAccount={pollable.params.fromAccount}
                fromAmount={pollable.params.fromAmount}
                takerUserCurrencyRate={pollable.data}
                fromAccountPortfolio={fromAccountPortfolio}
                installationId={installationId}
                takerPortfolio={takerPortfolio}
                networkMap={networkMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_from_account_click':
                            onMsg(msg)
                            break

                        case 'on_earn_deposit_submited':
                            postUserEvent({
                                type: 'EarnDepositInitiatedEvent',
                                source: 'zeal',
                                asset: taker.type,
                                installationId,
                            })
                            setModal({
                                type: 'submit_deposit',
                                earnDepositDirectSend: msg.earnDepositRequest,
                            })
                            break
                        case 'on_earn_account_click':
                            setModal({ type: 'select_earn_account' })
                            break

                        case 'on_select_from_currency_click':
                            setModal({ type: 'select_from_currency' })
                            break

                        case 'on_amount_change':
                            setModal({ type: 'closed' })
                            setPollable({
                                ...pollable,
                                params: {
                                    ...pollable.params,
                                    fromAmount: msg.amount,
                                },
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />

            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                fromAccount={pollable.params.fromAccount}
                portfolioMap={portfolioMap}
                taker={taker}
                earnOwner={earnOwner}
                earn={earn}
                currencyHiddenMap={currencyHiddenMap}
                currencyPinMap={currencyPinMap}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                installationId={installationId}
                keystoreMap={keystoreMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                sessionPassword={sessionPassword}
                state={modal}
                fromCurrencies={fromCurrencies}
                portfolio={fromAccountPortfolio}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_cancel_confirm_transaction_clicked':
                        case 'on_safe_transaction_failure_accepted':
                        case 'on_close_transaction_status_not_found_modal':
                        case 'on_transaction_cancelled_successfully_close_clicked':
                        case 'transaction_cancel_failure_accepted':
                        case 'cancel_submitted':
                        case 'transaction_failure_accepted':
                        case 'transaction_request_replaced':
                        case 'on_sign_cancel_button_clicked':
                            setModal({ type: 'closed' })
                            break
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_earn_configured':
                            onMsg(msg)
                            break

                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_submited':
                            noop()
                            break

                        case 'on_wrong_network_accepted':
                            setModal({ type: 'closed' })
                            captureError(
                                new ImperativeError(
                                    `${msg.type} should not appear in earn deposit flow`
                                )
                            )
                            break

                        case 'on_earn_deposit_success':
                            onMsg(msg)
                            setModal({ type: 'closed' })
                            break

                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_earn_account_selected':
                            setModal({ type: 'closed' })

                            if (
                                msg.taker.cryptoCurrency.id !==
                                    pollable.params.taker.cryptoCurrency.id &&
                                msg.taker.cryptoCurrency.networkHexChainId ===
                                    pollable.params.taker.cryptoCurrency
                                        .networkHexChainId
                            ) {
                                onMsg({
                                    type: 'on_earn_deposit_in_different_currency_on_same_network_selected',
                                    fromAmount: pollable.params.fromAmount,
                                    fromCurrency:
                                        pollable.params.taker.cryptoCurrency,
                                    taker: msg.taker,
                                })
                                break
                            }

                            if (
                                msg.taker.cryptoCurrency.id !==
                                    pollable.params.taker.cryptoCurrency.id &&
                                msg.taker.cryptoCurrency.networkHexChainId !==
                                    pollable.params.taker.cryptoCurrency
                                        .networkHexChainId
                            ) {
                                onMsg({
                                    type: 'on_earn_deposit_in_different_currency_on_another_network_selected',
                                    taker: msg.taker,
                                    amount: pollable.params.fromAmount,
                                    fromCurrency:
                                        pollable.params.taker.cryptoCurrency,
                                })
                                break
                            }
                            break

                        case 'on_from_currency_selected':
                            setModal({ type: 'closed' })

                            if (
                                msg.fromCurrency.id !==
                                    pollable.params.taker.cryptoCurrency.id &&
                                msg.fromCurrency.networkHexChainId ===
                                    pollable.params.taker.cryptoCurrency
                                        .networkHexChainId
                            ) {
                                onMsg({
                                    type: 'on_earn_deposit_in_different_currency_on_same_network_selected',
                                    fromAmount: pollable.params.fromAmount,
                                    fromCurrency: msg.fromCurrency,
                                    taker: pollable.params.taker,
                                })
                                break
                            }

                            if (
                                msg.fromCurrency.id !==
                                    pollable.params.taker.cryptoCurrency.id &&
                                msg.fromCurrency.networkHexChainId !==
                                    pollable.params.taker.cryptoCurrency
                                        .networkHexChainId
                            ) {
                                onMsg({
                                    type: 'on_earn_deposit_in_different_currency_on_another_network_selected',
                                    taker: pollable.params.taker,
                                    amount: pollable.params.fromAmount,
                                    fromCurrency: msg.fromCurrency,
                                })
                                break
                            }
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
