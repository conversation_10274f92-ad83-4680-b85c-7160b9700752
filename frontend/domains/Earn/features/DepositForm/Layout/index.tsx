import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { Earn, Taker } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { EarnEventLocation } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { DepositBridge } from './DepositBridge'
import { DepositDirectSend } from './DepositDirectSend'
import { DepositSwap } from './DepositSwap'

type Props = {
    initialTaker: Taker
    eventLocation: EarnEventLocation
    fromCurrencies: CryptoCurrency[]
    initialAmount: CryptoMoney
    networkMap: NetworkMap
    earn: Earn
    earnOwner: Account
    fromAccount: Account
    fromAccountPortfolio: ServerPortfolio2

    sessionPassword: string
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap

    swapsIOContractsMap: SwapsIOContractsMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type State =
    | {
          type: 'deposit_with_bridge'
          fromAmount: string | null
          fromCurrency: CryptoCurrency
          taker: Taker
      }
    | {
          type: 'deposit_with_swap'
          fromAmount: string | null
          fromCurrency: CryptoCurrency
          taker: Taker
      }
    | {
          type: 'deposit_direct_send'
          fromAmount: string | null
          taker: Taker
      }

type Msg =
    | Extract<
          MsgOf<typeof DepositSwap>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_deposit_success'
                  | 'on_earn_configured'
                  | 'on_from_account_click'
                  | 'close'
          }
      >
    | Extract<
          MsgOf<typeof DepositBridge>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_deposit_success'
                  | 'on_earn_configured'
                  | 'on_from_account_click'
                  | 'on_swaps_io_swap_request_created'
          }
      >

const calculateInitialState = ({
    initialAmount,
    taker,
}: {
    initialAmount: CryptoMoney
    taker: Taker
}): State => {
    const fromAmount =
        initialAmount.amount === 0n
            ? null
            : toFixedWithFraction(
                  initialAmount.amount,
                  initialAmount.currency.fraction
              )
    if (initialAmount.currency.id === taker.cryptoCurrency.id) {
        return {
            type: 'deposit_direct_send',
            fromAmount,
            taker,
        }
    }
    if (
        initialAmount.currency.networkHexChainId ===
        taker.cryptoCurrency.networkHexChainId
    ) {
        return {
            type: 'deposit_with_swap',
            fromAmount,
            fromCurrency: initialAmount.currency,
            taker,
        }
    }
    return {
        type: 'deposit_with_bridge',
        fromAmount,
        fromCurrency: initialAmount.currency,
        taker,
    }
}

export const Layout = ({
    onMsg,
    earnOwner,
    fromCurrencies,
    initialTaker,
    initialAmount,
    fromAccount,
    fromAccountPortfolio,
    portfolioMap,
    networkMap,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    earn,
    keystoreMap,
    sessionPassword,
    networkRPCMap,
    currencyHiddenMap,
    currencyPinMap,
    swapsIOContractsMap,
    defaultCurrencyConfig,
    eventLocation,
}: Props) => {
    const [state, setState] = useState<State>(
        calculateInitialState({
            initialAmount,
            taker: initialTaker,
        })
    )

    useEffect(() => {
        postUserEvent({
            type: 'EarnDepositFlowEnteredEvent',
            location: eventLocation,
            source: 'zeal',
            asset: initialTaker.type,
            installationId,
        })
    }, [eventLocation, initialTaker.type, installationId])

    switch (state.type) {
        case 'deposit_with_swap':
            return (
                <DepositSwap
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolioMap={portfolioMap}
                    fromAmount={state.fromAmount}
                    earnOwner={earnOwner}
                    fromCurrencies={fromCurrencies}
                    fromCurrency={state.fromCurrency}
                    fromAccountPortfolio={fromAccountPortfolio}
                    taker={state.taker}
                    fromAccount={fromAccount}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    earn={earn}
                    keystoreMap={keystoreMap}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_earn_deposit_success':
                            case 'on_earn_configured':
                            case 'on_from_account_click':
                                onMsg(msg)
                                break

                            case 'on_earn_deposit_in_same_currancy_as_taker_selected':
                                setState({
                                    type: 'deposit_direct_send',
                                    fromAmount: msg.amount,
                                    taker: msg.taker,
                                })
                                break

                            case 'on_earn_deposit_in_different_currency_on_another_network_selected':
                                setState({
                                    type: 'deposit_with_bridge',
                                    fromAmount: msg.amount,
                                    fromCurrency: msg.fromCurrency,
                                    taker: msg.taker,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'deposit_direct_send':
            return (
                <DepositDirectSend
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    fromAmount={state.fromAmount}
                    earnOwner={earnOwner}
                    fromCurrencies={fromCurrencies}
                    fromAccountPortfolio={fromAccountPortfolio}
                    taker={state.taker}
                    fromAccount={fromAccount}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    earn={earn}
                    keystoreMap={keystoreMap}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    portfolioMap={portfolioMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_earn_deposit_success':
                            case 'on_earn_configured':
                            case 'on_from_account_click':
                                onMsg(msg)
                                break

                            case 'on_earn_deposit_in_different_currency_on_same_network_selected':
                                setState({
                                    type: 'deposit_with_swap',
                                    fromAmount: msg.fromAmount,
                                    fromCurrency: msg.fromCurrency,
                                    taker: msg.taker,
                                })
                                break
                            case 'on_earn_deposit_in_different_currency_on_another_network_selected':
                                setState({
                                    type: 'deposit_with_bridge',
                                    fromAmount: msg.amount,
                                    fromCurrency: msg.fromCurrency,
                                    taker: msg.taker,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'deposit_with_bridge':
            return (
                <DepositBridge
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolioMap={portfolioMap}
                    fromAmount={state.fromAmount}
                    earnOwner={earnOwner}
                    fromCurrencies={fromCurrencies}
                    fromCurrency={state.fromCurrency}
                    fromAccountPortfolio={fromAccountPortfolio}
                    taker={state.taker}
                    fromAccount={fromAccount}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    earn={earn}
                    keystoreMap={keystoreMap}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    swapsIOContractsMap={swapsIOContractsMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_earn_deposit_success':
                            case 'on_earn_configured':
                            case 'on_from_account_click':
                            case 'on_swaps_io_swap_request_created':
                                onMsg(msg)
                                break
                            case 'on_earn_deposit_in_same_currancy_as_taker_selected':
                                setState({
                                    type: 'deposit_direct_send',
                                    taker: msg.taker,
                                    fromAmount: msg.amount,
                                })
                                break
                            case 'on_earn_deposit_in_different_currency_on_same_network_selected':
                                setState({
                                    type: 'deposit_with_swap',
                                    fromAmount: msg.fromAmount,
                                    fromCurrency: msg.fromCurrency,
                                    taker: msg.taker,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
