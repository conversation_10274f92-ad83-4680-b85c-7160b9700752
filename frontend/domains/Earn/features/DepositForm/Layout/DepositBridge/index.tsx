import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    SwapsIOContractsMap,
    SwapsIOQuote,
} from '@zeal/domains/Currency/domains/SwapsIO'
import { Earn, Taker } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { DeployAndSubmitDeposit } from './DeployAndSubmitDeposit'
import { Form } from './Form'

type Props = {
    taker: Taker
    fromAmount: string | null
    fromAccountPortfolio: ServerPortfolio2
    fromAccount: Account
    fromCurrencies: CryptoCurrency[]
    fromCurrency: CryptoCurrency
    networkMap: NetworkMap
    earn: Earn
    earnOwner: Account
    portfolioMap: PortfolioMap

    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap

    sessionPassword: string
    currencyHiddenMap: CurrencyHiddenMap
    swapsIOContractsMap: SwapsIOContractsMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Form>,
          {
              type:
                  | 'close'
                  | 'on_earn_deposit_in_same_currancy_as_taker_selected'
                  | 'on_earn_deposit_in_different_currency_on_same_network_selected'
                  | 'on_from_account_click'
          }
      >
    | Extract<
          MsgOf<typeof DeployAndSubmitDeposit>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_deposit_success'
                  | 'on_earn_configured'
                  | 'on_swaps_io_swap_request_created'
          }
      >

type State =
    | {
          type: 'form'
          initialTaker: Taker
          initialAmount: string | null
      }
    | {
          type: 'submit'
          swapsIOQuote: SwapsIOQuote
      }

export const DepositBridge = ({
    onMsg,
    earnOwner,
    fromAmount,
    fromCurrencies,
    fromCurrency,
    fromAccountPortfolio,
    portfolioMap,
    taker,
    fromAccount,
    networkMap,
    accountsMap,
    gasCurrencyPresetMap,
    installationId,
    earn,
    keystoreMap,
    networkRPCMap,
    currencyHiddenMap,
    currencyPinMap,
    feePresetMap,
    sessionPassword,
    swapsIOContractsMap,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'form',
        initialTaker: taker,
        initialAmount: fromAmount,
    })

    switch (state.type) {
        case 'form':
            return (
                <Form
                    installationId={installationId}
                    earn={earn}
                    fromAmount={state.initialAmount}
                    fromAccountPortfolio={fromAccountPortfolio}
                    fromCurrencies={fromCurrencies}
                    fromCurrency={fromCurrency}
                    taker={state.initialTaker}
                    fromAccount={fromAccount}
                    networkMap={networkMap}
                    keystoreMap={keystoreMap}
                    networkRPCMap={networkRPCMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    swapsIOContractsMap={swapsIOContractsMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_deposit_submited':
                                setState({
                                    type: 'submit',
                                    swapsIOQuote: msg.swapsIOQuote,
                                })
                                break
                            case 'close':
                            case 'on_earn_deposit_in_same_currancy_as_taker_selected':
                            case 'on_earn_deposit_in_different_currency_on_same_network_selected':
                            case 'on_from_account_click':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit': {
            const taker = earn.takers.find(
                (taker) =>
                    taker.cryptoCurrency.id ===
                    state.swapsIOQuote.to.currency.id
            )

            if (!taker) {
                throw new ImperativeError('Taker not found')
            }

            return (
                <DeployAndSubmitDeposit
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    earn={earn}
                    taker={taker}
                    fromAccountPortfolio={fromAccountPortfolio}
                    feePresetMap={feePresetMap}
                    earnOwner={earnOwner}
                    accountsMap={accountsMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keystoreMap}
                    networkMap={networkMap}
                    portfolioMap={portfolioMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    swapsIOQuote={state.swapsIOQuote}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({
                                    type: 'form',
                                    initialTaker: taker,
                                    initialAmount: toFixedWithFraction(
                                        state.swapsIOQuote.from.amount,
                                        state.swapsIOQuote.from.currency
                                            .fraction
                                    ),
                                })
                                break
                            case 'on_swap_cancelled_close_clicked':
                                onMsg({ type: 'close' })
                                break
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_earn_deposit_success':
                            case 'on_earn_configured':
                            case 'on_swaps_io_swap_request_created':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        }

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
