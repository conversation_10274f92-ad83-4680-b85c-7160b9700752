import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { SwapsIOQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import { SubmitSwap } from '@zeal/domains/Currency/domains/SwapsIO/features/SubmitSwap'
import {
    ConfiguredEarn,
    DeployedTaker,
    Earn,
    NotDeployedTaker,
    Taker,
} from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { DeployEarn } from '../../../DeployEarn'

type State =
    | {
          type: 'deploy_earn_account'
          taker: NotDeployedTaker
      }
    | {
          type: 'deposit'
          configuredEarn: ConfiguredEarn
          taker: DeployedTaker
      }

const calculateState = ({
    earn,
    taker,
}: {
    earn: Earn
    taker: Taker
}): State => {
    switch (earn.type) {
        case 'not_configured': {
            switch (taker.state) {
                case 'not_deployed':
                    return { type: 'deploy_earn_account', taker }
                case 'deployed':
                    throw new ImperativeError(
                        'Impossible state, taker is deployed while earn is not configured',
                        { taker, holder: earn.holder }
                    )

                default:
                    return notReachable(taker)
            }
        }
        case 'configured': {
            switch (taker.state) {
                case 'not_deployed':
                    return { type: 'deploy_earn_account', taker }

                case 'deployed':
                    return { type: 'deposit', configuredEarn: earn, taker }

                default:
                    return notReachable(taker)
            }
        }
        /* istanbul ignore next */
        default:
            return notReachable(earn)
    }
}

type Props = {
    taker: Taker
    swapsIOQuote: SwapsIOQuote

    fromAccountPortfolio: ServerPortfolio2
    earn: Earn
    earnOwner: Account
    sessionPassword: string
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    portfolioMap: PortfolioMap

    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof DeployEarn>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_configured'
          }
      >
    | Extract<
          MsgOf<typeof SubmitSwap>,
          {
              type:
                  | 'close'
                  | 'on_swap_cancelled_close_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_swaps_io_swap_request_created'
          }
      >
    | {
          type: 'on_earn_deposit_success'
          earnOwner: Account
          configuredEarn: ConfiguredEarn
      }

export const DeployAndSubmitDeposit = ({
    taker,
    earnOwner,
    swapsIOQuote,
    earn,
    sessionPassword,
    fromAccountPortfolio,
    accountsMap,
    keyStoreMap,
    feePresetMap,
    networkMap,
    networkRPCMap,
    installationId,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
    portfolioMap,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>(calculateState({ earn, taker }))

    switch (state.type) {
        case 'deploy_earn_account':
            return (
                <DeployEarn
                    earn={earn}
                    taker={state.taker}
                    account={earnOwner}
                    keyStore={getKeyStore({
                        keyStoreMap,
                        address: earnOwner.address,
                    })}
                    network={EARN_NETWORK}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    feePresetMap={feePresetMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                                onMsg(msg)
                                break
                            case 'on_earn_configured':
                                onMsg(msg)
                                setState({
                                    type: 'deposit',
                                    configuredEarn: msg.configuredEarn,
                                    taker: msg.taker,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'deposit':
            return (
                <SubmitSwap
                    source="earn"
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    simulation={{
                        type: 'OrderEarnDepositBridge',
                        swapsIOQuote,
                        taker,
                        takerPortfolioMap: earn.takerPortfolioMap,
                        takerApyMap: earn.takerApyMap,
                    }}
                    installationId={installationId}
                    quote={swapsIOQuote}
                    keyStoreMap={keyStoreMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_swap_cancelled_close_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_swaps_io_swap_request_created':
                                onMsg(msg)
                                break
                            case 'on_swap_success_clicked':
                            case 'on_swap_created_close_clicked':
                                onMsg({
                                    type: 'on_earn_deposit_success',
                                    earnOwner,
                                    configuredEarn: state.configuredEarn,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    sessionPassword={sessionPassword}
                    senderPortfolio={fromAccountPortfolio}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
