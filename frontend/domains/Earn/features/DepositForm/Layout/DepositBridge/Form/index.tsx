import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Result } from '@zeal/toolkit/Result'

import { Account } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    SwapsIOContractsMap,
    SwapsIOQuote,
} from '@zeal/domains/Currency/domains/SwapsIO'
import { fetchMaxBalance } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchMaxBalance'
import {
    fetchQuote,
    SwapsIOQuoteRequest,
    SwapsIOQuoteValidationsErrors,
} from '@zeal/domains/Currency/domains/SwapsIO/api/fetchQuote'
import { Earn, EarnDepositBridgeQouteRequest, Taker } from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout } from './Layout'
import { Modal, State } from './Modal'

const POLL_INTERVAL_MS = 60_000

type Props = {
    taker: Taker
    fromAmount: string | null
    fromAccountPortfolio: ServerPortfolio2
    fromAccount: Account
    fromCurrencies: CryptoCurrency[]
    fromCurrency: CryptoCurrency
    networkMap: NetworkMap
    earn: Earn

    keystoreMap: KeyStoreMap
    networkRPCMap: NetworkRPCMap
    gasCurrencyPresetMap: GasCurrencyPresetMap

    swapsIOContractsMap: SwapsIOContractsMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'close'
      }
    | {
          type: 'on_earn_deposit_in_same_currancy_as_taker_selected'
          taker: Taker
          amount: string | null
      }
    | {
          type: 'on_earn_deposit_in_different_currency_on_same_network_selected'
          fromAmount: string | null
          fromCurrency: CryptoCurrency
          taker: Taker
      }
    | Extract<
          MsgOf<typeof Layout>,
          { type: 'on_deposit_submited' | 'on_from_account_click' }
      >

const mapEarnDepositBridgeQuoteRequestToSwapsIOQouteRequest = ({
    networkMap,
    fromAccount,
    amount,
    fromCurrency,
    taker,
    networkRPCMap,
    swapsIOContractsMap,
    defaultCurrencyConfig,
}: EarnDepositBridgeQouteRequest): SwapsIOQuoteRequest => {
    return {
        amount,
        fromCurrency,
        toCurrency: taker.cryptoCurrency,
        sender: fromAccount,
        receiver: taker.address,

        networkRPCMap,
        networkMap,
        contractsMap: swapsIOContractsMap,
        defaultCurrencyConfig,
    }
}

const fetch = async ({
    networkRPCMap,
    networkMap,
    amount,
    fromAccount,
    fromCurrency,
    taker,
    defaultCurrencyConfig,
    swapsIOContractsMap,
    signal,
}: EarnDepositBridgeQouteRequest & { signal?: AbortSignal }): Promise<
    Result<SwapsIOQuoteValidationsErrors, SwapsIOQuote>
> =>
    fetchQuote({
        amount,
        fromCurrency,
        toCurrency: taker.cryptoCurrency,
        sender: fromAccount,
        receiver: taker.address,

        networkRPCMap,
        networkMap,
        contractsMap: swapsIOContractsMap,
        defaultCurrencyConfig,
        signal,
    })

export const Form = ({
    onMsg,
    fromAmount,
    fromAccountPortfolio,
    fromCurrencies,
    fromCurrency,
    taker,
    fromAccount,
    installationId,
    networkMap,
    gasCurrencyPresetMap,
    earn,
    keystoreMap,
    networkRPCMap,
    currencyHiddenMap,
    currencyPinMap,
    swapsIOContractsMap,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'closed' })

    const captureErrorOnce = useCaptureErrorOnce()

    const [pollable, setPollable] = usePollableData(
        fetch,
        {
            type: 'loading',
            params: {
                networkRPCMap,
                networkMap,
                amount: fromAmount,
                fromAccount,
                fromCurrency,
                taker,
                swapsIOContractsMap,
                defaultCurrencyConfig,
            },
        },
        { pollIntervalMilliseconds: POLL_INTERVAL_MS }
    )

    const takerPortfolio = earn.takerPortfolioMap[pollable.params.taker.type]

    const fromCurrencyBalance = getTokenByCryptoCurrency3({
        currency: pollable.params.fromCurrency,
        serverPortfolio: fromAccountPortfolio,
    })

    const swapsIOQuoteRequest =
        mapEarnDepositBridgeQuoteRequestToSwapsIOQouteRequest(pollable.params)

    const [maxBalanceLoadable, setMaxBalanceLoadable] = useLoadableData(
        fetchMaxBalance,
        {
            type: 'loading',
            params: {
                swapQuoteRequest: {
                    ...swapsIOQuoteRequest,
                    amount: toFixedWithFraction(
                        fromCurrencyBalance.balance.amount,
                        fromCurrencyBalance.balance.currency.fraction
                    ),
                },
                serverPortfolio: fromAccountPortfolio,
                gasCurrencyPresetMap,
                keyStore: getKeyStore({
                    address: fromAccount.address,
                    keyStoreMap: keystoreMap,
                }),
            },
        }
    )

    useEffect(() => {
        const fromBalance = getTokenByCryptoCurrency3({
            currency: swapsIOQuoteRequest.fromCurrency,
            serverPortfolio: fromAccountPortfolio,
        })

        setMaxBalanceLoadable((old) => ({
            type: 'loading',
            params: {
                ...old.params,
                swapQuoteRequest: {
                    ...old.params.swapQuoteRequest,
                    fromCurrency: swapsIOQuoteRequest.fromCurrency,
                    amount: toFixedWithFraction(
                        fromBalance.balance.amount,
                        fromBalance.balance.currency.fraction
                    ),
                },
                serverPortfolio: fromAccountPortfolio,
            },
        }))
    }, [
        swapsIOQuoteRequest.fromCurrency,
        setMaxBalanceLoadable,
        fromAccountPortfolio,
    ])

    useEffect(() => {
        switch (maxBalanceLoadable.type) {
            case 'error':
                captureError(maxBalanceLoadable.error, {
                    extra: { context: 'maxButton balance correction on swap' },
                })
                break

            case 'loaded':
            case 'loading':
                break

            /* istanbul ignore next */
            default:
                notReachable(maxBalanceLoadable)
        }
    }, [maxBalanceLoadable])

    useEffect(() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
            case 'loading':
                break
            case 'subsequent_failed':
            case 'error':
                captureErrorOnce(pollable.error)
                break
            default:
                notReachable(pollable)
        }
    }, [pollable, captureErrorOnce])

    return (
        <>
            <Layout
                installationId={installationId}
                pollable={pollable}
                takerPortfolio={takerPortfolio}
                fromAccountPortfolio={fromAccountPortfolio}
                maxBalanceLoadable={maxBalanceLoadable}
                networkMap={networkMap}
                takerUserCurrencyRate={
                    earn.takerPortfolioMap[pollable.params.taker.type]
                        .userCurrencyRate
                }
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_from_account_click':
                        case 'on_deposit_submited':
                            onMsg(msg)
                            break
                        case 'on_select_from_currency_click':
                            setState({ type: 'select_from_currency' })
                            break
                        case 'on_amount_change':
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    amount: msg.amount,
                                },
                            })
                            break
                        case 'on_earn_account_click':
                            setState({ type: 'select_earn_account' })
                            break
                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                fromAccountPortfolio={fromAccountPortfolio}
                earn={earn}
                currencyHiddenMap={currencyHiddenMap}
                currencyPinMap={currencyPinMap}
                networkMap={networkMap}
                state={state}
                fromCurrencies={fromCurrencies}
                pollable={pollable}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setState({ type: 'closed' })
                            break
                        case 'on_from_currency_selected':
                            setState({ type: 'closed' })
                            if (
                                msg.fromCurrency.id === taker.cryptoCurrency.id
                            ) {
                                onMsg({
                                    type: 'on_earn_deposit_in_same_currancy_as_taker_selected',
                                    taker: pollable.params.taker,
                                    amount: pollable.params.amount,
                                })
                                break
                            }
                            if (
                                msg.fromCurrency.networkHexChainId ===
                                taker.cryptoCurrency.networkHexChainId
                            ) {
                                onMsg({
                                    type: 'on_earn_deposit_in_different_currency_on_same_network_selected',
                                    fromAmount: pollable.params.amount,
                                    fromCurrency: msg.fromCurrency,
                                    taker: pollable.params.taker,
                                })
                                break
                            }

                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    fromCurrency: msg.fromCurrency,
                                },
                            })
                            break

                        case 'on_earn_account_selected':
                            setState({ type: 'closed' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    taker: msg.taker,
                                },
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
