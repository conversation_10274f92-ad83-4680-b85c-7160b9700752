import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { Earn, NotDeployedTaker, Taker } from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { createAccountDeployTransaction } from '@zeal/domains/Earn/helpers/createAccountDeployTransaction'
import { createCoordinatorDeployTransaction } from '@zeal/domains/Earn/helpers/createCoordinatorDeployTransaction'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { CheckIfAccountIsAlreadyDeployed } from './CheckIfAccountIsAlreadyDeployed'
import { FetchEarnState } from './FetchEarnState'

type Props = {
    owner: Account
    earn: Earn
    taker: Taker

    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    installationId: string
    networkRPCMap: NetworkRPCMap
    portfolioMap: PortfolioMap

    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | Extract<MsgOf<typeof FetchEarnState>, { type: 'on_earn_configured' }>
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
          }
      >

type State =
    | { type: 'check_if_taker_is_already_deployed' }
    | { type: 'deploy_earn'; transaction: EthSendTransaction }
    | { type: 'fetch_earn_state' }

const calculateTransaction = ({
    taker,
    earn,
    owner,
}: {
    owner: Web3.address.Address
    earn: Earn
    taker: NotDeployedTaker
}): EthSendTransaction => {
    switch (earn.type) {
        case 'not_configured':
            return createCoordinatorDeployTransaction({
                owner,
                takerType: taker.type,
                fromAddress: owner,
            })

        case 'configured': {
            return createAccountDeployTransaction({
                earn,
                owner,
                takerType: taker.type,
            })
        }

        default:
            return notReachable(earn)
    }
}

export const DeployEarnWithUserInteraction = ({
    owner,
    earn,
    taker,
    installationId,
    keyStoreMap,
    networkMap,
    feePresetMap,
    gasCurrencyPresetMap,
    accountsMap,
    portfolioMap,
    sessionPassword,
    networkRPCMap,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'check_if_taker_is_already_deployed',
    })

    switch (state.type) {
        case 'check_if_taker_is_already_deployed':
            return (
                /*
                 We need this check as the addAccount call doesn't fail if taker is already deployed
                 and corupts the coordinator state
                 */
                <CheckIfAccountIsAlreadyDeployed
                    owner={owner}
                    taker={taker}
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'taker_is_already_deployed':
                                setState({
                                    type: 'fetch_earn_state',
                                })
                                break
                            case 'taker_is_not_deployed':
                                setState({
                                    type: 'deploy_earn',
                                    transaction: calculateTransaction({
                                        owner: owner.address as Web3.address.Address,
                                        earn,
                                        taker: msg.taker,
                                    }),
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'deploy_earn':
            const portfolioCache = unsafe_GetPortfolioCache2({
                address: owner.address,
                portfolioMap,
            })
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    key={state.type}
                    network={EARN_NETWORK}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    portfolio={portfolioCache}
                    accounts={accountsMap}
                    keystores={keyStoreMap}
                    networkMap={networkMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    sendTransactionRequests={[state.transaction]}
                    account={owner}
                    fetchSimulationByRequest={async () => {
                        return {
                            type: 'simulated',
                            simulation: {
                                transaction: {
                                    type: 'deploy_earn_account',
                                    owner,
                                    taker,
                                    takerApyMap: earn.takerApyMap,
                                },
                                currencies: {},
                                checks: [],
                            },
                        }
                    }}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'deploy_earn_account',
                            owner,
                            taker,
                            takerApyMap: earn.takerApyMap,
                        },
                        currencies: {},
                    })}
                    state={{ type: 'maximised' }}
                    actionSource={{
                        type: 'internal',
                        transactionEventSource: 'earnHolderDeploy',
                    }}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'cancel_submitted':
                            case 'transaction_request_replaced':
                            case 'transaction_submited':
                            case 'on_user_operation_bundled':
                                noop()
                                break
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                                onMsg({
                                    type: 'close',
                                })
                                break

                            case 'drag':
                            case 'on_expand_request':
                            case 'on_wrong_network_accepted':
                                throw new ImperativeError(
                                    `should not got this msg in earn holder deploy`,
                                    msg
                                )

                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                                onMsg(msg)
                                break
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_completed_transaction_close_click':
                            case 'on_completed_safe_transaction_close_click':
                                postUserEvent({
                                    type: 'EarnAccountCreatedEvent',
                                    asset: taker.type,
                                    installationId,
                                })
                                setState({
                                    type: 'fetch_earn_state',
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'fetch_earn_state':
            return (
                <FetchEarnState
                    takerType={taker.type}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networkMap={networkMap}
                    installationId={installationId}
                    owner={owner}
                    networkRPCMap={networkRPCMap}
                    onMsg={onMsg}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
