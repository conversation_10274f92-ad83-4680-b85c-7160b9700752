import React from 'react'

import { Modal as UIModal } from '@zeal/uikit/Modal'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { Address } from '@zeal/domains/Address'
import { CardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    CardRecharge,
    ConfiguredEarn,
    EarnTakerMetrics,
    NotConfiguredEarn,
} from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { SelectAssetAndDeposit } from '@zeal/domains/Earn/features/SelectAssetAndDeposit'
import {
    createUpdateRechargeRequest,
    UpdateRechargeRequest,
} from '@zeal/domains/Earn/helpers/createUpdateRechargeRequest'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { SimulatedTransaction } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    state: State

    cardReadonlySigner: Account

    accountsMap: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    isEthereumNetworkFeeWarningSeen: boolean
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    installationId: string
    portfolioMap: PortfolioMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    customCurrencies: CustomCurrencyMap
    cardConfig: CardConfig
    earnTakerMetrics: EarnTakerMetrics
    onMsg: (msg: Msg) => void
}

export type Msg =
    | MsgOf<typeof SelectAssetAndDeposit>
    | {
          type: 'on_earn_recharge_configured'
          earn: ConfiguredEarn
          cardReadonlySignerAddress: Address
      }

export type State =
    | { type: 'closed' }
    | { type: 'select_asset_and_deposit_into_earn'; earn: NotConfiguredEarn }
    | {
          type: 'submit_update_recharge'
          request: UpdateRechargeRequest
          currentRecharge: CardRecharge
      }
    | { type: 'submit_disable_recharge'; request: UpdateRechargeRequest }

const getRechargeSimulatedTransaction = (
    currentRecharge: CardRecharge,
    request: UpdateRechargeRequest
): SimulatedTransaction => {
    switch (currentRecharge.type) {
        case 'recharge_disabled':
            return {
                type: 'earn_recharge_configured',
                request,
            }
        case 'recharge_enabled':
            return { type: 'earn_recharge_updated', request }
        /* istanbul ignore next */
        default:
            return notReachable(currentRecharge)
    }
}

export const Modal = ({
    onMsg,
    installationId,
    keystores,
    networkMap,
    networkRPCMap,
    earnTakerMetrics,
    sessionPassword,
    gasCurrencyPresetMap,
    feePresetMap,
    isEthereumNetworkFeeWarningSeen,
    accountsMap,
    portfolioMap,
    cardReadonlySigner,
    currencyHiddenMap,
    customCurrencies,
    cardConfig,
    currencyPinMap,
    state,
    defaultCurrencyConfig,
}: Props) => {
    const portfolioCache = unsafe_GetPortfolioCache2({
        address: cardReadonlySigner.address,
        portfolioMap,
    })

    switch (state.type) {
        case 'closed':
            return null
        case 'select_asset_and_deposit_into_earn':
            return (
                <UIModal>
                    <SelectAssetAndDeposit
                        variant="all_takers"
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        earnTakerMetrics={earnTakerMetrics}
                        portfolioMap={portfolioMap}
                        owner={cardReadonlySigner}
                        earn={state.earn}
                        accountsMap={accountsMap}
                        keyStoreMap={keystores}
                        networkMap={networkMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        sessionPassword={sessionPassword}
                        customCurrencyMap={customCurrencies}
                        cardConfig={cardConfig}
                        installationId={installationId}
                        networkRPCMap={networkRPCMap}
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        eventLocation="configure_recharge"
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'submit_update_recharge':
            return (
                <UIModal>
                    <SendTransaction
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        key={state.type}
                        network={EARN_NETWORK}
                        networkRPCMap={networkRPCMap}
                        sessionPassword={sessionPassword}
                        portfolio={portfolioCache}
                        accounts={accountsMap}
                        keystores={keystores}
                        networkMap={networkMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        installationId={installationId}
                        sendTransactionRequests={[
                            createUpdateRechargeRequest(state.request),
                        ]}
                        account={cardReadonlySigner}
                        fetchSimulationByRequest={async () => ({
                            type: 'simulated',
                            simulation: {
                                transaction: getRechargeSimulatedTransaction(
                                    state.currentRecharge,
                                    state.request
                                ),
                                checks: [],
                                currencies: {},
                            },
                        })}
                        fetchTransactionResultByRequest={async () => ({
                            transaction: getRechargeSimulatedTransaction(
                                state.currentRecharge,
                                state.request
                            ),
                            currencies: {},
                        })}
                        state={{ type: 'maximised' }}
                        actionSource={{
                            type: 'internal',
                            transactionEventSource: 'earnHolderDeploy',
                        }}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'cancel_submitted':
                                case 'transaction_request_replaced':
                                case 'transaction_submited':
                                case 'on_user_operation_bundled':
                                    noop()
                                    break
                                case 'on_minimize_click':
                                case 'on_cancel_confirm_transaction_clicked':
                                case 'on_safe_transaction_failure_accepted':
                                case 'on_transaction_cancelled_successfully_close_clicked':
                                case 'transaction_cancel_failure_accepted':
                                case 'transaction_failure_accepted':
                                case 'on_sign_cancel_button_clicked':
                                    onMsg({
                                        type: 'close',
                                    })
                                    break

                                case 'drag':
                                case 'on_expand_request':
                                case 'on_wrong_network_accepted':
                                    throw new ImperativeError(
                                        `should not got this msg in recharge update`,
                                        msg
                                    )

                                case 'on_4337_auto_gas_token_selection_clicked':
                                case 'on_4337_gas_currency_selected':
                                case 'import_keys_button_clicked':
                                case 'on_predefined_fee_preset_selected':
                                    onMsg(msg)
                                    break
                                case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                case 'on_close_transaction_status_not_found_modal': // TODO:  rename and remove "confirm_holder_deployed" step @max
                                case 'on_transaction_completed_splash_animation_screen_competed':
                                case 'on_completed_transaction_close_click':
                                case 'on_completed_safe_transaction_close_click': {
                                    postUserEvent({
                                        type: 'RechargeSaveCompletedEvent',
                                        amount: state.request.threshold.toString(),
                                        rechargeAssets:
                                            state.request.rebalancers.map(
                                                (item) => item.type
                                            ),
                                        installationId,
                                    })
                                    onMsg({
                                        type: 'on_earn_recharge_configured',
                                        cardReadonlySignerAddress:
                                            cardReadonlySigner.address,
                                        earn: {
                                            ...state.request.earn,
                                            cardRecharge: {
                                                type: 'recharge_enabled',
                                                rebalancers:
                                                    state.request.rebalancers,
                                                threshold:
                                                    state.request.threshold,
                                                cardSafeAddress:
                                                    state.request.cardSafe
                                                        .address,
                                                cardCurrency:
                                                    state.request.cardSafe
                                                        .cryptoCurrency,
                                            },
                                        },
                                    })
                                    break
                                }

                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </UIModal>
            )
        case 'submit_disable_recharge':
            return (
                <UIModal>
                    <SendTransaction
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        key={state.type}
                        network={EARN_NETWORK}
                        networkRPCMap={networkRPCMap}
                        sessionPassword={sessionPassword}
                        portfolio={portfolioCache}
                        accounts={accountsMap}
                        keystores={keystores}
                        networkMap={networkMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        installationId={installationId}
                        sendTransactionRequests={[
                            createUpdateRechargeRequest(state.request),
                        ]}
                        account={cardReadonlySigner}
                        fetchSimulationByRequest={async () => {
                            return {
                                type: 'simulated',
                                simulation: {
                                    transaction: {
                                        type: 'earn_recharge_disabled',
                                    },
                                    checks: [],
                                    currencies: {},
                                },
                            }
                        }}
                        fetchTransactionResultByRequest={async () => ({
                            transaction: {
                                type: 'earn_recharge_disabled',
                            },
                            currencies: {},
                        })}
                        state={{ type: 'maximised' }}
                        actionSource={{
                            type: 'internal',
                            transactionEventSource: 'earnDisableRecharge',
                        }}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'cancel_submitted':
                                case 'transaction_request_replaced':
                                case 'transaction_submited':
                                case 'on_user_operation_bundled':
                                    noop()
                                    break
                                case 'on_minimize_click':
                                case 'on_cancel_confirm_transaction_clicked':
                                case 'on_safe_transaction_failure_accepted':
                                case 'on_transaction_cancelled_successfully_close_clicked':
                                case 'transaction_cancel_failure_accepted':
                                case 'transaction_failure_accepted':
                                case 'on_sign_cancel_button_clicked':
                                    onMsg({
                                        type: 'close',
                                    })
                                    break

                                case 'drag':
                                case 'on_expand_request':
                                case 'on_wrong_network_accepted':
                                    throw new ImperativeError(
                                        `should not got this msg in recharge disable`,
                                        msg
                                    )

                                case 'on_4337_auto_gas_token_selection_clicked':
                                case 'on_4337_gas_currency_selected':
                                case 'import_keys_button_clicked':
                                case 'on_predefined_fee_preset_selected':
                                    onMsg(msg)
                                    break
                                case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                case 'on_close_transaction_status_not_found_modal': // TODO:  rename and remove "confirm_holder_deployed" step @max
                                case 'on_transaction_completed_splash_animation_screen_competed':
                                case 'on_completed_transaction_close_click':
                                case 'on_completed_safe_transaction_close_click':
                                    postUserEvent({
                                        type: 'RechargeDisabledEvent',
                                        installationId,
                                    })
                                    onMsg({
                                        type: 'on_earn_recharge_configured',
                                        earn: {
                                            ...state.request.earn,
                                            cardRecharge: {
                                                type: 'recharge_disabled',
                                            },
                                        },
                                        cardReadonlySignerAddress:
                                            cardReadonlySigner.address,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
