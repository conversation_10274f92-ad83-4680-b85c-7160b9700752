import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LoadingLayout as UILoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { uuid } from '@zeal/toolkit/Crypto'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { keys } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    currencyId,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { fetchCurrenciesMatrix } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { SWAPS_IO_SUPPORTED_CURRENCIES_MAP } from '@zeal/domains/Currency/constants'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { fetchContractsWithCache } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchContractAddresses'
import { Earn, Taker } from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { fetchServerPortfolioWithCache } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { EarnEventLocation } from '@zeal/domains/UserEvents'

import { Flow } from './Flow'

type Props = {
    installationId: string
    taker: Taker
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig

    owner: Account

    earn: Earn
    keyStoreMap: KeyStoreMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    customCurrencies: CustomCurrencyMap
    isEthereumNetworkFeeWarningSeen: boolean
    cardConfig: CardConfig
    eventLocation: EarnEventLocation

    onMsg: (msg: Msg) => void
}

type Data = {
    fromCurrencies: CryptoCurrency[]
    swapsIOContractsMap: SwapsIOContractsMap
    serverPortfolio: ServerPortfolio2
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Flow>,
          {
              type:
                  | 'close'
                  | 'add_wallet_clicked'
                  | 'on_bank_transfer_selected'
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'hardware_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_earn_deposit_success'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_earn_configured'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_swaps_io_swap_request_created'
          }
      >

const fetch = async ({
    signal,
    networkRPCMap,
    networkMap,
    installationId,
    defaultCurrencyConfig,
    currencyHiddenMap,
    account,
    cacheKey,
}: {
    signal?: AbortSignal
    account: Account
    cacheKey: string

    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
}): Promise<Data> => {
    const [matrix, addresses, serverPortfolio] = await Promise.all([
        fetchCurrenciesMatrix(),
        fetchContractsWithCache({ cacheKey }).catch((error) => {
            captureError(error)
            // Defaulting to empty map in case this fails, because we want to keep both - updated
            // contracts in case they change from 3rd party side and also not to break earn deposit flow if this fails
            // This also can be in separate data loader of swap flow if we ever introduce it
            return {}
        }),
        fetchServerPortfolioWithCache({
            address: account.address,
            currencyHiddenMap,
            defaultCurrencyConfig,
            installationId,
            networkMap,
            networkRPCMap,
            signal,
            cacheKey,
        }),
    ])

    const fromCurrencyIds =
        matrix.currencies[EARN_NETWORK.hexChainId]?.[EARN_NETWORK.hexChainId]
            ?.from || []

    const currencyIds = [
        ...fromCurrencyIds,
        ...keys(SWAPS_IO_SUPPORTED_CURRENCIES_MAP)
            // We use a sockets provider for Swap in Earn feature
            .filter((network) => network !== EARN_NETWORK.hexChainId)
            .flatMap((network) =>
                SWAPS_IO_SUPPORTED_CURRENCIES_MAP[network].map((address) =>
                    currencyId({ address, network })
                )
            ),
    ]

    const fromCurrencies = currencyIds
        .map((id): CryptoCurrency | null => {
            const currency = matrix.knownCurrencies[id] || null

            if (!currency) {
                return null
            }

            switch (currency.type) {
                case 'FiatCurrency':
                    return null
                case 'CryptoCurrency':
                    return currency

                default:
                    return notReachable(currency)
            }
        })
        .filter(excludeNullValues)

    return {
        fromCurrencies,
        swapsIOContractsMap: addresses,
        serverPortfolio,
    }
}

export const Deposit = ({
    onMsg,
    owner,
    earn,
    keyStoreMap,
    taker,
    customCurrencies,
    installationId,
    sessionPassword,
    portfolioMap,
    currencyHiddenMap,
    currencyPinMap,
    cardConfig,
    feePresetMap,
    gasCurrencyPresetMap,
    networkRPCMap,
    accountsMap,
    defaultCurrencyConfig,
    isEthereumNetworkFeeWarningSeen,
    networkMap,
    eventLocation,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            accountsMap,
            defaultCurrencyConfig,
            account: owner,
            cacheKey: uuid(),
            currencyHiddenMap,
            installationId,
            networkMap,
            networkRPCMap,
        },
    })

    switch (loadable.type) {
        case 'loading':
            return <LoadingLayout onClose={() => onMsg({ type: 'close' })} />
        case 'loaded':
            return (
                <Flow
                    serverPortfolio={loadable.data.serverPortfolio}
                    fromAccount={loadable.params.account}
                    owner={owner}
                    taker={taker}
                    earn={earn}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    installationId={installationId}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    portfolioMap={portfolioMap}
                    customCurrencies={customCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    cardConfig={cardConfig}
                    swapsIOContractsMap={loadable.data.swapsIOContractsMap}
                    eventLocation={eventLocation}
                    fromCurrencies={loadable.data.fromCurrencies}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'add_wallet_clicked':
                            case 'on_bank_transfer_selected':
                            case 'track_wallet_clicked':
                            case 'on_account_create_request':
                            case 'on_accounts_create_success_animation_finished':
                            case 'hardware_wallet_clicked':
                            case 'on_add_label_to_track_only_account_during_send':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'cancel_submitted':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'transaction_submited':
                            case 'on_earn_deposit_success':
                            case 'on_ethereum_network_fee_warning_understand_clicked':
                            case 'on_earn_configured':
                            case 'on_address_scanned':
                            case 'on_address_scanned_and_add_label':
                            case 'on_swaps_io_swap_request_created':
                                onMsg(msg)
                                break

                            case 'on_top_up_transaction_complete_close':
                                setLoadable({
                                    type: 'loading',
                                    params: loadable.params,
                                })
                                break
                            case 'on_account_selected':
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        ...loadable.params,
                                        account: msg.account,
                                    },
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'error':
            const error = parseAppError(loadable.error)
            return (
                <>
                    <LoadingLayout onClose={() => onMsg({ type: 'close' })} />

                    <AppErrorPopup
                        error={error}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        default:
            return notReachable(loadable)
    }
}

const LoadingLayout = ({ onClose }: { onClose: () => void }) => (
    <UILoadingLayout
        title={null}
        onClose={onClose}
        actionBar={
            <ActionBar
                left={
                    <Clickable onClick={onClose}>
                        <BackIcon size={24} color="iconDefault" />
                    </Clickable>
                }
            />
        }
    />
)
