import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { GroupList } from '@zeal/uikit/GroupList'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'

import { notReachable } from '@zeal/toolkit'
import { values } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { UnlockedListItem } from '@zeal/domains/Account/components/UnlockedListItem'
import { sortByBalance } from '@zeal/domains/Account/helpers/sortByBalance'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    installationId: string
    earnOwner: Account
    selectedAccount: Account | null
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | { type: 'on_account_selected'; account: Account }

export const ChooseWallet = ({
    accountsMap,
    selectedAccount,
    installationId,
    keyStoreMap,
    currencyHiddenMap,
    portfolioMap,
    earnOwner,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const accounts = values(accountsMap)
        .toSorted((a, b) => {
            if (a.address === earnOwner.address) return -1
            if (b.address === earnOwner.address) return 1
            return sortByBalance(
                portfolioMap,
                currencyHiddenMap,
                defaultCurrencyConfig
            )(a, b)
        })
        .filter((account) => {
            const keystore = getKeyStore({
                address: account.address,
                keyStoreMap,
            })
            switch (keystore.type) {
                case 'private_key_store':
                case 'ledger':
                case 'secret_phrase_key':
                case 'trezor':
                case 'safe_4337':
                    return true
                case 'track_only':
                    return false
                /* istanbul ignore next */
                default:
                    return notReachable(keystore)
            }
        })

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <Row spacing={8}>
                        <IconButton
                            variant="on_light"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            {({ color }) => (
                                <BackIcon size={24} color={color} />
                            )}
                        </IconButton>
                        <ActionBar.Header>
                            <FormattedMessage
                                defaultMessage="Deposit from"
                                id="earn.choose_wallet_to_deposit.title"
                            />
                        </ActionBar.Header>
                    </Row>
                }
            />

            <Column spacing={24} fill shrink alignY="stretch">
                <Column spacing={12} shrink fill>
                    <GroupList
                        variant="default"
                        emptyState={null}
                        renderItem={({ item }) => (
                            <UnlockedListItem
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                installationId={installationId}
                                currencyHiddenMap={currencyHiddenMap}
                                selectionVariant="background_color"
                                key={item.address}
                                portfolio={unsafe_GetPortfolioCache2({
                                    address: item.address,
                                    portfolioMap,
                                })}
                                keyStore={getKeyStore({
                                    address: item.address,
                                    keyStoreMap,
                                })}
                                selected={
                                    selectedAccount?.address === item.address
                                }
                                account={item}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'account_item_clicked':
                                            onMsg({
                                                type: 'on_account_selected',
                                                account: msg.account,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg.type)
                                    }
                                }}
                            />
                        )}
                        data={accounts}
                    />
                </Column>
            </Column>
        </Screen>
    )
}
