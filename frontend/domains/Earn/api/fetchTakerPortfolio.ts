import { notReachable } from '@zeal/toolkit'
import { unsafe_toNumberWithFraction } from '@zeal/toolkit/BigInt'
import { withRetries } from '@zeal/toolkit/Function'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import { array, combine, object } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { requestBalanceOfToken } from '@zeal/domains/Address/api/fetchBalanceOf'
import { NULL_ADDRESS } from '@zeal/domains/Address/constants'
import {
    CryptoCurrency,
    Currency,
    DefaultCurrency,
    FiatCurrency,
} from '@zeal/domains/Currency'
import {
    FIAT_CURRENCIES,
    GNOSIS_EURE,
    GNOSIS_SDAI,
} from '@zeal/domains/Currency/constants'
import { FXRate2 } from '@zeal/domains/FXRate'
import {
    fetchDefaultCurrencyRateFromUSD,
    fetchDefaultCurrencyRateToFiatCurrency,
} from '@zeal/domains/FXRate/api/fetchDefaultCurrencyRateToUSD'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ETHEREUM } from '@zeal/domains/Network/constants'
import { EthCall, EthLogs } from '@zeal/domains/RPCRequest'
import { requestBlockNumber } from '@zeal/domains/RPCRequest/api/fetchBlockNumber'
import { fetchBlockTimestamp } from '@zeal/domains/RPCRequest/api/fetchBlockTimestamp'
import {
    fetchPublicRPCBatch,
    fetchRPCBatchWithRetry,
    Request,
} from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { fetchEthTakerApy } from './fetchEthTakerApy'
import { fetchEthTakerUserCurrencyRate } from './fetchTakerUserCurrencyRate'

import { Taker, TakerPortfolio, TakerType } from '..'
import {
    AAVE_MARKET_GET_RESERVE_DATA_ABI,
    EARN_EUR_TAKER_USER_CURRENCY,
    EARN_NETWORK,
    EARN_PRIMARY_INVESTMENT_ASSETS_MAP,
    EARN_USD_TAKER_USER_CURRENCY,
    EURE_AAVE_POOL_ADDRESS,
    GNOSIS_S_DAI_VAULT_ADDRESS,
    GNOSIS_S_DAI_VAULT_APY_ABI,
    GNOSIS_SDAI_ABI,
} from '../constants'
import { getHolderPredictedAddress } from '../helpers/getHolderPredictedAddress'
import { getTakerPredictedAddress } from '../helpers/getTakerPredictedAddress'

const ethCall = (param: object): EthCall => {
    return {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'eth_call',
        params: [param, 'latest'],
    }
}

const requestSDaiAPY = (): Request<bigint> => {
    return {
        request: ethCall({
            from: NULL_ADDRESS,
            data: Web3.abi.encodeFunctionData({
                abi: GNOSIS_S_DAI_VAULT_APY_ABI,
                functionName: 'vaultAPY',
            }),
            to: GNOSIS_S_DAI_VAULT_ADDRESS,
        }),
        parser: (input) => {
            return Web3.abi.decodeFunctionResult({
                abi: GNOSIS_S_DAI_VAULT_APY_ABI,
                data: input as `0x${string}`,
                functionName: 'vaultAPY',
            })
        },
    }
}

const requestConvertToAssets = (): Request<bigint> => {
    return {
        request: ethCall({
            data: Web3.abi.encodeFunctionData({
                abi: GNOSIS_SDAI_ABI,
                functionName: 'convertToAssets',
                args: [
                    10n ** BigInt(EARN_USD_TAKER_USER_CURRENCY.rateFraction),
                ],
            }),
            to: GNOSIS_SDAI.address,
        }),
        parser: (input) => {
            return Web3.abi.decodeFunctionResult({
                abi: GNOSIS_SDAI_ABI,
                data: input as `0x${string}`,
                functionName: 'convertToAssets',
            })
        },
    }
}

export const requestAAVEEureAPY = (): Request<bigint> => {
    return {
        request: ethCall({
            from: NULL_ADDRESS,
            to: EURE_AAVE_POOL_ADDRESS,
            data: Web3.abi.encodeFunctionData({
                abi: AAVE_MARKET_GET_RESERVE_DATA_ABI,
                functionName: 'getReserveData',
                args: [GNOSIS_EURE.address as `0x${string}`],
            }),
        }),

        parser: (res) => {
            return Web3.abi.decodeFunctionResult({
                abi: AAVE_MARKET_GET_RESERVE_DATA_ABI,
                data: res as `0x${string}`,
                functionName: 'getReserveData',
            }).currentLiquidityRate
        },
    }
}

export const fetchTakerPortfolioByAddress = async ({
    defaultCurrencyConfig,
    takerAddress,
    networkMap,
    networkRPCMap,
    takerType,
    signal,
}: {
    takerType: TakerType
    takerAddress: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<TakerPortfolio> => {
    const currency = EARN_PRIMARY_INVESTMENT_ASSETS_MAP[takerType]
    const network = EARN_NETWORK
    switch (takerType) {
        case 'usd': {
            const [
                [
                    amount,
                    sDaiVaultAPYResponse,
                    sDAIconvertToAssetRate,
                    blockNumber,
                ],
                userCurrencyToDefaultCurrencyRate,
            ] = await Promise.all([
                fetchPublicRPCBatch(
                    [
                        requestBalanceOfToken({
                            address: takerAddress,
                            tokenAddress:
                                currency.address as Web3.address.Address,
                        }),
                        requestSDaiAPY(),
                        requestConvertToAssets(),
                        requestBlockNumber(),
                    ],
                    {
                        network,
                        signal,
                    }
                ),

                fetchTakerUserCurrencyToDefaultCurrencyRate({
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    takerType,
                    signal,
                }),
            ])
            const dataTimestampMs =
                await fetchLatestEarningEventTimestampWithRetry(
                    takerType,
                    blockNumber
                )

            const apy = unsafe_toNumberWithFraction(sDaiVaultAPYResponse, 16)

            const userCurrencyRate: FXRate2<CryptoCurrency, FiatCurrency> = {
                base: currency,
                quote: EARN_USD_TAKER_USER_CURRENCY,
                rate: sDAIconvertToAssetRate,
            }

            return {
                apy,
                assetBalance: { amount, currency },
                userCurrencyRate,
                dataTimestampMs,
                userCurrencyToDefaultCurrencyRate,
            }
        }

        case 'eur': {
            const [
                [amount, aaveMarketDataResponse],
                userCurrencyToDefaultCurrencyRate,
            ] = await Promise.all([
                fetchPublicRPCBatch(
                    [
                        requestBalanceOfToken({
                            address: takerAddress,
                            tokenAddress:
                                currency.address as Web3.address.Address,
                        }),
                        requestAAVEEureAPY(),
                    ],
                    {
                        network,
                        signal,
                    }
                ),
                fetchTakerUserCurrencyToDefaultCurrencyRate({
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    takerType,
                    signal,
                }),
            ])

            const userCurrencyRate = {
                base: currency,
                quote: EARN_EUR_TAKER_USER_CURRENCY,
                rate: 10n ** BigInt(EARN_EUR_TAKER_USER_CURRENCY.rateFraction),
            }

            const apy = unsafe_toNumberWithFraction(aaveMarketDataResponse, 25)

            return {
                apy,
                assetBalance: { amount, currency },
                userCurrencyRate,
                dataTimestampMs: Date.now(),
                userCurrencyToDefaultCurrencyRate,
            }
        }

        case 'eth': {
            const [
                apy,
                [amount],
                userCurrencyRate,
                userCurrencyToDefaultCurrencyRate,
            ] = await Promise.all([
                fetchEthTakerApy(),
                fetchPublicRPCBatch(
                    [
                        requestBalanceOfToken({
                            address: takerAddress,
                            tokenAddress:
                                currency.address as Web3.address.Address,
                        }),
                    ],
                    {
                        signal,
                        network,
                    }
                ),
                fetchEthTakerUserCurrencyRate({ networkRPCMap, signal }),
                fetchTakerUserCurrencyToDefaultCurrencyRate({
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    takerType,
                    signal,
                }),
            ])

            return {
                apy,
                assetBalance: { amount, currency },
                userCurrencyRate,
                dataTimestampMs: Date.now(),
                userCurrencyToDefaultCurrencyRate,
            }
        }

        default:
            return notReachable(takerType)
    }
}
export const fetchTakerPortfolioByEarnOwner = async ({
    defaultCurrencyConfig,
    earnOwnerAddress,
    networkMap,
    networkRPCMap,
    takerType,
    signal,
}: {
    takerType: TakerType
    earnOwnerAddress: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<TakerPortfolio> => {
    const holder = getHolderPredictedAddress({ earnOwnerAddress })
    const takerAddress = getTakerPredictedAddress({ holder, takerType })
    return fetchTakerPortfolioByAddress({
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        takerAddress,
        takerType,
        signal,
    })
}

export const fetchTakerPortfolio = async ({
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    taker,
    signal,
}: {
    taker: Taker
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<TakerPortfolio> => {
    return fetchTakerPortfolioByAddress({
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        takerAddress: taker.address,
        takerType: taker.type,
        signal,
    })
}

const fetchTakerUserCurrencyToDefaultCurrencyRate = ({
    takerType,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    signal,
}: {
    takerType: TakerType
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<FXRate2<Currency, DefaultCurrency> | null> => {
    switch (takerType) {
        case 'usd':
            return fetchDefaultCurrencyRateFromUSD({
                defaultCurrencyConfig,
                signal,
            })

        case 'eur':
            return fetchDefaultCurrencyRateToFiatCurrency({
                fiatCurrency: FIAT_CURRENCIES.EUR,
                defaultCurrency: defaultCurrencyConfig.defaultCurrency,
                signal,
            })

        case 'eth':
            return fetchRate({
                cryptoCurrency: ETHEREUM.nativeCurrency,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                signal,
            })

        default:
            return notReachable(takerType)
    }
}

const RETRY_DELAY_MS = 500
const MAX_RETRIES_COUNT = 10

const PAGE = 600

const fetchLatestEarningEventTimestamp = async (
    takerType: Taker['type'],
    startingFromBlock: number
): Promise<number> => {
    switch (takerType) {
        case 'usd':
            const start = startingFromBlock - PAGE
            const fromBlock = Hexadecimal.unpad(Hexadecimal.fromNumber(start))
            const toBlock = Hexadecimal.unpad(
                Hexadecimal.fromNumber(startingFromBlock)
            )

            const withdraw: EthLogs = {
                id: generateRandomNumber(),
                jsonrpc: '2.0' as const,
                method: 'eth_getLogs',
                params: [
                    {
                        address: GNOSIS_SDAI.address,
                        topics: [
                            '0xfbde797d201c681b91056529119e0b02407c7bb96a4a2c75c01fc9667232c8db',
                        ],
                        fromBlock: fromBlock,
                        toBlock: toBlock,
                    },
                ],
            }

            const deposit: EthLogs = {
                id: generateRandomNumber(),
                jsonrpc: '2.0' as const,
                method: 'eth_getLogs',
                params: [
                    {
                        address: GNOSIS_SDAI.address,
                        topics: [
                            '0xdcbc1c05240f31ff3ad067ef1ee35ce4997762752e3a095284754544f4c709d7',
                        ],
                        fromBlock: fromBlock,
                        toBlock: toBlock,
                    },
                ],
            }

            const responses = await fetchRPCBatchWithRetry({
                network: EARN_NETWORK,
                networkRPCMap: {},
                requests: [deposit, withdraw],
            })

            const blocks = responses.flatMap((res) =>
                array(res)
                    .andThen((arr) =>
                        combine(
                            arr.map((item) =>
                                object(item).andThen((obj) =>
                                    Hexadecimal.parserHexAsNumber(
                                        obj.blockNumber
                                    )
                                )
                            )
                        )
                    )
                    .getSuccessResultOrThrow(
                        'Failed to parse blocks from earnings event logs'
                    )
            )

            if (blocks.length === 0) {
                return fetchLatestEarningEventTimestamp(takerType, start - 1)
            }

            const blockNumber = Math.max(...blocks)
            return fetchBlockTimestamp({
                blockNumber: Hexadecimal.fromNumber(blockNumber),
                network: EARN_NETWORK,
                networkRPCMap: {},
            })
        case 'eur':
        case 'eth':
            return Date.now()
        /* istanbul ignore next */
        default:
            return notReachable(takerType)
    }
}

const fetchLatestEarningEventTimestampWithRetry = withRetries(
    fetchLatestEarningEventTimestamp,
    MAX_RETRIES_COUNT,
    RETRY_DELAY_MS
)
