import { get } from '@zeal/api/requestBackend'

import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import {
    array,
    groupByType,
    number,
    numberString,
    object,
    Result,
    shape,
    string,
    UnexpectedResultFailureError,
} from '@zeal/toolkit/Result'

import { UsdEarnTakerMetrics } from '@zeal/domains/Earn'
import { EARN_USD_TAKER_USER_CURRENCY } from '@zeal/domains/Earn/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'

type SkyCollateralGroups = {
    stableCoins: number
    bitcoinEther: number
    usTBills: number
    other: number
}

type SkyCollateralItem = {
    symbol: string
    amount: number
}

const STABLE_COINS = new Set([
    'sUSDS',
    'PT-sUSDe',
    'sUSDe',
    'PT-eUSDE-29MAY2025',
    'PT-USDS-14AUG2025',
    'USDe',
    'DAI',
    'PT-USDe',
    'sDAI',
    'USDT',
    'USDC',
    'USDS',
    'PT-eUSDE-14AUG2025',
    'PYUSD',
    'eUSDe',
    'USR',
    'LP_USR',
    'EURC',
])

const BITCOIN_ETHER = new Set([
    'ETH',
    'wstETH',
    'cbBTC',
    'LBTC',
    'WBTC',
    'rETH',
    'weETH',
    'tBTC',
    'rsETH',
    'ezETH',
    'BTC',
    'tETH',
])

const US_TBILLS = new Set(['T-Bills', 'USTB'])

const OTHER = new Set([
    'AAVE',
    'cbETH',
    'LINK',
    'eBTC',
    'UNI',
    'USDS',
    'MKR',
    'osETH',
    'LDO',
    'SNX',
    'PYUSD',
    '1INCH',
    'eUSDe',
    'ETHx',
    'CRV',
    'BAL',
    'ENS',
    'G-UNI',
    'RWA',
    'SOL',
    'UNI-V2',
    'SKY',
    'XRP',
    'jitoSOL',
])

export const fetchUsdEarnTakerMetrics = async ({
    signal,
}: {
    signal?: AbortSignal
}): Promise<UsdEarnTakerMetrics> => {
    const [totalSupplyResponse, collateralResponse] = await Promise.all([
        get('/proxy/cba/sky/usds/total-supply', {}, signal),
        get('/proxy/cba/spark/savings/usds/backed/', {}, signal),
    ])

    const totalSupply = number(totalSupplyResponse).getSuccessResultOrThrow(
        'Failed to parse Sky total supply response'
    )

    const collateralItems = array(collateralResponse).getSuccessResultOrThrow(
        'Failed to parse Sky collateral response'
    )

    const [errors, parsedCollateralItems] = groupByType(
        collateralItems.map(parseSkyCollateralItem)
    )

    if (errors.length) {
        captureError(
            new UnexpectedResultFailureError(
                'Failed to parse some sky collateral items',
                errors
            )
        )
    }

    const collateralGroups = groupCollateralItems(parsedCollateralItems)

    if (collateralGroups.unknownAssets.length) {
        captureError(
            new UnexpectedResultFailureError(
                'Unknown sky collateral assets found',
                { assets: collateralGroups.unknownAssets }
            )
        )
    }

    const totalCollateral = parsedCollateralItems.reduce(
        (acc, item) => acc + item.amount,
        0
    )

    return {
        reserveTotal: {
            amount: fromFixedWithFraction(
                totalCollateral.toString(10),
                EARN_USD_TAKER_USER_CURRENCY.fraction
            ),
            currency: EARN_USD_TAKER_USER_CURRENCY,
        },
        depositorObligations: {
            amount: fromFixedWithFraction(
                totalSupply.toString(10),
                EARN_USD_TAKER_USER_CURRENCY.fraction
            ),
            currency: EARN_USD_TAKER_USER_CURRENCY,
        },
        assetCoverageRatio: totalCollateral / totalSupply,
        collateralComposition: {
            stableCoins: {
                amount: fromFixedWithFraction(
                    collateralGroups.grouped.stableCoins.toString(10),
                    EARN_USD_TAKER_USER_CURRENCY.fraction
                ),
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
            bitcoinEther: {
                amount: fromFixedWithFraction(
                    collateralGroups.grouped.bitcoinEther.toString(10),
                    EARN_USD_TAKER_USER_CURRENCY.fraction
                ),
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
            usTBills: {
                amount: fromFixedWithFraction(
                    collateralGroups.grouped.usTBills.toString(10),
                    EARN_USD_TAKER_USER_CURRENCY.fraction
                ),
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
            other: {
                amount: fromFixedWithFraction(
                    collateralGroups.grouped.other.toString(10),
                    EARN_USD_TAKER_USER_CURRENCY.fraction
                ),
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
        },
    }
}

const groupCollateralItems = (
    items: SkyCollateralItem[]
): { grouped: SkyCollateralGroups; unknownAssets: string[] } => {
    return items.reduce(
        (map, item) => {
            if (STABLE_COINS.has(item.symbol)) {
                map.grouped.stableCoins += item.amount
                return map
            }
            if (BITCOIN_ETHER.has(item.symbol)) {
                map.grouped.bitcoinEther += item.amount
                return map
            }
            if (US_TBILLS.has(item.symbol)) {
                map.grouped.usTBills += item.amount
                return map
            }
            if (OTHER.has(item.symbol)) {
                map.grouped.other += item.amount
                return map
            }
            map.grouped.other += item.amount
            map.unknownAssets.push(item.symbol)
            return map
        },
        {
            grouped: {
                stableCoins: 0,
                bitcoinEther: 0,
                usTBills: 0,
                other: 0,
            } as SkyCollateralGroups,
            unknownAssets: [] as string[],
        }
    )
}

const parseSkyCollateralItem = (
    input: unknown
): Result<unknown, SkyCollateralItem> =>
    object(input).andThen((obj) =>
        shape({
            symbol: string(obj.symbol),
            amount: numberString(obj.amount),
        })
    )
