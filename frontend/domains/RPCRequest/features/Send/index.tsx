import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { useReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import {
    ActionSource2,
    SendERC20Token as SendERC20Entrypoint,
    SendNFT as SendNFTEntrypoint,
} from '@zeal/domains/Main'
import { SendERC20 } from '@zeal/domains/Money/features/SendERC20/'
import { SendERC20v2 } from '@zeal/domains/Money/features/SendERC20v2'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { fetchServerPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { SendNFT } from './SendNFT'

type Props = {
    entrypoint: SendNFTEntrypoint | SendERC20Entrypoint
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    actionSource: ActionSource2
    presetMap: FeePresetMap
    isEthereumNetworkFeeWarningSeen: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof SendERC20>
    | MsgOf<typeof SendNFT>
    | MsgOf<typeof SendERC20v2>

export const Send = ({
    entrypoint,
    accountsMap,
    customCurrencies,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    feePresetMap,
    portfolioMap,
    sessionPassword,
    currencyHiddenMap,
    currencyPinMap,
    cardConfig,
    onMsg,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
    actionSource,
    presetMap,
    isEthereumNetworkFeeWarningSeen,
}: Props) => {
    const portfolioCache = unsafe_GetPortfolioCache2({
        address: entrypoint.fromAddress,
        portfolioMap,
    })

    const params = {
        address: entrypoint.fromAddress,
        networkMap,
        networkRPCMap,
        customCurrencies,
        cardConfig,
        forceRefresh: false,
        defaultCurrencyConfig,
        currencyHiddenMap,
        installationId,
    }
    const [loadable, setLoadable] = useReloadableData(
        fetchServerPortfolio2,
        portfolioCache
            ? {
                  type: 'reloading',
                  params,
                  data: portfolioCache,
              }
            : {
                  type: 'loading',
                  params,
              }
    )

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout title={null} actionBar={null} onClose={null} />
            )
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed':
            switch (entrypoint.type) {
                case 'send_nft':
                    return (
                        <SendNFT
                            actionSource={actionSource}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            portfolio={loadable.data}
                            currencyHiddenMap={currencyHiddenMap}
                            feePresetMap={feePresetMap}
                            cardConfig={cardConfig}
                            accountsMap={accountsMap}
                            customCurrencyMap={customCurrencies}
                            entryPoint={entrypoint}
                            installationId={installationId}
                            keyStoreMap={keyStoreMap}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            portfolioMap={portfolioMap}
                            sessionPassword={sessionPassword}
                            isEthereumNetworkFeeWarningSeen={
                                isEthereumNetworkFeeWarningSeen
                            }
                            onMsg={onMsg}
                        />
                    )

                case 'send_erc20_token':
                    const keystore = getKeyStore({
                        address: entrypoint.fromAddress,
                        keyStoreMap,
                    })

                    switch (keystore.type) {
                        case 'private_key_store':
                        case 'ledger':
                        case 'secret_phrase_key':
                        case 'trezor':
                        case 'track_only':
                            return (
                                <SendERC20
                                    actionSource={actionSource}
                                    initialToAddress={entrypoint.toAddress}
                                    defaultCurrencyConfig={
                                        defaultCurrencyConfig
                                    }
                                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                                    portfolio={loadable.data}
                                    feePresetMap={feePresetMap}
                                    currencyHiddenMap={currencyHiddenMap}
                                    currencyPinMap={currencyPinMap}
                                    account={
                                        accountsMap[entrypoint.fromAddress]
                                    }
                                    accountsMap={accountsMap}
                                    currencyId={entrypoint.tokenCurrencyId}
                                    customCurrencies={customCurrencies}
                                    installationId={installationId}
                                    cardConfig={cardConfig}
                                    keyStoreMap={keyStoreMap}
                                    networkMap={networkMap}
                                    networkRPCMap={networkRPCMap}
                                    portfolioMap={portfolioMap}
                                    sessionPassword={sessionPassword}
                                    isEthereumNetworkFeeWarningSeen={
                                        isEthereumNetworkFeeWarningSeen
                                    }
                                    onMsg={onMsg}
                                />
                            )
                        case 'safe_4337':
                            return entrypoint.toAddress ? (
                                <SendERC20v2
                                    cardConfig={cardConfig}
                                    currencyHiddenMap={currencyHiddenMap}
                                    defaultCurrencyConfig={
                                        defaultCurrencyConfig
                                    }
                                    installationId={installationId}
                                    keyStoreMap={keyStoreMap}
                                    networkMap={networkMap}
                                    networkRPCMap={networkRPCMap}
                                    sessionPassword={sessionPassword}
                                    fromWallet={
                                        accountsMap[entrypoint.fromAddress]
                                    }
                                    accountsMap={accountsMap}
                                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                                    isEthereumNetworkFeeWarningSeen={
                                        isEthereumNetworkFeeWarningSeen
                                    }
                                    portfolioMap={portfolioMap}
                                    currencyPinMap={currencyHiddenMap}
                                    actionSource={actionSource}
                                    customCurrencies={customCurrencies}
                                    fromCurrencyId={entrypoint.tokenCurrencyId}
                                    presetMap={presetMap}
                                    toAddress={entrypoint.toAddress}
                                    onMsg={onMsg}
                                />
                            ) : (
                                <SendERC20
                                    actionSource={actionSource}
                                    initialToAddress={entrypoint.toAddress}
                                    defaultCurrencyConfig={
                                        defaultCurrencyConfig
                                    }
                                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                                    portfolio={loadable.data}
                                    feePresetMap={feePresetMap}
                                    currencyHiddenMap={currencyHiddenMap}
                                    currencyPinMap={currencyPinMap}
                                    account={
                                        accountsMap[entrypoint.fromAddress]
                                    }
                                    accountsMap={accountsMap}
                                    currencyId={entrypoint.tokenCurrencyId}
                                    customCurrencies={customCurrencies}
                                    installationId={installationId}
                                    cardConfig={cardConfig}
                                    keyStoreMap={keyStoreMap}
                                    networkMap={networkMap}
                                    networkRPCMap={networkRPCMap}
                                    portfolioMap={portfolioMap}
                                    sessionPassword={sessionPassword}
                                    isEthereumNetworkFeeWarningSeen={
                                        isEthereumNetworkFeeWarningSeen
                                    }
                                    onMsg={onMsg}
                                />
                            )

                        default:
                            return notReachable(keystore)
                    }

                /* istanbul ignore next */
                default:
                    return notReachable(entrypoint)
            }
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={null}
                        onClose={null}
                    />
                    <AppErrorPopup
                        installationId={installationId}
                        error={parseAppError(loadable.error)}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg({
                                        type: 'close',
                                    })
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
