import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ActionSource2, SendNFT as SendNFTEntrypoint } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    PortfolioNFT,
    PortfolioNFTCollection,
} from '@zeal/domains/NFTCollection'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Flow } from './Flow'

type Props = {
    accountsMap: AccountsMap
    portfolioMap: PortfolioMap
    customCurrencyMap: CustomCurrencyMap
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    installationId: string
    entryPoint: SendNFTEntrypoint
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    currencyHiddenMap: CurrencyHiddenMap
    portfolio: ServerPortfolio2
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    isEthereumNetworkFeeWarningSeen: boolean
    actionSource: ActionSource2
    onMsg: (msg: Msg) => void
}

export type Msg = MsgOf<typeof Flow>

const calculateToken = ({
    entryPoint,
    accountsMap,
    portfolio,
}: {
    entryPoint: SendNFTEntrypoint
    accountsMap: AccountsMap
    portfolio: ServerPortfolio2
}): {
    fromAccount: Account
    nft: PortfolioNFT
    collection: PortfolioNFTCollection
} => {
    const fromAccount = accountsMap[entryPoint.fromAddress]
    if (!fromAccount) {
        throw new ImperativeError(
            'we try to send token from non existent account'
        )
    }

    const collection = portfolio.nftCollections.find((collection) => {
        return (
            collection.networkHexId === entryPoint.networkHexId &&
            collection.mintAddress === entryPoint.mintAddress
        )
    })
    if (!collection) {
        throw new ImperativeError(
            'we try to send NFT but we collection is missing in portfolio'
        )
    }

    const nft = collection.nfts.find((nft) => nft.tokenId === entryPoint.nftId)
    if (!nft) {
        throw new ImperativeError(
            'we try to send NFT but we cannot find NFT in collection'
        )
    }

    return { fromAccount, nft, collection }
}

export const SendNFT = ({
    entryPoint,
    accountsMap,
    customCurrencyMap,
    keyStoreMap,
    portfolio,
    sessionPassword,
    installationId,
    networkMap,
    networkRPCMap,
    feePresetMap,
    currencyHiddenMap,
    portfolioMap,
    cardConfig,
    onMsg,
    isEthereumNetworkFeeWarningSeen,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
    actionSource,
}: Props) => {
    const { fromAccount, collection, nft } = calculateToken({
        entryPoint,
        accountsMap,
        portfolio,
    })

    return (
        <Flow
            actionSource={actionSource}
            defaultCurrencyConfig={defaultCurrencyConfig}
            gasCurrencyPresetMap={gasCurrencyPresetMap}
            cardConfig={cardConfig}
            portfolio={portfolio}
            currencyHiddenMap={currencyHiddenMap}
            feePresetMap={feePresetMap}
            networkMap={networkMap}
            networkRPCMap={networkRPCMap}
            installationId={installationId}
            collection={collection}
            nft={nft}
            fromAccount={fromAccount}
            customCurrencyMap={customCurrencyMap}
            accountsMap={accountsMap}
            keyStoreMap={keyStoreMap}
            portfolioMap={portfolioMap}
            sessionPassword={sessionPassword}
            isEthereumNetworkFeeWarningSeen={isEthereumNetworkFeeWarningSeen}
            onMsg={onMsg}
        />
    )
}
