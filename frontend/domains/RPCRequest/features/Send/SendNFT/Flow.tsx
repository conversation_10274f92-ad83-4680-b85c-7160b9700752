import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { SelectToAddress } from '@zeal/domains/Account/features/SelectToAddress'
import { CardConfig } from '@zeal/domains/Card'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import {
    PortfolioNFT,
    PortfolioNFTCollection,
} from '@zeal/domains/NFTCollection'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { createNFTEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createNFTEthSendTransaction'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { fetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { fetchTransactionResultByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'

type Props = {
    nft: PortfolioNFT
    collection: PortfolioNFTCollection

    fromAccount: Account
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    accountsMap: AccountsMap
    portfolio: ServerPortfolio2
    portfolioMap: PortfolioMap
    customCurrencyMap: CustomCurrencyMap
    keyStoreMap: KeyStoreMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: CardConfig
    actionSource: ActionSource2
    installationId: string

    sessionPassword: string
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    isEthereumNetworkFeeWarningSeen: boolean
    onMsg: (msg: Msg) => void
}

export type Msg =
    | Extract<
          MsgOf<typeof SelectToAddress>,
          {
              type:
                  | 'close'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_account_create_request'
                  | 'track_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'on_address_scanned_and_add_label'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
          }
      >
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'transaction_submited'
                  | 'cancel_submitted'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'on_safe_transaction_completed_splash_animation_screen_competed'
                  | 'on_gas_currency_selected'
                  | 'transaction_request_replaced'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >

type State =
    | {
          type: 'select_to_address'
      }
    | {
          type: 'send_token'
          ethTransaction: EthSendTransaction
          network: Network
      }

export const Flow = ({
    collection,
    nft,
    fromAccount,
    actionSource,
    onMsg,
    portfolio,
    portfolioMap,
    accountsMap,
    keyStoreMap,
    sessionPassword,
    installationId,
    cardConfig,
    customCurrencyMap,
    networkMap,
    networkRPCMap,
    feePresetMap,
    currencyHiddenMap,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
    isEthereumNetworkFeeWarningSeen,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'select_to_address' })

    const network = findNetworkByHexChainId(collection.networkHexId, networkMap)

    switch (state.type) {
        case 'select_to_address':
            return (
                <SelectToAddress
                    account={fromAccount}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationId={installationId}
                    currencyHiddenMap={currencyHiddenMap}
                    cardConfig={cardConfig}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    accountsMap={accountsMap}
                    customCurrencies={customCurrencyMap}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    toAddress={null}
                    sessionPassword={sessionPassword}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'track_wallet_clicked':
                            case 'add_wallet_clicked':
                            case 'hardware_wallet_clicked':
                            case 'on_ethereum_network_fee_warning_understand_clicked':
                                onMsg(msg)
                                break

                            case 'on_accounts_create_success_animation_finished':
                                // we don't do redirect on send flow
                                break

                            case 'on_add_label_skipped':
                                setState({
                                    type: 'send_token',
                                    network,
                                    ethTransaction: createNFTEthSendTransaction(
                                        {
                                            fromAccount,
                                            toAddress: msg.address,
                                            collection,
                                            nft,
                                        }
                                    ),
                                })
                                break

                            case 'on_add_label_to_track_only_account_during_send':
                            case 'on_address_scanned_and_add_label':
                            case 'on_account_create_request':
                                onMsg(msg)
                                setState({
                                    type: 'send_token',
                                    network,
                                    ethTransaction: createNFTEthSendTransaction(
                                        {
                                            fromAccount,
                                            toAddress:
                                                msg.accountsWithKeystores[0]
                                                    .account.address,
                                            collection,
                                            nft,
                                        }
                                    ),
                                })
                                break
                            case 'on_address_scanned':
                            case 'account_item_clicked':
                                setState({
                                    type: 'send_token',
                                    network,
                                    ethTransaction: createNFTEthSendTransaction(
                                        {
                                            fromAccount,
                                            toAddress: msg.account.address,
                                            collection,
                                            nft,
                                        }
                                    ),
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'send_token':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={portfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    fetchSimulationByRequest={fetchSimulationByRequest}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    installationId={installationId}
                    accounts={accountsMap}
                    keystores={keyStoreMap}
                    state={{ type: 'maximised' }}
                    network={state.network}
                    networkRPCMap={networkRPCMap}
                    account={fromAccount}
                    sendTransactionRequests={[state.ethTransaction]}
                    sessionPassword={sessionPassword}
                    actionSource={actionSource}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_sign_cancel_button_clicked':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'on_minimize_click':
                            case 'on_completed_transaction_close_click':
                            case 'on_completed_safe_transaction_close_click':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_close_transaction_status_not_found_modal':
                                onMsg({ type: 'close' })
                                break
                            case 'on_user_operation_bundled':
                            case 'on_expand_request':
                            case 'drag':
                                break

                            case 'import_keys_button_clicked':
                            case 'transaction_submited':
                            case 'cancel_submitted':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_4337_gas_currency_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'transaction_request_replaced':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
