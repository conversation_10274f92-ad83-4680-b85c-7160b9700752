import { externalFetch } from '@zeal/api/externalFetch'
import * as requestBackend from '@zeal/api/requestBackend'
import { prepareBaseUrl } from '@zeal/api/requestBackend'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { withRetries } from '@zeal/toolkit/Function'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { arrayOf, combine, object, string } from '@zeal/toolkit/Result'

import { ActionSource2 } from '@zeal/domains/Main'
import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import { GNOSIS } from '@zeal/domains/Network/constants'
import { getNetworkRPC } from '@zeal/domains/Network/helpers/getNetworkRPC'
import { RPCRequest } from '@zeal/domains/RPCRequest'

import { parseRPCResponse } from '../parsers/parseRPCResponse'

const RPC_RETRY_DELAY = 1000
const RPC_RETRY_COUNT = 3

// TODO :: @max move to toolkit/Function
const retryWithResources = async <T, Data>(
    resorces: T[],
    callback: (resorce: T) => Promise<Data>
): Promise<Data> => {
    if (!resorces.length) {
        throw new ImperativeError('resource is empty array')
    }
    const [first, ...rest] = resorces
    try {
        return await callback(first)
    } catch (e) {
        if (rest.length) {
            return await retryWithResources(rest, callback)
        }
        throw e
    }
}

export async function fetchPublicRPCBatch<T extends unknown[]>(
    requests: { [K in keyof T]: Request<T[K]> },
    params: { signal?: AbortSignal; network: typeof GNOSIS }
): Promise<T>

export async function fetchPublicRPCBatch<T>(
    requests: Request<T>[],
    params: { network: typeof GNOSIS; signal?: AbortSignal }
): Promise<T[]> {
    return await retryWithResources(
        getPublickRPCurls(params.network),
        async (url) => {
            const reqs = requests.map(({ request }) => request)

            const data = await externalFetch(url, {
                signal: params.signal,
                method: 'POST',
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                    'Accept-Encoding': 'gzip, deflate',
                },
                body: JSON.stringify(reqs),
            })
            const json = await data.json()
            const alignedResponses = alignResponses({
                requests: reqs,
                responses: json,
            })
            const rawRPCResponse = combine(
                alignedResponses.map((item, index) =>
                    parseRPCResponse({
                        input: item,
                        networkHexId: params.network.hexChainId,
                        requestBody: requests[index],
                    })
                )
            ).getSuccessResultOrThrow('Failed to parse one of batch responses')

            return rawRPCResponse.map((item, index) =>
                requests[index].parser(item)
            )
        }
    )
}

const getPublickRPCurls = (network: typeof GNOSIS): string[] => {
    switch (network.name) {
        case 'Gnosis':
            return [
                'https://rpc.gnosis.gateway.fm',
                'https://rpc.gnosischain.com',
                prepareBaseUrl({ path: `/proxy/rpc/${network.hexChainId}` }),
            ]
        default:
            return notReachable(network.name)
    }
}

const rpcPost = async ({
    body,
    network,
    networkRPCMap,
    signal,
}: {
    body: object
    networkRPCMap: NetworkRPCMap
    network: Network
    signal?: AbortSignal
}): Promise<unknown> => {
    const networkRPC = getNetworkRPC({ network, networkRPCMap })

    switch (networkRPC.current.type) {
        case 'default':
            switch (network.type) {
                case 'predefined':
                case 'testnet':
                    return requestBackend
                        .post(
                            `/proxy/rpc/${network.hexChainId}`,
                            { body },
                            signal
                        )
                        .then((response) =>
                            string(response)
                                .andThen(parseJSON)
                                .getSuccessResultOrThrow(
                                    'Failed to parse RPC response JSON'
                                )
                        )

                case 'custom':
                    return externalFetch(network.rpcUrl, {
                        body: JSON.stringify(body),
                        method: 'POST',
                        headers: {
                            'content-type': 'application/json',
                        },
                        signal,
                    }).then((res) => res.json())

                /* istanbul ignore next */
                default:
                    return notReachable(network)
            }

        case 'custom':
            return externalFetch(networkRPC.current.url, {
                body: JSON.stringify(body),
                method: 'POST',
                headers: {
                    'content-type': 'application/json',
                },
                signal,
            }).then((res) => res.json())

        default:
            return notReachable(networkRPC.current)
    }
}

const alignResponses = ({
    requests,
    responses,
}: {
    requests: RPCRequest[]
    responses: unknown
}): unknown[] => {
    const ids = requests.map((r) => r.id)
    const responsesArray = arrayOf(responses, object).getSuccessResultOrThrow(
        'Failed to parse batch responses array'
    )

    const alignedResponses = ids.reduce((acc, id) => {
        const response = responsesArray.find((r) => r.id === id)

        if (!response) {
            throw new ImperativeError('Response not found for request', {
                id,
                requests,
                responses,
            })
        }

        acc.push(response)

        return acc
    }, [] as unknown[])

    if (alignedResponses.length !== requests.length) {
        throw new ImperativeError(
            'Batch response length does not match request length',
            { requests, responses, alignedResponses }
        )
    }

    return alignedResponses
}

export type Request<T> = {
    request: RPCRequest
    parser: (response: unknown) => T
}

type RPCRequestParams = {
    network: Network
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}

export function fetchRPCBatch2<T extends unknown[]>(
    requests: { [K in keyof T]: Request<T[K]> },
    params: RPCRequestParams
): Promise<T>

export async function fetchRPCBatch2<T>(
    requests: Request<T>[],
    { networkRPCMap, network, signal }: RPCRequestParams
): Promise<T[]> {
    const responses = await fetchRPCBatch({
        requests: requests.map(({ request }) => request),
        networkRPCMap,
        network,
        signal,
    })
    return responses.map((res, index) => requests[index].parser(res))
}

export async function fetchRPCResponseWithRetry2<T>(
    request: Request<T>,
    params: RPCRequestParams
): Promise<T> {
    const response = await fetchRPCResponseWithRetry({
        request: request.request,
        networkRPCMap: params.networkRPCMap,
        network: params.network,
        signal: params.signal,
    })

    return request.parser(response)
}

// Batch is ususally used to read data, so does not make much sense to export it without retries
// TODO @resetko-zeal there should be an option to enforce "readonly" requests with types
const fetchRPCBatch = async ({
    requests,
    networkRPCMap,
    network,
    signal,
}: {
    requests: RPCRequest[]
    networkRPCMap: NetworkRPCMap
    network: Network
    signal?: AbortSignal
}): Promise<unknown[]> => {
    const responses = await rpcPost({
        body: requests,
        network,
        networkRPCMap,
        signal,
    }).then((responses) => alignResponses({ requests, responses }))

    return combine(
        responses.map((item, index) =>
            parseRPCResponse({
                input: item,
                networkHexId: network.hexChainId,
                requestBody: requests[index],
            })
        )
    ).getSuccessResultOrThrow('Failed to parse one of batch responses')
}

// TODO @resetko-zeal there should be an option to enforce "readonly" requests with types
export const fetchRPCResponse = async ({
    request,
    networkRPCMap,
    network,
    actionSource,
    signal,
}: {
    request: RPCRequest
    networkRPCMap: NetworkRPCMap
    network: Network
    actionSource?: ActionSource2
    signal?: AbortSignal
}): Promise<unknown> => {
    const response = await rpcPost({
        body: request,
        network,
        networkRPCMap,
        signal,
    })

    return parseRPCResponse({
        input: response,
        networkHexId: network.hexChainId,
        requestBody: request,
        actionSource,
    }).getSuccessResultOrThrow('Failed to parse RPC response')
}

/**
 * @deprecated use fetchRPCResponseWithRetry2
 */
export const fetchRPCResponseWithRetry = withRetries(
    fetchRPCResponse,
    RPC_RETRY_COUNT,
    RPC_RETRY_DELAY
)

/**
 * @deprecated use fetchRPCBatch2WithRetry
 */
export const fetchRPCBatchWithRetry = withRetries(
    fetchRPCBatch,
    RPC_RETRY_COUNT,
    RPC_RETRY_DELAY
)

export const fetchRPCBatch2WithRetry = withRetries(
    fetchRPCBatch2,
    RPC_RETRY_COUNT,
    RPC_RETRY_DELAY
)
