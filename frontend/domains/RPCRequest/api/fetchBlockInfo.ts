import { notReachable } from '@zeal/toolkit'
import type { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import * as Hex from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import { toHex } from '@zeal/toolkit/Number'
import {
    arrayOf,
    nullable,
    object,
    oneOf,
    Result,
    shape,
    success,
} from '@zeal/toolkit/Result'
import { Address } from '@zeal/toolkit/Web3/address'

import { currencyId } from '@zeal/domains/Currency'
import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import {
    ParsedLog,
    RPCLogDTO,
    TransactionReceipt,
    TransactionReceiptDTO,
} from '@zeal/domains/RPCRequest'
import { parserTransactionReceiptDTO } from '@zeal/domains/RPCRequest/parsers/parserTransactionReceipt'

import { fetchRPCResponseWithRetry } from './fetchRPCResponse'

type Params = {
    blockNumber: number
    network: Network
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}

export type BlockInfo = PendingBlock | IncludedBlock

export type PendingBlock = {
    type: 'pending'
    network: Network
    blockNumber: number
}

export type IncludedBlock = {
    type: 'included'
    network: Network
    blockHash: Hexadecimal
    blockNumber: number
    timestamp: number
    reciepts: TransactionReceipt[]
}

export const fetchBlockInfo = async ({
    blockNumber,
    networkRPCMap,
    network,
    signal,
}: Params): Promise<BlockInfo> => {
    const blockNumberHex = toHex(blockNumber)
    // TODO @max use batch request
    const [receiptsResponse, blockResponse] = await Promise.all([
        fetchRPCResponseWithRetry({
            request: {
                id: generateRandomNumber(),
                jsonrpc: '2.0' as const,
                method: 'eth_getBlockReceipts',
                params: [blockNumberHex],
            },
            networkRPCMap,
            network,
            signal,
        }),
        fetchRPCResponseWithRetry({
            request: {
                id: generateRandomNumber(),
                jsonrpc: '2.0' as const,
                method: 'eth_getBlockByNumber',
                params: [blockNumberHex, false],
            },
            networkRPCMap,
            network,
            signal,
        }),
    ])

    const block = parseBlock(blockResponse).getSuccessResultOrThrow(
        'cannot parser block info'
    )
    switch (block.type) {
        case 'pending':
            return {
                type: 'pending',
                blockNumber,
                network,
            }
        case 'included':
            if (!receiptsResponse) {
                return {
                    type: 'pending',
                    blockNumber,
                    network,
                }
            }
            const reciepts = arrayOf(
                receiptsResponse,
                parserTransactionReceiptDTO
            )
                .map((dtos) =>
                    dtos.map((dto) => parseTransactionReceipt(dto, network))
                )
                .getSuccessResultOrThrow(
                    'cannot parse TransactionsReceiptsForBlockResponse'
                )
            return {
                type: 'included',
                blockNumber,
                timestamp: block.timestamp,
                blockHash: block.hash,
                network,
                reciepts,
            }

        /* istanbul ignore next */
        default:
            return notReachable(block)
    }
}

export const parseTransactionReceipt = (
    input: TransactionReceiptDTO,
    network: Network
): TransactionReceipt => {
    return {
        type: input.to ? 'transaction' : 'contract_deploy',
        network,
        blockHash: input.blockHash!,
        blockNumber: input.blockNumber,
        cumulativeGasUsed: input.cumulativeGasUsed,
        effectiveGasPrice: input.effectiveGasPrice,
        gasUsed: input.gasUsed,
        transactionType: input.type,
        from: input.from,
        transactionIndex: input.transactionIndex!,
        status: input.status,
        transactionHash: input.transactionHash!,
        to: input.to!,
        logs: input.logs.map((log) => parseLogs(log, network)),
    }
}

type BlockDTO =
    | { type: 'pending' }
    | {
          type: 'included'
          hash: Hexadecimal
          timestamp: number
          transactions: Hexadecimal[]
      }

// TODO :: @max keep it private, then extract as api/fetch function
const parseBlock = (input: unknown): Result<any, BlockDTO> => {
    return oneOf(input, [
        nullable(input).map(() => ({
            type: 'pending' as const,
        })),
        object(input).andThen((obj) =>
            shape({
                type: success('included' as const),
                hash: Hex.parse(obj.hash),
                timestamp: Hex.parserHexAsNumber(obj.timestamp),
                transactions: arrayOf(obj.transactions, Hex.parse),
            })
        ),
    ])
}

const ERC20_TRANSFER_HASH =
    '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'
const ADDRESS_PREFIX = '0x000000000000000000000000'
// TODO :: @max keep it private for now
const parseLogs = (input: RPCLogDTO, network: Network): ParsedLog => {
    switch (true) {
        case input.topics.length === 3 &&
            input.topics[0] === ERC20_TRANSFER_HASH &&
            input.topics[1].startsWith(ADDRESS_PREFIX) &&
            input.topics[2].startsWith(ADDRESS_PREFIX) &&
            input.data &&
            input.logIndex &&
            input.data.length === 66: {
            return {
                type: 'erc20_transfer' as const,
                eventSignature: 'Transfer(address,address,amount)',
                from: input.topics[1].replace(ADDRESS_PREFIX, '0x') as Address, // add parsing from string? regexp check is expensive
                to: input.topics[2].replace(ADDRESS_PREFIX, '0x') as Address,
                amount: Hex.toBigInt(input.data),
                currencyId: currencyId({
                    network: network.hexChainId,
                    address: input.address,
                }),
                logIndex: input.logIndex,
            }
        }
        default:
            return {
                type: 'unknown',
                log: input,
                logIndex: input.logIndex,
            }
    }
}
