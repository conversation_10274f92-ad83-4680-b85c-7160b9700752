import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import * as Web3 from '@zeal/toolkit/Web3'

import { Network, NetworkRPCMap } from '@zeal/domains/Network'

import { fetchRPCResponseWithRetry, Request } from './fetchRPCResponse'

export const requestIsContractDeployed = ({
    address,
}: {
    address: Web3.address.Address
}): Request<boolean> => {
    const { request, parser } = requestGetContractCode({ address })
    return {
        request,
        parser: (response) => parser(response) !== null,
    }
}

export const requestGetContractCode = ({
    address,
}: {
    address: Web3.address.Address
}): Request<Hexadecimal.Hexadecimal | null> => ({
    request: {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'eth_getCode',
        params: [address, 'latest'],
    },
    parser: (response) =>
        Hexadecimal.nullableParse(response).getSuccessResultOrThrow(
            'failed to parse eth_getCode result'
        ),
})

export const fetchIsContractDeployed = async ({
    address,
    network,
    networkRPCMap,
    signal,
}: {
    address: Web3.address.Address
    network: Network
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}) => {
    const request = requestIsContractDeployed({ address })
    const response = await fetchRPCResponseWithRetry({
        network,
        networkRPCMap,
        request: request.request,
        signal,
    })
    return request.parser(response)
}
