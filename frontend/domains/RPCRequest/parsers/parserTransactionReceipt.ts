import * as Hex from '@zeal/toolkit/Hexadecimal'
import {
    arrayOf,
    boolean,
    nullableOf,
    object,
    Result,
    shape,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { RPCLogDTO, TransactionReceiptDTO } from '@zeal/domains/RPCRequest'

export const parserTransactionReceiptDTO = (
    input: unknown
): Result<unknown, TransactionReceiptDTO> =>
    object(input).andThen((obj) =>
        shape({
            blockHash: nullableOf(obj.blockHash, Hex.parse),
            blockNumber: Hex.parserHexAsNumber(obj.blockNumber),
            contractAddress: nullableOf(
                obj.contractAddress,
                Web3.address.parse
            ),
            to: nullableOf(obj.to, Web3.address.parse),
            cumulativeGasUsed: Hex.parse(obj.cumulativeGasUsed),
            effectiveGasPrice: Hex.parse(obj.effectiveGasPrice),
            gasUsed: Hex.parse(obj.gasUsed),
            from: Web3.address.parse(obj.from),
            logs: arrayOf(obj.logs, parseRPCLogDTO),
            logsBloom: Hex.parse(obj.logsBloom),
            status: Hex.parse(obj.status).map(() => 0 as const), // TODO :: @max
            transactionHash: nullableOf(obj.transactionHash, Hex.parse),
            transactionIndex: Hex.parserHexAsNumber(obj.transactionIndex),
            type: Hex.parse(obj.type),
        })
    )

export const parseRPCLogDTO = (input: unknown): Result<unknown, RPCLogDTO> =>
    object(input).andThen((obj) =>
        shape({
            address: Web3.address.parse(obj.address),
            topics: arrayOf(obj.topics, Hex.parse),
            data: Hex.nullableParse(obj.data),
            blockNumber: Hex.parserHexAsNumber(obj.blockNumber),
            transactionHash: nullableOf(obj.transactionHash, Hex.parse),
            transactionIndex: nullableOf(
                obj.transactionIndex,
                Hex.parserHexAsNumber
            ),
            blockHash: nullableOf(obj.blockHash, Hex.parse),
            logIndex: Hex.parserHexAsNumber(obj.logIndex),
            removed: boolean(obj.removed),
        })
    )
