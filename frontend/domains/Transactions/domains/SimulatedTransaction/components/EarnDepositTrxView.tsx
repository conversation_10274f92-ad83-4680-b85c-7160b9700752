import { FormattedMessage } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { GroupHeader, Section } from '@zeal/uikit/Group'
import { ListItem as UIListItem } from '@zeal/uikit/ListItem'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'

import { Avatar } from '@zeal/domains/Currency/components/Avatar'
import { TakerAPYLabel } from '@zeal/domains/Earn/components/TakerAPYLabel'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { TakerTitle } from '@zeal/domains/Earn/components/TakerTitle'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import {
    EarnDepositDirectSend,
    EarnDepositWithSwap,
} from '@zeal/domains/Transactions/domains/SimulatedTransaction'

type Props = {
    transaction: EarnDepositWithSwap | EarnDepositDirectSend
}
export const EarnDepositTrxView = ({ transaction }: Props) => {
    const { taker, from, fromAccountPortfolio } = transaction.earnDepositRequest

    const toAmountInUserCurrency = (() => {
        switch (transaction.type) {
            case 'earn_deposit_with_swap':
                return applyRate2({
                    baseAmount: transaction.earnDepositRequest.swapRoute.to,
                    rate: transaction.earnDepositRequest.takerUserCurrencyRate,
                })
            case 'earn_deposit_direct_send':
                return applyRate2({
                    baseAmount: transaction.earnDepositRequest.from,
                    rate: transaction.earnDepositRequest.takerUserCurrencyRate,
                })
            default:
                return notReachable(transaction)
        }
    })()

    const toAmountInDefaultCurrency = (() => {
        switch (transaction.type) {
            case 'earn_deposit_with_swap':
                return transaction.earnDepositRequest.swapRoute
                    .toInDefaultCurrency
            case 'earn_deposit_direct_send': {
                const token = getTokenByCryptoCurrency3({
                    currency: from.currency,
                    serverPortfolio: fromAccountPortfolio,
                })

                return (
                    token.rate &&
                    applyRate2({
                        baseAmount: transaction.earnDepositRequest.from,
                        rate: token.rate,
                    })
                )
            }
            default:
                return notReachable(transaction)
        }
    })()

    return (
        <Column spacing={16}>
            <Section>
                <GroupHeader
                    left={({ color, textVariant, textWeight }) => (
                        <Text
                            color={color}
                            variant={textVariant}
                            weight={textWeight}
                        >
                            <FormattedMessage
                                id="earn-deposit-view.deposit"
                                defaultMessage="Deposit"
                            />
                        </Text>
                    )}
                    right={null}
                />
                <UIListItem
                    aria-current={false}
                    size="regular"
                    primaryText={
                        transaction.earnDepositRequest.from.currency.code
                    }
                    avatar={({ size }) => (
                        <Avatar
                            currency={
                                transaction.earnDepositRequest.from.currency
                            }
                            size={size}
                        />
                    )}
                    side={{
                        title: (
                            <FormattedMoneyPrecise
                                withSymbol={false}
                                sign="-"
                                money={transaction.earnDepositRequest.from}
                            />
                        ),
                    }}
                />
            </Section>
            <Section>
                <GroupHeader
                    left={({ color, textVariant, textWeight }) => (
                        <Text
                            color={color}
                            variant={textVariant}
                            weight={textWeight}
                        >
                            <FormattedMessage
                                id="earn-deposit-view.into"
                                defaultMessage="Into"
                            />
                        </Text>
                    )}
                    right={null}
                />
                <UIListItem
                    avatar={({ size }) => (
                        <TakerAvatar takerType={taker.type} size={size} />
                    )}
                    primaryText={<TakerTitle takerType={taker.type} />}
                    shortText={
                        <TakerAPYLabel
                            taker={taker}
                            takerApyMap={transaction.takerApyMap}
                        />
                    }
                    side={{
                        title: (
                            <FormattedMoneyPrecise
                                withSymbol={false}
                                sign="+"
                                money={toAmountInUserCurrency}
                            />
                        ),
                        subtitle: toAmountInDefaultCurrency && (
                            <FormattedMoneyPrecise
                                withSymbol
                                sign="+"
                                money={toAmountInDefaultCurrency}
                            />
                        ),
                    }}
                    size="large"
                    aria-current={false}
                />
            </Section>
        </Column>
    )
}
