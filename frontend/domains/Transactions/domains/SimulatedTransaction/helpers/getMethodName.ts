import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'

const KNOWN_METHOD_HASHES: Record<Hexadecimal.Hexadecimal, string> = {
    '0x7bb37428': 'executeUserOp',
}

export const getMethodName = ({
    input,
}: {
    input: Hexadecimal.Hexadecimal
}): string => {
    // TODO @resetko-zeal some function to get slice of Hexadecimal
    const methodHash = Hexadecimal.fromBuffer(
        Hexadecimal.toBuffer(input).slice(0, 4)
    )

    return KNOWN_METHOD_HASHES[methodHash] || methodHash
}
