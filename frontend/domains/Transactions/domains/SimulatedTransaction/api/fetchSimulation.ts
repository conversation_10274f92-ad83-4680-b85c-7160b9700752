import { post } from '@zeal/api/request'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'

import { DefaultCurrency, FiatCurrency } from '@zeal/domains/Currency'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { FXRate2 } from '@zeal/domains/FXRate'
import { fetchDefaultCurrencyRateFromUSD } from '@zeal/domains/FXRate/api/fetchDefaultCurrencyRateToUSD'
import { applyNullableRate } from '@zeal/domains/FXRate/helpers/applyRate'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { parseSimulateTransactionResponse } from '@zeal/domains/Transactions/domains/SimulatedTransaction/parsers/parseSimulateTransactionResponse'
import { InitialUserOperation } from '@zeal/domains/UserOperation'

import { fetchSimulationWithSafetyChecksByRequest2 } from './fetchSimulation2'

import { UnknownTransactionToken } from '../SimulatedTransaction'
import { SimulateTransactionResponse } from '../SimulateTransactionResponse'

export type SimulationResult =
    | { type: 'failed' }
    | { type: 'not_supported' }
    | {
          type: 'simulated'
          simulation: SimulateTransactionResponse
      }

type RequestToSimulate =
    | { type: 'rpc_request'; rpcRequest: EthSendTransaction }
    | { type: 'user_operation'; initialUserOperation: InitialUserOperation }

export type FetchSimulationByRequest = (_: {
    requestToSimulate: RequestToSimulate
    network: Network
    dApp: DAppSiteInfo | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}) => Promise<SimulationResult>

export const fetchSimulationByRequest: FetchSimulationByRequest = async ({
    network,
    requestToSimulate,
    defaultCurrencyConfig,
    dApp,
    networkMap,
    networkRPCMap,
    signal,
}): Promise<SimulationResult> => {
    const rate = await fetchDefaultCurrencyRateFromUSD({
        defaultCurrencyConfig,
        signal,
    })

    const simulationResult = await (() => {
        switch (requestToSimulate.type) {
            case 'rpc_request':
                return fetchRPCRequestSimulation({
                    dApp,
                    network,
                    rpcRequest: requestToSimulate.rpcRequest,
                    rate,
                })
            case 'user_operation':
                return fetchAccountAbstractionTransactionSimulation({
                    network,
                    dApp,
                    initialUserOperation:
                        requestToSimulate.initialUserOperation,
                    rate,
                })
            /* istanbul ignore next */
            default:
                return notReachable(requestToSimulate)
        }
    })()

    fetchSimulationWithSafetyChecksByRequest2({
        defaultCurrencyConfig,
        network,
        networkMap,
        networkRPCMap,
        requestToSimulate,
        dApp,
    }).then((result2) => {
        const typeCombo = `${simulationResult.type}-${result2.type}` as const

        switch (typeCombo) {
            case 'failed-not_supported':
            case 'failed-simulated':
            case 'not_supported-failed':
            case 'not_supported-simulated':
            case 'simulated-failed':
            case 'simulated-not_supported':
                captureError(
                    new ImperativeError(
                        '[simulation2] SimulationResult level discrepancy detected',
                        {
                            requestToSimulate,
                            typeCombo,
                            networkHexId: network.hexChainId,
                        }
                    )
                )
                break
            case 'not_supported-not_supported':
            case 'failed-failed':
                // No need to report
                break

            case 'simulated-simulated': {
                const a = simulationResult as Extract<
                    SimulationResult,
                    { type: 'simulated' }
                >
                const b: SimulationResult = result2 as Extract<
                    SimulationResult,
                    { type: 'simulated' }
                >

                const trx = a.simulation.transaction
                const trx2 = b.simulation.transaction

                if (trx.type !== trx2.type) {
                    captureError(
                        new ImperativeError(
                            '[simulation2] SimulationResult level discrepancy detected',
                            {
                                networkHexId: network.hexChainId,
                                requestToSimulate,
                                trx,
                                trx2,
                            }
                        )
                    )
                } else if (
                    trx.type === 'UnknownTransaction' &&
                    trx2.type === 'UnknownTransaction' &&
                    trx.tokens.length !== trx2.tokens.length
                ) {
                    captureError(
                        new ImperativeError(
                            '[simulation2] SimulationResult level discrepancy detected',
                            {
                                networkHexId: network.hexChainId,
                                requestToSimulate,
                                trx,
                                trx2,
                            }
                        )
                    )
                }

                const aChecksStates = Array.from(
                    new Set(a.simulation.checks.map((check) => check.state))
                )
                const bChecksStates = Array.from(
                    new Set(b.simulation.checks.map((check) => check.state))
                )

                if (
                    aChecksStates.length !== bChecksStates.length ||
                    a.simulation.checks.length !== b.simulation.checks.length
                ) {
                    captureError(
                        new ImperativeError(
                            '[simulation2] checks length discrepancy detected',
                            {
                                networkHexId: network.hexChainId,
                                requestToSimulate,
                                a: a.simulation.checks,
                                b: b.simulation.checks,
                            }
                        )
                    )
                }

                break
            }

            default:
                notReachable(typeCombo)
        }

        switch (simulationResult.type) {
            case 'failed':
            case 'not_supported':
                break
            case 'simulated': {
                switch (result2.type) {
                    case 'failed':
                    case 'not_supported':
                        break
                    case 'simulated':
                        break

                    default:
                        notReachable(result2)
                }
                break
            }

            default:
                notReachable(simulationResult)
        }
    })

    return simulationResult
}

const fetchRPCRequestSimulation = ({
    network,
    rpcRequest,
    dApp,
    signal,
    rate,
}: {
    network: Network
    rpcRequest: EthSendTransaction
    dApp: DAppSiteInfo | null
    rate: FXRate2<FiatCurrency, DefaultCurrency> | null
    signal?: AbortSignal
}): Promise<SimulationResult> => {
    if (!network.isSimulationSupported) {
        return Promise.resolve({ type: 'not_supported' })
    }

    return post(
        '/wallet/transaction/simulate/',
        {
            query: { network: network.name },
            body: rpcRequest.params[0],
            requestSource: dApp?.hostname,
        },
        signal
    )
        .then((data) => {
            const simulation = parseSimulateTransactionResponse(
                data
            ).getSuccessResultOrThrow('failed to parse simulation response')

            return {
                type: 'simulated' as const,
                simulation: mapSimulateTransactionResponse(simulation, rate),
            }
        })
        .catch((e) => {
            captureError(e)

            return { type: 'failed' as const }
        })
}

const fetchAccountAbstractionTransactionSimulation = async ({
    initialUserOperation,
    network,
    signal,
    dApp,
    rate,
}: {
    initialUserOperation: InitialUserOperation
    network: Network
    dApp: DAppSiteInfo | null
    rate: FXRate2<FiatCurrency, DefaultCurrency> | null
    signal?: AbortSignal
}): Promise<SimulationResult> => {
    switch (network.type) {
        case 'predefined':
        case 'testnet':
            return network.isSimulationSupported
                ? post(
                      '/wallet/user-ops-transaction/simulate',
                      {
                          query: { network: network.name },
                          body: {
                              callData: initialUserOperation.callData,
                              initCode: initialUserOperation.initCode,
                              nonce: Hexadecimal.fromBigInt(
                                  initialUserOperation.nonce
                              ),
                              sender: initialUserOperation.sender,

                              callGasLimit: null,
                              maxFeePerGas: null,
                              maxPriorityFeePerGas: null,
                              paymasterAndData: null,
                              preVerificationGas: null,
                              signature: null,
                              verificationGasLimit: null,
                          },
                          requestSource: dApp?.hostname,
                      },
                      signal
                  )
                      .then((data) => {
                          const simulation = parseSimulateTransactionResponse(
                              data
                          ).getSuccessResultOrThrow(
                              'failed to parse simulation response'
                          )

                          return {
                              type: 'simulated' as const,
                              simulation: mapSimulateTransactionResponse(
                                  simulation,
                                  rate
                              ),
                          }
                      })
                      .catch((e) => {
                          captureError(e)

                          return { type: 'failed' as const }
                      })
                : { type: 'not_supported' }
        case 'custom':
            return { type: 'not_supported' }

        default:
            return notReachable(network)
    }
}

const mapSimulateTransactionResponse = (
    simulation: SimulateTransactionResponse,
    rate: FXRate2<FiatCurrency, DefaultCurrency> | null
): SimulateTransactionResponse => {
    switch (simulation.transaction.type) {
        case 'UnknownTransaction':
            return {
                ...simulation,
                transaction: {
                    ...simulation.transaction,
                    tokens: simulation.transaction.tokens.map(
                        (item: UnknownTransactionToken) => {
                            return {
                                ...item,
                                priceInDefaultCurrency: applyNullableRate({
                                    baseAmount: item.priceInDefaultCurrency,
                                    rate,
                                }),
                            }
                        }
                    ),
                },
            }

        case 'P2PTransaction':
            return {
                ...simulation,
                transaction: {
                    ...simulation.transaction,
                    token: {
                        ...simulation.transaction.token,
                        priceInDefaultCurrency: applyNullableRate({
                            baseAmount:
                                simulation.transaction.token
                                    .priceInDefaultCurrency,
                            rate,
                        }),
                    },
                },
            }
        case 'BridgeTrx':
            return {
                ...simulation,
                transaction: {
                    ...simulation.transaction,
                    bridgeRoute: {
                        ...simulation.transaction.bridgeRoute,
                        toPriceInDefaultCurrency: applyNullableRate({
                            baseAmount:
                                simulation.transaction.bridgeRoute
                                    .toPriceInDefaultCurrency,
                            rate,
                        }),
                        fromPriceInDefaultCurrency: applyNullableRate({
                            baseAmount:
                                simulation.transaction.bridgeRoute
                                    .fromPriceInDefaultCurrency,
                            rate,
                        }),
                        feeInDefaultCurrency: applyNullableRate({
                            baseAmount:
                                simulation.transaction.bridgeRoute
                                    .feeInDefaultCurrency,
                            rate,
                        }),
                    },
                },
            }
        case 'smart_wallet_activation':
        case 'card_cashback_deposit':
        case 'earn_deposit_with_swap':
        case 'earn_deposit_direct_send':
        case 'earn_withdraw':
        case 'earn_recharge_disabled':
        case 'earn_recharge_updated':
        case 'earn_recharge_configured':
        case 'deploy_earn_account':
        case 'CardTopUpTrx':
        case 'WithdrawalTrx':
        case 'P2PNftTransaction':
        case 'ApprovalTransaction':
        case 'FailedTransaction':
        case 'NftCollectionApprovalTransaction':
        case 'SingleNftApprovalTransaction':
        case 'card_top_up_from_earn':
        case 'swaps_io_native_token_swap':
            return simulation
        default:
            return notReachable(simulation.transaction)
    }
}
