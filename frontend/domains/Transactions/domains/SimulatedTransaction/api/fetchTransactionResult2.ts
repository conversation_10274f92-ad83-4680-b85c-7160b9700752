import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { KnownCurrencies } from '@zeal/domains/Currency'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import {
    ParsedLog,
    SafeModuleTransactionLog,
    UserOperationEventLog,
} from '@zeal/domains/RPCRequest'
import { fetchRPCBatch2WithRetry } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { createGetTransactionReceiptRequest } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/fetchTransactionReceiptWithRetry'
import { createGetTransactionByHashRequest } from '@zeal/domains/Transactions/api/fetchTransactionByHash'
import { SimulatedTransaction } from '@zeal/domains/Transactions/domains/SimulatedTransaction'

import { fetchSimulatedTransactionFromOnchainData } from './fetchSimulatedTransactionFromOnchainData'

import { getMethodName } from '../helpers/getMethodName'

export type FetchTransactionResultByRequest2 = (_: {
    network: PredefinedNetwork
    request: Request
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    sender: Web3.address.Address
    signal?: AbortSignal
}) => Promise<{
    simulatedTransaction: SimulatedTransaction
    knownCurrencies: KnownCurrencies
}>

type Request =
    | { type: 'rpc_request'; transactionHash: Hexadecimal.Hexadecimal }
    | {
          type: 'user_operation'
          userOperationHash: Hexadecimal.Hexadecimal
          bundleTransactionHash: Hexadecimal.Hexadecimal
      }

export const fetchTransactionResultByRequest: FetchTransactionResultByRequest2 =
    ({
        network,
        request,
        networkRPCMap,
        defaultCurrencyConfig,
        networkMap,
        sender,
        signal,
    }): Promise<{
        simulatedTransaction: SimulatedTransaction
        knownCurrencies: KnownCurrencies
    }> => {
        switch (request.type) {
            case 'rpc_request':
                return fetchEOATransactionResult({
                    network,
                    transactionHash: request.transactionHash,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    sender,
                    signal,
                })
            case 'user_operation':
                return fetchUserOperationResult({
                    network,
                    userOperationHash: request.userOperationHash,
                    bundleTransactionHash: request.bundleTransactionHash,
                    sender,
                    signal,
                    networkRPCMap,
                    defaultCurrencyConfig,
                    networkMap,
                })
            /* istanbul ignore next */
            default:
                return notReachable(request)
        }
    }

const fetchEOATransactionResult = async ({
    network,
    transactionHash,
    networkRPCMap,
    defaultCurrencyConfig,
    networkMap,
    sender,
    signal,
}: {
    network: PredefinedNetwork
    sender: Web3.address.Address
    transactionHash: Hexadecimal.Hexadecimal
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<{
    simulatedTransaction: SimulatedTransaction
    knownCurrencies: KnownCurrencies
}> => {
    const [receipt, body] = await fetchRPCBatch2WithRetry(
        [
            createGetTransactionReceiptRequest({
                network,
                transactionHash,
            }),
            createGetTransactionByHashRequest({
                transactionHash,
            }),
        ],
        {
            network,
            networkRPCMap,
            signal,
        }
    )

    switch (receipt.status) {
        case 'failed':
            return {
                simulatedTransaction: {
                    type: 'FailedTransaction',
                    method: body.input
                        ? getMethodName({ input: body.input })
                        : '',
                },
                knownCurrencies: {},
            }

        case 'success':
            return fetchSimulatedTransactionFromOnchainData({
                network,
                logs: receipt.logs,
                to: receipt.to,
                value: body.value,
                input: body.input,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                sender,
                signal,
            })

        /* istanbul ignore next */
        default:
            return notReachable(receipt)
    }
}

const fetchUserOperationResult = async ({
    network,
    sender,
    userOperationHash,
    bundleTransactionHash,
    networkRPCMap,
    defaultCurrencyConfig,
    networkMap,
    signal,
}: {
    network: PredefinedNetwork
    userOperationHash: Hexadecimal.Hexadecimal
    bundleTransactionHash: Hexadecimal.Hexadecimal
    sender: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<{
    simulatedTransaction: SimulatedTransaction
    knownCurrencies: KnownCurrencies
}> => {
    const [bundleReceipt] = await fetchRPCBatch2WithRetry(
        [
            createGetTransactionReceiptRequest({
                network,
                transactionHash: bundleTransactionHash,
            }),
        ],
        {
            network,
            networkRPCMap,
            signal,
        }
    )

    switch (bundleReceipt.status) {
        case 'failed':
            throw new ImperativeError(
                'UserOperation bundle failed or not found after being confirmed',
                {
                    bundleTransactionHash,
                    userOperationHash,
                    status: bundleReceipt.status,
                }
            )

        case 'success': {
            const { userOpEvent, userOpRelatedLogs } = bundleReceipt.logs
                .toSorted((a, b) => a.logIndex - b.logIndex)
                .reduceRight(
                    (acc, log) => {
                        switch (log.type) {
                            case 'erc20_transfer':
                            case 'safe_module_transaction':
                            case 'account_deployed':
                            case 'added_owner':
                            case 'approval':
                            case 'disable_module':
                            case 'enable_module':
                            case 'safe_received':
                            case 'set_allowance':
                            case 'threshold_updated':
                            case 'unknown':
                            case 'user_operation_revert_reason':
                                break
                            case 'user_operation_event':
                                acc.userOpEvent = log
                                break
                            /* istanbul ignore next */
                            default:
                                notReachable(log)
                        }

                        if (
                            acc.userOpEvent &&
                            acc.userOpEvent.userOpHash === userOperationHash
                        ) {
                            acc.userOpRelatedLogs.unshift(log)
                        }

                        return acc
                    },
                    { userOpRelatedLogs: [], userOpEvent: null } as {
                        userOpRelatedLogs: ParsedLog[]
                        userOpEvent: UserOperationEventLog | null
                    }
                )

            if (userOpRelatedLogs.length === 0 || userOpEvent === null) {
                throw new ImperativeError(
                    'Were not able to find a user operation logs in receipt',
                    {
                        bundleTransactionHash,
                        userOperationHash,
                        userOpRelatedLogs,
                        userOpEvent,
                    }
                )
            }

            const safeModuleTransactionEvent = userOpRelatedLogs.find(
                (log): log is SafeModuleTransactionLog => {
                    switch (log.type) {
                        case 'safe_module_transaction':
                            return true
                        case 'erc20_transfer':
                        case 'account_deployed':
                        case 'added_owner':
                        case 'approval':
                        case 'disable_module':
                        case 'enable_module':
                        case 'safe_received':
                        case 'set_allowance':
                        case 'threshold_updated':
                        case 'unknown':
                        case 'user_operation_event':
                        case 'user_operation_revert_reason':
                            return false
                        /* istanbul ignore next */
                        default:
                            return notReachable(log)
                    }
                }
            )

            const revertEvent =
                userOpRelatedLogs.find(
                    (log): log is SafeModuleTransactionLog => {
                        switch (log.type) {
                            case 'user_operation_revert_reason':
                                return true
                            case 'account_deployed':
                            case 'added_owner':
                            case 'approval':
                            case 'disable_module':
                            case 'enable_module':
                            case 'erc20_transfer':
                            case 'safe_module_transaction':
                            case 'safe_received':
                            case 'set_allowance':
                            case 'threshold_updated':
                            case 'unknown':
                            case 'user_operation_event':
                                return false
                            /* istanbul ignore next */
                            default:
                                return notReachable(log)
                        }
                    }
                ) || null

            if (revertEvent) {
                return {
                    simulatedTransaction: {
                        type: 'FailedTransaction',
                        method: safeModuleTransactionEvent
                            ? getMethodName({
                                  input: safeModuleTransactionEvent.data,
                              })
                            : '', // TODO @resetko-zeal it can fail even thought we don't have safe module transaction event, should be nullable
                    },
                    knownCurrencies: {},
                }
            }

            if (!safeModuleTransactionEvent) {
                throw new ImperativeError(
                    'Were not able to find a safe module transaction log in receipt',
                    {
                        bundleTransactionHash,
                        userOperationHash,
                        userOpRelatedLogs,
                    }
                )
            }

            return fetchSimulatedTransactionFromOnchainData({
                network,
                logs: userOpRelatedLogs,
                to: safeModuleTransactionEvent.to,
                value: Hexadecimal.fromBigInt(safeModuleTransactionEvent.value),
                input: safeModuleTransactionEvent.data,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                sender,
                signal,
            })
        }
        /* istanbul ignore next */
        default:
            return notReachable(bundleReceipt)
    }
}
