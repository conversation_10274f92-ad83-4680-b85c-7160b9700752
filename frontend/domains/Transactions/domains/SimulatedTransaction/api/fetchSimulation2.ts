import { post } from '@zeal/api/requestBackend'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import {
    arrayOf,
    combine,
    match,
    nonEmptyArray,
    nullable,
    object,
    oneOf,
    Result,
    safeArrayOf,
    shape,
    string,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { KnownCurrencies } from '@zeal/domains/Currency'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import {
    Network,
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import { EthSendTransaction, ParsedLog } from '@zeal/domains/RPCRequest'
import { fetchRPCResponseWithRetry2 } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { TransactionSafetyCheck } from '@zeal/domains/SafetyCheck'
import { fetchBlockaidTransactionChecks } from '@zeal/domains/SafetyCheck/api/fetchBlockaidTransactionChecks'
import { fetchTokenVerificationSafetyCheck } from '@zeal/domains/SafetyCheck/api/fetchTokenVerificationSafetyCheck'
import { createApprovalSpenderSafetyCheckRequest } from '@zeal/domains/SafetyCheck/helpers/createApprovalSpenderSafetyCheckRequest'
import { createP2PReceiverSafetyCheckRequest } from '@zeal/domains/SafetyCheck/helpers/createP2PReceiverSafetyCheckRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { IndexedTransactionLogDTO } from '@zeal/domains/Transactions'
import { parseLog } from '@zeal/domains/Transactions/helpers/parseLog'
import { InitialUserOperation } from '@zeal/domains/UserOperation'

import { fetchSimulatedTransactionFromOnchainData } from './fetchSimulatedTransactionFromOnchainData'

import { getMethodName } from '../helpers/getMethodName'
import { SimulatedTransaction } from '../SimulatedTransaction'
import { SimulateTransactionResponse } from '../SimulateTransactionResponse'

export type SimulationResult =
    | { type: 'failed' }
    | { type: 'not_supported' }
    | {
          type: 'simulated'
          simulation: SimulateTransactionResponse
      }

type RequestToSimulate =
    | { type: 'rpc_request'; rpcRequest: EthSendTransaction }
    | { type: 'user_operation'; initialUserOperation: InitialUserOperation }

export type FetchSimulationByRequest2 = (_: {
    requestToSimulate: RequestToSimulate
    network: Network
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    dApp: DAppSiteInfo | null
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}) => Promise<SimulationResult>

type SimulatedTransactionRawSuccess = {
    status: 'success'
    logs: ParsedLog[]
    to: Web3.address.Address
    value: Hexadecimal.Hexadecimal | null
    input: Hexadecimal.Hexadecimal | null
}

type SimulatedTransactionRawReverted = {
    status: 'reverted'
    to: Web3.address.Address
    input: Hexadecimal.Hexadecimal | null
    value: Hexadecimal.Hexadecimal | null
    reason: string | null
}

type SimulatedTransactionRaw =
    | SimulatedTransactionRawSuccess
    | SimulatedTransactionRawReverted

const parseSimulatedTransactionRaw = ({
    item,
    network,
}: {
    item: unknown
    network: PredefinedNetwork
}): Result<unknown, SimulatedTransactionRaw> =>
    object(item)
        .andThen((itemObj) => object(itemObj.transaction))
        .andThen((trxObj) =>
            oneOf(trxObj, [
                shape({
                    status: match(trxObj.status, false).map(
                        () => 'reverted' as const
                    ),
                    input: Hexadecimal.nullableParse(trxObj.input),
                    value: Hexadecimal.nullableParse(trxObj.value),
                    to: Web3.address.parse(trxObj.to),
                    reason: object(trxObj.transaction_info)
                        .andThen((trxInfo) =>
                            safeArrayOf(trxInfo.stack_trace, (item) =>
                                object(item).andThen((itemObj) =>
                                    shape({
                                        op: match(itemObj.op, 'REVERT'),
                                        reason: string(itemObj.error_reason),
                                    })
                                )
                            )
                        )
                        .map((calls) => (calls[0] ? calls[0].reason : null)),
                }),

                shape({
                    status: match(trxObj.status, true).map(
                        () => 'success' as const
                    ),
                    input: Hexadecimal.nullableParse(trxObj.input),
                    value: Hexadecimal.nullableParse(trxObj.value),
                    to: Web3.address.parse(trxObj.to),
                    logs: object(trxObj.transaction_info).andThen((infoObj) =>
                        oneOf(infoObj.logs, [
                            nullable(infoObj.logs).map(() => []),
                            arrayOf(infoObj.logs, (logItem) =>
                                object(logItem)
                                    .andThen((logObj) => object(logObj.raw))
                                    .andThen((rawObj) =>
                                        shape({
                                            address: Web3.address.parse(
                                                rawObj.address
                                            ),
                                            data: Hexadecimal.nullableParse(
                                                rawObj.data
                                            ),
                                            topics: arrayOf(
                                                rawObj.topics,
                                                Hexadecimal.parse
                                            ),
                                        })
                                    )
                            )
                                .map((arr) =>
                                    arr.map(
                                        (
                                            { address, data, topics },
                                            logIndex
                                        ): IndexedTransactionLogDTO => ({
                                            address,
                                            data,
                                            logIndex,
                                            topics,
                                        })
                                    )
                                )
                                .andThen((rawLogsArr) =>
                                    combine(
                                        rawLogsArr.map((rawLog) =>
                                            parseLog(rawLog, network)
                                        )
                                    )
                                ),
                        ])
                    ),
                }),
            ])
        )

const fetchSimulatedTransactionBasedTransactionSafetyChecks = async ({
    simulatedTransaction,
    network,
    networkRPCMap,
    signal,
}: {
    simulatedTransaction: SimulatedTransaction
    network: PredefinedNetwork
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<TransactionSafetyCheck[]> => {
    const succesfullSimulationCheck: TransactionSafetyCheck = {
        type: 'TransactionSimulationCheck',
        severity: 'Danger',
        state: 'Passed',
        simulationUrl: '', // TODO @resetko-zeal add sharing, feels like it's inevidable
        checkSource: {
            source: 'Tenderly',
            url: null,
        },
    }
    switch (simulatedTransaction.type) {
        case 'ApprovalTransaction': {
            const [spenderCheck, tokenVerificationCheck] = await Promise.all([
                fetchRPCResponseWithRetry2(
                    createApprovalSpenderSafetyCheckRequest({
                        spenderAddress: simulatedTransaction.approveTo.address,
                    }),
                    { network, networkRPCMap, signal }
                ),
                fetchTokenVerificationSafetyCheck({
                    currencyIds: [
                        simulatedTransaction.amount.amount.currency.id,
                    ],
                }),
            ])

            return [
                succesfullSimulationCheck,
                spenderCheck,
                ...tokenVerificationCheck,
            ]
        }
        case 'UnknownTransaction':
            const tokenVerificationChecks =
                await fetchTokenVerificationSafetyCheck({
                    currencyIds: simulatedTransaction.tokens.map(
                        (token) => token.amount.currencyId
                    ),
                })
            return [succesfullSimulationCheck, ...tokenVerificationChecks]

        case 'P2PTransaction': {
            const [receiverCheck, tokenVerificationCheck] = await Promise.all([
                fetchRPCResponseWithRetry2(
                    createP2PReceiverSafetyCheckRequest({
                        receiverAddress: simulatedTransaction.toAddress,
                    }),
                    { network, networkRPCMap, signal }
                ),
                fetchTokenVerificationSafetyCheck({
                    currencyIds: [simulatedTransaction.token.amount.currencyId],
                }),
            ])
            return [
                succesfullSimulationCheck,
                receiverCheck,
                ...tokenVerificationCheck,
            ]
        }

        case 'FailedTransaction':
            return [
                {
                    type: 'TransactionSimulationCheck',
                    message: '', // TODO @resetko-zeal need to add real simulation revert reason to transaction
                    severity: 'Danger',
                    state: 'Failed',
                    checkSource: {
                        source: 'Tenderly',
                        url: null,
                    },
                },
            ]

        case 'BridgeTrx':
        case 'CardTopUpTrx':
        case 'NftCollectionApprovalTransaction':
        case 'P2PNftTransaction': // TODO @resetko-zeal if nft p2p - check receiver is not smart contract, request to Rarible to check it (private fun verifyNftCollections)
        case 'SingleNftApprovalTransaction':
        case 'WithdrawalTrx':
        case 'card_cashback_deposit':
        case 'card_top_up_from_earn':
        case 'deploy_earn_account':
        case 'earn_deposit_direct_send':
        case 'earn_deposit_with_swap':
        case 'earn_recharge_configured':
        case 'earn_recharge_disabled':
        case 'earn_recharge_updated':
        case 'earn_withdraw':
        case 'smart_wallet_activation':
        case 'swaps_io_native_token_swap':
            return [succesfullSimulationCheck]

        /* istanbul ignore next */
        default:
            return notReachable(simulatedTransaction)
    }
}

export const fetchSimulationWithSafetyChecksByRequest2: FetchSimulationByRequest2 =
    async ({
        network,
        requestToSimulate,
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        dApp,
        signal,
    }): Promise<SimulationResult> => {
        switch (network.type) {
            case 'predefined': {
                try {
                    if (network.isSimulationSupported) {
                        const [transactionSafetyChecks, simulation] =
                            await Promise.all([
                                fetchBlockaidTransactionChecks({
                                    dApp,
                                    network,
                                    requestToCheck: requestToSimulate,
                                    signal,
                                }),
                                // TODO @resetko-zeal nft simulation
                                fetchSimulationByRequest({
                                    network,
                                    requestToSimulate,
                                    defaultCurrencyConfig,
                                    networkMap,
                                    networkRPCMap,
                                    signal,
                                }),
                            ])

                        const simulatedTransactionBasedTransactionSafetyChecks =
                            await fetchSimulatedTransactionBasedTransactionSafetyChecks(
                                {
                                    simulatedTransaction:
                                        simulation.transaction,
                                    network,
                                    networkRPCMap,
                                }
                            )

                        const checks = [
                            ...transactionSafetyChecks,
                            ...simulatedTransactionBasedTransactionSafetyChecks,
                        ]

                        return {
                            type: 'simulated',
                            simulation: {
                                transaction: simulation.transaction,
                                currencies: simulation.currencies,
                                checks,
                            },
                        }
                    } else {
                        return { type: 'not_supported' }
                    }
                } catch (e) {
                    captureError(e)
                    return { type: 'failed' }
                }
            }
            case 'testnet':
            case 'custom':
                return { type: 'not_supported' }
            default:
                return notReachable(network)
        }
    }

const fetchSimulationByRequest = async ({
    network,
    requestToSimulate,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    signal,
}: {
    network: PredefinedNetwork
    requestToSimulate: RequestToSimulate
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<{
    transaction: SimulatedTransaction
    currencies: KnownCurrencies
}> => {
    switch (requestToSimulate.type) {
        case 'rpc_request':
            return fetchRPCRequestSimulation({
                network,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                rpcRequest: requestToSimulate.rpcRequest,
                signal,
            })
        case 'user_operation':
            return fetchAccountAbstractionTransactionSimulation({
                network,
                initialUserOperation: requestToSimulate.initialUserOperation,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                signal,
            })
        /* istanbul ignore next */
        default:
            return notReachable(requestToSimulate)
    }
}

const fetchRPCRequestSimulation = async ({
    network,
    rpcRequest,
    signal,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
}: {
    network: PredefinedNetwork
    rpcRequest: EthSendTransaction
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<{
    transaction: SimulatedTransaction
    currencies: KnownCurrencies
}> => {
    const trx = rpcRequest.params[0]

    const tenderlyResponse = string(
        await post(
            '/proxy/simulator/simulate',
            {
                body: {
                    network_id: parseInt(network.hexChainId, 16),
                    from: trx.from,
                    to: trx.to,
                    input: trx.data,
                    save: true,
                    simulation_type: 'quick',
                    value: trx.value,
                },
            },
            signal
        )
    )
        .andThen(parseJSON)
        .getSuccessResultOrThrow(
            'Failed to parse batch simulation response JSONs'
        )

    const rawTransaction = parseSimulatedTransactionRaw({
        item: tenderlyResponse,
        network,
    }).getSuccessResultOrThrow('Failed to parse raw transaction for EoA sim')

    switch (rawTransaction.status) {
        case 'success': {
            const simulatedTransctionResponse =
                await fetchSimulatedTransactionFromOnchainData({
                    network,
                    logs: rawTransaction.logs,
                    to: rawTransaction.to,
                    value: rawTransaction.value,
                    input: rawTransaction.input,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    sender: trx.from as Web3.address.Address,
                    signal,
                })

            return {
                currencies: simulatedTransctionResponse.knownCurrencies,
                transaction: simulatedTransctionResponse.simulatedTransaction,
            }
        }

        case 'reverted':
            return {
                currencies: {},
                transaction: {
                    type: 'FailedTransaction',
                    method: rawTransaction.input
                        ? getMethodName({ input: rawTransaction.input })
                        : '', // TODO @resetko-zeal we should not have name if it's native transfer, although it can also fail
                },
            }

        default:
            return notReachable(rawTransaction)
    }
}

const fetchAccountAbstractionTransactionSimulation = async ({
    initialUserOperation,
    network,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    signal,
}: {
    initialUserOperation: InitialUserOperation
    network: PredefinedNetwork
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<{
    transaction: SimulatedTransaction
    currencies: KnownCurrencies
}> => {
    const { initCode, callData, entrypoint, sender } = initialUserOperation

    const deployTx: EthSendTransaction['params'][0] | null = initCode
        ? {
              from: entrypoint,
              to: initCode.slice(0, 42),
              data: `0x${initCode.slice(42)}`,
          }
        : null

    const userOpBundleTx: EthSendTransaction['params'][0] = {
        data: callData,
        from: entrypoint,
        to: sender,
    }

    const transactions = deployTx
        ? [deployTx, userOpBundleTx]
        : [userOpBundleTx]

    const rawTransactions = string(
        await post(
            '/proxy/simulator/simulate-bundle',
            {
                body: {
                    simulations: transactions.map(
                        (trx) =>
                            ({
                                network_id: parseInt(network.hexChainId, 16),
                                from: trx.from,
                                to: trx.to,
                                input: trx.data,
                                save: true,
                                simulation_type: 'quick',
                                value: trx.value,
                            }) as const
                    ),
                },
            },
            signal
        )
    )
        .andThen(parseJSON)
        .andThen(object)
        .andThen((obj) =>
            arrayOf(obj.simulation_results, (item) =>
                parseSimulatedTransactionRaw({ item, network })
            ).andThen(nonEmptyArray)
        )
        .getSuccessResultOrThrow(
            'Failed to parse raw simulated batch transactions'
        )

    // TODO @resetko-zeal use grouping instead if double filtering
    const failedTransaction = rawTransactions.find(
        (trx): trx is SimulatedTransactionRawReverted => {
            switch (trx.status) {
                case 'success':
                    return false
                case 'reverted':
                    return true
                default:
                    return notReachable(trx)
            }
        }
    )

    if (failedTransaction) {
        return {
            currencies: {},
            transaction: {
                type: 'FailedTransaction',
                method: failedTransaction.input
                    ? getMethodName({ input: failedTransaction.input })
                    : '', // TODO @resetko-zeal we should not have name if it's native transfer, although it can also fail
            },
        }
    }

    // TODO @resetko-zeal use grouping instead if double filtering
    const successTransactions = rawTransactions.filter(
        (trx): trx is SimulatedTransactionRawSuccess => {
            switch (trx.status) {
                case 'success':
                    return true
                case 'reverted':
                    return false
                default:
                    return notReachable(trx)
            }
        }
    )

    const mainTransaction =
        successTransactions[successTransactions.length - 1] || null

    if (!mainTransaction) {
        throw new ImperativeError('Failed to get main transaction in bundle')
    }

    const simulatedTransctionResponse =
        await fetchSimulatedTransactionFromOnchainData({
            network,
            logs: mainTransaction.logs,
            to: mainTransaction.to,
            value: mainTransaction.value,
            input: mainTransaction.input,
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            sender: sender as Web3.address.Address,
            signal,
        })

    return {
        currencies: simulatedTransctionResponse.knownCurrencies,
        transaction: simulatedTransctionResponse.simulatedTransaction,
    }
}
