import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { CurrencyId } from '@zeal/domains/Currency'
import { NetworkHexId } from '@zeal/domains/Network'

type DeBankCategoryId = 'send' | 'receive' | 'cancel' | 'approve' | 'deploy'

declare const DeBankProjectIdSymbol: unique symbol

export type DeBankProjectId = string & {
    __opaque: typeof DeBankProjectIdSymbol
}

export type DeBankTransactionActivityRaw = {
    cate_id: DeBankCategoryId | null
    networkHexId: NetworkHexId
    id: Hexadecimal
    is_scam: boolean
    other_addr: Web3.address.Address | null
    project_id: DeBankProjectId | null
    receives: {
        amount: number
        from_addr: Web3.address.Address
        currency_id: CurrencyId
    }[]
    sends: {
        amount: number
        to_addr: Web3.address.Address
        currency_id: CurrencyId
    }[]
    time_at: number
    token_approve: {
        spender: Web3.address.Address
        value: number
        currency_id: CurrencyId
    } | null
    tx: {
        from_addr: Web3.address.Address | null
        id: Hexadecimal | string
        selector: Hexadecimal | null
        status: 1 | 0
        to_addr: Web3.address.Address | null
    } | null
}

export type DeBankActivityRawFeed = {
    rawTransactions: DeBankTransactionActivityRaw[]
    currencies: CurrencyId[]
    projectsMap: DeBankProjectsInfoMap
}

export type DeBankProjectsInfoMap = Record<
    DeBankProjectId,
    {
        id: string
        logo: string | null
        name: string | null
        url: string | null
    }
>

export type DeBankNetwork =
    | 'eth'
    | 'matic'
    | 'pze'
    | 'linea'
    | 'bsc'
    | 'avax'
    | 'arb'
    | 'era'
    | 'ftm'
    | 'op'
    | 'base'
    | 'blast'
    | 'opbnb'
    | 'celo'
    | 'cro'
    | 'mnt'
    | 'manta'
    | 'xdai'
    | 'aurora'
