import { parse as parseHex } from '@zeal/toolkit/Hexadecimal'
import { values } from '@zeal/toolkit/Object'
import {
    arrayOf,
    boolean,
    failure,
    match,
    nullableOf,
    number,
    object,
    oneOf,
    recordOf,
    Result,
    safeArrayOf,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { currencyId } from '@zeal/domains/Currency'
import { MONERIUM_V1_TOKENS } from '@zeal/domains/Currency/constants'
import { PredefinedNetwork } from '@zeal/domains/Network'

import {
    DeBankActivityRawFeed,
    DeBankNetwork,
    DeBankProjectId,
    DeBankTransactionActivityRaw,
} from '..'
import { DEBANK_NETWORK_TO_NETWORK_MAP } from '../constants'

export const parseDeBankTransactionActivityRaw = (
    input: unknown
): Result<unknown, DeBankTransactionActivityRaw> =>
    object(input)
        .andThen((obj) =>
            parseDeBankNetwork(obj.chain).map((chain) => ({
                chain,
                obj,
            }))
        )
        .andThen(({ chain, obj }) =>
            shape({
                cate_id: nullableOf(obj.cate_id, (cate_id) =>
                    oneOf(cate_id, [
                        match(cate_id, 'send'),
                        match(cate_id, 'receive'),
                        match(cate_id, 'cancel'),
                        match(cate_id, 'approve'),
                        match(cate_id, 'deploy'),
                    ])
                ),
                networkHexId: success(chain.hexChainId),
                id: parseHex(obj.id),
                is_scam: boolean(obj.is_scam),
                other_addr: oneOf(obj.other_addr, [
                    Web3.address.parse(obj.other_addr),
                    success(null),
                ]),
                project_id: nullableOf(obj.project_id, string).map(
                    (projectId) => projectId as DeBankProjectId
                ),
                receives: safeArrayOf(obj.receives, (receive) =>
                    object(receive).andThen((receiveObj) =>
                        shape({
                            amount: number(receiveObj.amount),
                            from_addr: Web3.address.parse(receiveObj.from_addr),
                            currency_id: parseDeBankTokenId(
                                receiveObj.token_id
                            ).map((tokenAddress) =>
                                currencyId({
                                    network: chain.hexChainId,
                                    address: tokenAddress,
                                })
                            ),
                        })
                    )
                ).map((receives) =>
                    receives.filter(
                        (receive) =>
                            !MONERIUM_V1_TOKENS.has(receive.currency_id)
                    )
                ),
                sends: safeArrayOf(obj.sends, (send) =>
                    object(send).andThen((sendObj) =>
                        shape({
                            amount: number(sendObj.amount),
                            to_addr: Web3.address.parse(sendObj.to_addr),
                            currency_id: parseDeBankTokenId(
                                sendObj.token_id
                            ).map((tokenAddress) =>
                                currencyId({
                                    network: chain.hexChainId,
                                    address: tokenAddress,
                                })
                            ),
                        })
                    )
                ).map((sends) =>
                    sends.filter(
                        (send) => !MONERIUM_V1_TOKENS.has(send.currency_id)
                    )
                ),
                time_at: number(obj.time_at).map((time_at) => time_at * 1000),
                token_approve: nullableOf(obj.token_approve, (approve) =>
                    object(approve).andThen((approveObj) =>
                        shape({
                            spender: Web3.address.parse(approveObj.spender),
                            currency_id: parseDeBankTokenId(
                                approveObj.token_id
                            ).map((tokenAddress) =>
                                currencyId({
                                    network: chain.hexChainId,
                                    address: tokenAddress,
                                })
                            ),
                            value: number(approveObj.value),
                        })
                    )
                ),
                tx: nullableOf(obj.tx, (txInput) =>
                    object(txInput).andThen((transaction) =>
                        shape({
                            from_addr: oneOf(transaction.from_addr, [
                                Web3.address.parse(transaction.from_addr),
                                success(null),
                            ]),
                            id: oneOf(transaction.id, [
                                parseHex(transaction.id),
                                string(transaction.id),
                            ]),
                            selector: nullableOf(transaction.selector, () =>
                                oneOf(transaction.selector, [
                                    parseHex(transaction.selector),
                                    match(transaction.selector, '0x'),
                                ])
                            ),
                            status: oneOf(transaction.status, [
                                match(transaction.status, 1),
                                match(transaction.status, 0),
                            ]),
                            to_addr: oneOf(transaction.to_addr, [
                                Web3.address.parse(transaction.to_addr),
                                success(null),
                            ]),
                        })
                    )
                ),
            })
        )

export const parseDeBankActivityRawFeed = (
    input: unknown
): Result<unknown, DeBankActivityRawFeed> =>
    object(input).andThen((obj) =>
        shape({
            rawTransactions: arrayOf(obj.history_list, (rawTransaction) =>
                parseDeBankTransactionActivityRaw(rawTransaction)
            ),
            currencies: object(obj.token_dict).andThen((tokenDict) =>
                safeArrayOf(values(tokenDict), (tokenInput) =>
                    object(tokenInput).andThen((tokenObj) =>
                        shape({
                            network: parseDeBankNetwork(tokenObj.chain),
                            id: parseDeBankTokenId(tokenObj.id),
                        }).map((parsedTokenObj) =>
                            currencyId({
                                network: parsedTokenObj.network.hexChainId,
                                address: parsedTokenObj.id,
                            })
                        )
                    )
                )
            ),
            projectsMap: recordOf(obj.project_dict, {
                keyParser: string,
                valueParser: (projectInput) =>
                    object(projectInput).andThen((projectInputObj) =>
                        shape({
                            id: string(projectInputObj.id),
                            logo: nullableOf(projectInputObj.logo, string),
                            name: nullableOf(projectInputObj.name, string),
                            url: nullableOf(projectInputObj.url, string),
                        })
                    ),
            }),
        })
    )

const parseDeBankNetwork = (
    input: unknown
): Result<unknown, PredefinedNetwork> =>
    string(input).andThen((network) => {
        if (network in DEBANK_NETWORK_TO_NETWORK_MAP) {
            return success(
                DEBANK_NETWORK_TO_NETWORK_MAP[network as DeBankNetwork]
            )
        }
        return failure({ type: 'invalid_network', network })
    })

const parseDeBankTokenId = (
    input: unknown
): Result<unknown, Web3.address.Address> =>
    oneOf(input, [
        Web3.address.parse(input),
        parseDeBankNetwork(input).map(
            (network) => network.nativeCurrency.address as Web3.address.Address
        ),
    ])
