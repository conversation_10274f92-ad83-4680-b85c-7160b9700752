import { unsafe_fromNumberWithFraction } from '@zeal/toolkit/BigInt'
import { notReachable } from '@zeal/toolkit/notReachable'
import {
    combine,
    failure,
    oneOf,
    Result,
    shape,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import { CurrencyId, KnownCryptoCurrencies } from '@zeal/domains/Currency'
import { SOCKET_GATEWAY_CONTRACTS } from '@zeal/domains/Currency/domains/Bridge/api/fetchBridgeRoutes'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkHexId } from '@zeal/domains/Network'
import {
    ARBITRUM,
    AVALANCHE,
    BASE,
    BLAST,
    BSC,
    ETHEREUM,
    GNOSIS,
    LINEA,
    MANTLE,
    OPTIMISM,
    POLYGON,
    POLYGON_ZKEVM,
} from '@zeal/domains/Network/constants'
import {
    ArbitrarySmartContractTransactionActivity,
    ArbitrarySmartContractTransactionType,
    FailedTransactionActivity,
    IncomingBridgeTransactionActivity,
    OutgoingBridgeTransactionActivity,
    ReceiveTransactionActivity,
    RegularTransactionActivity,
    SendTransactionActivity,
    SwapTransactionActivity,
    TokenApprovalRevokedTransactionActivity,
    TokenApprovalTransactionActivity,
} from '@zeal/domains/Transactions'

import { DeBankProjectsInfoMap, DeBankTransactionActivityRaw } from '..'

const DE_BANK_UNLIMITED_APPROVAL_AMOUNT =
    115792089237316190183760311394656140241344763643041023198016376741560320n

const HANDLE_USER_OPERATION_SELECTOR = '0x1fad948c'

const SPECIAL_TRANSFER_SELECTORS = ['0x8794a451']

const EXCLUDED_SELECTORS = ['0xad718d2a'] // delay module

const PAYMASTERS_FEE_RECEIVERS: Record<NetworkHexId, Web3.address.Address> = {
    // ******************************************

    [ETHEREUM.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [POLYGON.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [LINEA.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [BSC.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [AVALANCHE.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [ARBITRUM.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [OPTIMISM.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [BASE.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [MANTLE.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),

    // ******************************************

    [BLAST.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [POLYGON_ZKEVM.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
    [GNOSIS.hexChainId]: Web3.address.staticFromString(
        '******************************************'
    ),
}

const groupByUserOperationFeeAndTransfers = (
    input: DeBankTransactionActivityRaw
): {
    feeInCryptoCurrency: {
        amount: number
        currencyId: CurrencyId
    } | null
    outgoingTransfers: DeBankTransactionActivityRaw['sends']
} => {
    const feeInCryptoCurrency = input.sends.find((send) =>
        [
            PAYMASTERS_FEE_RECEIVERS[input.networkHexId],
            SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
        ].includes(send.to_addr)
    )
    const outgoingTransfers = input.sends.filter(
        (send) =>
            ![
                PAYMASTERS_FEE_RECEIVERS[input.networkHexId],
                SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
            ].includes(send.to_addr)
    )
    return {
        feeInCryptoCurrency: feeInCryptoCurrency
            ? {
                  amount: feeInCryptoCurrency.amount,
                  currencyId: feeInCryptoCurrency.currency_id,
              }
            : null,
        outgoingTransfers,
    }
}

export const parseTokenApproval = ({
    input,
    deBankProjectsInfoMap,
    knownCurrencies,
}: {
    input: DeBankTransactionActivityRaw
    deBankProjectsInfoMap: DeBankProjectsInfoMap
    knownCurrencies: KnownCryptoCurrencies
}): Result<
    unknown,
    TokenApprovalTransactionActivity | TokenApprovalRevokedTransactionActivity
> => {
    switch (input.cate_id) {
        case null:
        case 'cancel':
        case 'deploy':
        case 'receive':
        case 'send':
            return failure({ type: 'not_approve_transaction' })
        case 'approve':
            const { token_approve } = input

            if (token_approve === null) {
                return failure({ type: 'not_approve_transaction' })
            }

            const deBankProjectInfo =
                input.project_id && deBankProjectsInfoMap[input.project_id]

            return parseDeBankCryptoMoney({
                amount: token_approve.value,
                currencyId: token_approve.currency_id,
                knownCurrencies,
            }).map((approveAmount) => {
                return {
                    type: (() => {
                        if (approveAmount.amount === 0n) {
                            return 'token_approval_revoked' as const
                        }
                        return 'token_approval' as const
                    })(),
                    approveTo: {
                        address: token_approve.spender,
                        networkHexId: input.networkHexId,
                        name: deBankProjectInfo?.name ?? null,
                        logo: deBankProjectInfo?.logo ?? null,
                        website: deBankProjectInfo?.url ?? null,
                    },
                    limit: {
                        type:
                            approveAmount.amount >=
                            DE_BANK_UNLIMITED_APPROVAL_AMOUNT
                                ? ('Unlimited' as const)
                                : ('Limited' as const),
                        amount: approveAmount,
                    },
                    hash: input.id,
                    timestamp: new Date(input.time_at),
                    networkHexId: input.networkHexId,
                    paidFee: null,
                }
            })

        default:
            return notReachable(input.cate_id)
    }
}

export const parseSendTransactionActivity = ({
    input,
    knownCurrencies,
}: {
    input: DeBankTransactionActivityRaw
    knownCurrencies: KnownCryptoCurrencies
}): Result<unknown, SendTransactionActivity> => {
    switch (input.cate_id) {
        case 'approve':
        case 'cancel':
        case 'deploy':
        case 'receive':
            return failure({ type: 'not_send_transaction' })
        case null:
            if (
                input.tx &&
                input.tx.selector &&
                SPECIAL_TRANSFER_SELECTORS.includes(input.tx.selector) &&
                input.receives.length === 0 &&
                input.sends.length === 1
            ) {
                const [send] = input.sends
                return parseDeBankCryptoMoney({
                    amount: send.amount,
                    currencyId: send.currency_id,
                    knownCurrencies,
                }).map((amount) => ({
                    type: 'send' as const,
                    hash: input.id,
                    receiver: send.to_addr,
                    amount: amount,
                    networkHexId: input.networkHexId,
                    timestamp: new Date(input.time_at),
                    paidFee: null,
                    amountInDefaultCurrency: null,
                }))
            }

            const { feeInCryptoCurrency, outgoingTransfers } =
                groupByUserOperationFeeAndTransfers(input)

            if (
                (input.tx?.selector === HANDLE_USER_OPERATION_SELECTOR ||
                    input.tx === null) &&
                input.receives.length === 0 &&
                outgoingTransfers.length === 1
            ) {
                return shape({
                    sendAmount: parseDeBankCryptoMoney({
                        amount: outgoingTransfers[0].amount,
                        currencyId: outgoingTransfers[0].currency_id,
                        knownCurrencies,
                    }),
                    fee: feeInCryptoCurrency
                        ? parseDeBankCryptoMoney({
                              amount: feeInCryptoCurrency.amount,
                              currencyId: feeInCryptoCurrency.currencyId,
                              knownCurrencies,
                          })
                        : success(null),
                }).map(({ sendAmount, fee }) => ({
                    type: 'send' as const,
                    hash: input.id,
                    receiver: outgoingTransfers[0].to_addr,
                    amount: sendAmount,
                    networkHexId: input.networkHexId,
                    timestamp: new Date(input.time_at),
                    paidFee: fee
                        ? {
                              priceInNativeCurrency: fee,
                              priceInDefaultCurrency: null,
                          }
                        : null,
                    amountInDefaultCurrency: null,
                }))
            }

            return failure({ type: 'not_send_transaction' })
        case 'send': {
            if (input.sends.length === 1 && input.receives.length === 0) {
                const [send] = input.sends

                return parseDeBankCryptoMoney({
                    amount: send.amount,
                    currencyId: send.currency_id,
                    knownCurrencies,
                }).map((amount) => ({
                    type: 'send' as const,
                    hash: input.id,
                    receiver: send.to_addr,
                    amount: amount,
                    networkHexId: input.networkHexId,
                    timestamp: new Date(input.time_at),
                    paidFee: null,
                    amountInDefaultCurrency: null,
                }))
            }
            return failure({ type: 'not_send_transaction' })
        }
        /* istanbul ignore next */
        default:
            return notReachable(input.cate_id)
    }
}

export const parseReceiveTransactionActivity = ({
    input,
    knownCurrencies,
}: {
    input: DeBankTransactionActivityRaw
    knownCurrencies: KnownCryptoCurrencies
}): Result<unknown, ReceiveTransactionActivity> => {
    switch (input.cate_id) {
        case 'approve':
        case 'cancel':
        case 'deploy':
        case 'send':
            return failure({ tyep: 'not_receive_transaction' })
        case null:
        case 'receive': {
            if (input.sends.length === 0 && input.receives.length === 1) {
                const [receive] = input.receives

                return parseDeBankCryptoMoney({
                    amount: receive.amount,
                    currencyId: receive.currency_id,
                    knownCurrencies,
                }).map((money) => ({
                    type: 'receive' as const,
                    hash: input.id,
                    sender: receive.from_addr,
                    amount: money,
                    networkHexId: input.networkHexId,
                    timestamp: new Date(input.time_at),
                    paidFee: null,
                    amountInDefaultCurrency: null,
                }))
            }
            return failure({ type: 'not_send_transaction' })
        }
        /* istanbul ignore next */
        default:
            return notReachable(input.cate_id)
    }
}

export const parseArbitrarySmartContractTransactionActivity = ({
    input,
    knownCurrencies,
    deBankProjectsInfoMap,
}: {
    input: DeBankTransactionActivityRaw
    knownCurrencies: KnownCryptoCurrencies
    deBankProjectsInfoMap: DeBankProjectsInfoMap
}): Result<unknown, ArbitrarySmartContractTransactionActivity> => {
    switch (input.cate_id) {
        case 'send':
        case 'approve':
        case 'cancel':
        case 'deploy':
        case 'receive':
            return failure({ type: 'not_arbitrary_smart_contract' })
        case null: {
            const { tx } = input
            if (tx === null) {
                return failure({ type: 'not_arbitrary_smart_contract' })
            }
            if (tx.selector && EXCLUDED_SELECTORS.includes(tx.selector)) {
                return failure({ type: 'excluded_transaction_type' })
            }

            const { feeInCryptoCurrency, outgoingTransfers } =
                groupByUserOperationFeeAndTransfers(input)

            return shape({
                outgoing: combine(
                    outgoingTransfers.map((transfer) =>
                        parseDeBankCryptoMoney({
                            amount: transfer.amount,
                            currencyId: transfer.currency_id,
                            knownCurrencies,
                        })
                    )
                ),
                incoming: combine(
                    input.receives.map((receive) =>
                        parseDeBankCryptoMoney({
                            amount: receive.amount,
                            currencyId: receive.currency_id,
                            knownCurrencies,
                        })
                    )
                ),
                fee: feeInCryptoCurrency
                    ? parseDeBankCryptoMoney({
                          amount: feeInCryptoCurrency.amount,
                          currencyId: feeInCryptoCurrency.currencyId,
                          knownCurrencies,
                      })
                    : success(null),
            }).andThen(({ outgoing, incoming, fee }) =>
                parseArbitrarySmartContractTransactionActivityTransactionType({
                    outgoing,
                    incoming,
                }).andThen((transactionType) => {
                    const deBankProjectInfo =
                        input.project_id &&
                        deBankProjectsInfoMap[input.project_id]

                    return success({
                        type: 'arbitrary_smart_contract_interaction' as const,
                        hash: input.id,
                        transactionType,
                        functionSignature: tx.selector,
                        smartContract:
                            tx.selector !== HANDLE_USER_OPERATION_SELECTOR &&
                            tx.to_addr &&
                            deBankProjectInfo?.name
                                ? {
                                      address: tx.to_addr,
                                      networkHexId: input.networkHexId,
                                      name: deBankProjectInfo.name,
                                      logo: deBankProjectInfo?.logo ?? null,
                                      website: deBankProjectInfo?.url ?? null,
                                  }
                                : null,
                        networkHexId: input.networkHexId,
                        timestamp: new Date(input.time_at),
                        paidFee: fee
                            ? {
                                  priceInNativeCurrency: fee,
                                  priceInDefaultCurrency: null,
                              }
                            : null,
                        priceInDefaultCurrency: null,
                    })
                })
            )
        }
        /* istanbul ignore next */
        default:
            return notReachable(input.cate_id)
    }
}

const parseArbitrarySmartContractTransactionActivityTransactionType = ({
    incoming,
    outgoing,
}: {
    incoming: CryptoMoney[]
    outgoing: CryptoMoney[]
}): Result<unknown, ArbitrarySmartContractTransactionType> => {
    if (incoming.length === 0 && outgoing.length === 0) {
        return success({
            type: 'noInNoOut' as const,
        })
    }

    if (incoming.length === 1 && outgoing.length === 1) {
        return success({
            type: 'exactlyOneInOneOut' as const,
            incoming: incoming[0],
            outgoing: outgoing[0],
        })
    }

    if (incoming.length >= 1 && outgoing.length === 0) {
        return success({
            type: 'onlyIn' as const,
            incoming,
            priceInDefaultCurrency: null,
        })
    }

    if (incoming.length === 0 && outgoing.length >= 1) {
        return success({
            type: 'onlyOut' as const,
            outgoing,
            priceInDefaultCurrency: null,
        })
    }

    return success({
        type: 'multipleInAndOut' as const,
        incoming,
        outgoing,
    })
}

export const parseSwapTransactionActivity = ({
    input,
    knownCurrencies,
}: {
    input: DeBankTransactionActivityRaw
    knownCurrencies: KnownCryptoCurrencies
}): Result<unknown, SwapTransactionActivity> => {
    switch (input.cate_id) {
        case 'send':
        case 'approve':
        case 'cancel':
        case 'deploy':
        case 'receive':
            return failure({ type: 'not_swap_transaction' })
        case null: {
            if (
                input.sends.length === 1 &&
                SOCKET_GATEWAY_CONTRACTS.has(input.sends[0].to_addr) &&
                input.receives.length === 1
            ) {
                return shape({
                    toToken: parseDeBankCryptoMoney({
                        amount: input.receives[0].amount,
                        currencyId: input.receives[0].currency_id,
                        knownCurrencies,
                    }),
                    fromToken: parseDeBankCryptoMoney({
                        amount: input.sends[0].amount,
                        currencyId: input.sends[0].currency_id,
                        knownCurrencies,
                    }),
                }).map(({ toToken, fromToken }) => ({
                    type: 'swap' as const,
                    toToken,
                    fromToken,
                    hash: input.id,
                    networkHexId: input.networkHexId,
                    timestamp: new Date(input.time_at),
                    paidFee: null,
                }))
            }
            return failure({ type: 'not_swap_transaction' })
        }
        /* istanbul ignore next */
        default:
            return notReachable(input.cate_id)
    }
}

const parseFailedTransaction = ({
    input,
    deBankProjectsInfoMap,
}: {
    input: DeBankTransactionActivityRaw
    deBankProjectsInfoMap: DeBankProjectsInfoMap
}): Result<unknown, FailedTransactionActivity> => {
    const { tx } = input
    if (tx === null) {
        return failure({ type: 'not_failed_transaction' })
    }

    const deBankProjectInfo =
        input.project_id && deBankProjectsInfoMap[input.project_id]

    switch (tx.status) {
        case 0:
            return success({
                type: 'failed_transaction' as const,
                functionSignature: tx.selector,
                smartContract: tx.to_addr
                    ? {
                          address: tx.to_addr,
                          networkHexId: input.networkHexId,
                          name: deBankProjectInfo?.name ?? null,
                          logo: deBankProjectInfo?.logo ?? null,
                          website: deBankProjectInfo?.url ?? null,
                      }
                    : null,
                hash: tx.id,
                networkHexId: input.networkHexId,
                timestamp: new Date(input.time_at),
                paidFee: null,
            })
        case 1:
            return failure({
                type: 'not_failed_transaction',
            })

        /* istanbul ignore next */
        default:
            return notReachable(tx.status)
    }
}

const parseDeBankCryptoMoney = ({
    amount,
    currencyId,
    knownCurrencies,
}: {
    amount: number
    currencyId: string
    knownCurrencies: KnownCryptoCurrencies
}): Result<unknown, CryptoMoney> => {
    const currency = knownCurrencies[currencyId]

    if (!currency) {
        return failure({
            type: 'unknown_currency',
            currencyId,
        })
    }

    return success({
        amount: unsafe_fromNumberWithFraction(amount, currency.fraction),
        currency,
    })
}

const parseBridgeTransactionOutgoingActivity = (
    input: SendTransactionActivity
): Result<unknown, OutgoingBridgeTransactionActivity> => {
    if (!SOCKET_GATEWAY_CONTRACTS.has(input.receiver)) {
        return failure({ type: 'not_bridge_transaction' })
    }
    return success({
        type: 'outgoing_bridge',
        toToken: input.amount,
        networkHexId: input.amount.currency.networkHexChainId,
        hash: input.hash,
        timestamp: new Date(input.timestamp),
        paidFee: input.paidFee,
    })
}

const parseBridgeTransactionInComingActivity = (
    input: ReceiveTransactionActivity
): Result<unknown, IncomingBridgeTransactionActivity> => {
    if (!SOCKET_GATEWAY_CONTRACTS.has(input.sender)) {
        return failure({ type: 'not_bridge_transaction' })
    }
    return success({
        type: 'incoming_bridge' as const,
        fromToken: input.amount,
        networkHexId: input.amount.currency.networkHexChainId,
        hash: input.hash,
        timestamp: new Date(input.timestamp),
        paidFee: input.paidFee,
    })
}

export const parseDeBankActitityTransation = ({
    input,
    deBankProjectsInfoMap,
    knownCurrencies,
}: {
    input: DeBankTransactionActivityRaw
    deBankProjectsInfoMap: DeBankProjectsInfoMap
    knownCurrencies: KnownCryptoCurrencies
}): Result<unknown, RegularTransactionActivity> => {
    return oneOf(input, [
        parseFailedTransaction({
            input,
            deBankProjectsInfoMap,
        }),
        parseTokenApproval({ input, deBankProjectsInfoMap, knownCurrencies }),
        parseSwapTransactionActivity({ input, knownCurrencies }),
        parseSendTransactionActivity({ input, knownCurrencies }).andThen(
            (send) =>
                oneOf(send, [
                    parseBridgeTransactionOutgoingActivity(send),
                    success(send),
                ])
        ),
        parseReceiveTransactionActivity({ input, knownCurrencies }).andThen(
            (receive) =>
                oneOf(receive, [
                    parseBridgeTransactionInComingActivity(receive),
                    success(receive),
                ])
        ),
        parseArbitrarySmartContractTransactionActivity({
            input,
            deBankProjectsInfoMap,
            knownCurrencies,
        }),
    ])
}
