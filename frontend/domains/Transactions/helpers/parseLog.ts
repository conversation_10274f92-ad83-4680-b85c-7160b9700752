import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { toBigInt } from '@zeal/toolkit/Hexadecimal'
import { oneOf, Result, shape, success } from '@zeal/toolkit/Result'
import { required, string } from '@zeal/toolkit/Result/validators'
import * as Web3 from '@zeal/toolkit/Web3'
import { decodeErrorResult } from '@zeal/toolkit/Web3/abi'
import { parse as parseAddress } from '@zeal/toolkit/Web3/address'

import { currencyId } from '@zeal/domains/Currency'
import { PredefinedNetwork } from '@zeal/domains/Network'
import { ParsedLog } from '@zeal/domains/RPCRequest'

import { IndexedTransactionLogDTO } from '..'

const ERC20_TRANSFER_HASH =
    '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'

const ADDRESS_PREFIX = '0x000000000000000000000000'

const ACCOUNT_DEPLOYED_ABI = [
    {
        type: 'event',
        name: 'AccountDeployed',
        inputs: [
            { type: 'bytes32', name: 'userOperationHash', indexed: true },
            { type: 'address', name: 'sender', indexed: true },
            { type: 'address', name: 'factory', indexed: false },
            { type: 'address', name: 'paymaster', indexed: false },
        ],
    },
] as const

const SET_ALLOWANCE_ABI = [
    {
        type: 'event',
        name: 'SetAllowance',
        inputs: [
            { type: 'bytes32', name: 'allowanceKey', indexed: false },
            { type: 'uint128', name: 'balance', indexed: false },
            { type: 'uint128', name: 'maxRefill', indexed: false },
            { type: 'uint128', name: 'refill', indexed: false },
            { type: 'uint64', name: 'period', indexed: false },
            { type: 'uint64', name: 'timestamp', indexed: false },
        ],
    },
] as const

const ENABLE_MODULE_ABI = [
    {
        type: 'event',
        name: 'EnabledModule',
        inputs: [{ type: 'address', name: 'module', indexed: false }],
    },
] as const

const DISABLE_MODULE_ABI = [
    {
        type: 'event',
        name: 'DisabledModule',
        inputs: [{ type: 'address', name: 'module', indexed: false }],
    },
] as const

const SAFE_MODULE_TRANSACTION_ABI = [
    {
        type: 'event',
        name: 'SafeModuleTransaction',
        inputs: [
            { type: 'address', name: 'module', indexed: false },
            { type: 'address', name: 'to', indexed: false },
            { type: 'uint256', name: 'value', indexed: false },
            { type: 'bytes', name: 'data', indexed: false },
            { type: 'uint8', name: 'operation', indexed: false },
        ],
    },
] as const

const SAFE_RECEIVED_ABI = [
    {
        type: 'event',
        name: 'SafeReceived',
        inputs: [
            { type: 'address', name: 'sender', indexed: true },
            { type: 'uint256', name: 'value', indexed: false },
        ],
    },
] as const

const USER_OPERATION_EVENT_ABI = [
    {
        type: 'event',
        name: 'UserOperationEvent',
        inputs: [
            { type: 'bytes32', name: 'userOpHash', indexed: true },
            { type: 'address', name: 'sender', indexed: true },
            { type: 'address', name: 'paymaster', indexed: true },
            { type: 'uint256', name: 'nonce', indexed: false },
            { type: 'bool', name: 'success', indexed: false },
            { type: 'uint256', name: 'actualGasCost', indexed: false },
            { type: 'uint256', name: 'actualGasUsed', indexed: false },
        ],
    },
] as const

const USER_OPERATION_REVERT_REASON_ABI = [
    {
        anonymous: false,
        inputs: [
            { indexed: true, name: 'userOpHash', type: 'bytes32' },
            { indexed: true, name: 'sender', type: 'address' },
            { indexed: false, name: 'nonce', type: 'uint256' },
            { indexed: false, name: 'revertReason', type: 'bytes' },
        ],
        name: 'UserOperationRevertReason',
        type: 'event',
    },
] as const

const THRESHOLD_UPDATED_ABI = [
    {
        type: 'event',
        name: 'ThresholdUpdated',
        inputs: [{ type: 'uint256', name: 'threshold', indexed: false }],
    },
] as const

const parseErrorResult = (
    input: Hexadecimal.Hexadecimal
): Result<unknown, string | null> => {
    try {
        const {
            args: [errorName],
        } = decodeErrorResult({ data: input })

        return string(errorName)
    } catch (error) {
        return success(null)
    }
}

export const parseLog = (
    input: IndexedTransactionLogDTO,
    network: PredefinedNetwork
): Result<unknown, ParsedLog> => {
    switch (true) {
        case input.topics.length === 3 &&
            input.topics[0] === ERC20_TRANSFER_HASH &&
            input.topics[1].startsWith(ADDRESS_PREFIX) &&
            input.topics[2].startsWith(ADDRESS_PREFIX) &&
            input.data &&
            input.data.length === 66: {
            return shape({
                from: parseAddress(
                    input.topics[1].replace(ADDRESS_PREFIX, '0x')
                ),
                to: parseAddress(input.topics[2].replace(ADDRESS_PREFIX, '0x')),
                amount: success(input.data),
            }).map(({ from, to, amount }) => ({
                type: 'erc20_transfer' as const,
                eventSignature: 'Transfer(address,address,amount)',
                from,
                to,
                amount: toBigInt(amount),
                currencyId: currencyId({
                    network: network.hexChainId,
                    address: input.address,
                }),
                logIndex: input.logIndex,
            }))
        }

        case input.topics.length === 3 &&
            input.topics[0] ===
                '0x1c4fada7374c0a9ee8841fc38afe82932dc0f8e69012e927f061a8bae611a201': {
            const decoded = Web3.abi.decodeEventLog({
                abi: USER_OPERATION_REVERT_REASON_ABI,
                eventName: 'UserOperationRevertReason',
                data: input.data ?? undefined,
                topics: [
                    input.topics[0],
                    input.topics[1],
                    input.topics[2],
                    input.topics[3],
                ],
            })

            // TODO @resetko-zeal why decoded.args is non-nullable here?
            return oneOf(decoded.args, [
                required(decoded.args).andThen((decodedArgs) =>
                    shape({
                        type: success('user_operation_revert_reason' as const),
                        eventSignature: success(
                            'UserOperationRevertReason(bytes32,address,uint256,bytes)' as const
                        ),
                        userOpHash: Hexadecimal.parse(decodedArgs.userOpHash),
                        sender: Web3.address.parse(decodedArgs.sender),
                        nonce: success(decodedArgs.nonce),
                        revertReason: Hexadecimal.parse(
                            decodedArgs.revertReason
                        ).andThen(parseErrorResult),
                        logIndex: success(input.logIndex),
                    })
                ),

                success({
                    type: 'unknown',
                    log: input,
                    logIndex: input.logIndex,
                }),
            ])
        }

        case input.topics.length === 3 &&
            input.topics[0] ===
                '0xd51a9c61267aa6196961883ecf5ff2da6619c37dac0fa92122513fb32c032d2d': {
            const decoded = Web3.abi.decodeEventLog({
                abi: ACCOUNT_DEPLOYED_ABI,
                eventName: 'AccountDeployed',
                data: input.data ?? undefined,
                topics: [input.topics[0], input.topics[1], input.topics[2]],
            })

            if (!decoded.args) {
                return success({
                    type: 'unknown',
                    log: input,
                    logIndex: input.logIndex,
                })
            }

            return shape({
                sender: parseAddress(decoded.args.sender),
                factory: parseAddress(decoded.args.factory),
                paymaster: parseAddress(decoded.args.paymaster),
            }).map(({ sender, factory, paymaster }) => ({
                type: 'account_deployed' as const,
                eventSignature:
                    'AccountDeployed(bytes32,address,address,address)',
                userOperationHash: decoded.args.userOperationHash,
                sender,
                factory,
                paymaster,
                logIndex: input.logIndex,
            }))
        }

        case input.topics.length === 2 &&
            input.topics[0] ===
                '0x9465fa0c962cc76958e6373a993326400c1c94f8be2fe3a952adfa7f60b2ea26': {
            return parseAddress(
                input.topics[1].replace(ADDRESS_PREFIX, '0x')
            ).map((ownerAddress) => ({
                type: 'added_owner' as const,
                eventSignature: 'AddedOwner(address)',
                owner: ownerAddress,
                logIndex: input.logIndex,
            }))
        }

        case input.topics.length === 3 &&
            input.topics[0] ===
                '0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925' &&
            input.data !== null: {
            return shape({
                owner: parseAddress(
                    input.topics[1].replace(ADDRESS_PREFIX, '0x')
                ),
                spender: parseAddress(
                    input.topics[2].replace(ADDRESS_PREFIX, '0x')
                ),
                amount: success(input.data),
            }).map(({ owner, spender, amount }) => ({
                type: 'approval' as const,
                eventSignature: 'Approval(address,address,uint256)',
                owner,
                spender,
                amount: toBigInt(amount),
                currencyId: currencyId({
                    network: network.hexChainId,
                    address: input.address,
                }),
                logIndex: input.logIndex,
            }))
        }

        case input.topics.length === 1 &&
            input.topics[0] ===
                '0xadfa8ecb21b6962ebcd0adbd9ab985b7b4c5b5eb3b0dead683171565c7bfe171': {
            const decoded = Web3.abi.decodeEventLog({
                abi: THRESHOLD_UPDATED_ABI,
                eventName: 'ThresholdUpdated',
                data: input.data ?? undefined,
                topics: [input.topics[0]],
            })

            if (!decoded.args) {
                return success({
                    type: 'unknown',
                    log: input,
                    logIndex: input.logIndex,
                })
            }

            return success({
                type: 'threshold_updated' as const,
                eventSignature: 'ThresholdUpdated(uint256)',
                threshold: decoded.args.threshold,
                logIndex: input.logIndex,
            })
        }

        case input.topics.length === 1 &&
            input.topics[0] ===
                '0x63d7ec44a20b176da1d60d75259d264ee67b3d8213706afa71a28f69ed8ebece': {
            const decoded = Web3.abi.decodeEventLog({
                abi: SET_ALLOWANCE_ABI,
                eventName: 'SetAllowance',
                data: input.data ?? undefined,
                topics: [input.topics[0]],
            })

            if (!decoded.args) {
                return success({
                    type: 'unknown',
                    log: input,
                    logIndex: input.logIndex,
                })
            }

            return success({
                type: 'set_allowance' as const,
                eventSignature:
                    'SetAllowance(bytes32,uint128,uint128,uint128,uint64,uint64)',
                allowanceKey: decoded.args.allowanceKey,
                balance: decoded.args.balance,
                maxRefill: decoded.args.maxRefill,
                refill: decoded.args.refill,
                period: Number(decoded.args.period),
                timestamp: Number(decoded.args.timestamp),
                logIndex: input.logIndex,
            })
        }

        case input.topics.length === 1 &&
            input.topics[0] ===
                '0xecdf3a3effea5783a3c4c2140e677577666428d44ed9d474a0b3a4c9943f8440': {
            const decoded = Web3.abi.decodeEventLog({
                abi: ENABLE_MODULE_ABI,
                eventName: 'EnabledModule',
                data: input.data ?? undefined,
                topics: [input.topics[0]],
            })

            if (!decoded.args) {
                return success({
                    type: 'unknown',
                    log: input,
                    logIndex: input.logIndex,
                })
            }

            return parseAddress(decoded.args.module).map((moduleAddress) => ({
                type: 'enable_module' as const,
                eventSignature: 'EnabledModule(address)',
                module: moduleAddress,
                logIndex: input.logIndex,
            }))
        }

        case input.topics.length === 1 &&
            input.topics[0] ===
                '0xaab4fa2b463f581b2b32cb3b7e3b704b9ce37cc209b5fb4d77e593ace4054276': {
            const decoded = Web3.abi.decodeEventLog({
                abi: DISABLE_MODULE_ABI,
                eventName: 'DisabledModule',
                data: input.data ?? undefined,
                topics: [input.topics[0]],
            })

            if (!decoded.args) {
                return success({
                    type: 'unknown',
                    log: input,
                    logIndex: input.logIndex,
                })
            }

            return parseAddress(decoded.args.module).map((moduleAddress) => ({
                type: 'disable_module' as const,
                eventSignature: 'DisabledModule(address)',
                module: moduleAddress,
                logIndex: input.logIndex,
            }))
        }

        case input.topics.length === 1 &&
            input.topics[0] ===
                '0xb648d3644f584ed1c2232d53c46d87e693586486ad0d1175f8656013110b714e': {
            const decoded = Web3.abi.decodeEventLog({
                abi: SAFE_MODULE_TRANSACTION_ABI,
                eventName: 'SafeModuleTransaction',
                data: input.data ?? undefined,
                topics: [input.topics[0]],
            })

            if (!decoded.args) {
                return success({
                    type: 'unknown',
                    log: input,
                    logIndex: input.logIndex,
                })
            }

            return shape({
                module: parseAddress(decoded.args.module),
                to: parseAddress(decoded.args.to),
                decodedArgs: success(decoded.args),
            }).map(({ module, to, decodedArgs }) => ({
                type: 'safe_module_transaction' as const,
                eventSignature:
                    'SafeModuleTransaction(address,address,uint256,bytes,uint8)',
                module,
                to,
                from: input.address,
                value: decodedArgs.value,
                data: decodedArgs.data,
                operation: decodedArgs.operation,
                logIndex: input.logIndex,
            }))
        }

        case input.topics.length === 2 &&
            input.topics[0] ===
                '0x3d0ce9bfc3ed7d6862dbb28b2dea94561fe714a1b4d019aa8af39730d1ad7c3d': {
            const decoded = Web3.abi.decodeEventLog({
                abi: SAFE_RECEIVED_ABI,
                eventName: 'SafeReceived',
                data: input.data ?? undefined,
                topics: [input.topics[0], input.topics[1]],
            })

            if (!decoded.args) {
                return success({
                    type: 'unknown',
                    log: input,
                    logIndex: input.logIndex,
                })
            }

            return parseAddress(decoded.args.sender).map((senderAddress) => ({
                type: 'safe_received',
                eventSignature: 'SafeReceived(address,uint256)',
                from: senderAddress,
                to: input.address,
                value: decoded.args.value,
                logIndex: input.logIndex,
            }))
        }

        case input.topics.length === 4 &&
            input.topics[0] ===
                '0x49628fd1471006c1482da88028e9ce4dbb080b815c9b0344d39e5a8e6ec1419f': {
            const decoded = Web3.abi.decodeEventLog({
                abi: USER_OPERATION_EVENT_ABI,
                eventName: 'UserOperationEvent',
                data: input.data ?? undefined,
                topics: [
                    input.topics[0],
                    input.topics[1],
                    input.topics[2],
                    input.topics[3],
                ],
            })

            if (!decoded.args) {
                return success({
                    type: 'unknown',
                    log: input,
                    logIndex: input.logIndex,
                })
            }

            return shape({
                sender: parseAddress(decoded.args.sender),
                paymaster: parseAddress(decoded.args.paymaster),
            }).map(({ sender, paymaster }) => ({
                type: 'user_operation_event' as const,
                eventSignature:
                    'UserOperationEvent(bytes32,address,address,uint256,bool,uint256,uint256)',
                userOpHash: decoded.args.userOpHash,
                sender,
                paymaster,
                nonce: decoded.args.nonce,
                success: decoded.args.success,
                actualGasCost: decoded.args.actualGasCost,
                actualGasUsed: decoded.args.actualGasUsed,
                logIndex: input.logIndex,
            }))
        }

        default:
            return success({
                type: 'unknown',
                log: input,
                logIndex: input.logIndex,
            })
    }
}
