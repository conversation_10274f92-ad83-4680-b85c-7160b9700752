import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { sub } from '@zeal/toolkit/Date'
import { useReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    SwapsIOSwapRequest,
    SwapsIOSwapRequestsMap,
} from '@zeal/domains/Currency/domains/SwapsIO'
import { SwapsIOContinuationToken } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchUserSwaps'
import { HistoricalTakerUserCurrencyRateMap } from '@zeal/domains/Earn'
import { AppErrorBanner } from '@zeal/domains/Error/components/AppErrorBanner'
import { AppErrorListItem } from '@zeal/domains/Error/components/AppErrorListItem'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import {
    SwapsIOTransactionActivityPending,
    TransactionActivityFilter,
    TransactionActivityV2,
} from '@zeal/domains/Transactions'
import { fetchCardTransactionActivities } from '@zeal/domains/Transactions/api/fetchCardTransactionActivities'
import { fetchTransactionActivities } from '@zeal/domains/Transactions/api/fetchTransactionActivities'
import {
    ListItemSkeleton,
    TransactionsSkeleton,
} from '@zeal/domains/Transactions/components/Skeleton'
import {
    DeBankContinuationToken,
    fetchDebankTransactionActivitiesFromRawFeed,
} from '@zeal/domains/Transactions/domains/DeBank/api/fetchDeBankActivityRawFeed'

import { Layout } from './Layout'

type Props = {
    address: Web3.address.Address
    cardConfig: CardConfig
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    portfolio: Portfolio2
    accountsMap: AccountsMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    installationId: string
    transactionActivityFilter: TransactionActivityFilter
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_transaction_activity_2_clicked'
                  | 'on_swaps_io_transaction_activity_swap_started'
                  | 'on_swaps_io_transaction_activity_completed'
                  | 'on_swaps_io_transaction_activity_failed'
                  | 'on_swaps_io_pending_transaction_clicked'
                  | 'on_swaps_io_completed_transaction_activity_clicked'
          }
      >
    | {
          type: 'on_historical_taker_user_currency_rate_fetched'
          earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
      }
    | {
          type: 'on_swaps_io_swap_requests_fetched'
          address: Web3.address.Address
          swapsIOSwapRequests: SwapsIOSwapRequest[]
      }

const PAGINATION_TIME_WINDOW_IN_DAYS = 7

const MINIMUM_TRANSACTIONS_ACTIVITES_COUNT = 10
const MAXIMUM_LOAD_ATTEMPTS = 8

const fetchWithMinimumTransactionsCount = async ({
    address,
    portfolio,
    cardConfig,
    keyStoreMap,
    sessionPassword,
    startTime,
    endTime,
    networkRPCMap,
    networkMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    minimumTransactionsCount,
    maximumLoadAttemps,
    transactionActivities,
    transactionActivityFilter,
    installationId,
    deBankContinuationToken,
    swapsIOContinuationToken,
}: {
    address: Web3.address.Address
    portfolio: Portfolio2
    cardConfig: CardConfig
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    startTime: Date
    endTime: Date
    minimumTransactionsCount: number
    maximumLoadAttemps: number
    transactionActivities: TransactionActivityV2[]
    transactionActivityFilter: TransactionActivityFilter
    installationId: string
    deBankContinuationToken: DeBankContinuationToken | null
    swapsIOContinuationToken: SwapsIOContinuationToken | null
}): Promise<{
    transactionActivities: TransactionActivityV2[]
    swapsIOPendignTransactionActivities: SwapsIOTransactionActivityPending[]
    continueFromTimestamp: Date | null
    deBankContinuationToken: DeBankContinuationToken | null
    swapsIOContinuationToken: SwapsIOContinuationToken | null
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequests: SwapsIOSwapRequest[]
}> => {
    const {
        transactionActivities: newTransactionActivities,
        swapsIOPendignTransactionActivities,
        deBankContinuationToken: nextDeBankContinuationToken,
        swapsIOContinuationToken: nextSwapsIOContinuationToken,
        earnHistoricalTakerUserCurrencyRateMap:
            newEarnHistoricalTakerUserCurrencyRateMap,
        swapsIOSwapRequests: newSwapsIOSwapRequests,
    } = await fetchTransactionActivities({
        address,
        portfolio,
        cardConfig,
        keyStoreMap,
        sessionPassword,
        startTime,
        endTime,
        networkRPCMap,
        networkMap,
        earnHistoricalTakerUserCurrencyRateMap,
        transactionActivityFilter,
        installationId,
        swapsIOSwapRequestsMap,
        deBankContinuationToken,
        swapsIOContinuationToken,
    })

    const mergedTransactionActivities = [
        ...transactionActivities,
        ...newTransactionActivities,
    ].toSorted((a, b) => b.timestamp.getTime() - a.timestamp.getTime())

    if (
        mergedTransactionActivities.length < minimumTransactionsCount &&
        maximumLoadAttemps > 0
    ) {
        return fetchWithMinimumTransactionsCount({
            address,
            portfolio,
            cardConfig,
            keyStoreMap,
            sessionPassword,
            startTime: endTime,
            endTime: sub(endTime, {
                days: PAGINATION_TIME_WINDOW_IN_DAYS,
            }),
            networkRPCMap,
            networkMap,
            earnHistoricalTakerUserCurrencyRateMap,
            swapsIOSwapRequestsMap,
            minimumTransactionsCount,
            transactionActivityFilter,
            installationId,
            maximumLoadAttemps: maximumLoadAttemps - 1,
            transactionActivities: mergedTransactionActivities,
            deBankContinuationToken: nextDeBankContinuationToken,
            swapsIOContinuationToken: nextSwapsIOContinuationToken,
        })
    }

    if (
        mergedTransactionActivities.length < minimumTransactionsCount &&
        maximumLoadAttemps === 0
    ) {
        switch (transactionActivityFilter.type) {
            case 'card_filter':
                const fallbackCardTransactionActivities =
                    await fetchCardTransactionActivities({
                        address,
                        cardConfig,
                        keyStoreMap,
                        sessionPassword,
                        startTime: startTime,
                        endTime: null,
                    })
                const combinedTransactions = [
                    ...mergedTransactionActivities,
                    ...fallbackCardTransactionActivities,
                ].toSorted(
                    (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
                )

                const lastFallbackCardTransactionActivity =
                    fallbackCardTransactionActivities[
                        fallbackCardTransactionActivities.length - 1
                    ]
                return {
                    transactionActivities: combinedTransactions,
                    continueFromTimestamp: lastFallbackCardTransactionActivity
                        ? lastFallbackCardTransactionActivity.timestamp
                        : null,
                    deBankContinuationToken: nextDeBankContinuationToken,
                    swapsIOContinuationToken: nextSwapsIOContinuationToken,
                    earnHistoricalTakerUserCurrencyRateMap:
                        newEarnHistoricalTakerUserCurrencyRateMap,
                    swapsIOSwapRequests: newSwapsIOSwapRequests,
                    swapsIOPendignTransactionActivities,
                }
            case 'network_filter': {
                //FIXME :: @Kat - might cause issues with card pagination, but this depends on long periods of no transactions and would have to be handled differently
                if (!nextDeBankContinuationToken) {
                    return {
                        transactionActivities: [],
                        swapsIOPendignTransactionActivities,
                        continueFromTimestamp: null,
                        deBankContinuationToken: null,
                        swapsIOContinuationToken: nextSwapsIOContinuationToken,
                        earnHistoricalTakerUserCurrencyRateMap:
                            newEarnHistoricalTakerUserCurrencyRateMap,
                        swapsIOSwapRequests: newSwapsIOSwapRequests,
                    }
                }

                const fallbackDeBankTransactionActivities =
                    await fetchDebankTransactionActivitiesFromRawFeed({
                        withScam: false,
                        networkRPCMap,
                        currentNetwork: transactionActivityFilter.network,
                        deBankActivityRawFeed:
                            nextDeBankContinuationToken.deBankActivityRawFeed,
                    })

                const continueFromTimestamp = (() => {
                    switch (nextDeBankContinuationToken.type) {
                        case 'cached_transactions':
                            return nextDeBankContinuationToken.endTime
                        case 'no_more_transactions':
                            return null
                        default:
                            return notReachable(nextDeBankContinuationToken)
                    }
                })()

                return {
                    transactionActivities: fallbackDeBankTransactionActivities,
                    swapsIOPendignTransactionActivities,
                    continueFromTimestamp,
                    deBankContinuationToken: nextDeBankContinuationToken,
                    swapsIOContinuationToken: nextSwapsIOContinuationToken,
                    earnHistoricalTakerUserCurrencyRateMap:
                        newEarnHistoricalTakerUserCurrencyRateMap,
                    swapsIOSwapRequests: newSwapsIOSwapRequests,
                }
            }
            default:
                return notReachable(transactionActivityFilter)
        }
    }

    return {
        transactionActivities: mergedTransactionActivities,
        swapsIOPendignTransactionActivities,
        continueFromTimestamp: endTime,
        deBankContinuationToken: nextDeBankContinuationToken,
        swapsIOContinuationToken: nextSwapsIOContinuationToken,
        earnHistoricalTakerUserCurrencyRateMap:
            newEarnHistoricalTakerUserCurrencyRateMap,
        swapsIOSwapRequests: newSwapsIOSwapRequests,
    }
}

const calculateInitParams = ({
    address,
    cardConfig,
    keyStoreMap,
    sessionPassword,
    portfolio,
    installationId,
    networkRPCMap,
    networkMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    transactionActivityFilter,
}: {
    address: Web3.address.Address
    cardConfig: CardConfig
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    portfolio: Portfolio2
    installationId: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    transactionActivityFilter: TransactionActivityFilter
}): {
    address: Web3.address.Address
    portfolio: Portfolio2
    cardConfig: CardConfig
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    startTime: Date
    endTime: Date
    installationId: string
    minimumTransactionsCount: number
    maximumLoadAttemps: number
    transactionActivities: TransactionActivityV2[]
    deBankContinuationToken: DeBankContinuationToken | null
    swapsIOContinuationToken: SwapsIOContinuationToken | null
    transactionActivityFilter: TransactionActivityFilter
} => {
    const startTime = new Date()
    const endTime = sub(startTime, {
        days: PAGINATION_TIME_WINDOW_IN_DAYS,
    })

    return {
        address,
        cardConfig,
        keyStoreMap,
        sessionPassword,
        portfolio,
        startTime,
        endTime,
        installationId,
        minimumTransactionsCount: MINIMUM_TRANSACTIONS_ACTIVITES_COUNT,
        maximumLoadAttemps: MAXIMUM_LOAD_ATTEMPTS,
        transactionActivities: [],
        deBankContinuationToken: null,
        swapsIOContinuationToken: null,
        networkRPCMap,
        networkMap,
        earnHistoricalTakerUserCurrencyRateMap,
        swapsIOSwapRequestsMap,
        transactionActivityFilter,
    }
}

export const TransactionActivityFeed = ({
    address,
    cardConfig,
    keyStoreMap,
    sessionPassword,
    portfolio,
    accountsMap,
    networkMap,
    networkRPCMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    installationId,
    transactionActivityFilter,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useReloadableData(
        fetchWithMinimumTransactionsCount,
        {
            type: 'loading',
            params: calculateInitParams({
                address,
                cardConfig,
                keyStoreMap,
                sessionPassword,
                portfolio,
                networkRPCMap,
                networkMap,
                earnHistoricalTakerUserCurrencyRateMap,
                swapsIOSwapRequestsMap,
                installationId,
                transactionActivityFilter,
            }),
        },
        {
            accumulate: (newData, prevData) => ({
                transactionActivities: [
                    ...prevData.transactionActivities,
                    ...newData.transactionActivities,
                ],
                swapsIOPendignTransactionActivities:
                    newData.swapsIOPendignTransactionActivities,
                continueFromTimestamp: newData.continueFromTimestamp,
                deBankContinuationToken: newData.deBankContinuationToken,
                swapsIOContinuationToken: newData.swapsIOContinuationToken,
                earnHistoricalTakerUserCurrencyRateMap:
                    newData.earnHistoricalTakerUserCurrencyRateMap,
                swapsIOSwapRequests: newData.swapsIOSwapRequests,
            }),
        }
    )

    const onLiveMsg = useLiveRef(onMsg)
    const liveSwapsIOSwapRequestsMap = useLiveRef(swapsIOSwapRequestsMap)
    const liveEarnHistoricalTakerUserCurrencyRateMap = useLiveRef(
        earnHistoricalTakerUserCurrencyRateMap
    )

    useEffect(() => {
        if (
            loadable.params.transactionActivityFilter !==
                transactionActivityFilter ||
            loadable.params.address !== address
        ) {
            setLoadable({
                type: 'loading',
                params: calculateInitParams({
                    address,
                    cardConfig,
                    keyStoreMap,
                    sessionPassword,
                    portfolio,
                    networkRPCMap,
                    networkMap,
                    earnHistoricalTakerUserCurrencyRateMap:
                        liveEarnHistoricalTakerUserCurrencyRateMap.current,
                    swapsIOSwapRequestsMap: liveSwapsIOSwapRequestsMap.current,
                    transactionActivityFilter,
                    installationId,
                }),
            })
        }
    }, [
        loadable.params.transactionActivityFilter,
        loadable.params.address,
        cardConfig,
        keyStoreMap,
        sessionPassword,
        portfolio,
        address,
        networkRPCMap,
        networkMap,
        liveEarnHistoricalTakerUserCurrencyRateMap,
        liveSwapsIOSwapRequestsMap,
        transactionActivityFilter,
        installationId,
        setLoadable,
    ])

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                onLiveMsg.current({
                    type: 'on_historical_taker_user_currency_rate_fetched',
                    earnHistoricalTakerUserCurrencyRateMap:
                        loadable.data.earnHistoricalTakerUserCurrencyRateMap,
                })
                onLiveMsg.current({
                    type: 'on_swaps_io_swap_requests_fetched',
                    address: loadable.params.address,
                    swapsIOSwapRequests: loadable.data.swapsIOSwapRequests,
                })
                break
            case 'reloading':
            case 'loading':
                break
            case 'subsequent_failed':
            case 'error':
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, onLiveMsg])

    switch (loadable.type) {
        case 'loaded':
            return (
                <Layout
                    transactionActivities={loadable.data.transactionActivities}
                    swapsIOPendignTransactionActivities={
                        loadable.data.swapsIOPendignTransactionActivities
                    }
                    accountsMap={accountsMap}
                    networkMap={networkMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_transaction_activity_2_clicked':
                            case 'on_swaps_io_completed_transaction_activity_clicked':
                            case 'on_swaps_io_transaction_activity_swap_started':
                            case 'on_swaps_io_transaction_activity_completed':
                            case 'on_swaps_io_transaction_activity_failed':
                            case 'on_swaps_io_pending_transaction_clicked':
                                onMsg(msg)
                                break
                            case 'on_end_reached':
                                if (loadable.data.continueFromTimestamp) {
                                    setLoadable({
                                        type: 'reloading',
                                        data: loadable.data,
                                        params: {
                                            ...loadable.params,
                                            deBankContinuationToken:
                                                loadable.data
                                                    .deBankContinuationToken,
                                            swapsIOContinuationToken:
                                                loadable.data
                                                    .swapsIOContinuationToken,
                                            startTime:
                                                loadable.data
                                                    .continueFromTimestamp,
                                            endTime: sub(
                                                loadable.data
                                                    .continueFromTimestamp,
                                                {
                                                    days: PAGINATION_TIME_WINDOW_IN_DAYS,
                                                }
                                            ),
                                        },
                                    })
                                }
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                    footer={null}
                />
            )
        case 'loading':
            return <TransactionsSkeleton />
        case 'reloading':
            return (
                <Layout
                    transactionActivities={loadable.data.transactionActivities}
                    swapsIOPendignTransactionActivities={
                        loadable.data.swapsIOPendignTransactionActivities
                    }
                    accountsMap={accountsMap}
                    networkMap={networkMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_transaction_activity_2_clicked':
                            case 'on_swaps_io_completed_transaction_activity_clicked':
                            case 'on_swaps_io_transaction_activity_swap_started':
                            case 'on_swaps_io_transaction_activity_completed':
                            case 'on_swaps_io_transaction_activity_failed':
                            case 'on_swaps_io_pending_transaction_clicked':
                                onMsg(msg)
                                break
                            case 'on_end_reached': // we are already reloading
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                    footer={<ListItemSkeleton />}
                />
            )

        case 'subsequent_failed':
            return (
                <Layout
                    transactionActivities={loadable.data.transactionActivities}
                    swapsIOPendignTransactionActivities={
                        loadable.data.swapsIOPendignTransactionActivities
                    }
                    accountsMap={accountsMap}
                    networkMap={networkMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_transaction_activity_2_clicked':
                            case 'on_swaps_io_completed_transaction_activity_clicked':
                            case 'on_swaps_io_transaction_activity_swap_started':
                            case 'on_swaps_io_transaction_activity_completed':
                            case 'on_swaps_io_transaction_activity_failed':
                            case 'on_swaps_io_pending_transaction_clicked':
                                onMsg(msg)
                                break
                            case 'on_end_reached': // we are at the end, and we need to retry manually
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                    footer={
                        <AppErrorListItem
                            installationId={installationId}
                            error={parseAppError(loadable.error)}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'try_again_clicked':
                                        setLoadable({
                                            type: 'loading',
                                            params: loadable.params,
                                        })
                                        break

                                    default:
                                        notReachable(msg.type)
                                }
                            }}
                        />
                    }
                />
            )

        case 'error':
            return (
                <AppErrorBanner
                    error={parseAppError(loadable.error)}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'try_again_clicked':
                                setLoadable({
                                    type: 'loading',
                                    params: loadable.params,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg.type)
                        }
                    }}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
