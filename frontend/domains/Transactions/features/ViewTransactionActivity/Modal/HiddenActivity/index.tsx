import { useEffect, useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { EmptyStateWidget } from '@zeal/uikit/EmptyStateWidget'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Tokens } from '@zeal/uikit/Icon/Empty'
import { IconButton } from '@zeal/uikit/IconButton'
import { Screen } from '@zeal/uikit/Screen/Screen'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { uuid } from '@zeal/toolkit/Crypto'
import { sub } from '@zeal/toolkit/Date'
import { useReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { RegularTransactionActivity } from '@zeal/domains/Transactions'
import { fetchHiddenTransactionActivities } from '@zeal/domains/Transactions/api/fetchHiddenTransactionActivities'
import {
    ListItemSkeleton,
    TransactionsSkeleton,
} from '@zeal/domains/Transactions/components/Skeleton'
import {
    DeBankContinuationToken,
    fetchDebankTransactionActivitiesFromRawFeed,
} from '@zeal/domains/Transactions/domains/DeBank/api/fetchDeBankActivityRawFeed'

import { Layout } from './Layout'
import type { State as ModalState } from './Modal'
import { Modal as HiddenActivityModal } from './Modal'

type Props = {
    address: Web3.address.Address
    portfolio: Portfolio2
    accountsMap: AccountsMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

const PAGINATION_TIME_WINDOW_IN_DAYS = 30

const MINIMUM_TRANSACTIONS_ACTIVITES_COUNT = 10
const MAXIMUM_LOAD_ATTEMPTS = 5

const fetchWithMinimumTransactionsCount = async ({
    address,
    startTime,
    endTime,
    networkRPCMap,
    networkMap,
    portfolio,
    minimumTransactionsCount,
    maximumLoadAttemps,
    transactionActivities,
    installationId,
    deBankContinuationToken,
}: {
    address: Web3.address.Address
    portfolio: Portfolio2
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    startTime: Date
    endTime: Date
    minimumTransactionsCount: number
    maximumLoadAttemps: number
    transactionActivities: RegularTransactionActivity[]
    installationId: string
    deBankContinuationToken: DeBankContinuationToken | null
}): Promise<{
    transactionActivities: RegularTransactionActivity[]
    continueFromTimestamp: Date | null
    deBankContinuationToken: DeBankContinuationToken
}> => {
    const {
        transactionActivities: newTransactionActivities,
        deBankContinuationToken: nextDeBankContinuationToken,
    } = await fetchHiddenTransactionActivities({
        address,
        portfolio,
        startTime,
        endTime,
        networkRPCMap,
        networkMap,
        installationId,
        deBankContinuationToken,
    })

    const allTransactionActivities = [
        ...transactionActivities,
        ...newTransactionActivities,
    ]

    if (
        allTransactionActivities.length < minimumTransactionsCount &&
        maximumLoadAttemps > 0
    ) {
        return fetchWithMinimumTransactionsCount({
            address,
            portfolio,
            startTime: endTime,
            endTime: sub(endTime, {
                days: PAGINATION_TIME_WINDOW_IN_DAYS,
            }),
            networkRPCMap,
            networkMap,
            minimumTransactionsCount,
            installationId,
            maximumLoadAttemps: maximumLoadAttemps - 1,
            transactionActivities: allTransactionActivities,
            deBankContinuationToken: nextDeBankContinuationToken,
        })
    }

    if (allTransactionActivities.length === 0 && maximumLoadAttemps === 0) {
        const fallbackDeBankTransactionActivities =
            await fetchDebankTransactionActivitiesFromRawFeed({
                withScam: false,
                currentNetwork: { type: 'all_networks' },
                networkRPCMap,
                deBankActivityRawFeed:
                    nextDeBankContinuationToken.deBankActivityRawFeed,
            })

        return {
            transactionActivities: fallbackDeBankTransactionActivities,
            continueFromTimestamp: null,
            deBankContinuationToken: nextDeBankContinuationToken,
        }
    }
    return {
        transactionActivities: allTransactionActivities,
        continueFromTimestamp: maximumLoadAttemps > 0 ? endTime : null,
        deBankContinuationToken: nextDeBankContinuationToken,
    }
}

const calculateInitParams = ({
    address,
    portfolio,
    installationId,
    networkRPCMap,
    networkMap,
}: {
    address: Web3.address.Address
    portfolio: Portfolio2
    installationId: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
}): {
    address: Web3.address.Address
    portfolio: Portfolio2
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    startTime: Date
    endTime: Date
    installationId: string
    minimumTransactionsCount: number
    maximumLoadAttemps: number
    transactionActivities: RegularTransactionActivity[]
    deBankContinuationToken: DeBankContinuationToken | null
} => {
    const startTime = new Date()
    const endTime = sub(startTime, {
        days: PAGINATION_TIME_WINDOW_IN_DAYS,
    })

    return {
        address,
        portfolio,
        startTime,
        endTime,
        installationId,
        minimumTransactionsCount: MINIMUM_TRANSACTIONS_ACTIVITES_COUNT,
        maximumLoadAttemps: MAXIMUM_LOAD_ATTEMPTS,
        transactionActivities: [],
        deBankContinuationToken: null,
        networkRPCMap,
        networkMap,
    }
}

export const HiddenActivity = ({
    address,
    portfolio,
    accountsMap,
    networkMap,
    networkRPCMap,
    installationId,
    onMsg,
}: Props) => {
    const [sectionId] = useState(() => uuid())
    const layoutId = `hidden-activity-${sectionId}`
    const [modalState, setModalState] = useState<ModalState>({ type: 'closed' })

    const [loadable, setLoadable] = useReloadableData(
        fetchWithMinimumTransactionsCount,
        {
            type: 'loading',
            params: calculateInitParams({
                address,
                portfolio,
                networkRPCMap,
                networkMap,
                installationId,
            }),
        },
        {
            accumulate: (newData, prevData) => ({
                transactionActivities: [
                    ...prevData.transactionActivities,
                    ...newData.transactionActivities,
                ],
                continueFromTimestamp: newData.continueFromTimestamp,
                deBankContinuationToken: newData.deBankContinuationToken,
            }),
        }
    )

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                break
            case 'reloading':
            case 'loading':
                break
            case 'subsequent_failed':
            case 'error':
                captureError(loadable.error)
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable])
    return (
        <Screen
            background="light"
            padding="form"
            aria-labelledby={layoutId}
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} shrink fill>
                <ActionBar
                    left={
                        <>
                            <IconButton
                                variant="on_light"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                {({ color }) => (
                                    <BackIcon size={24} color={color} />
                                )}
                            </IconButton>
                            <Text
                                variant="title3"
                                weight="medium"
                                color="textPrimary"
                            >
                                <FormattedMessage
                                    id="transactions.page.hiddenActivity.title"
                                    defaultMessage="Hidden activity"
                                />
                            </Text>
                        </>
                    }
                />
                {(() => {
                    switch (loadable.type) {
                        case 'loaded':
                            return (
                                <>
                                    <Layout
                                        transactions={
                                            loadable.data.transactionActivities
                                        }
                                        accountsMap={accountsMap}
                                        networkMap={networkMap}
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'on_hidden_activity_transaction_click':
                                                    setModalState({
                                                        type: 'regular_transaction_details',
                                                        transaction:
                                                            msg.transaction,
                                                    })
                                                    break
                                                case 'close':
                                                    onMsg(msg)
                                                    break
                                                case 'on_end_reached':
                                                    if (
                                                        loadable.data
                                                            .continueFromTimestamp
                                                    ) {
                                                        setLoadable({
                                                            type: 'reloading',
                                                            data: loadable.data,
                                                            params: {
                                                                ...loadable.params,
                                                                deBankContinuationToken:
                                                                    loadable
                                                                        .data
                                                                        .deBankContinuationToken,
                                                                startTime:
                                                                    loadable
                                                                        .data
                                                                        .continueFromTimestamp,
                                                                endTime: sub(
                                                                    loadable
                                                                        .data
                                                                        .continueFromTimestamp,
                                                                    {
                                                                        days: PAGINATION_TIME_WINDOW_IN_DAYS,
                                                                    }
                                                                ),
                                                            },
                                                        })
                                                    }
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(msg)
                                            }
                                        }}
                                        footer={null}
                                    />
                                    <HiddenActivityModal
                                        state={modalState}
                                        accountsMap={accountsMap}
                                        networkMap={networkMap}
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'close':
                                                    setModalState({
                                                        type: 'closed',
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        msg.type
                                                    )
                                            }
                                        }}
                                    />
                                </>
                            )
                        case 'loading':
                            return <TransactionsSkeleton />
                        case 'reloading':
                        case 'subsequent_failed':
                            return (
                                <>
                                    <Layout
                                        transactions={
                                            loadable.data.transactionActivities
                                        }
                                        accountsMap={accountsMap}
                                        networkMap={networkMap}
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'on_hidden_activity_transaction_click':
                                                    setModalState({
                                                        type: 'regular_transaction_details',
                                                        transaction:
                                                            msg.transaction,
                                                    })
                                                    break
                                                case 'close':
                                                    onMsg(msg)
                                                    break
                                                case 'on_end_reached':
                                                    if (
                                                        loadable.data
                                                            .continueFromTimestamp
                                                    ) {
                                                        setLoadable({
                                                            type: 'reloading',
                                                            data: loadable.data,
                                                            params: {
                                                                ...loadable.params,
                                                                deBankContinuationToken:
                                                                    loadable
                                                                        .data
                                                                        .deBankContinuationToken,
                                                                startTime:
                                                                    loadable
                                                                        .data
                                                                        .continueFromTimestamp,
                                                                endTime: sub(
                                                                    loadable
                                                                        .data
                                                                        .continueFromTimestamp,
                                                                    {
                                                                        days: PAGINATION_TIME_WINDOW_IN_DAYS,
                                                                    }
                                                                ),
                                                            },
                                                        })
                                                    }
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(msg)
                                            }
                                        }}
                                        footer={<ListItemSkeleton />}
                                    />
                                    <HiddenActivityModal
                                        state={modalState}
                                        accountsMap={accountsMap}
                                        networkMap={networkMap}
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'close':
                                                    setModalState({
                                                        type: 'closed',
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        msg.type
                                                    )
                                            }
                                        }}
                                    />
                                </>
                            )
                        case 'error':
                            return (
                                <EmptyStateWidget
                                    size="regular"
                                    icon={({ size }) => (
                                        <Tokens
                                            size={size}
                                            color="backgroundLight"
                                        />
                                    )}
                                    title={
                                        <FormattedMessage
                                            id="transactions.viewTRXHistory.errorMessage"
                                            defaultMessage="We failed to load your transactions history"
                                        />
                                    }
                                />
                            )
                        /* istanbul ignore next */
                        default:
                            return notReachable(loadable)
                    }
                })()}
            </Column>
        </Screen>
    )
}
