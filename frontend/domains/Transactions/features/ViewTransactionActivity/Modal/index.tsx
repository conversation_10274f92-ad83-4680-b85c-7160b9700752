import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { Manage as ManageAccounts } from '@zeal/domains/Account/features/Manage'
import { CardConfig } from '@zeal/domains/Card'
import { UserAReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { Earn, HistoricalTakerUserCurrencyRateMap } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import {
    CurrentNetwork,
    NetworkMap,
    NetworkRPCMap,
} from '@zeal/domains/Network'
import { NetworkFilter } from '@zeal/domains/Network/features/Fillter'
import { getAllNetworksFromNetworkMap } from '@zeal/domains/Network/helpers/getAllNetworksFromNetworkMap'
import { Portfolio2, PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import {
    SwapsIOTransactionActivityPending,
    TransactionActivityV2,
} from '@zeal/domains/Transactions'
import { TransactionDetails2 } from '@zeal/domains/Transactions/features/TransactionDetails2'

import { HiddenActivity } from './HiddenActivity'

type Props = {
    state: State
    portfolioMap: PortfolioMap
    networkRPCMap: NetworkRPCMap
    account: Account
    earn: Earn
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    networkMap: NetworkMap
    currencyHiddenMap: CurrencyHiddenMap
    cardConfig: CardConfig
    accounts: AccountsMap
    encryptedPassword: string
    installationId: string
    isEthereumNetworkFeeWarningSeen: boolean
    sessionPassword: string
    customCurrencyMap: CustomCurrencyMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    portfolio: Portfolio2
    userAReferralConfig: UserAReferralConfig
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | MsgOf<typeof NetworkFilter>
    | MsgOf<typeof ManageAccounts>

export type State =
    | {
          type: 'closed' | 'account_filter'
      }
    | {
          type: 'hidden_activity'
      }
    | {
          type: 'transaction_details_2'
          transaction: TransactionActivityV2 | SwapsIOTransactionActivityPending
      }
    | {
          type: 'network_filter'
          network: CurrentNetwork
      }

export const Modal = ({
    state,
    earn,
    account,
    portfolioMap,
    networkRPCMap,
    accountsMap,
    keystoreMap,
    networkMap,
    currencyHiddenMap,
    cardConfig,
    accounts,
    encryptedPassword,
    installationId,
    sessionPassword,
    isEthereumNetworkFeeWarningSeen,
    customCurrencyMap,
    defaultCurrencyConfig,
    onMsg,
    userAReferralConfig,
    portfolio,
    historicalTakerUserCurrencyRateMap,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'hidden_activity':
            return (
                <UIModal>
                    <HiddenActivity
                        address={account.address as Web3.address.Address}
                        portfolio={portfolio}
                        accountsMap={accountsMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'network_filter':
            return (
                <UIModal>
                    <NetworkFilter
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        networkMap={networkMap}
                        currencyHiddenMap={currencyHiddenMap}
                        account={account}
                        keyStoreMap={keystoreMap}
                        networks={getAllNetworksFromNetworkMap(networkMap)}
                        networkRPCMap={networkRPCMap}
                        portfolio={portfolio}
                        currentNetwork={state.network}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'account_filter':
            return (
                <UIModal>
                    <ManageAccounts
                        userAReferralConfig={userAReferralConfig}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        customCurrencyMap={customCurrencyMap}
                        networkMap={networkMap}
                        sessionPassword={sessionPassword}
                        installationId={installationId}
                        networkRPCMap={networkRPCMap}
                        currencyHiddenMap={currencyHiddenMap}
                        encryptedPassword={encryptedPassword}
                        keystoreMap={keystoreMap}
                        cardConfig={cardConfig}
                        portfolioMap={portfolioMap}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        accounts={accounts}
                        account={account}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'transaction_details_2':
            return (
                <TransactionDetails2
                    historicalTakerUserCurrencyRateMap={
                        historicalTakerUserCurrencyRateMap
                    }
                    transaction={state.transaction}
                    earn={earn}
                    cardConfig={cardConfig}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    onMsg={onMsg}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
