import { useState } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { EmptyStateWidget } from '@zeal/uikit/EmptyStateWidget'
import { Tokens } from '@zeal/uikit/Icon/Empty'
import { ExternalLink } from '@zeal/uikit/Icon/ExternalLink'
import { NavCardIconOutline } from '@zeal/uikit/Icon/NavCardIconOutline'
import { NavCardIconSolid } from '@zeal/uikit/Icon/NavCardIconSolid'
import { SpamFolder } from '@zeal/uikit/Icon/SpamFolder'
import { IconButton } from '@zeal/uikit/IconButton'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'
import { TextButton } from '@zeal/uikit/TextButton'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'
import { openExternalURL } from '@zeal/toolkit/Window'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ActionBarAccountSelector } from '@zeal/domains/Account/components/ActionBarAccountSelector'
import { Address } from '@zeal/domains/Address'
import { CardConfig } from '@zeal/domains/Card'
import {
    BridgeSubmitted,
    SubmitedBridgesMap,
} from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { BridgeWidget } from '@zeal/domains/Currency/features/BridgeWidget'
import { HistoricalTakerUserCurrencyRateMap } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import {
    CurrentNetwork,
    CustomNetwork,
    NetworkMap,
    NetworkRPCMap,
    TestNetwork,
} from '@zeal/domains/Network'
import { NetworkSelector } from '@zeal/domains/Network/components/NetworkSelector'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { Submited } from '@zeal/domains/TransactionRequest'
import { List as TransactionRequestList } from '@zeal/domains/TransactionRequest/features/List'
import { TransactionActivityFilter } from '@zeal/domains/Transactions'
import { TransactionActivityFeed } from '@zeal/domains/Transactions/features/TransactionActivityFeed'

type Props = {
    transactionRequests: Record<Address, Submited[]>
    cardConfig: CardConfig
    sessionPassword: string
    portfolio: Portfolio2
    networkMap: NetworkMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    account: Account
    networkRPCMap: NetworkRPCMap
    submitedBridgesMap: SubmitedBridgesMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequstsMap: SwapsIOSwapRequestsMap
    installationId: string
    transactionActivityFilter: TransactionActivityFilter
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type:
              | 'network_filter_click'
              | 'on_hidden_activity_icon_click'
              | 'on_account_selector_click'
      }
    | MsgOf<typeof TransactionRequestList>
    | MsgOf<typeof TransactionActivityFeed>
    | Extract<
          MsgOf<typeof BridgeWidget>,
          { type: 'on_bridge_submitted_click' | 'bridge_completed' }
      >
    | { type: 'on_card_activity_filter_clicked' }

export const Layout = ({
    account,
    networkRPCMap,
    transactionRequests,
    accountsMap,
    keyStoreMap,
    onMsg,
    submitedBridgesMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequstsMap,
    defaultCurrencyConfig,
    networkMap,
    cardConfig,
    sessionPassword,
    portfolio,
    transactionActivityFilter,
    installationId,
}: Props) => {
    const { formatMessage } = useIntl()
    // Capture array so it won't change only after remount
    const [cachedTransactionRequests] = useState<Submited[]>(
        transactionRequests[account.address] || []
    )
    const [bridges] = useState<BridgeSubmitted[]>(() =>
        (submitedBridgesMap[account.address as Web3.address.Address] || []).map(
            ({ submittedBridge }) => submittedBridge
        )
    )

    const currentNetwork: CurrentNetwork = (() => {
        switch (transactionActivityFilter.type) {
            case 'network_filter':
                return transactionActivityFilter.network
            case 'card_filter':
                return { type: 'all_networks' }
            default:
                return notReachable(transactionActivityFilter)
        }
    })()

    return (
        <Screen
            padding="controller_tabs_fullscreen_scroll"
            background="default"
            onNavigateBack={null}
        >
            <Column spacing={12} shrink fill>
                <ActionBar
                    top={
                        <ActionBarAccountSelector
                            account={account}
                            onMsg={() => {
                                onMsg({ type: 'on_account_selector_click' })
                            }}
                        />
                    }
                    left={
                        <Text
                            variant="title3"
                            weight="medium"
                            color="textPrimary"
                        >
                            <FormattedMessage
                                id="transactions.page.title"
                                defaultMessage="Activity"
                            />
                        </Text>
                    }
                    right={
                        <Row spacing={0}>
                            {(() => {
                                switch (cardConfig.type) {
                                    case 'card_readonly_signer_address_is_not_selected':
                                    case 'card_readonly_signer_address_is_selected':
                                        return null
                                    case 'card_readonly_signer_address_is_selected_fully_onboarded':
                                        if (
                                            account.address !==
                                            cardConfig.readonlySignerAddress
                                        ) {
                                            return null
                                        }
                                        return (
                                            <IconButton
                                                variant="on_light_bold"
                                                aria-label={formatMessage({
                                                    id: 'activity.filter.card',
                                                    defaultMessage: 'Card',
                                                })}
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_card_activity_filter_clicked',
                                                    })
                                                }}
                                            >
                                                {({ color }) => {
                                                    switch (
                                                        transactionActivityFilter.type
                                                    ) {
                                                        case 'card_filter':
                                                            return (
                                                                <NavCardIconSolid
                                                                    size={28}
                                                                    color="teal40"
                                                                />
                                                            )
                                                        case 'network_filter':
                                                            return (
                                                                <NavCardIconOutline
                                                                    size={28}
                                                                    color={
                                                                        color
                                                                    }
                                                                />
                                                            )
                                                        default:
                                                            return notReachable(
                                                                transactionActivityFilter
                                                            )
                                                    }
                                                }}
                                            </IconButton>
                                        )
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(cardConfig)
                                }
                            })()}
                            {(() => {
                                switch (currentNetwork.type) {
                                    case 'all_networks':
                                        return (
                                            <IconButton
                                                variant="on_light_bold"
                                                onClick={() =>
                                                    onMsg({
                                                        type: 'on_hidden_activity_icon_click',
                                                    })
                                                }
                                            >
                                                {({ color }) => (
                                                    <SpamFolder
                                                        size={24}
                                                        color={color}
                                                    />
                                                )}
                                            </IconButton>
                                        )
                                    case 'specific_network':
                                        const net = currentNetwork.network
                                        switch (net.type) {
                                            case 'predefined':
                                                return (
                                                    <IconButton
                                                        variant="on_light_bold"
                                                        onClick={() =>
                                                            onMsg({
                                                                type: 'on_hidden_activity_icon_click',
                                                            })
                                                        }
                                                    >
                                                        {({ color }) => (
                                                            <SpamFolder
                                                                size={24}
                                                                color={color}
                                                            />
                                                        )}
                                                    </IconButton>
                                                )

                                            case 'custom':
                                            case 'testnet':
                                                return null

                                            /* istanbul ignore next */
                                            default:
                                                return notReachable(net)
                                        }
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(currentNetwork)
                                }
                            })()}

                            <NetworkSelector
                                variant="on_light_bold"
                                currentNetwork={currentNetwork}
                                size={24}
                                onClick={() => {
                                    onMsg({ type: 'network_filter_click' })
                                }}
                            />
                        </Row>
                    }
                />

                {bridges.length > 0 && (
                    <Column spacing={8}>
                        {bridges.map((bridge) => (
                            <BridgeWidget
                                lastDismissedOnboardingBannerState={false}
                                key={bridge.sourceTransactionHash}
                                bridgeSubmitted={bridge}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_bridge_submitted_click':
                                        case 'bridge_completed':
                                            onMsg(msg)
                                            break
                                        case 'on_dismiss_bridge_widget_click':
                                            throw new ImperativeError(
                                                'Impossible state: Cannot dismiss bridge widget on activity tab'
                                            )
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        ))}
                    </Column>
                )}

                {cachedTransactionRequests.length > 0 && (
                    <TransactionRequestList
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        accountsMap={accountsMap}
                        keyStoreMap={keyStoreMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        transactionRequests={cachedTransactionRequests}
                        onMsg={onMsg}
                    />
                )}

                {(() => {
                    switch (currentNetwork.type) {
                        case 'all_networks':
                            return (
                                <TransactionActivityFeed
                                    address={
                                        account.address as Web3.address.Address
                                    }
                                    cardConfig={cardConfig}
                                    keyStoreMap={keyStoreMap}
                                    sessionPassword={sessionPassword}
                                    portfolio={portfolio}
                                    swapsIOSwapRequestsMap={
                                        swapsIOSwapRequstsMap
                                    }
                                    earnHistoricalTakerUserCurrencyRateMap={
                                        earnHistoricalTakerUserCurrencyRateMap
                                    }
                                    installationId={installationId}
                                    networkRPCMap={networkRPCMap}
                                    accountsMap={accountsMap}
                                    networkMap={networkMap}
                                    transactionActivityFilter={
                                        transactionActivityFilter
                                    }
                                    onMsg={onMsg}
                                />
                            )
                        case 'specific_network':
                            const net = currentNetwork.network
                            switch (net.type) {
                                case 'predefined':
                                    return (
                                        <TransactionActivityFeed
                                            address={
                                                account.address as Web3.address.Address
                                            }
                                            cardConfig={cardConfig}
                                            keyStoreMap={keyStoreMap}
                                            sessionPassword={sessionPassword}
                                            portfolio={portfolio}
                                            networkRPCMap={networkRPCMap}
                                            accountsMap={accountsMap}
                                            networkMap={networkMap}
                                            swapsIOSwapRequestsMap={
                                                swapsIOSwapRequstsMap
                                            }
                                            earnHistoricalTakerUserCurrencyRateMap={
                                                earnHistoricalTakerUserCurrencyRateMap
                                            }
                                            installationId={installationId}
                                            transactionActivityFilter={
                                                transactionActivityFilter
                                            }
                                            onMsg={onMsg}
                                        />
                                    )
                                case 'custom':
                                case 'testnet':
                                    return <TestNetworkStub testNetwork={net} />

                                /* istanbul ignore next */
                                default:
                                    return notReachable(net)
                            }
                        /* istanbul ignore next */
                        default:
                            return notReachable(currentNetwork)
                    }
                })()}
            </Column>
        </Screen>
    )
}

const TestNetworkStub = ({
    testNetwork,
}: {
    testNetwork: TestNetwork | CustomNetwork
}) => (
    <EmptyStateWidget
        size="regular"
        icon={({ size }) => <Tokens size={size} color="backgroundLight" />}
        title={
            !testNetwork.blockExplorerUrl ? (
                <FormattedMessage
                    id="transactions.viewTRXHistory.noTxHistoryForTestNets"
                    defaultMessage="Activity not supported for testnets"
                />
            ) : (
                <FormattedMessage
                    id="transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink"
                    defaultMessage="Activity not supported for testnets{br}<link>Go to block explorer</link>"
                    values={{
                        br: '\n',
                        link: (msg) => (
                            <TextButton
                                onClick={() =>
                                    openExternalURL(
                                        testNetwork.blockExplorerUrl!
                                    )
                                }
                            >
                                {msg}
                                <ExternalLink size={14} />
                            </TextButton>
                        ),
                    }}
                />
            )
        }
    />
)
