import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { Address } from '@zeal/domains/Address'
import { CardConfig } from '@zeal/domains/Card'
import { UserAReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { SubmitedBridgesMap } from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { HistoricalTakerUserCurrencyRateMap } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2, PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { Submited } from '@zeal/domains/TransactionRequest'
import { TransactionActivityFilter } from '@zeal/domains/Transactions'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State } from './Modal'

type Props = {
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    account: Account
    networkRPCMap: NetworkRPCMap
    accounts: AccountsMap
    portfolioMap: PortfolioMap
    keystoreMap: KeyStoreMap
    transactionRequests: Record<Address, Submited[]>
    submitedBridgesMap: SubmitedBridgesMap
    networkMap: NetworkMap
    currencyHiddenMap: CurrencyHiddenMap
    encryptedPassword: string
    isEthereumNetworkFeeWarningSeen: boolean
    sessionPassword: string
    customCurrencyMap: CustomCurrencyMap
    cardConfig: CardConfig
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    portfolio: Portfolio2
    userAReferralConfig: UserAReferralConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'bridge_completed'
                  | 'on_bridge_submitted_click'
                  | 'on_transaction_request_widget_click'
                  | 'transaction_request_cancelled'
                  | 'transaction_request_completed'
                  | 'transaction_request_failed'
                  | 'transaction_request_replaced'
                  | 'on_historical_taker_user_currency_rate_fetched'
                  | 'on_swaps_io_swap_requests_fetched'
                  | 'on_swaps_io_transaction_activity_swap_started'
                  | 'on_swaps_io_transaction_activity_completed'
                  | 'on_swaps_io_transaction_activity_failed'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'account_item_clicked'
                  | 'add_wallet_clicked'
                  | 'confirm_account_delete_click'
                  | 'on_rewards_warning_confirm_account_delete_click'
                  | 'hardware_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_account_label_change_submit'
                  | 'on_add_private_key_click'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_recovery_kit_setup'
                  | 'on_rpc_change_confirmed'
                  | 'on_select_rpc_click'
                  | 'recover_safe_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'track_wallet_clicked'
          }
      >

export const ViewTransactionActivity = ({
    networkRPCMap,
    portfolioMap,
    accounts,
    userAReferralConfig,
    account,
    keystoreMap,
    transactionRequests,
    submitedBridgesMap,
    isEthereumNetworkFeeWarningSeen,
    networkMap,
    currencyHiddenMap,
    encryptedPassword,
    installationId,
    cardConfig,
    sessionPassword,
    customCurrencyMap,
    defaultCurrencyConfig,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    portfolio,
    onMsg,
}: Props) => {
    const [transactionActivityFilter, setTransactionActivityFilter] =
        useState<TransactionActivityFilter>({
            type: 'network_filter',
            network: { type: 'all_networks' },
        })
    const [modal, setModal] = useState<State>({ type: 'closed' })

    useEffect(() => {
        postUserEvent({
            type: 'ActivityEnteredEvent',
            installationId,
        })
    }, [installationId])

    return (
        <>
            <Layout
                defaultCurrencyConfig={defaultCurrencyConfig}
                accountsMap={accounts}
                keyStoreMap={keystoreMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                submitedBridgesMap={submitedBridgesMap}
                transactionRequests={transactionRequests}
                account={account}
                cardConfig={cardConfig}
                sessionPassword={sessionPassword}
                portfolio={portfolio}
                swapsIOSwapRequstsMap={swapsIOSwapRequestsMap}
                earnHistoricalTakerUserCurrencyRateMap={
                    earnHistoricalTakerUserCurrencyRateMap
                }
                transactionActivityFilter={transactionActivityFilter}
                installationId={installationId}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_transaction_activity_2_clicked':
                            setModal({
                                type: 'transaction_details_2',
                                transaction: msg.transaction,
                            })
                            break
                        case 'network_filter_click':
                            switch (transactionActivityFilter.type) {
                                case 'card_filter':
                                    setModal({
                                        type: 'network_filter',
                                        network: { type: 'all_networks' },
                                    })
                                    break
                                case 'network_filter':
                                    setModal({
                                        type: 'network_filter',
                                        network:
                                            transactionActivityFilter.network,
                                    })
                                    break
                                default:
                                    notReachable(transactionActivityFilter)
                            }
                            break
                        case 'on_hidden_activity_icon_click':
                            setModal({ type: 'hidden_activity' })
                            break
                        case 'on_swaps_io_pending_transaction_clicked':
                        case 'on_swaps_io_completed_transaction_activity_clicked':
                            setModal({
                                type: 'transaction_details_2',
                                transaction: msg.transaction,
                            })
                            break
                        case 'transaction_request_completed':
                        case 'transaction_request_failed':
                        case 'bridge_completed':
                        case 'on_bridge_submitted_click':
                        case 'on_transaction_request_widget_click':
                        case 'transaction_request_replaced':
                        case 'on_historical_taker_user_currency_rate_fetched':
                        case 'on_swaps_io_swap_requests_fetched':
                        case 'on_swaps_io_transaction_activity_swap_started':
                        case 'on_swaps_io_transaction_activity_completed':
                        case 'on_swaps_io_transaction_activity_failed':
                            onMsg(msg)
                            break

                        case 'on_account_selector_click':
                            setModal({ type: 'account_filter' })
                            break
                        case 'on_card_activity_filter_clicked': {
                            switch (transactionActivityFilter.type) {
                                case 'card_filter':
                                    setTransactionActivityFilter({
                                        type: 'network_filter',
                                        network: { type: 'all_networks' },
                                    })
                                    break
                                case 'network_filter':
                                    setTransactionActivityFilter({
                                        type: 'card_filter',
                                    })
                                    break
                                default:
                                    notReachable(transactionActivityFilter)
                            }
                            break
                        }
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                historicalTakerUserCurrencyRateMap={
                    earnHistoricalTakerUserCurrencyRateMap
                }
                userAReferralConfig={userAReferralConfig}
                defaultCurrencyConfig={defaultCurrencyConfig}
                sessionPassword={sessionPassword}
                customCurrencyMap={customCurrencyMap}
                installationId={installationId}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                earn={portfolio.earn}
                currencyHiddenMap={currencyHiddenMap}
                accountsMap={accounts}
                networkMap={networkMap}
                keystoreMap={keystoreMap}
                state={modal}
                portfolioMap={portfolioMap}
                cardConfig={cardConfig}
                networkRPCMap={networkRPCMap}
                account={account}
                accounts={accounts}
                encryptedPassword={encryptedPassword}
                portfolio={portfolio}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_network_item_click':
                            setTransactionActivityFilter({
                                type: 'network_filter',
                                network: msg.network,
                            })
                            setModal({ type: 'closed' })
                            break
                        case 'account_item_clicked':
                        case 'confirm_account_delete_click':
                        case 'on_rewards_warning_confirm_account_delete_click':
                            setModal({ type: 'closed' })
                            onMsg(msg)
                            break
                        case 'on_rpc_change_confirmed':
                        case 'on_select_rpc_click':
                        case 'on_account_label_change_submit':
                        case 'on_recovery_kit_setup':
                        case 'on_add_private_key_click':
                        case 'track_wallet_clicked':
                        case 'on_account_create_request':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'add_wallet_clicked':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
