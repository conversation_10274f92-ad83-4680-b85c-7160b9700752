import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import { bigint, object, shape } from '@zeal/toolkit/Result'

import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import {
    fetchRPCResponseWithRetry,
    Request,
} from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { RpcTransaction } from '@zeal/domains/Transactions'
import { parseRpcTransaction } from '@zeal/domains/Transactions/helpers/parseRpcTransaction'

type Params = {
    transactionHash: string
    network: Network
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}

export const createGetTransactionByHashRequest = ({
    transactionHash,
}: {
    transactionHash: string
}): Request<{
    blockNumber: bigint
    nonce: bigint
    value: Hexadecimal.Hexadecimal
    input: Hexadecimal.Hexadecimal
}> => ({
    request: {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'eth_getTransactionByHash',
        params: [transactionHash],
    },
    parser: (response) =>
        object(response)
            .andThen((obj) =>
                shape({
                    blockNumber: bigint(obj.blockNumber),
                    nonce: bigint(obj.nonce),
                    input: Hexadecimal.parse(obj.input),
                    value: Hexadecimal.parse(obj.value),
                })
            )
            .getSuccessResultOrThrow(
                'Failed to parse transaction by hash response'
            ),
})

/**
 * @deprecated use createGetTransactionByHashRequest
 */
export const fetchTransactionByHash = async ({
    transactionHash,
    network,
    networkRPCMap,
    signal,
}: Params): Promise<RpcTransaction> =>
    fetchRPCResponseWithRetry({
        network,
        networkRPCMap,
        request: {
            id: generateRandomNumber(),
            jsonrpc: '2.0',
            method: 'eth_getTransactionByHash',
            params: [transactionHash],
        },
        signal,
    }).then((response) =>
        parseRpcTransaction(response).getSuccessResultOrThrow(
            'failed to parse eth_getTransactionByHash'
        )
    )
