import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { values } from '@zeal/toolkit/Object'
import { groupByType } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { SAFE_4337_PROXY_FACTORY_ADDRESS } from '@zeal/domains/Address/constants'
import { CardConfig } from '@zeal/domains/Card'
import { CurrencyId } from '@zeal/domains/Currency'
import { fetchCryptoCurrency2 } from '@zeal/domains/Currency/api/fetchCryptoCurrency2'
import { SOCKET_GATEWAY_CONTRACTS } from '@zeal/domains/Currency/domains/Bridge/api/fetchBridgeRoutes'
import {
    SwapsIOSwapRequest,
    SwapsIOSwapRequestsMap,
} from '@zeal/domains/Currency/domains/SwapsIO'
import {
    fetchUserSwaps,
    SwapsIOContinuationToken,
} from '@zeal/domains/Currency/domains/SwapsIO/api/fetchUserSwaps'
import { SWAPS_IO_DIAMOND_CONTRACTS } from '@zeal/domains/Currency/domains/SwapsIO/constants'
import { HistoricalTakerUserCurrencyRateMap } from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { GNOSIS } from '@zeal/domains/Network/constants'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import {
    SwapsIOTransactionActivityFailed,
    TransactionActivityFilter,
} from '@zeal/domains/Transactions'
import {
    DeBankContinuationToken,
    fetchDeBankActivityRawFeed,
} from '@zeal/domains/Transactions/domains/DeBank/api/fetchDeBankActivityRawFeed'
import { parseDeBankActitityTransation } from '@zeal/domains/Transactions/domains/DeBank/helpers/parseDeBankActivityTransaction'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { fetchCardTransactionActivities } from './fetchCardTransactionActivities'
import { fetchIndexedTransactionActivities } from './fetchIndexedTransactionActivities'

import {
    CardTransactionActivity,
    DepositIntoEarnTransactionActivity,
    RegularTransactionActivity,
    SwapsIOTransactionActivityCompleted,
    SwapsIOTransactionActivityPending,
    TransactionActivityV2,
} from '..'
import { parseIndexedActivityTransaction } from '../helpers/parseIndexedTransactionActivity'
import {
    parseSwapsIOTransactionActivities,
    parseSwapsIOTransactionActivitiesFromRawSwaps,
} from '../helpers/parseSwapsIOTransactionActivities'

export const fetchTransactionActivities = async ({
    address,
    portfolio,
    cardConfig,
    keyStoreMap,
    sessionPassword,
    startTime,
    endTime,
    networkRPCMap,
    networkMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    installationId,
    deBankContinuationToken,
    swapsIOContinuationToken,
    transactionActivityFilter,
}: {
    address: Web3.address.Address
    portfolio: Portfolio2
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    cardConfig: CardConfig
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    startTime: Date
    endTime: Date
    installationId: string
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    deBankContinuationToken: DeBankContinuationToken | null
    swapsIOContinuationToken: SwapsIOContinuationToken | null
    transactionActivityFilter: TransactionActivityFilter
}): Promise<{
    transactionActivities: TransactionActivityV2[]
    swapsIOPendignTransactionActivities: SwapsIOTransactionActivityPending[]
    deBankContinuationToken: DeBankContinuationToken | null
    swapsIOContinuationToken: SwapsIOContinuationToken | null
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequests: SwapsIOSwapRequest[]
}> => {
    switch (transactionActivityFilter.type) {
        case 'card_filter': {
            const filteredCardTransactions =
                await fetchCardTransactionActivities({
                    address,
                    cardConfig,
                    keyStoreMap,
                    sessionPassword,
                    startTime,
                    endTime,
                })

            return {
                transactionActivities: filteredCardTransactions,
                swapsIOPendignTransactionActivities: [],
                deBankContinuationToken: deBankContinuationToken || {
                    type: 'no_more_transactions',
                    startTime,
                    deBankActivityRawFeed: {
                        currencies: [],
                        projectsMap: {},
                        rawTransactions: [],
                    },
                },
                swapsIOContinuationToken: swapsIOContinuationToken || {
                    type: 'no_more_swaps',
                    startTime,
                    rawSwapRequests: [],
                },
                earnHistoricalTakerUserCurrencyRateMap,
                swapsIOSwapRequests: [],
            }
        }

        case 'network_filter': {
            const [
                {
                    deBankRawActivityFeed,
                    deBankContinuationToken: nextDeBankContinuationToken,
                },
                {
                    indexedTransactions,
                    currencies: indexedTransactionCurrencies,
                    earnHistoricalTakerUserCurrencyRateMap:
                        newEarnHistoricalTakerUserCurrencyRateMap,
                },
                {
                    rawSwapRequests,
                    currencies: swapsIOCurrencies,
                    swapsIOContinuationToken: nextSwapsIOContinuationToken,
                },
                allCardTransactions,
            ] = await (async () => {
                switch (transactionActivityFilter.network.type) {
                    case 'all_networks': {
                        return await Promise.all([
                            fetchDeBankActivityRawFeed({
                                address,
                                startTime,
                                withScam: false,
                                endTime,
                                deBankContinuationToken,
                                currentNetwork:
                                    transactionActivityFilter.network,
                            }),
                            fetchIndexedTransactionActivities({
                                address,
                                currentNetwork:
                                    transactionActivityFilter.network,
                                startTime,
                                endTime,
                                portfolio,
                                cardConfig,
                                networkMap,
                                earnHistoricalTakerUserCurrencyRateMap,
                                networkRPCMap,
                                withScam: false,
                            }),
                            fetchUserSwaps({
                                address,
                                startTime,
                                endTime,
                                currentNetwork:
                                    transactionActivityFilter.network,
                                swapsIOContinuationToken,
                            }).catch(
                                (
                                    error
                                ): {
                                    rawSwapRequests: null
                                    currencies: CurrencyId[]
                                    swapsIOContinuationToken: SwapsIOContinuationToken | null
                                } => {
                                    captureError(error)
                                    return {
                                        rawSwapRequests: null,
                                        currencies: [],
                                        swapsIOContinuationToken: null,
                                    }
                                }
                            ),
                            fetchCardTransactionActivities({
                                address,
                                cardConfig,
                                keyStoreMap,
                                sessionPassword,
                                startTime,
                                endTime,
                            }),
                        ])
                    }
                    case 'specific_network': {
                        return await Promise.all([
                            fetchDeBankActivityRawFeed({
                                address,
                                startTime,
                                withScam: false,
                                endTime,
                                deBankContinuationToken,
                                currentNetwork:
                                    transactionActivityFilter.network,
                            }),
                            fetchIndexedTransactionActivities({
                                address,
                                currentNetwork:
                                    transactionActivityFilter.network,
                                startTime,
                                endTime,
                                portfolio,
                                cardConfig,
                                networkMap,
                                earnHistoricalTakerUserCurrencyRateMap,
                                networkRPCMap,
                                withScam: false,
                            }),
                            fetchUserSwaps({
                                address,
                                startTime,
                                endTime,
                                currentNetwork:
                                    transactionActivityFilter.network,
                                swapsIOContinuationToken,
                            }).catch(
                                (
                                    error
                                ): {
                                    rawSwapRequests: null
                                    currencies: CurrencyId[]
                                    swapsIOContinuationToken: SwapsIOContinuationToken | null
                                } => {
                                    captureError(error)
                                    return {
                                        rawSwapRequests: null,
                                        currencies: [],
                                        swapsIOContinuationToken: null,
                                    }
                                }
                            ),
                        ])
                    }

                    /* istanbul ignore next */
                    default:
                        return notReachable(transactionActivityFilter.network)
                }
            })()

            const knownCurrencies = await fetchCryptoCurrency2({
                currencies: Array.from(
                    new Set([
                        ...deBankRawActivityFeed.currencies,
                        ...indexedTransactionCurrencies,
                        ...swapsIOCurrencies,
                    ])
                ),
                networkRPCMap,
            })

            const [, deBankTransactionActivities] = groupByType(
                deBankRawActivityFeed.rawTransactions.map((rawTransaction) =>
                    parseDeBankActitityTransation({
                        input: rawTransaction,
                        deBankProjectsInfoMap:
                            deBankRawActivityFeed.projectsMap,
                        knownCurrencies,
                    })
                )
            )

            const [, indexedTransactionActivities] = groupByType(
                indexedTransactions.map((indexedRawTransaction) =>
                    parseIndexedActivityTransaction({
                        input: indexedRawTransaction,
                        knownCurrencies,
                        accountAddress: address,
                        historicalTakerUserCurrencyRateMap:
                            newEarnHistoricalTakerUserCurrencyRateMap,
                        cardConfig,
                        earn: portfolio.earn,
                        network: GNOSIS,
                    })
                )
            )

            const depositIntoEarnTransactionActivities =
                indexedTransactionActivities.filter(
                    (trx): trx is DepositIntoEarnTransactionActivity => {
                        switch (trx.type) {
                            case 'arbitrary_smart_contract_interaction':
                            case 'deposit_from_bank':
                            case 'send_to_bank':
                            case 'deposit_into_card':
                            case 'withdraw_from_card':
                            case 'card_owners_updated':
                            case 'card_spend_limit_updated':
                            case 'cashback_deposit':
                            case 'cashback_withdraw':
                            case 'cashback_reward':
                            case 'deployed_smart_wallet_gnosis':
                            case 'recovered_smart_wallet_gnosis':
                            case 'withdraw_from_earn':
                            case 'swap':
                            case 'incoming_bridge':
                            case 'outgoing_bridge':
                            case 'recharge_disabled':
                            case 'recharge_target_set':
                            case 'receive':
                            case 'send':
                            case 'token_approval':
                            case 'token_approval_revoked':
                            case 'failed_transaction':
                                return false
                            case 'deposit_into_earn':
                            case 'breward_claim':
                                return true
                            /* istanbul ignore next */
                            default:
                                return notReachable(trx)
                        }
                    }
                )

            const {
                swapsIOPendignTransactionActivities,
                swapsIOFinishedTransactionActivities,
                swapsIOSwapRequests,
            } = rawSwapRequests
                ? parseSwapsIOTransactionActivitiesFromRawSwaps({
                      rawSwapRequests,
                      knownCurrencies,
                      address,
                      earn: portfolio.earn,
                      cardConfig,
                      depositIntoEarnTransactionActivities,
                  })
                : parseSwapsIOTransactionActivities({
                      swapsIOSwapRequests: (
                          swapsIOSwapRequestsMap[address] ?? []
                      ).filter(({ createdAt }) => {
                          return (
                              createdAt.getTime() <= startTime.getTime() &&
                              createdAt.getTime() >= endTime.getTime()
                          )
                      }),
                      address,
                      cardConfig,
                      earn: portfolio.earn,
                      depositIntoEarnTransactionActivities,
                  })

            reportTransactionActivitiesDiscrepancy({
                deBankTransactions: deBankTransactionActivities,
                indexedTransactions: indexedTransactionActivities,
                installationId,
            })

            return {
                transactionActivities: mergeTransactionActivities(
                    deBankTransactionActivities,
                    indexedTransactionActivities,
                    allCardTransactions || [],
                    swapsIOFinishedTransactionActivities,
                    swapsIOPendignTransactionActivities
                ).filter(excludeUnintentionalApprovals),
                swapsIOPendignTransactionActivities,
                deBankContinuationToken: nextDeBankContinuationToken,
                swapsIOContinuationToken: nextSwapsIOContinuationToken,
                earnHistoricalTakerUserCurrencyRateMap:
                    newEarnHistoricalTakerUserCurrencyRateMap,
                swapsIOSwapRequests,
            }
        }
        /* istanbul ignore next */
        default:
            return notReachable(transactionActivityFilter)
    }
}

const mergeTransactionActivities = (
    deBankTransactions: RegularTransactionActivity[],
    indexedTransactions: RegularTransactionActivity[],
    cardTransactions: CardTransactionActivity[],
    swapsIOFinishedTrandations: (
        | SwapsIOTransactionActivityCompleted
        | SwapsIOTransactionActivityFailed
    )[],
    swapsIOPendingTrandations: SwapsIOTransactionActivityPending[]
): TransactionActivityV2[] => {
    const mergedRegularTransactions = values(
        [...deBankTransactions, ...indexedTransactions].reduce(
            (acc, transaction) => ({
                ...acc,
                [`${transaction.networkHexId}-${transaction.hash}`]:
                    transaction,
            }),
            {} as Record<string, RegularTransactionActivity>
        )
    )

    const swapsIOTransactionHashes = new Set<Hexadecimal>(
        [...swapsIOFinishedTrandations, ...swapsIOPendingTrandations]
            .map(({ swapsIOSwapRequest }) => {
                switch (swapsIOSwapRequest.state) {
                    case 'completed_liq_sent':
                    case 'completed_sent':
                        return [
                            swapsIOSwapRequest.toTransactionHash,
                            swapsIOSwapRequest.fromTransaction?.hash ?? null,
                        ].filter(excludeNullValues)
                    case 'awaiting_send':
                    case 'awaiting_liq_send':
                        return [swapsIOSwapRequest.fromTransactionHash]
                    case 'awaiting_signature':
                    case 'awaiting_receive':
                    case 'cancelled_no_slash':
                    case 'cancelled_awaiting_slash':
                    case 'cancelled_slashed':
                        return null
                    /* istanbul ignore next */
                    default:
                        return notReachable(swapsIOSwapRequest)
                }
            })
            .filter(excludeNullValues)
            .flat()
    )

    return [
        ...mergedRegularTransactions.filter(
            (tx) => !swapsIOTransactionHashes.has(tx.hash as Hexadecimal)
        ),
        ...swapsIOFinishedTrandations,
        ...cardTransactions,
    ].toSorted((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
}

const reportTransactionActivitiesDiscrepancy = ({
    deBankTransactions,
    indexedTransactions,
    installationId,
}: {
    deBankTransactions: RegularTransactionActivity[]
    indexedTransactions: RegularTransactionActivity[]
    installationId: string
}) => {
    const indexedTransactionHashes = new Set(
        indexedTransactions.map((tx) => tx.hash)
    )
    const deBankTransactionHashes = new Set(
        deBankTransactions.map((tx) => tx.hash)
    )

    const deBankMissingTransactionHashes = Array.from(
        deBankTransactionHashes
    ).filter((txHash) => !indexedTransactionHashes.has(txHash))
    const indexedMissingTransactionHashes = Array.from(
        indexedTransactionHashes
    ).filter((txHash) => !deBankTransactionHashes.has(txHash))

    if (
        deBankMissingTransactionHashes.length > 0 ||
        indexedMissingTransactionHashes.length > 0
    ) {
        postUserEvent({
            type: 'ActivityDiscrepanciesDetectedEvent',
            debankMissingTransactions: deBankMissingTransactionHashes,
            indexerMissingTransactions: indexedMissingTransactionHashes,
            installationId,
        })
    }
}

const excludeUnintentionalApprovals = (
    transactionActivity: TransactionActivityV2
): boolean => {
    switch (transactionActivity.type) {
        case 'arbitrary_smart_contract_interaction':
        case 'deposit_from_bank':
        case 'send_to_bank':
        case 'deposit_into_card':
        case 'withdraw_from_card':
        case 'card_owners_updated':
        case 'card_spend_limit_updated':
        case 'cashback_deposit':
        case 'cashback_withdraw':
        case 'cashback_reward':
        case 'deployed_smart_wallet_gnosis':
        case 'recovered_smart_wallet_gnosis':
        case 'withdraw_from_earn':
        case 'breward_claim':
        case 'swap':
        case 'incoming_bridge':
        case 'outgoing_bridge':
        case 'recharge_disabled':
        case 'recharge_target_set':
        case 'receive':
        case 'send':
        case 'failed_transaction':
        case 'deposit_into_earn':
        case 'card_transaction':
        case 'swaps_io_into_card':
        case 'swaps_io_into_earn':
        case 'swaps_io_buy':
            return true
        case 'token_approval':
        case 'token_approval_revoked':
            if (
                transactionActivity.approveTo.address ===
                    SAFE_4337_PROXY_FACTORY_ADDRESS ||
                SOCKET_GATEWAY_CONTRACTS.has(
                    transactionActivity.approveTo.address
                ) ||
                SWAPS_IO_DIAMOND_CONTRACTS.has(
                    transactionActivity.approveTo.address
                )
            ) {
                return false
            }
            return true
        /* istanbul ignore next */
        default:
            return notReachable(transactionActivity)
    }
}
