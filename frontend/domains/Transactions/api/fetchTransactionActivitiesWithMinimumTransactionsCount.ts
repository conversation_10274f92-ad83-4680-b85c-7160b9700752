import { sub } from '@zeal/toolkit/Date'
import * as Web3 from '@zeal/toolkit/Web3'

import { CardConfig } from '@zeal/domains/Card'
import {
    SwapsIOSwapRequest,
    SwapsIOSwapRequestsMap,
} from '@zeal/domains/Currency/domains/SwapsIO'
import { SwapsIOContinuationToken } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchUserSwaps'
import { HistoricalTakerUserCurrencyRateMap } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import {
    CurrentNetwork,
    NetworkMap,
    NetworkRPCMap,
} from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import {
    DeBankContinuationToken,
    fetchDebankTransactionActivitiesFromRawFeed,
} from '@zeal/domains/Transactions/domains/DeBank/api/fetchDeBankActivityRawFeed'

import { fetchTransactionActivities } from './fetchTransactionActivities'

import { SwapsIOTransactionActivityPending, TransactionActivityV2 } from '..'

export const fetchTransactionActivitiesWithMinimumTransactionsCount = async ({
    address,
    currentNetwork,
    cardConfig,
    keyStoreMap,
    sessionPassword,
    startTime,
    networkRPCMap,
    networkMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    portfolio,
    minimumTransactionsCount,
    maximumLoadAttemps,
    paginationTimeWindowInDays,
    transactionActivities,
    installationId,
    deBankContinuationToken,
    swapsIOContinuationToken,
}: {
    address: Web3.address.Address
    currentNetwork: CurrentNetwork
    portfolio: Portfolio2
    cardConfig: CardConfig
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    startTime: Date
    minimumTransactionsCount: number
    maximumLoadAttemps: number
    paginationTimeWindowInDays: number
    transactionActivities: TransactionActivityV2[]
    installationId: string
    deBankContinuationToken: DeBankContinuationToken | null
    swapsIOContinuationToken: SwapsIOContinuationToken | null
}): Promise<{
    transactionActivities: TransactionActivityV2[]
    swapsIOPendignTransactionActivities: SwapsIOTransactionActivityPending[]
    continueFromTimestamp: Date | null
    deBankContinuationToken: DeBankContinuationToken | null
    swapsIOContinuationToken: SwapsIOContinuationToken | null
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequests: SwapsIOSwapRequest[]
}> => {
    const endTime = sub(startTime, {
        days: paginationTimeWindowInDays,
    })

    const {
        transactionActivities: newTransactionActivities,
        swapsIOPendignTransactionActivities,
        deBankContinuationToken: nextDeBankContinuationToken,
        swapsIOContinuationToken: nextSwapsIOContinuationToken,
        earnHistoricalTakerUserCurrencyRateMap:
            newEarnHistoricalTakerUserCurrencyRateMap,
        swapsIOSwapRequests: newSwapsIOSwapRequests,
    } = await fetchTransactionActivities({
        address,
        transactionActivityFilter: {
            type: 'network_filter',
            network: currentNetwork,
        },
        portfolio,
        cardConfig,
        keyStoreMap,
        sessionPassword,
        startTime,
        endTime,
        networkRPCMap,
        networkMap,
        earnHistoricalTakerUserCurrencyRateMap,
        installationId,
        swapsIOSwapRequestsMap,
        deBankContinuationToken,
        swapsIOContinuationToken,
    })

    const mergedTransactionActivities = [
        ...transactionActivities,
        ...newTransactionActivities,
    ].toSorted((a, b) => b.timestamp.getTime() - a.timestamp.getTime())

    if (
        mergedTransactionActivities.length < minimumTransactionsCount &&
        maximumLoadAttemps > 0
    ) {
        return fetchTransactionActivitiesWithMinimumTransactionsCount({
            address,
            currentNetwork,
            portfolio,
            cardConfig,
            keyStoreMap,
            sessionPassword,
            startTime: endTime,
            networkRPCMap,
            networkMap,
            earnHistoricalTakerUserCurrencyRateMap,
            swapsIOSwapRequestsMap,
            minimumTransactionsCount:
                minimumTransactionsCount - mergedTransactionActivities.length,
            installationId,
            paginationTimeWindowInDays,
            maximumLoadAttemps: maximumLoadAttemps - 1,
            transactionActivities: mergedTransactionActivities,
            deBankContinuationToken: nextDeBankContinuationToken,
            swapsIOContinuationToken: nextSwapsIOContinuationToken,
        })
    }

    if (mergedTransactionActivities.length === 0 && maximumLoadAttemps === 0) {
        if (!nextDeBankContinuationToken) {
            return {
                transactionActivities: [],
                swapsIOPendignTransactionActivities,
                continueFromTimestamp: null,
                deBankContinuationToken: null,
                swapsIOContinuationToken: nextSwapsIOContinuationToken,
                earnHistoricalTakerUserCurrencyRateMap:
                    newEarnHistoricalTakerUserCurrencyRateMap,
                swapsIOSwapRequests: newSwapsIOSwapRequests,
            }
        }

        const fallbackDeBankTransactionActivities =
            await fetchDebankTransactionActivitiesFromRawFeed({
                withScam: false,
                currentNetwork,
                networkRPCMap,
                deBankActivityRawFeed:
                    nextDeBankContinuationToken.deBankActivityRawFeed,
            })

        return {
            transactionActivities: fallbackDeBankTransactionActivities,
            swapsIOPendignTransactionActivities,
            continueFromTimestamp: null,
            deBankContinuationToken: nextDeBankContinuationToken,
            swapsIOContinuationToken: nextSwapsIOContinuationToken,
            earnHistoricalTakerUserCurrencyRateMap:
                newEarnHistoricalTakerUserCurrencyRateMap,
            swapsIOSwapRequests: newSwapsIOSwapRequests,
        }
    }

    return {
        transactionActivities: mergedTransactionActivities,
        swapsIOPendignTransactionActivities,
        continueFromTimestamp: maximumLoadAttemps > 0 ? endTime : null,
        deBankContinuationToken: nextDeBankContinuationToken,
        swapsIOContinuationToken: nextSwapsIOContinuationToken,
        earnHistoricalTakerUserCurrencyRateMap:
            newEarnHistoricalTakerUserCurrencyRateMap,
        swapsIOSwapRequests: newSwapsIOSwapRequests,
    }
}
