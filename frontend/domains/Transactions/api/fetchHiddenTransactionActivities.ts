import { values } from '@zeal/toolkit/Object'
import { groupByType } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { fetchCryptoCurrency2 } from '@zeal/domains/Currency/api/fetchCryptoCurrency2'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { GNOSIS } from '@zeal/domains/Network/constants'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { RegularTransactionActivity } from '@zeal/domains/Transactions'
import {
    DeBankContinuationToken,
    fetchDeBankActivityRawFeed,
} from '@zeal/domains/Transactions/domains/DeBank/api/fetchDeBankActivityRawFeed'
import { parseDeBankActitityTransation } from '@zeal/domains/Transactions/domains/DeBank/helpers/parseDeBankActivityTransaction'
import { parseIndexedScamActivityTransaction } from '@zeal/domains/Transactions/helpers/parseIndexedTransactionActivity'

import { fetchIndexedScamTransactionActivities } from './fetchIndexedTransactionActivities'

const mergeTransactionActivities = (
    deBankTransactions: RegularTransactionActivity[],
    indexedTransactions: RegularTransactionActivity[]
): RegularTransactionActivity[] => {
    const merged = [...deBankTransactions, ...indexedTransactions].reduce(
        (acc, transaction) => ({
            ...acc,
            [`${transaction.networkHexId}-${transaction.hash}`]: transaction,
        }),
        {} as Record<string, RegularTransactionActivity>
    )
    return values(merged).sort(
        (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    )
}

export const fetchHiddenTransactionActivities = async ({
    address,
    startTime,
    endTime,
    networkRPCMap,
    deBankContinuationToken,
    portfolio,
}: {
    address: Web3.address.Address
    startTime: Date
    endTime: Date
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    installationId: string
    deBankContinuationToken: DeBankContinuationToken | null
    portfolio: Portfolio2
}): Promise<{
    transactionActivities: RegularTransactionActivity[]
    deBankContinuationToken: DeBankContinuationToken
}> => {
    const [
        {
            deBankRawActivityFeed,
            deBankContinuationToken: nextDeBankContinuationToken,
        },
        { indexedTransactions, currencies: indexedTransactionCurrencies },
    ] = await Promise.all([
        fetchDeBankActivityRawFeed({
            address,
            startTime,
            withScam: true,
            endTime,
            deBankContinuationToken,
            currentNetwork: { type: 'all_networks' },
        }),
        fetchIndexedScamTransactionActivities({
            address,
            portfolio,
            networkRPCMap,
            startTime,
            endTime,
        }),
    ])

    const knownCurrencies = await fetchCryptoCurrency2({
        currencies: Array.from(
            new Set([
                ...deBankRawActivityFeed.currencies,
                ...indexedTransactionCurrencies,
            ])
        ),
        networkRPCMap,
    })

    const [, deBankTransactionActivities] = groupByType(
        deBankRawActivityFeed.rawTransactions.map((rawTransaction) =>
            parseDeBankActitityTransation({
                input: rawTransaction,
                deBankProjectsInfoMap: deBankRawActivityFeed.projectsMap,
                knownCurrencies,
            })
        )
    )

    const [, indexedTransactionActivities] = groupByType(
        indexedTransactions.map((indexedRawTransaction) =>
            parseIndexedScamActivityTransaction({
                input: indexedRawTransaction,
                knownCurrencies,
                accountAddress: address,
                network: GNOSIS,
            })
        )
    )

    return {
        transactionActivities: mergeTransactionActivities(
            deBankTransactionActivities,
            indexedTransactionActivities
        ),

        deBankContinuationToken: nextDeBankContinuationToken,
    }
}
