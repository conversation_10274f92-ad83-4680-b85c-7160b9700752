import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap, Safe4337 } from '@zeal/domains/KeyStore'
import { CheckSafe4337Deployment } from '@zeal/domains/KeyStore/domains/Safe/features/CheckSafe4337Deployment'
import { ActionSource2 } from '@zeal/domains/Main'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Signing } from './Signing'

type Props = {
    transactionHash: Hexadecimal.Hexadecimal

    account: Account
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStore: Safe4337
    keyStoreMap: KeyStoreMap
    network: Network
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    portfolio: Portfolio2 | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    actionSource: ActionSource2
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof Signing>
    | Extract<
          MsgOf<typeof CheckSafe4337Deployment>,
          {
              type:
                  | 'close'
                  | 'drag'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_expand_request'
                  | 'on_minimize_click'
                  | 'on_safe_deployemnt_cancelled'
                  | 'on_safe_deployment_error_popup_cancel_clicked'
                  | 'on_safe_transaction_failure_accepted'
                  | 'on_wrong_network_accepted'
          }
      >

type State = { type: 'check_deployment' } | { type: 'signing' }

export const SignSafeTransactionHash = ({
    accountsMap,
    account,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStore,
    keyStoreMap,
    network,
    networkMap,
    networkRPCMap,
    onMsg,
    sessionPassword,
    transactionHash,
    portfolio,
    defaultCurrencyConfig,
    actionSource,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'check_deployment' })

    switch (state.type) {
        case 'check_deployment':
            return (
                <CheckSafe4337Deployment
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    account={account}
                    accountsMap={accountsMap}
                    actionSource={actionSource}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStore={keyStore}
                    keyStoreMap={keyStoreMap}
                    network={network}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolio={portfolio}
                    sessionPassword={sessionPassword}
                    state={{ type: 'maximised' }}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_safe_deployed':
                                setState({ type: 'signing' })
                                break

                            case 'on_safe_deployment_error_popup_cancel_clicked':
                            case 'close':
                            case 'drag':
                            case 'on_expand_request':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_minimize_click':
                            case 'on_safe_deployemnt_cancelled':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_wrong_network_accepted':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'signing':
            return (
                <Signing
                    installationId={installationId}
                    keyStore={keyStore}
                    sessionPassword={sessionPassword}
                    transactionHash={transactionHash}
                    onMsg={onMsg}
                />
            )

        default:
            return notReachable(state)
    }
}
