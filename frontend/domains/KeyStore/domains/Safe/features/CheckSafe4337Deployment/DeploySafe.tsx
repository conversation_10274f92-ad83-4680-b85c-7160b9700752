import { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { Setting } from '@zeal/uikit/Icon/Setting'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'

import { noop, notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { generateRandomNumber } from '@zeal/toolkit/Number'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { ConnectedMinimized } from '@zeal/domains/DApp/domains/ConnectionState/features/ConnectedMinimized'
import { KeyStoreMap, Safe4337 } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { GNOSIS, OPTIMISM } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { ActionBar } from '@zeal/domains/Transactions/components/ActionBar'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    sessionPassword: string

    visualState: VisualState

    account: Account
    keyStore: Safe4337
    network: Network
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap

    accountsMap: AccountsMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolio: ServerPortfolio2 | null
    actionSource: ActionSource2
    feePresetMap: FeePresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_safe_deployemnt_cancelled' }
    | { type: 'on_safe_deployed' }
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'on_minimize_click'
                  | 'drag'
                  | 'on_expand_request'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_safe_transaction_failure_accepted'
                  | 'on_wrong_network_accepted'
          }
      >

type State =
    | { type: 'deployment_prompt' }
    | { type: 'deploy_safe'; transactionRequest: EthSendTransaction }

type VisualState = { type: 'minimised' } | { type: 'maximised' }

export const DeploySafe = ({
    onMsg,
    account,
    accountsMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    network,
    networkMap,
    networkRPCMap,
    portfolio,
    sessionPassword,
    visualState,
    actionSource,
    feePresetMap,
    keyStore: _keyStore,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'deployment_prompt' })

    switch (state.type) {
        case 'deployment_prompt':
            switch (visualState.type) {
                case 'minimised':
                    return (
                        <ConnectedMinimized
                            installationId={installationId}
                            onMsg={onMsg}
                        />
                    )
                case 'maximised':
                    return (
                        <Screen
                            background="light"
                            padding="form"
                            onNavigateBack={() =>
                                onMsg({ type: 'on_minimize_click' })
                            }
                        >
                            <ActionBar
                                title={null}
                                account={account}
                                actionSourceType={actionSource.type}
                                network={null}
                                onMsg={onMsg}
                            />

                            <Column spacing={12} fill>
                                <Header
                                    title={
                                        <FormattedMessage
                                            id="Sign.CheckSafeDeployment.title"
                                            defaultMessage="Activate device on this network"
                                        />
                                    }
                                    subtitle={
                                        <FormattedMessage
                                            id="Sign.CheckSafeDeployment.subtitle"
                                            defaultMessage="Before you can sign in to an app or sign an off-chain message, you need to activate your device on this network. This happens after you have installed or recovered a smart wallet."
                                        />
                                    }
                                    icon={({ size, color }) => (
                                        <Setting size={size} color={color} />
                                    )}
                                />

                                <Spacer />

                                <Actions variant="default">
                                    <Button
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_safe_deployemnt_cancelled',
                                            })
                                        }
                                        size="regular"
                                        variant="secondary"
                                    >
                                        <FormattedMessage
                                            id="actions.cancel"
                                            defaultMessage="Cancel"
                                        />
                                    </Button>
                                    <Button
                                        onClick={() =>
                                            setState({
                                                type: 'deploy_safe',
                                                transactionRequest: {
                                                    id: generateRandomNumber(),
                                                    jsonrpc: '2.0',
                                                    method: 'eth_sendTransaction',
                                                    params: [
                                                        {
                                                            from: account.address,
                                                            to: account.address,
                                                            value: '0x0',
                                                            data: '',
                                                        },
                                                    ],
                                                },
                                            })
                                        }
                                        size="regular"
                                        variant="primary"
                                    >
                                        <FormattedMessage
                                            id="Sign.CheckSafeDeployment.activate"
                                            defaultMessage="Activate"
                                        />
                                    </Button>
                                </Actions>
                            </Column>
                        </Screen>
                    )
                default:
                    return notReachable(visualState)
            }

        case 'deploy_safe':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    network={network}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    portfolio={portfolio}
                    sendTransactionRequests={[state.transactionRequest]}
                    account={account}
                    accounts={accountsMap}
                    keystores={keyStoreMap}
                    networkMap={networkMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    networksToSponsor={[GNOSIS.hexChainId, OPTIMISM.hexChainId]}
                    installationId={installationId}
                    fetchSimulationByRequest={async () => ({
                        type: 'simulated',
                        simulation: {
                            checks: [],
                            currencies: {},
                            transaction: {
                                type: 'smart_wallet_activation',
                                account,
                                network,
                            },
                        },
                    })}
                    fetchTransactionResultByRequest={async () => ({
                        currencies: {},
                        transaction: {
                            type: 'smart_wallet_activation',
                            account,
                            network,
                        },
                    })}
                    state={visualState}
                    actionSource={actionSource}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'cancel_submitted':
                            case 'on_completed_transaction_close_click':
                            case 'transaction_failure_accepted':
                            case 'transaction_submited':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'transaction_request_replaced':
                                noop() // Not relevant to smart wallet
                                break
                            case 'on_user_operation_bundled':
                                noop()
                                break
                            case 'on_minimize_click':
                            case 'drag':
                            case 'on_expand_request':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_wrong_network_accepted':
                            case 'on_cancel_confirm_transaction_clicked':
                                onMsg(msg)
                                break

                            case 'on_completed_safe_transaction_close_click':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                onMsg({ type: 'on_safe_deployed' })
                                break

                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        default:
            return notReachable(state)
    }
}
