import { useEffect } from 'react'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { ConnectedMinimized } from '@zeal/domains/DApp/domains/ConnectionState/features/ConnectedMinimized'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap, Safe4337 } from '@zeal/domains/KeyStore'
import { fetchSafeOnChainState } from '@zeal/domains/KeyStore/api/fetchSafeOnChainState'
import { ActionSource2 } from '@zeal/domains/Main'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { DeploySafe } from './DeploySafe'

type Props = {
    sessionPassword: string
    keyStore: Safe4337

    state: VisualState

    account: Account
    network: Network
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap

    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolio: ServerPortfolio2 | null
    actionSource: ActionSource2
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_safe_deployment_error_popup_cancel_clicked' }
    | { type: 'close' }
    | { type: 'on_safe_deployed' }
    | Extract<
          MsgOf<typeof DeploySafe>,
          {
              type:
                  | 'drag'
                  | 'on_expand_request'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_minimize_click'
                  | 'on_safe_deployemnt_cancelled'
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_safe_transaction_failure_accepted'
                  | 'on_wrong_network_accepted'
          }
      >

type VisualState = { type: 'minimised' } | { type: 'maximised' }

export const CheckSafe4337Deployment = ({
    account,
    keyStore,
    network,
    networkMap,
    onMsg,
    sessionPassword,
    state,
    networkRPCMap,
    accountsMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    feePresetMap,
    portfolio,
    actionSource,
    defaultCurrencyConfig,
}: Props) => {
    const onMsgLive = useLiveRef(onMsg)
    const [loadable, setLoadable] = useLoadableData(fetchSafeOnChainState, {
        type: 'loading',
        params: {
            keyStore,
            network,
            networkRPCMap,
        },
    })

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                switch (loadable.data) {
                    case 'deployed':
                        onMsgLive.current({ type: 'on_safe_deployed' })
                        break
                    case 'not_deployed':
                    case 'no_local_signer_added':
                        break

                    default:
                        notReachable(loadable.data)
                }
                break

            case 'loading':
            case 'error':
                break

            default:
                return notReachable(loadable)
        }
    }, [loadable, onMsgLive])

    switch (loadable.type) {
        case 'loading':
            switch (state.type) {
                case 'minimised':
                    return (
                        <ConnectedMinimized
                            installationId={installationId}
                            onMsg={onMsg}
                        />
                    )

                case 'maximised':
                    return (
                        <LoadingLayout
                            title={null}
                            onClose={null}
                            actionBar={
                                <ActionBar
                                    top={
                                        <ActionBarAccountIndicator
                                            account={account}
                                        />
                                    }
                                    left={
                                        <Clickable
                                            onClick={() =>
                                                onMsg({ type: 'close' })
                                            }
                                        >
                                            <BackIcon
                                                size={24}
                                                color="iconDefault"
                                            />
                                        </Clickable>
                                    }
                                />
                            }
                        />
                    )
                default:
                    return notReachable(state)
            }

        case 'error':
            switch (state.type) {
                case 'minimised':
                    return (
                        <ConnectedMinimized
                            installationId={installationId}
                            onMsg={onMsg}
                        />
                    )

                case 'maximised':
                    return (
                        <>
                            <LoadingLayout
                                title={null}
                                onClose={null}
                                actionBar={
                                    <ActionBar
                                        left={
                                            <ActionBarAccountIndicator
                                                account={account}
                                            />
                                        }
                                    />
                                }
                            />

                            <AppErrorPopup
                                installationId={installationId}
                                error={parseAppError(loadable.error)}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg({
                                                type: 'on_safe_deployment_error_popup_cancel_clicked',
                                            })
                                            break
                                        case 'try_again_clicked':
                                            setLoadable({
                                                type: 'loading',
                                                params: loadable.params,
                                            })
                                            break

                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        </>
                    )

                default:
                    return notReachable(state)
            }

        case 'loaded': {
            switch (loadable.data) {
                case 'deployed':
                    return (
                        <LoadingLayout
                            title={null}
                            onClose={null}
                            actionBar={
                                <ActionBar
                                    left={
                                        <ActionBarAccountIndicator
                                            account={account}
                                        />
                                    }
                                />
                            }
                        />
                    )
                case 'no_local_signer_added':
                case 'not_deployed':
                    return (
                        <DeploySafe
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            feePresetMap={feePresetMap}
                            keyStore={keyStore}
                            account={account}
                            accountsMap={accountsMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            installationId={installationId}
                            keyStoreMap={keyStoreMap}
                            network={network}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            portfolio={portfolio}
                            sessionPassword={sessionPassword}
                            visualState={state}
                            actionSource={actionSource}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'drag':
                                    case 'on_expand_request':
                                    case 'on_4337_auto_gas_token_selection_clicked':
                                    case 'on_4337_gas_currency_selected':
                                    case 'on_minimize_click':
                                    case 'on_safe_deployemnt_cancelled':
                                    case 'on_cancel_confirm_transaction_clicked':
                                    case 'on_safe_transaction_failure_accepted':
                                    case 'on_wrong_network_accepted':
                                        onMsg(msg)
                                        break

                                    case 'on_safe_deployed':
                                        setLoadable({
                                            type: 'loading',
                                            params: loadable.params,
                                        })
                                        break

                                    default:
                                        return notReachable(msg)
                                }
                            }}
                        />
                    )

                default:
                    return notReachable(loadable.data)
            }
        }

        default:
            return notReachable(loadable)
    }
}
