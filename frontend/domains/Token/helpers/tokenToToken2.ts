import {
    CryptoCurrency,
    FiatCurrency,
    FiatCurrencyCode,
    KnownCurrencies,
} from '@zeal/domains/Currency'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { getCryptoCurrencyOrThrow } from '@zeal/domains/Currency/helpers/getCryptoCurrency'
import { FXRate2 } from '@zeal/domains/FXRate'
import { applyNullableRate } from '@zeal/domains/FXRate/helpers/applyRate'
import { FiatMoney } from '@zeal/domains/Money'

import { Token, Token2 } from '..'

/**
 * @deprecated Use portfolioToPortfolio2 if you can, remove once Token type is dead
 */
export const tokenToToken2 = ({
    token,
    knownCurrencies,
}: {
    token: Token
    knownCurrencies: KnownCurrencies
}): Token2 => {
    const cryptoCurrency = getCryptoCurrencyOrThrow({
        cryptoCurrencyId: token.balance.currencyId,
        knownCurrencies,
    })
    const balance = {
        amount: token.balance.amount,
        currency: cryptoCurrency,
    }

    const priceInDefaultCurrency = ((): {
        priceInDefaultCurrency: FiatMoney | null
        rate: FXRate2<CryptoCurrency, FiatCurrency> | null
    } => {
        if (token.rate === null) {
            return { rate: null, priceInDefaultCurrency: null }
        }

        const defaultCurrency =
            FIAT_CURRENCIES[token.rate.quote as FiatCurrencyCode]
        if (defaultCurrency) {
            return { rate: null, priceInDefaultCurrency: null }
        }
        const defaultCurrencyRate = {
            rate: token.rate.rate,
            base: cryptoCurrency,
            quote: defaultCurrency,
        }

        return {
            rate: defaultCurrencyRate,
            priceInDefaultCurrency: applyNullableRate({
                baseAmount: balance,
                rate: defaultCurrencyRate,
            }),
        }
    })()

    return {
        balance,
        rate: priceInDefaultCurrency.rate,
        scam: token.scam,
        marketData: token.marketData,
        priceInDefaultCurrency: priceInDefaultCurrency.priceInDefaultCurrency,
    }
}
