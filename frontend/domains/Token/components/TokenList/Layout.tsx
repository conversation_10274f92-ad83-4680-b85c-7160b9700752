import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Avatar } from '@zeal/uikit/Avatar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { EmptyStateWidget } from '@zeal/uikit/EmptyStateWidget'
import { GroupList } from '@zeal/uikit/GroupList'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Tokens } from '@zeal/uikit/Icon/Empty'
import { Plus } from '@zeal/uikit/Icon/Plus'
import { SpamFolder } from '@zeal/uikit/Icon/SpamFolder'
import { IconButton } from '@zeal/uikit/IconButton'
import {
    RefreshContainer,
    RefreshContainerState,
} from '@zeal/uikit/RefreshContainer'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import {
    FetchPortfolioRequest,
    FetchPortfolioResponse,
} from '@zeal/domains/Account/api/fetchAccounts'
import { ActionBarAccountSelector } from '@zeal/domains/Account/components/ActionBarAccountSelector'
import { CurrencyHiddenMap, CurrencyPinMap } from '@zeal/domains/Currency'
import { sortCurrencyByPinStatus } from '@zeal/domains/Currency/helpers/sortCurrencyByPinStatus'
import {
    CurrentNetwork,
    CustomNetwork,
    NetworkMap,
    TestNetwork,
} from '@zeal/domains/Network'
import { NetworkSelector } from '@zeal/domains/Network/components/NetworkSelector'
import { filterServerPortfolioByNetwork2 } from '@zeal/domains/Portfolio/helpers/filterPortfolioByNetwork'
import { Token2 } from '@zeal/domains/Token'
import { ListItem2 } from '@zeal/domains/Token/components/ListItem2'
import { filterByHideMap } from '@zeal/domains/Token/helpers/filterByHideMap'

type Props = {
    account: Account
    currentNetwork: CurrentNetwork
    networkMap: NetworkMap
    portfolioLoadable: LoadedReloadableData<
        FetchPortfolioResponse,
        FetchPortfolioRequest
    >
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    refreshContainerState: RefreshContainerState
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | MsgOf<typeof ActionBarAccountSelector>
    | { type: 'network_filter_click' }
    | { type: 'on_token_click'; token: Token2 }
    | {
          type: 'on_add_custom_currency_click'
          network: TestNetwork | CustomNetwork
      }
    | { type: 'on_show_hidden_token_click' }
    | { type: 'on_tokens_refresh_pulled' }

// TODO :: @Nicvaniek we should not be mixing ScrollView + FlatList here (it is rendering all flatList items). Rather just use flatList with its refresh control support
export const Layout = ({
    account,
    currentNetwork,
    portfolioLoadable,
    networkMap,
    currencyHiddenMap,
    currencyPinMap,
    refreshContainerState,
    onMsg,
}: Props) => {
    const portfolio = portfolioLoadable.data.portfolio

    const filteredPortfolio = filterServerPortfolioByNetwork2(
        portfolio,
        currentNetwork,
        networkMap
    )

    filteredPortfolio.tokens.sort((a, b) =>
        sortCurrencyByPinStatus(currencyPinMap)(
            a.balance.currency,
            b.balance.currency
        )
    )

    const filteredTokens = filteredPortfolio.tokens.filter(
        filterByHideMap(currencyHiddenMap)
    )

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <RefreshContainer
                state={refreshContainerState}
                padding="default"
                onRefreshPulled={() =>
                    onMsg({ type: 'on_tokens_refresh_pulled' })
                }
            >
                <ActionBar
                    top={
                        <ActionBarAccountSelector
                            account={account}
                            onMsg={onMsg}
                        />
                    }
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4} shrink>
                                <BackIcon size={24} color="iconDefault" />

                                <Text
                                    variant="title3"
                                    weight="medium"
                                    color="textPrimary"
                                >
                                    <FormattedMessage
                                        id="action_bar_title.tokens"
                                        defaultMessage="Tokens"
                                    />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                    right={
                        <Row spacing={12}>
                            {(() => {
                                switch (currentNetwork.type) {
                                    case 'all_networks':
                                        return (
                                            <IconButton
                                                variant="on_light"
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_show_hidden_token_click',
                                                    })
                                                }}
                                            >
                                                {({ color }) => (
                                                    <SpamFolder
                                                        size={24}
                                                        color={color}
                                                    />
                                                )}
                                            </IconButton>
                                        )
                                    case 'specific_network':
                                        const net = currentNetwork.network
                                        switch (net.type) {
                                            case 'predefined':
                                                return (
                                                    <IconButton
                                                        variant="on_light"
                                                        onClick={() => {
                                                            onMsg({
                                                                type: 'on_show_hidden_token_click',
                                                            })
                                                        }}
                                                    >
                                                        {({ color }) => (
                                                            <SpamFolder
                                                                size={24}
                                                                color={color}
                                                            />
                                                        )}
                                                    </IconButton>
                                                )
                                            case 'custom':
                                            case 'testnet':
                                                return (
                                                    <Tertiary
                                                        color="on_light"
                                                        size="regular"
                                                        onClick={() =>
                                                            onMsg({
                                                                type: 'on_add_custom_currency_click',
                                                                network: net,
                                                            })
                                                        }
                                                    >
                                                        {({ color }) => (
                                                            <Avatar size={24}>
                                                                <Plus
                                                                    size={24}
                                                                    color={
                                                                        color
                                                                    }
                                                                />
                                                            </Avatar>
                                                        )}
                                                    </Tertiary>
                                                )
                                            /* istanbul ignore next */
                                            default:
                                                return notReachable(net)
                                        }

                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(currentNetwork)
                                }
                            })()}

                            <NetworkSelector
                                variant="on_light"
                                size={24}
                                currentNetwork={currentNetwork}
                                onClick={() => {
                                    onMsg({ type: 'network_filter_click' })
                                }}
                            />
                        </Row>
                    }
                />

                <Column spacing={12} shrink>
                    <GroupList
                        variant="default"
                        data={filteredTokens}
                        renderItem={({ item: token }) => (
                            <ListItem2
                                variant="portfolio"
                                currencyHiddenMap={currencyHiddenMap}
                                currencyPinMap={currencyPinMap}
                                aria-current={false}
                                networkMap={networkMap}
                                onClick={() =>
                                    onMsg({
                                        type: 'on_token_click',
                                        token,
                                    })
                                }
                                key={`${token.balance.currency.networkHexChainId}-${token.balance.currency.id}`}
                                token={token}
                            />
                        )}
                        emptyState={
                            <EmptyState
                                currentNetwork={currentNetwork}
                                onAddCustomClick={(testNetwork) =>
                                    onMsg({
                                        type: 'on_add_custom_currency_click',
                                        network: testNetwork,
                                    })
                                }
                            />
                        }
                    />
                </Column>
            </RefreshContainer>
        </Screen>
    )
}

// TODO @resetko-zeal Move to separate component if it's too much copypaste with other token list empty states
const EmptyState = ({
    currentNetwork,
    onAddCustomClick,
}: {
    currentNetwork: CurrentNetwork
    onAddCustomClick: (network: TestNetwork | CustomNetwork) => void
}) => {
    switch (currentNetwork.type) {
        case 'all_networks':
            return (
                <EmptyStateWidget
                    size="regular"
                    icon={({ size }) => (
                        <Tokens size={size} color="backgroundLight" />
                    )}
                    title={
                        <FormattedMessage
                            id="token.widget.emptyState"
                            defaultMessage="No tokens in wallet"
                        />
                    }
                />
            )
        case 'specific_network': {
            const net = currentNetwork.network
            switch (net.type) {
                case 'predefined':
                    return (
                        <EmptyStateWidget
                            size="regular"
                            icon={({ size }) => (
                                <Tokens size={size} color="backgroundLight" />
                            )}
                            title={
                                <FormattedMessage
                                    id="token.widget.emptyState"
                                    defaultMessage="No tokens in wallet"
                                />
                            }
                        />
                    )
                case 'custom':
                case 'testnet':
                    return (
                        <EmptyStateWidget
                            onClick={() => onAddCustomClick(net)}
                            size="regular"
                            icon={({ size }) => (
                                <Tokens size={size} color="backgroundLight" />
                            )}
                            title={
                                <FormattedMessage
                                    id="token.widget.addTokens"
                                    defaultMessage="Add token"
                                />
                            }
                        />
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(net)
            }
        }
        /* istanbul ignore next */
        default:
            return notReachable(currentNetwork)
    }
}
