import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ShowAndScanQRCode } from '@zeal/domains/Account/features/ShowAndScanQRCode'
import { CurrencyHiddenMap, CurrencyPinMap } from '@zeal/domains/Currency'
import { AddCustom } from '@zeal/domains/Currency/features/AddCustom'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import {
    CurrentNetwork,
    CustomNetwork,
    NetworkMap,
    NetworkRPCMap,
    TestNetwork,
} from '@zeal/domains/Network'
import { NetworkFilter } from '@zeal/domains/Network/features/Fillter'
import { getAllNetworksFromNetworkMap } from '@zeal/domains/Network/helpers/getAllNetworksFromNetworkMap'
import { Portfolio2, PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { SendOrReceiveToken } from '@zeal/domains/RPCRequest/features/SendOrReceiveToken'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { HiddenTokens } from '@zeal/domains/Token/components/HiddenTokens'

import { Token2 } from '../..'

type Props = {
    state: State
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    portfolio: Portfolio2
    portfolioMap: PortfolioMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    account: Account
    keystoreMap: KeyStoreMap
    currentNetwork: CurrentNetwork
    defaultCurrencyConfig: DefaultCurrencyConfig
    isEthereumNetworkFeeWarningSeen: boolean
    customCurrencyMap: CustomCurrencyMap
    accountsMap: AccountsMap
    onMsg: (msg: Msg) => void
}

export type State =
    | { type: 'closed' }
    | { type: 'add_custom_currency'; network: TestNetwork | CustomNetwork }
    | { type: 'hidden_tokens' }
    | { type: 'network_filter' }
    | { type: 'send_or_receive_token'; token: Token2 }
    | { type: 'receive_funds' }

type Msg =
    | MsgOf<typeof AddCustom>
    | MsgOf<typeof HiddenTokens>
    | MsgOf<typeof NetworkFilter>
    | MsgOf<typeof SendOrReceiveToken>
    | MsgOf<typeof ShowAndScanQRCode>

export const Modal = ({
    state,
    currencyHiddenMap,
    currencyPinMap,
    portfolio,
    portfolioMap,
    networkMap,
    networkRPCMap,
    installationId,
    account,
    keystoreMap,
    accountsMap,
    isEthereumNetworkFeeWarningSeen,
    currentNetwork,
    customCurrencyMap,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'hidden_tokens':
            return (
                <UIModal>
                    <HiddenTokens
                        networkMap={networkMap}
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        portfolio={portfolio}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'add_custom_currency':
            return (
                <UIModal>
                    <AddCustom
                        cryptoCurrency={null}
                        onMsg={onMsg}
                        network={state.network}
                        networkRPCMap={networkRPCMap}
                    />
                </UIModal>
            )

        case 'network_filter':
            return (
                <UIModal>
                    <NetworkFilter
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        networkMap={networkMap}
                        currencyHiddenMap={currencyHiddenMap}
                        account={account}
                        keyStoreMap={keystoreMap}
                        networks={getAllNetworksFromNetworkMap(networkMap)}
                        networkRPCMap={networkRPCMap}
                        portfolio={unsafe_GetPortfolioCache2({
                            address: account.address,
                            portfolioMap,
                        })}
                        currentNetwork={currentNetwork}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'send_or_receive_token':
            return (
                <SendOrReceiveToken
                    installationId={installationId}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    customCurrencies={customCurrencyMap}
                    networkMap={networkMap}
                    fromAccount={account}
                    portfolio={portfolio}
                    networkRPCMap={networkRPCMap}
                    token={state.token}
                    onMsg={onMsg}
                />
            )

        case 'receive_funds':
            return (
                <UIModal>
                    <ShowAndScanQRCode
                        installationId={installationId}
                        account={account}
                        networkMap={networkMap}
                        keyStore={getKeyStore({
                            address: account.address,
                            keyStoreMap: keystoreMap,
                        })}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        accountMap={accountsMap}
                        initialState={{
                            type: 'show_qr_code',
                        }}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
