import { FormattedMessage } from 'react-intl'

import { Group, GroupHeader, Section } from '@zeal/uikit/Group'
import { BoldAddWallet } from '@zeal/uikit/Icon/BoldAddWallet'
import { ForwardIcon } from '@zeal/uikit/Icon/ForwardIcon'
import { ListItem } from '@zeal/uikit/ListItem'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { values } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ListItemWithDetails } from '@zeal/domains/Account/components/ListItemWithDetails'
import { sortByBalance } from '@zeal/domains/Account/helpers/sortByBalance'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    accountsMap: AccountsMap
    selectedAccount: Account
    portfolioMap: PortfolioMap
    keyStoreMap: KeyStoreMap
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'settings_add_new_account_click' }
    | { type: 'on_select_account_clicked' }
    | MsgOf<typeof ListItemWithDetails>

const ACCOUNTS_SHORT_LIST_COUNT = 3

type State =
    | { type: 'single_account'; selectedAccount: Account }
    | {
          type: 'accounts_short_list'
          selectedAccount: Account
          accountsShortList: Account[]
      }
    | {
          type: 'accounts_short_list_with_see_all'
          selectedAccount: Account
          accountsShortList: Account[]
      }

const calculateState = ({
    accountsMap,
    selectedAccount,
    currencyHiddenMap,
    defaultCurrencyConfig,
    portfolioMap,
    keyStoreMap,
}: {
    accountsMap: AccountsMap
    selectedAccount: Account
    portfolioMap: PortfolioMap
    keyStoreMap: KeyStoreMap
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}): State => {
    const accounts = values(accountsMap)

    if (accounts.length === 1) {
        return { type: 'single_account', selectedAccount }
    }

    const otherAccounts = accounts
        .filter((account) => account.address !== selectedAccount.address)
        .toSorted(
            sortByBalance(
                portfolioMap,
                currencyHiddenMap,
                defaultCurrencyConfig
            )
        )

    const accountsShortList = otherAccounts
        .filter((acc) => {
            const keyStore = getKeyStore({ address: acc.address, keyStoreMap })

            switch (keyStore.type) {
                case 'private_key_store':
                case 'ledger':
                case 'secret_phrase_key':
                case 'trezor':
                case 'safe_4337':
                    return true
                case 'track_only':
                    return false

                default:
                    return notReachable(keyStore)
            }
        })
        .slice(0, ACCOUNTS_SHORT_LIST_COUNT - 1)

    if (otherAccounts.length === accountsShortList.length) {
        return {
            type: 'accounts_short_list',
            selectedAccount,
            accountsShortList,
        }
    }
    return {
        type: 'accounts_short_list_with_see_all',
        selectedAccount,
        accountsShortList,
    }
}

export const AccountsSection = ({
    accountsMap,
    currencyHiddenMap,
    defaultCurrencyConfig,
    portfolioMap,
    installationId,
    selectedAccount,
    keyStoreMap,
    onMsg,
}: Props) => {
    const state = calculateState({
        accountsMap,
        currencyHiddenMap,
        defaultCurrencyConfig,
        portfolioMap,
        selectedAccount,
        keyStoreMap,
    })

    return (
        <Section>
            <Group variant="default">
                {(() => {
                    switch (state.type) {
                        case 'accounts_short_list':
                        case 'single_account':
                            return (
                                <GroupHeader
                                    left={({
                                        color,
                                        textVariant,
                                        textWeight,
                                    }) => (
                                        <Text
                                            color={color}
                                            variant={textVariant}
                                            weight={textWeight}
                                        >
                                            <FormattedMessage
                                                id="settings.accounts"
                                                defaultMessage="Accounts"
                                            />
                                        </Text>
                                    )}
                                    right={null}
                                />
                            )

                        case 'accounts_short_list_with_see_all':
                            return (
                                <GroupHeader
                                    left={({
                                        color,
                                        textVariant,
                                        textWeight,
                                    }) => (
                                        <Text
                                            color={color}
                                            variant={textVariant}
                                            weight={textWeight}
                                        >
                                            <FormattedMessage
                                                id="settings.accounts"
                                                defaultMessage="Accounts"
                                            />
                                        </Text>
                                    )}
                                    right={({
                                        color,
                                        textVariant,
                                        textWeight,
                                    }) => (
                                        <>
                                            <Text
                                                color={color}
                                                variant={textVariant}
                                                weight={textWeight}
                                            >
                                                <FormattedMessage
                                                    id="settings.accountsSeeAll"
                                                    defaultMessage="See all"
                                                />
                                            </Text>
                                            <ForwardIcon
                                                size={16}
                                                color={color}
                                            />
                                        </>
                                    )}
                                    onClick={() =>
                                        onMsg({
                                            type: 'on_select_account_clicked',
                                        })
                                    }
                                />
                            )

                        default:
                            return notReachable(state)
                    }
                })()}

                {(() => {
                    switch (state.type) {
                        case 'single_account':
                            return (
                                <ListItemWithDetails
                                    selected={false}
                                    keystore={getKeyStore({
                                        address: state.selectedAccount.address,
                                        keyStoreMap,
                                    })}
                                    portfolio={unsafe_GetPortfolioCache2({
                                        address: state.selectedAccount.address,
                                        portfolioMap,
                                    })}
                                    account={state.selectedAccount}
                                    currencyHiddenMap={currencyHiddenMap}
                                    defaultCurrencyConfig={
                                        defaultCurrencyConfig
                                    }
                                    installationId={installationId}
                                    copyAddressEventLocation="settings"
                                    onMsg={onMsg}
                                />
                            )

                        case 'accounts_short_list_with_see_all':
                        case 'accounts_short_list':
                            return (
                                <>
                                    <ListItemWithDetails
                                        selected
                                        keystore={getKeyStore({
                                            address:
                                                state.selectedAccount.address,
                                            keyStoreMap,
                                        })}
                                        portfolio={unsafe_GetPortfolioCache2({
                                            address:
                                                state.selectedAccount.address,
                                            portfolioMap,
                                        })}
                                        account={state.selectedAccount}
                                        currencyHiddenMap={currencyHiddenMap}
                                        defaultCurrencyConfig={
                                            defaultCurrencyConfig
                                        }
                                        installationId={installationId}
                                        copyAddressEventLocation="settings"
                                        onMsg={onMsg}
                                    />

                                    {state.accountsShortList.map((account) => (
                                        <ListItemWithDetails
                                            key={account.address}
                                            selected={false}
                                            keystore={getKeyStore({
                                                address: account.address,
                                                keyStoreMap,
                                            })}
                                            portfolio={unsafe_GetPortfolioCache2(
                                                {
                                                    address: account.address,
                                                    portfolioMap,
                                                }
                                            )}
                                            account={account}
                                            currencyHiddenMap={
                                                currencyHiddenMap
                                            }
                                            defaultCurrencyConfig={
                                                defaultCurrencyConfig
                                            }
                                            installationId={installationId}
                                            copyAddressEventLocation="settings"
                                            onMsg={onMsg}
                                        />
                                    ))}
                                </>
                            )

                        default:
                            return notReachable(state)
                    }
                })()}

                <AddNewAccountItem onMsg={onMsg} />
            </Group>
        </Section>
    )
}

const AddNewAccountItem = ({ onMsg }: { onMsg: (msg: Msg) => void }) => (
    <ListItem
        size="regular"
        aria-current={false}
        avatar={({ size }) => <BoldAddWallet size={size} color="textAccent2" />}
        primaryText={
            <FormattedMessage
                id="settings.addAccount"
                defaultMessage="Add wallet"
            />
        }
        onClick={() =>
            onMsg({
                type: 'settings_add_new_account_click',
            })
        }
    />
)
