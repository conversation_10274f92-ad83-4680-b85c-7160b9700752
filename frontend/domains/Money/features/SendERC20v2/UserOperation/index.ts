import { Address } from 'web3'

import { notReachable } from '@zeal/toolkit'
import { max } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hex from '@zeal/toolkit/Hexadecimal'
import { fromBigInt } from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import * as Web3 from '@zeal/toolkit/Web3'

import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import {
    CryptoCurrency,
    DefaultCurrency,
    GasCurrencyPresetMap,
    KnownCryptoCurrencies,
} from '@zeal/domains/Currency'
import {
    PAYMASTER_ADDRESS,
    PAYMASTER_MAP,
} from '@zeal/domains/Currency/constants'
import { FXRate2 } from '@zeal/domains/FXRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { SafeInstance } from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'
import {
    BundlerGasEstimate,
    BundlerGasPrice,
    ERC20GasAbstractionTransactionFee,
    GasAbstractionTransactionFeeResponse2,
} from '@zeal/domains/UserOperation'
import {
    calculateIfApprovalIsNeeded,
    checkIfApprovalIsRequired,
} from '@zeal/domains/UserOperation/api/fetchGasAbstractionTransactionFees'
import {
    fetchBiconomySponsorshipPaymasterAndData,
    parsePaymasterAndData,
} from '@zeal/domains/UserOperation/api/fetchPaymasterAndData'
import { fetchPaymasterResponse } from '@zeal/domains/UserOperation/api/fetchPaymasterResponse'
import { fetchUserOperationHash } from '@zeal/domains/UserOperation/api/fetchUserOperationHash'
import {
    APPROVAL_CALL_GAS_LIMIT_BUFFER,
    DUMMY_EOA_SIGNATURE,
    DUMMY_PASSKEY_SIGNATURE,
    EOA_SIGNATURE_VERIFICATION_GAS_LIMIT_BUFFER,
    passkeySignerGasBufferConfig,
    TOKEN_PAYMASTER_VERIFICATION_GAS_LIMIT_BUFFER,
} from '@zeal/domains/UserOperation/constants'
import {
    MAX_FEE_PER_GAS_BUFFER_MULTIPLIER,
    MINIMUM_CALL_GAS_LIMIT,
    PRECISION,
} from '@zeal/domains/UserOperation/helpers/calculateGasEstimates'
import { createAddOwnerMetaTransaction } from '@zeal/domains/UserOperation/helpers/createAddOwnerMetaTransaction'
import { ethSendTransactionToMetaTransactionData } from '@zeal/domains/UserOperation/helpers/ethSendTransactionToMetaTransactionData'
import { metaTransactionDatasToUserOperationCallData } from '@zeal/domains/UserOperation/helpers/metaTransactionDatasToUserOperationCallData'
import { signUserOperationHashWithLocalSigner } from '@zeal/domains/UserOperation/helpers/signUserOperationHashWithLocalSigner'
import { signUserOperationHashWithPassKey } from '@zeal/domains/UserOperation/helpers/signUserOperationHashWithPassKey'

import { SEND_SPONSORED_ON_NETWORKS } from './constants'

import { Form, SafeTransactionRequest } from '../fetch'

export type InitialUserOperation = {
    sender: Web3.address.Address
    nonce: Hex.Hexadecimal
    initCode: Hex.Hexadecimal
    callData: Hex.Hexadecimal
    signature: Hex.Hexadecimal
    paymasterAndData: Hex.Hexadecimal
}

export type FeeAndGasEstimates = {
    gasEstimate: BundlerGasEstimate
    gasPrice: BundlerGasPrice
}

const ensureMinGasAndPrice = (
    fee: FeeAndGasEstimates,
    safeInstance: SafeInstance,
    keystore: Safe4337
): FeeAndGasEstimates => {
    const signatureGasLimitBuffer = (() => {
        switch (safeInstance.type) {
            case 'deployed':
                return safeInstance.owners.includes(
                    keystore.localSignerKeyStore.address
                )
                    ? EOA_SIGNATURE_VERIFICATION_GAS_LIMIT_BUFFER
                    : passkeySignerGasBufferConfig[
                          keystore.safeDeplymentConfig.passkeyOwner
                              .signerVersion
                      ].verificationGasLimitBuffer

            case 'not_deployed':
                return passkeySignerGasBufferConfig[
                    keystore.safeDeplymentConfig.passkeyOwner.signerVersion
                ].verificationGasLimitBuffer
            default:
                return notReachable(safeInstance)
        }
    })()

    return {
        gasEstimate: {
            ...fee.gasEstimate,
            callGasLimit: max(
                fee.gasEstimate.callGasLimit,
                MINIMUM_CALL_GAS_LIMIT
            ),
            verificationGasLimit:
                fee.gasEstimate.verificationGasLimit + signatureGasLimitBuffer,
        },
        gasPrice: {
            maxFeePerGas:
                (fee.gasPrice.maxFeePerGas *
                    BigInt(MAX_FEE_PER_GAS_BUFFER_MULTIPLIER * PRECISION)) /
                BigInt(PRECISION),
            maxPriorityFeePerGas: fee.gasPrice.maxPriorityFeePerGas,
        },
    }
}

const addPaymasterBuffer = (fee: FeeAndGasEstimates): FeeAndGasEstimates => {
    const { gasEstimate } = fee
    return {
        ...fee,
        gasEstimate: {
            ...gasEstimate,
            verificationGasLimit:
                gasEstimate.verificationGasLimit +
                TOKEN_PAYMASTER_VERIFICATION_GAS_LIMIT_BUFFER,
        },
    }
}

const addAprovalBuffer = (fee: FeeAndGasEstimates): FeeAndGasEstimates => {
    const { gasEstimate } = fee
    return {
        ...fee,
        gasEstimate: {
            ...gasEstimate,
            callGasLimit:
                gasEstimate.callGasLimit + APPROVAL_CALL_GAS_LIMIT_BUFFER,
        },
    }
}

export const createInitialUserOperation = ({
    form,
    safeInstance,
    keystore,
    network,
    nonce,
}: {
    safeInstance: SafeInstance
    form: Form
    nonce: Hex.Hexadecimal
    network: Network
    keystore: Safe4337
}): InitialUserOperation => {
    const cryptoCurrency = form.cryptoCurrency
    // this one is for estimation only, we do 1n amount to avoid 0 transaction that is lower on gas as it doesnt write to storage
    const inputAmount = 1n // fromFixedWithFraction(
    // '',
    // cryptoCurrency.fraction
    // )
    const ethSendTransaction = createTransferEthSendTransaction({
        network,
        amount: { amount: inputAmount, currency: cryptoCurrency },
        from: form.fromWallet.address,
        to: form.toAddress,
    })
    const sender = form.fromWallet.address as Web3.address.Address
    const ethSendMetaTransaction =
        ethSendTransactionToMetaTransactionData(ethSendTransaction)
    const metaTransactions = (() => {
        const addOwner = createAddOwnerMetaTransaction({
            safeAddress: safeInstance.safeAddress,
            owner: keystore.localSignerKeyStore.address,
            treshold: 1n,
        })
        switch (safeInstance.type) {
            case 'deployed':
                return safeInstance.owners.includes(
                    keystore.localSignerKeyStore.address
                )
                    ? []
                    : [addOwner]
            case 'not_deployed':
                return [addOwner]
            default:
                return notReachable(safeInstance)
        }
    })()
    const callData = metaTransactionDatasToUserOperationCallData({
        metaTransactionDatas: [...metaTransactions, ethSendMetaTransaction],
    }) as Hex.Hexadecimal

    const initCode = (() => {
        switch (safeInstance.type) {
            case 'not_deployed':
                return safeInstance.deploymentInitCode
            case 'deployed':
                return '0x'
            default:
                return notReachable(safeInstance)
        }
    })()
    const signature = (() => {
        switch (safeInstance.type) {
            case 'deployed':
                return safeInstance.owners.includes(
                    keystore.localSignerKeyStore.address
                )
                    ? DUMMY_EOA_SIGNATURE
                    : DUMMY_PASSKEY_SIGNATURE

            case 'not_deployed':
                return DUMMY_PASSKEY_SIGNATURE
            default:
                return notReachable(safeInstance)
        }
    })()

    const estimateInitialGasUserOperation: InitialUserOperation = {
        initCode,
        callData,
        signature,
        nonce,
        sender,
        paymasterAndData: '0x',
    }

    return estimateInitialGasUserOperation
}

const calculateAmount = (
    fee: FeeAndGasEstimates,
    currency: CryptoCurrency
): CryptoMoney => {
    return {
        amount:
            (fee.gasEstimate.callGasLimit +
                fee.gasEstimate.verificationGasLimit +
                fee.gasEstimate.preVerificationGas) *
            fee.gasPrice.maxFeePerGas,
        currency,
    }
}

export type UserOpeationFee = GasAbstractionTransactionFeeResponse2

export const fetchUserOperationFee = ({
    estimate,
    keystore,
    network,
    nativeCurrency,
    gasCurrencyPresetMap,
    safeInstance,
    portfolio,
    form,
    nativeToDefaultRate,
    currencies,
    approvals,
    crossRates,
}: {
    nativeToDefaultRate: FXRate2<CryptoCurrency, DefaultCurrency> | null
    estimate: FeeAndGasEstimates
    form: Form
    nativeCurrency: CryptoCurrency
    network: Network
    keystore: Safe4337
    gasCurrencyPresetMap: GasCurrencyPresetMap
    portfolio: ServerPortfolio2
    safeInstance: SafeInstance
    approvals: CryptoMoney[]
    crossRates: FXRate2<CryptoCurrency, CryptoCurrency>[]
    currencies: KnownCryptoCurrencies
}): {
    selectedFee: UserOpeationFee
    feeInDefaultCurrency: FiatMoney | null
    erc20FeeInDefaultCurrency: FiatMoney | null
    feeInNativeTokenCurrency: CryptoMoney
    maxBalance: CryptoMoney
} => {
    const normalizedEstimate = ensureMinGasAndPrice(
        estimate,
        safeInstance,
        keystore
    )

    const feeInNativeTokenCurrency = calculateAmount(
        normalizedEstimate,
        nativeCurrency
    )

    const feeInDefaultCurrency = nativeToDefaultRate
        ? applyRate2({
              baseAmount: feeInNativeTokenCurrency,
              rate: nativeToDefaultRate,
          })
        : null
    const paymasterFee = addPaymasterBuffer(normalizedEstimate)
    const paymasterWithApproval = addAprovalBuffer(paymasterFee)
    const erc20FeeInNativeCurrency = calculateAmount(
        paymasterWithApproval,
        nativeCurrency
    )
    const erc20FeeInDefaultCurrency = nativeToDefaultRate
        ? applyRate2({
              baseAmount: erc20FeeInNativeCurrency,
              rate: nativeToDefaultRate,
          })
        : null

    const selectedFee = calculateGasAbstractionTransactionFeeResponse2({
        normalizedEstimate,
        network,
        gasCurrencyPresetMap,
        portfolio,
        feeInDefaultCurrency,
        feeInNativeTokenCurrency,
        currencies,
        nativeToDefaultRate,
        crossRates,
        approvals,
    })
    const selectedCryptoCurrency = form.cryptoCurrency

    const selectedTokenOnForm = portfolio.tokens.find(
        (token) => token.balance.currency.id === selectedCryptoCurrency.id
    )
    if (!selectedTokenOnForm) {
        // impossible state?
        return {
            selectedFee,
            maxBalance: {
                amount: 0n,
                currency: selectedCryptoCurrency,
            },
            erc20FeeInDefaultCurrency,
            feeInDefaultCurrency,
            feeInNativeTokenCurrency,
        }
    }

    switch (selectedFee.type) {
        case 'sponsored_gas_abstraction_transaction_fee':
            return {
                selectedFee,
                maxBalance: {
                    amount: selectedTokenOnForm.balance.amount,
                    currency: selectedCryptoCurrency,
                },
                erc20FeeInDefaultCurrency,
                feeInDefaultCurrency,
                feeInNativeTokenCurrency,
            }
        case 'erc20_gas_abstraction_transaction_fee':
        case 'native_gas_abstraction_transaction_fee':
            if (
                selectedTokenOnForm.balance.currency.id ===
                selectedFee.feeInTokenCurrency.currency.id
            ) {
                return {
                    selectedFee,
                    maxBalance:
                        selectedTokenOnForm.balance.amount >
                        selectedFee.feeInTokenCurrency.amount
                            ? {
                                  amount:
                                      selectedTokenOnForm.balance.amount -
                                      selectedFee.feeInTokenCurrency.amount,
                                  currency: selectedCryptoCurrency,
                              }
                            : {
                                  amount: 0n,
                                  currency: selectedCryptoCurrency,
                              },
                    erc20FeeInDefaultCurrency,
                    feeInDefaultCurrency,
                    feeInNativeTokenCurrency,
                }
            }
            return {
                selectedFee,
                maxBalance: {
                    amount: selectedTokenOnForm.balance.amount,
                    currency: selectedCryptoCurrency,
                },
                erc20FeeInDefaultCurrency,
                feeInDefaultCurrency,
                feeInNativeTokenCurrency,
            }

        default:
            return notReachable(selectedFee)
    }
}

const calculateGasAbstractionTransactionFeeResponse2 = ({
    normalizedEstimate,
    gasCurrencyPresetMap,
    portfolio,
    network,
    feeInDefaultCurrency,
    feeInNativeTokenCurrency,
    currencies,
    nativeToDefaultRate,
    approvals,
    crossRates,
}: {
    normalizedEstimate: FeeAndGasEstimates
    gasCurrencyPresetMap: GasCurrencyPresetMap
    network: Network
    portfolio: ServerPortfolio2
    feeInDefaultCurrency: FiatMoney | null
    feeInNativeTokenCurrency: CryptoMoney
    currencies: KnownCryptoCurrencies
    approvals: CryptoMoney[]
    crossRates: FXRate2<CryptoCurrency, CryptoCurrency>[]

    nativeToDefaultRate: FXRate2<CryptoCurrency, DefaultCurrency> | null
}): GasAbstractionTransactionFeeResponse2 => {
    if (SEND_SPONSORED_ON_NETWORKS.find((id) => id === network.hexChainId)) {
        return {
            type: 'sponsored_gas_abstraction_transaction_fee',
            gasEstimate: normalizedEstimate.gasEstimate,
            gasPrice: normalizedEstimate.gasPrice,
            callData: '', // in vanity flow callData in fee not used
        }
    }

    const currencyId = gasCurrencyPresetMap[network.hexChainId]

    const supportedCurrencies: CryptoCurrency[] = PAYMASTER_MAP[
        network.hexChainId
    ].map((id) => currencies[id])

    if (currencyId) {
        const selectedCurrency = supportedCurrencies.find(
            ({ id }) => id === currencyId
        )

        if (selectedCurrency) {
            if (selectedCurrency.id === feeInNativeTokenCurrency.currency.id) {
                return {
                    type: 'native_gas_abstraction_transaction_fee',
                    feeInTokenCurrency: feeInNativeTokenCurrency,
                    feeInDefaultCurrency: feeInDefaultCurrency,
                    gasEstimate: normalizedEstimate.gasEstimate,
                    gasPrice: normalizedEstimate.gasPrice,
                    callData: '', // in vanity flow callData in fee not used
                }
            } else {
                return calculateERC20GasAbstractionTransactionFee({
                    network,
                    feeSelectedTokenAddress: selectedCurrency.address,
                    normalizedEstimate,
                    nativeToDefaultRate,
                    crossRates,
                    approvals,
                })
            }
        }
    }

    const filteredPortfolioTokens = portfolio.tokens
        .filter((token) =>
            supportedCurrencies.find(
                ({ id }) => token.balance.currency.id === id
            )
        )
        .toSorted((a, b) => {
            const defaultCurrencyA = a.priceInDefaultCurrency?.amount || 0n
            const defaultCurrencyB = b.priceInDefaultCurrency?.amount || 0n

            if (defaultCurrencyA > defaultCurrencyB) return -1
            if (defaultCurrencyA < defaultCurrencyB) return 1
            return 0
        })
    if (!filteredPortfolioTokens.length) {
        return {
            type: 'native_gas_abstraction_transaction_fee',
            feeInTokenCurrency: feeInNativeTokenCurrency,
            feeInDefaultCurrency: feeInDefaultCurrency,
            gasEstimate: normalizedEstimate.gasEstimate,
            gasPrice: normalizedEstimate.gasPrice,
            callData: '', // in vanity flow callData in fee not used
        }
    }

    const nativeToken =
        filteredPortfolioTokens.find(
            (token) =>
                token.balance.currency.id ===
                    feeInNativeTokenCurrency.currency.id &&
                token.balance.amount > feeInNativeTokenCurrency.amount
        ) ||
        (filteredPortfolioTokens[0].balance.currency.id ===
        feeInNativeTokenCurrency.currency.id
            ? filteredPortfolioTokens[0]
            : null)

    if (nativeToken) {
        return {
            type: 'native_gas_abstraction_transaction_fee',
            feeInTokenCurrency: feeInNativeTokenCurrency,
            feeInDefaultCurrency: feeInDefaultCurrency,
            gasEstimate: normalizedEstimate.gasEstimate,
            gasPrice: normalizedEstimate.gasPrice,
            callData: '', // in vanity flow callData in fee not used
        }
    }
    return calculateERC20GasAbstractionTransactionFee({
        network,
        feeSelectedTokenAddress:
            filteredPortfolioTokens[0].balance.currency.address,
        normalizedEstimate,
        nativeToDefaultRate,
        crossRates,
        approvals,
    })
}

const calculateERC20GasAbstractionTransactionFee = ({
    feeSelectedTokenAddress,
    network,
    normalizedEstimate,
    nativeToDefaultRate,
    crossRates,
    approvals,
}: {
    approvals: CryptoMoney[]
    normalizedEstimate: FeeAndGasEstimates
    crossRates: FXRate2<CryptoCurrency, CryptoCurrency>[]
    network: Network
    nativeToDefaultRate: FXRate2<CryptoCurrency, DefaultCurrency> | null
    feeSelectedTokenAddress: Address
}): ERC20GasAbstractionTransactionFee => {
    const crossRate = crossRates.find(
        (crossRate) => crossRate.quote.address === feeSelectedTokenAddress
    )

    const paymasterFee = addPaymasterBuffer(normalizedEstimate)
    const erc20FeeInNativeCurrency = calculateAmount(
        paymasterFee,
        network.nativeCurrency
    )

    if (!crossRate) {
        throw new ImperativeError(
            "we don't get cross rate for fee selected token",
            { token: feeSelectedTokenAddress }
        )
    }

    const feeInTokenCurrency = applyRate2({
        baseAmount: erc20FeeInNativeCurrency,
        rate: crossRate,
    })

    const allowance = approvals.find(
        (approval) => approval.currency.address === feeSelectedTokenAddress
    )
    if (!allowance) {
        throw new ImperativeError(
            "we don't get allowance for fee selected token",
            { token: feeSelectedTokenAddress }
        )
    }
    const aproval = calculateIfApprovalIsNeeded({
        feeAmount: feeInTokenCurrency,
        allowance,
    })

    switch (aproval.type) {
        case 'approval_not_required': {
            const erc20FeeInDefaultCurrency = nativeToDefaultRate
                ? applyRate2({
                      baseAmount: erc20FeeInNativeCurrency,
                      rate: nativeToDefaultRate,
                  })
                : null

            return {
                type: 'erc20_gas_abstraction_transaction_fee',
                feeInTokenCurrency,
                feeInDefaultCurrency: erc20FeeInDefaultCurrency,
                gasEstimate: normalizedEstimate.gasEstimate,
                gasPrice: normalizedEstimate.gasPrice,
                callData: '', // in vanity flow callData in fee not used
            }
        }
        case 'approval_required': {
            const paymasterWithApproval = addAprovalBuffer(paymasterFee)
            const erc20FeeInNativeCurrency = calculateAmount(
                paymasterWithApproval,
                network.nativeCurrency
            )
            const erc20FeeInDefaultCurrency = nativeToDefaultRate
                ? applyRate2({
                      baseAmount: erc20FeeInNativeCurrency,
                      rate: nativeToDefaultRate,
                  })
                : null

            return {
                type: 'erc20_gas_abstraction_transaction_fee',
                feeInTokenCurrency,
                feeInDefaultCurrency: erc20FeeInDefaultCurrency,
                gasEstimate: paymasterWithApproval.gasEstimate,
                gasPrice: paymasterWithApproval.gasPrice,
                callData: '', // in vanity flow callData in fee not used
            }
        }
        default:
            return notReachable(aproval)
    }
}

export type UserOperation = {
    type: 'user_operation_with_signature'
    sender: Web3.address.Address
    callData: Hex.Hexadecimal
    nonce: Hex.Hexadecimal
    entrypoint: Web3.address.Address

    signature: Hex.Hexadecimal

    initCode: Hex.Hexadecimal

    maxFeePerGas: bigint
    maxPriorityFeePerGas: bigint

    callGasLimit: Hex.Hexadecimal
    verificationGasLimit: bigint
    preVerificationGas: bigint

    paymasterAndData: Hex.Hexadecimal
}

export const sign = async ({
    userOperationRequest,
    sessionPassword,
    networkRPCMap,
    networkMap,
    signal,
}: {
    userOperationRequest: SafeTransactionRequest
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<UserOperation> => {
    const { network, form, keyStore, safeInstance } = userOperationRequest
    const fee = form.fees
    const ethSendTransaction = createTransferEthSendTransaction({
        network,
        amount: form.amount,
        from: form.fromWallet.address,
        to: form.toAddress,
    })

    const ethSendMetaTransaction =
        ethSendTransactionToMetaTransactionData(ethSendTransaction)

    const metaTransactions = (() => {
        const addOwner = createAddOwnerMetaTransaction({
            safeAddress: safeInstance.safeAddress,
            owner: keyStore.localSignerKeyStore.address,
            treshold: 1n,
        })

        switch (safeInstance.type) {
            case 'deployed':
                return safeInstance.owners.includes(
                    keyStore.localSignerKeyStore.address
                )
                    ? []
                    : [addOwner]
            case 'not_deployed':
                return [addOwner]
            default:
                return notReachable(safeInstance)
        }
    })()

    const metaAllowanceTransactions = await (async () => {
        switch (form.fees.type) {
            case 'erc20_gas_abstraction_transaction_fee':
                const approvalCheckResult = await checkIfApprovalIsRequired({
                    feeAmount: form.fees.feeInTokenCurrency,
                    address: form.fromWallet.address,
                    spenderAddress: PAYMASTER_ADDRESS,
                    networkMap,
                    networkRPCMap,
                    signal,
                })

                switch (approvalCheckResult.type) {
                    case 'approval_not_required':
                        return []
                    case 'approval_required':
                        return [approvalCheckResult.approvalMetaTransactionData]
                    default:
                        return notReachable(approvalCheckResult)
                }
            case 'native_gas_abstraction_transaction_fee':
            case 'sponsored_gas_abstraction_transaction_fee':
                return []
            default:
                return notReachable(form.fees)
        }
    })()

    const callData = metaTransactionDatasToUserOperationCallData({
        metaTransactionDatas: [
            ...metaTransactions,
            ethSendMetaTransaction,
            ...metaAllowanceTransactions,
        ],
    }) as Hex.Hexadecimal

    const initCode = (() => {
        switch (safeInstance.type) {
            case 'not_deployed':
                return safeInstance.deploymentInitCode
            case 'deployed':
                return '0x'
            default:
                return notReachable(safeInstance)
        }
    })()
    const signature = (() => {
        switch (safeInstance.type) {
            case 'deployed':
                return safeInstance.owners.includes(
                    keyStore.localSignerKeyStore.address
                )
                    ? DUMMY_EOA_SIGNATURE
                    : DUMMY_PASSKEY_SIGNATURE

            case 'not_deployed':
                return DUMMY_PASSKEY_SIGNATURE
            default:
                return notReachable(safeInstance)
        }
    })()

    const initialUserOperation: InitialUserOperation = {
        sender: form.fromWallet.address as Web3.address.Address,
        callData,
        nonce: userOperationRequest.nonce,
        initCode,
        signature,
        paymasterAndData: '0x',
    }

    const paymasterAndData = await getPaymasterAndData({
        signal,
        userOperationRequest,
        initialUserOperation,
    })

    const userOperationHash = await fetchUserOperationHash({
        networkRPCMap,
        network,
        userOperation: {
            type: 'user_operation_without_signature',
            sender: initialUserOperation.sender,
            callData: initialUserOperation.callData,
            nonce: Hex.toBigInt(initialUserOperation.nonce),
            entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
            initCode: initialUserOperation.initCode,
            paymasterAndData,
            maxFeePerGas: form.fees.gasPrice.maxFeePerGas,
            maxPriorityFeePerGas: form.fees.gasPrice.maxPriorityFeePerGas,
            callGasLimit: form.fees.gasEstimate.callGasLimit,
            verificationGasLimit: form.fees.gasEstimate.verificationGasLimit,
            preVerificationGas: form.fees.gasEstimate.preVerificationGas,
        },
    })

    switch (safeInstance.type) {
        case 'deployed':
            return safeInstance.owners.includes(
                keyStore.localSignerKeyStore.address
            )
                ? {
                      type: 'user_operation_with_signature',
                      initCode: initialUserOperation.initCode,
                      callData: initialUserOperation.callData,
                      sender: initialUserOperation.sender,
                      nonce: initialUserOperation.nonce,

                      entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,

                      callGasLimit: Hex.fromBigInt(
                          fee.gasEstimate.callGasLimit
                      ),
                      maxFeePerGas: fee.gasPrice.maxFeePerGas,
                      maxPriorityFeePerGas: fee.gasPrice.maxPriorityFeePerGas,
                      preVerificationGas: fee.gasEstimate.preVerificationGas,
                      verificationGasLimit:
                          fee.gasEstimate.verificationGasLimit,

                      signature: (await signUserOperationHashWithLocalSigner({
                          keyStore,
                          network,
                          sessionPassword,
                          userOperationHash,
                          dApp: null, // only used for gnosis pay hack
                      })) as Hex.Hexadecimal,
                      paymasterAndData,
                  }
                : {
                      type: 'user_operation_with_signature',
                      initCode: initialUserOperation.initCode,
                      callData: initialUserOperation.callData,
                      sender: initialUserOperation.sender,
                      nonce: initialUserOperation.nonce,

                      entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,

                      callGasLimit: Hex.fromBigInt(
                          fee.gasEstimate.callGasLimit
                      ),
                      maxFeePerGas: fee.gasPrice.maxFeePerGas,
                      maxPriorityFeePerGas: fee.gasPrice.maxPriorityFeePerGas,
                      preVerificationGas: fee.gasEstimate.preVerificationGas,
                      verificationGasLimit:
                          fee.gasEstimate.verificationGasLimit,

                      signature: (await signUserOperationHashWithPassKey({
                          passkey: keyStore.safeDeplymentConfig.passkeyOwner,
                          userOperationHash,
                          sessionPassword,
                      })) as Hex.Hexadecimal,
                      paymasterAndData,
                  }

        case 'not_deployed':
            return {
                type: 'user_operation_with_signature',
                initCode: initialUserOperation.initCode,
                callData: initialUserOperation.callData,
                sender: initialUserOperation.sender,
                nonce: initialUserOperation.nonce,

                entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,

                callGasLimit: Hex.fromBigInt(fee.gasEstimate.callGasLimit),
                maxFeePerGas: fee.gasPrice.maxFeePerGas,
                maxPriorityFeePerGas: fee.gasPrice.maxPriorityFeePerGas,
                preVerificationGas: fee.gasEstimate.preVerificationGas,
                verificationGasLimit: fee.gasEstimate.verificationGasLimit,

                signature: (await signUserOperationHashWithPassKey({
                    passkey: keyStore.safeDeplymentConfig.passkeyOwner,
                    userOperationHash,
                    sessionPassword,
                })) as Hex.Hexadecimal,
                paymasterAndData,
            }
        default:
            return notReachable(safeInstance)
    }
}

const fetchERC20PaymasterAndDataV2 = async ({
    initialUserOperation,
    network,
    nonce,
    signal,
    fee,
}: {
    fee: ERC20GasAbstractionTransactionFee
    network: Network
    nonce: Hex.Hexadecimal
    initialUserOperation: InitialUserOperation
    signal?: AbortSignal
}): Promise<string> => {
    switch (network.type) {
        // TODO :: @Nicvaniek create narrowed network type for chains that support gas abstraction
        case 'predefined':
        case 'testnet':
            return fetchPaymasterResponse({
                network,
                signal,
                request: {
                    id: generateRandomNumber(),
                    jsonrpc: '2.0',
                    method: 'pm_sponsorUserOperation',
                    params: [
                        {
                            sender: initialUserOperation.sender,
                            initCode: initialUserOperation.initCode || '0x',
                            callData: initialUserOperation.callData,
                            nonce,
                            maxFeePerGas: fromBigInt(fee.gasPrice.maxFeePerGas),
                            maxPriorityFeePerGas: fromBigInt(
                                fee.gasPrice.maxPriorityFeePerGas
                            ),
                            callGasLimit:
                                fee.gasEstimate.callGasLimit.toString(),
                            verificationGasLimit:
                                fee.gasEstimate.verificationGasLimit.toString(),
                            preVerificationGas:
                                fee.gasEstimate.preVerificationGas.toString(),
                        },
                        {
                            mode: 'ERC20',
                            calculateGasLimits: false,
                            tokenInfo: {
                                feeTokenAddress:
                                    fee.feeInTokenCurrency.currency.address,
                            },
                        },
                    ],
                },
            }).then((response) =>
                parsePaymasterAndData(response).getSuccessResultOrThrow(
                    'Failed to parse ERC20 paymaster and data response'
                )
            )

        case 'custom':
            throw new ImperativeError('Custom network not supported')
        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}

const getPaymasterAndData = async ({
    signal,
    userOperationRequest,
    initialUserOperation,
}: {
    userOperationRequest: SafeTransactionRequest
    initialUserOperation: InitialUserOperation
    signal?: AbortSignal
}): Promise<Hex.Hexadecimal> => {
    switch (userOperationRequest.form.fees.type) {
        case 'sponsored_gas_abstraction_transaction_fee':
            return (await fetchBiconomySponsorshipPaymasterAndData({
                network: userOperationRequest.network,
                initialUserOperation: {
                    ...initialUserOperation,
                    nonce: Hex.toBigInt(initialUserOperation.nonce),
                    type: 'initial_user_operation',
                    entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
                },
                bundlerGasPrice: userOperationRequest.form.fees.gasPrice,
                gasEstimate: userOperationRequest.form.fees.gasEstimate,
                signal,
            })) as Hex.Hexadecimal

        case 'erc20_gas_abstraction_transaction_fee':
            return (await fetchERC20PaymasterAndDataV2({
                signal,
                initialUserOperation,
                network: userOperationRequest.network,
                nonce: userOperationRequest.nonce,
                fee: userOperationRequest.form.fees,
            })) as Hex.Hexadecimal
        case 'native_gas_abstraction_transaction_fee':
            return '0x' as Hex.Hexadecimal
        default:
            return userOperationRequest.form.fees
    }
}
