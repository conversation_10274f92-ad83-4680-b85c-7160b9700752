import { notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { NetworkMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Data, Pollable, TransactionRequest } from '../../../fetch'
import {
    NotEnoughBalance,
    NotEnoughGasToken,
    PollableErrored,
    PollableLoading,
    validateAmountBalance,
    validatePollableLoading,
    validateZeroAmount,
    ZeroAmount,
} from '../../validate'

export type ConfirmFormErrors = {
    maxButton?: NotEnoughBalance
    banner?: NotEnoughGasToken | PollableLoading | PollableErrored
    submit?: SubmitErrors
}

export type SubmitErrors =
    | NotEnoughBalance
    | NotEnoughGasToken
    | PollableErrored
    | PollableLoading
    | ZeroAmount

export const validate = (
    pollable: Pollable,
    networkMap: NetworkMap,
    defaultCurrencyConfig: DefaultCurrencyConfig
): Result<ConfirmFormErrors, TransactionRequest> =>
    shape({
        maxButton: validateAmountBalance({ pollable }),
        banner: validatePollableLoading({ pollable }).andThen((data) =>
            validateGas(data)
        ),
        submit: validatePollableLoading({ pollable }).andThen((data) =>
            validateGas(data).andThen(() =>
                validateAmountBalance({ pollable }).andThen((cryptoAmount) =>
                    validateZeroAmount(cryptoAmount).map(() => ({
                        cryptoAmount,
                        data,
                    }))
                )
            )
        ),
    }).map(({ submit: { cryptoAmount, data } }): TransactionRequest => {
        const network = findNetworkByHexChainId(
            cryptoAmount.currency.networkHexChainId,
            networkMap
        )

        const token = data.serverPortfolio.tokens.find(
            (token) => token.balance.currency.id === cryptoAmount.currency.id
        )
        const amountInDefaultCurrency =
            (token &&
                token.rate &&
                applyRate2({
                    baseAmount: cryptoAmount,
                    rate: {
                        base: cryptoAmount.currency,
                        quote: defaultCurrencyConfig.defaultCurrency,
                        rate: token.rate.rate,
                    },
                })) ||
            null

        switch (data.fees.type) {
            case 'private_key_store':
            case 'ledger':
            case 'secret_phrase_key':
            case 'trezor':
            case 'track_only':
                throw new Error(`Not implemented`) // TODO @max implementation part 2
            case 'safe_4337': {
                return {
                    type: 'safe_operation_request',

                    form: {
                        fees: data.fees.fees,
                        amount: cryptoAmount,
                        toAddress: pollable.params.form.toAddress,
                        fromWallet: pollable.params.form.fromWallet,
                        maxBalance: data.fees.maxBalance,
                        amountInDefaultCurrency,
                    },
                    network,
                    keyStore: data.fees.keystore,
                    nonce: Hexadecimal.fromBigInt(data.nonce),
                    safeInstance: data.fees.safeInstance,
                }
            }
            default:
                return notReachable(data.fees)
        }
    })

const validateGas = (data: Data): Result<NotEnoughGasToken, unknown> => {
    switch (data.fees.type) {
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor': {
            const fee = data.fees.fees
            const token = data.serverPortfolio.tokens.find(
                (token) =>
                    token.balance.currency.id ===
                    fee.balanceInNativeCurrency.currency.id
            )
            const amount = token?.balance.amount || 0n
            const youNeedAmount =
                data.fees.fees.balanceInNativeCurrency.amount - amount
            if (youNeedAmount > 0) {
                return failure({
                    type: 'not_enough_gas_token',
                    youNeed: {
                        amount: youNeedAmount,
                        currency: fee.balanceInNativeCurrency.currency,
                    },
                })
            }
            return success(null)
        }
        case 'safe_4337':
            const fee = data.fees.fees

            switch (fee.type) {
                case 'sponsored_gas_abstraction_transaction_fee':
                    return success(null)
                case 'erc20_gas_abstraction_transaction_fee':
                case 'native_gas_abstraction_transaction_fee':
                    const token = data.serverPortfolio.tokens.find(
                        (token) =>
                            token.balance.currency.id ===
                            fee.feeInTokenCurrency.currency.id
                    )
                    const amount = token?.balance.amount || 0n
                    const youNeedAmount = fee.feeInTokenCurrency.amount - amount
                    if (youNeedAmount > 0) {
                        return failure({
                            type: 'not_enough_gas_token',
                            youNeed: {
                                amount: youNeedAmount,
                                currency: fee.feeInTokenCurrency.currency,
                            },
                        })
                    }
                    break

                default:
                    notReachable(fee)
                    break
            }

            return success(null)
        case 'track_only':
            return success(null)
        default:
            return notReachable(data.fees)
    }
}
