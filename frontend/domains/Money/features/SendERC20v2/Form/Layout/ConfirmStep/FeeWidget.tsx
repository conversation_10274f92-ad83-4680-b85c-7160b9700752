import { FormattedMessage } from 'react-intl'

import { FeeInputButton } from '@zeal/uikit/FeeInputButton'
import { Row } from '@zeal/uikit/Row'
import { Spinner } from '@zeal/uikit/Spinner'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'

import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { SafeFeeWidget } from '@zeal/domains/Money/features/SendERC20v2/SafeFeeWidget'
import { Network, NetworkMap } from '@zeal/domains/Network'
import { parseNetworkFromMapByHexId } from '@zeal/domains/Network/helpers/parse'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    ERC20GasAbstractionTransactionFee,
    NativeGasAbstractionTransactionFee,
} from '@zeal/domains/UserOperation'

import { Pollable } from '../../../fetch'

type Msg =
    | {
          type: 'on_select_gas_token_settings_clicked'
          portfolio: ServerPortfolio2
          nativeFee: CryptoMoney
          nativeFeeInDefaultCurrency: FiatMoney | null
          erc20FeeInDefaultCurrency: FiatMoney | null
          currentFee:
              | ERC20GasAbstractionTransactionFee
              | NativeGasAbstractionTransactionFee
      }
    | {
          type: 'fee_info_popup_clicked'
          network: Network
      }

export const FeeWidget = ({
    pollable,
    networkMap,
    onMsg,
}: {
    networkMap: NetworkMap
    pollable: Pollable
    onMsg: (msg: Msg) => void
}) => {
    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed': {
            switch (pollable.data.fees.type) {
                case 'private_key_store':
                case 'ledger':
                case 'secret_phrase_key':
                case 'trezor':
                case 'track_only':
                    return null // TODO @negriienko implementation part 2
                case 'safe_4337': {
                    const fees = pollable.data.fees
                    return (
                        <SafeFeeWidget
                            disabled={false}
                            currentFee={fees.fees}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'on_select_gas_token_settings_clicked':
                                        onMsg({
                                            type: 'on_select_gas_token_settings_clicked',
                                            currentFee: msg.currentFee,
                                            portfolio:
                                                pollable.data.serverPortfolio,
                                            nativeFee: fees.nativeFee,
                                            nativeFeeInDefaultCurrency:
                                                fees.nativeFeeInDefaultCurrency,
                                            erc20FeeInDefaultCurrency:
                                                fees.erc20FeeInDefaultCurrency,
                                        })
                                        break
                                    case 'fee_info_popup_clicked':
                                        const network =
                                            parseNetworkFromMapByHexId({
                                                networkHexId:
                                                    pollable.params.form
                                                        .cryptoCurrency
                                                        .networkHexChainId,
                                                networkMap,
                                            }).getSuccessResult()
                                        if (network) {
                                            onMsg({
                                                type: 'fee_info_popup_clicked',
                                                network,
                                            })
                                        }
                                        break

                                    /* istanbul ignore next */
                                    default:
                                        notReachable(msg)
                                }
                            }}
                        />
                    )
                }
                default:
                    return notReachable(pollable.data.fees)
            }
        }
        case 'loading':
            return (
                <FeeInputButton
                    disabled
                    left={
                        <Row spacing={4}>
                            <Text
                                variant="paragraph"
                                weight="regular"
                                color="textPrimary"
                            >
                                <FormattedMessage
                                    id="send-safe-transaction.network-fee-widget.title"
                                    defaultMessage="Fees"
                                />
                            </Text>
                        </Row>
                    }
                    right={
                        <Row spacing={4}>
                            <Spinner
                                variant="regular"
                                size={18}
                                color="teal40"
                            />
                        </Row>
                    }
                />
            )
        case 'error':
            return null

        default:
            return notReachable(pollable)
    }
}
