import { useIntl } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { Divider } from '@zeal/uikit/Divider'
import { FancyButton } from '@zeal/uikit/FancyButton'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { AccountsMap } from '@zeal/domains/Account'
import { AvatarWithoutBadge as AccountAvatar } from '@zeal/domains/Account/components/Avatar'
import { NullableListItemButton } from '@zeal/domains/Account/components/NullableListItemButton'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { MaxButton } from './MaxButton'

import { Pollable } from '../../fetch'
import { NotEnoughBalance } from '../validate'

type Props = {
    pollable: Pollable
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    accountsMap: AccountsMap
    portfolioMap: PortfolioMap
    errors: {
        maxButton?: NotEnoughBalance
    }
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_from_token_selector_clicked'; portfolio: ServerPortfolio2 }
    | { type: 'on_to_wallet_clicked' }
    | { type: 'on_pollable_error_widget_clicked' }
    | { type: 'import_keys_button_clicked' }
    | { type: 'on_amount_in_crypto_currency_changed'; amount: string | null }
    | { type: 'on_form_continue_clicked' }
    | { type: 'on_amount_input_clicked' }

export const FormLayout = ({
    onMsg,
    pollable,
    networkMap,
    defaultCurrencyConfig,
    portfolioMap,
    accountsMap,
    errors,
}: Props) => {
    const { formatMessage } = useIntl()

    const { amount, cryptoCurrency, toAddress } = pollable.params.form

    return (
        <Column spacing={4}>
            <AmountInput
                label={cryptoCurrency.code}
                top={
                    <Column spacing={0}>
                        <FancyButton
                            color="secondary"
                            rounded
                            left={() => (
                                <Row grow shrink spacing={4}>
                                    <AccountAvatar
                                        size={12}
                                        account={
                                            pollable.params.form.fromWallet
                                        }
                                    />
                                    <Text
                                        variant="caption1"
                                        color="gray40"
                                        weight="medium"
                                        ellipsis
                                    >
                                        {pollable.params.form.fromWallet.label}
                                    </Text>
                                </Row>
                            )}
                            right={null}
                            onClick={noop}
                        />
                        <Divider variant="secondary" />
                    </Column>
                }
                content={{
                    topLeft: (
                        <IconButton
                            variant="on_light"
                            onClick={() => {
                                switch (pollable.type) {
                                    case 'error':
                                    case 'loading':
                                        const portfolio =
                                            unsafe_GetPortfolioCache2({
                                                address:
                                                    pollable.params.form
                                                        .fromWallet.address,
                                                portfolioMap,
                                            })
                                        if (portfolio) {
                                            onMsg({
                                                type: 'on_from_token_selector_clicked',
                                                portfolio,
                                            })
                                        }

                                        break
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        onMsg({
                                            type: 'on_from_token_selector_clicked',
                                            portfolio:
                                                pollable.data.serverPortfolio,
                                        })
                                        break

                                    default:
                                        notReachable(pollable)
                                        break
                                }
                            }}
                        >
                            {({ color }) => (
                                <Row spacing={4}>
                                    <CurrencyAvatar
                                        key={cryptoCurrency.id}
                                        currency={cryptoCurrency}
                                        size={32}
                                        rightBadge={({ size }) => (
                                            <Badge
                                                size={size}
                                                network={findNetworkByHexChainId(
                                                    cryptoCurrency.networkHexChainId,
                                                    networkMap
                                                )}
                                            />
                                        )}
                                    />
                                    <Text
                                        variant="title3"
                                        color="textPrimary"
                                        weight="medium"
                                    >
                                        {cryptoCurrency.code}
                                    </Text>
                                    <LightArrowDown2 size={18} color={color} />
                                </Row>
                            )}
                        </IconButton>
                    ),
                    topRight: ({ onBlur, onFocus }) => (
                        <AmountInput.Input
                            onPressIn={() =>
                                onMsg({ type: 'on_amount_input_clicked' })
                            }
                            onFocus={onFocus}
                            onBlur={onBlur}
                            label={formatMessage({
                                id: 'send_token.form.send-amount',
                                defaultMessage: 'Send amount',
                            })}
                            fraction={cryptoCurrency.fraction}
                            autoFocus
                            readOnly={(() => {
                                switch (ZealPlatform.OS) {
                                    case 'ios':
                                    case 'android':
                                        return true
                                    case 'web':
                                        return false
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(ZealPlatform)
                                }
                            })()}
                            prefix=""
                            amount={amount}
                            onChange={(value) => {
                                onMsg({
                                    type: 'on_amount_in_crypto_currency_changed',
                                    amount: value,
                                })
                            }}
                            onSubmitEditing={() => {
                                onMsg({ type: 'on_form_continue_clicked' })
                            }}
                        />
                    ),
                    bottomLeft: (
                        <MaxButton
                            pollable={pollable}
                            portfolioMap={portfolioMap}
                            onMsg={onMsg}
                            error={errors.maxButton || null}
                        />
                    ),
                    bottomRight: (
                        <ButtonInDefaultCurrency
                            pollable={pollable}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                        />
                    ),
                }}
                state="normal"
            />

            <NextStepSeparator />

            <NullableListItemButton
                disabled={false}
                address={toAddress}
                accountsMap={accountsMap}
                onClick={() => onMsg({ type: 'on_to_wallet_clicked' })}
            />
        </Column>
    )
}

const ButtonInDefaultCurrency = ({
    pollable,
    defaultCurrencyConfig,
}: {
    pollable: Pollable
    defaultCurrencyConfig: DefaultCurrencyConfig
}) => {
    switch (pollable.type) {
        case 'error':
        case 'loading':
            return null

        case 'loaded':
        case 'reloading':
        case 'subsequent_failed':
            const data = pollable.data
            const currency = pollable.params.form.cryptoCurrency
            const token = data.serverPortfolio.tokens.find(
                (token) => token.balance.currency.id === currency.id
            )
            const amountInDefaultCurrency =
                (token &&
                    token.rate &&
                    pollable.params.form.amount &&
                    applyRate2({
                        baseAmount: {
                            amount: fromFixedWithFraction(
                                pollable.params.form.amount,
                                currency.fraction
                            ),
                            currency,
                        },
                        rate: {
                            base: currency,
                            quote: defaultCurrencyConfig.defaultCurrency,
                            rate: token.rate.rate,
                        },
                    })) ||
                null

            if (!amountInDefaultCurrency) {
                return null
            }

            return (
                <Text
                    variant="input_footnote"
                    color="textSecondary"
                    weight="regular"
                >
                    <FormattedMoneyPrecise
                        withSymbol
                        sign={null}
                        money={amountInDefaultCurrency}
                    />
                </Text>
            )

        default:
            return notReachable(pollable)
    }
}
