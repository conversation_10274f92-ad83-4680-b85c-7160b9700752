import { FormattedMessage } from 'react-intl'

import { Row } from '@zeal/uikit/Row'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'

import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Pollable } from '../../fetch'
import { NotEnoughBalance } from '../validate'

type Msg = {
    type: 'on_amount_in_crypto_currency_changed'
    amount: string
}
export const MaxButton = ({
    pollable,
    portfolioMap,
    error,
    onMsg,
}: {
    onMsg: (msg: Msg) => void
    pollable: Pollable
    portfolioMap: PortfolioMap
    error: NotEnoughBalance | null
}) => {
    switch (pollable.type) {
        case 'loading':
            return (
                <Row spacing={4}>
                    <Text
                        color="gray40"
                        variant="paragraph"
                        weight="regular"
                        textDecorationLine="underline"
                    >
                        <FormattedMessage
                            id="currency.max_loading"
                            defaultMessage="Max:"
                        />
                    </Text>
                    <Skeleton variant="default" width={60} height={18} />
                </Row>
            )
        case 'error':
            const portfolio = unsafe_GetPortfolioCache2({
                address: pollable.params.form.fromWallet.address,
                portfolioMap,
            })
            if (portfolio) {
                const token = getTokenByCryptoCurrency3({
                    serverPortfolio: portfolio,
                    currency: pollable.params.form.cryptoCurrency,
                })
                if (token) {
                    const money = {
                        amount: token.balance.amount,
                        currency: pollable.params.form.cryptoCurrency,
                    }
                    return (
                        <Tertiary
                            color={error ? 'warning' : 'on_light'}
                            size="regular"
                            onClick={() => {
                                postUserEvent({
                                    type: 'MaxButtonClickedEvent',
                                    location: 'send',
                                    installationId:
                                        pollable.params.installationId,
                                    asset: pollable.params.form.cryptoCurrency
                                        .code,
                                })
                                onMsg({
                                    type: 'on_amount_in_crypto_currency_changed',
                                    amount: toFixedWithFraction(
                                        money.amount,
                                        money.currency.fraction
                                    ),
                                })
                            }}
                        >
                            {({ color, textVariant, textWeight }) => (
                                <Row spacing={4}>
                                    <Text
                                        color={color}
                                        variant={textVariant}
                                        weight={textWeight}
                                        textDecorationLine="underline"
                                    >
                                        <FormattedMessage
                                            id="bankTransfer.withdraw.max_loading"
                                            defaultMessage="Max: {amount}"
                                            values={{
                                                amount: (
                                                    <FormattedMoneyPrecise
                                                        withSymbol={false}
                                                        sign={null}
                                                        money={money}
                                                    />
                                                ),
                                            }}
                                        />
                                    </Text>
                                </Row>
                            )}
                        </Tertiary>
                    )
                }
            }
            return (
                <Row spacing={4}>
                    <Text
                        color="gray40"
                        variant="paragraph"
                        weight="regular"
                        textDecorationLine="underline"
                    >
                        <FormattedMessage
                            id="currency.max_loading"
                            defaultMessage="Max:"
                        />
                    </Text>
                    <Skeleton variant="default" width={60} height={18} />
                </Row>
            )
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed':
            return (
                <Tertiary
                    color={error ? 'warning' : 'on_light'}
                    size="regular"
                    onClick={() => {
                        postUserEvent({
                            type: 'MaxButtonClickedEvent',
                            location: 'send',
                            installationId: pollable.params.installationId,
                            asset: pollable.params.form.cryptoCurrency.code,
                        })
                        onMsg({
                            type: 'on_amount_in_crypto_currency_changed',
                            amount: toFixedWithFraction(
                                pollable.data.fees.maxBalance.amount,
                                pollable.data.fees.maxBalance.currency.fraction
                            ),
                        })
                    }}
                >
                    {({ color, textVariant, textWeight }) => (
                        <Row spacing={4}>
                            <Text
                                color={color}
                                variant={textVariant}
                                weight={textWeight}
                                textDecorationLine="underline"
                            >
                                <FormattedMessage
                                    id="bankTransfer.withdraw.max_loading"
                                    defaultMessage="Max: {amount}"
                                    values={{
                                        amount: (
                                            <FormattedMoneyPrecise
                                                withSymbol={false}
                                                sign={null}
                                                money={
                                                    pollable.data.fees
                                                        .maxBalance
                                                }
                                            />
                                        ),
                                    }}
                                />
                            </Text>
                        </Row>
                    )}
                </Tertiary>
            )

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}
