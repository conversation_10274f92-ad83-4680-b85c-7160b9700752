import { FormattedMessage } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { SelectToken } from '@zeal/domains/Token/components/SelectToken'

type Props = {
    portfolio: ServerPortfolio2
    networkMap: NetworkMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    selectedCurrency: CryptoCurrency
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_from_crypto_currency_selected'
          currency: CryptoCurrency
      }
    | { type: 'close' }

export const SelectFromToken = ({
    networkMap,
    currencyHiddenMap,
    currencyPinMap,
    portfolio,
    onMsg,
    selectedCurrency,
}: Props) => {
    const cryptoCurrencies = portfolio.tokens.map(
        (token) => token.balance.currency
    )
    return (
        <Screen
            background="light"
            padding="form"
            aria-labelledby="select-currency-label"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <UIActionBar
                top={null}
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="textPrimary"
                            >
                                <FormattedMessage
                                    id="buy.select-currency.title"
                                    defaultMessage="Select token"
                                />
                            </Text>
                        </Row>
                    </Clickable>
                }
            />

            <Column fill shrink spacing={16}>
                <SelectToken
                    networkMap={networkMap}
                    serverPortfolio={portfolio}
                    cryptoCurrencies={cryptoCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    onCryptoCurrencySelected={(currency) => {
                        onMsg({
                            type: 'on_from_crypto_currency_selected',
                            currency,
                        })
                    }}
                    selectedCurrency={selectedCurrency}
                />
            </Column>
        </Screen>
    )
}
