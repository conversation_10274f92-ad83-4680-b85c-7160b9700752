import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyId,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { GNOSIS_GNO } from '@zeal/domains/Currency/constants'
import { calculateInitialFromCurrency } from '@zeal/domains/Currency/helpers/calculateInitialFromCurrency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Form, TransactionRequest } from './fetch'
import { FormComponent } from './Form'
import { Submit } from './Submit'

type Props = {
    keyStoreMap: KeyStoreMap
    customCurrencies: CustomCurrencyMap
    cardConfig: CardConfig
    sessionPassword: string
    portfolioMap: PortfolioMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    accountsMap: AccountsMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    presetMap: FeePresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    isEthereumNetworkFeeWarningSeen: boolean
    fromWallet: Account
    toAddress: Web3.address.Address
    fromCurrencyId: CurrencyId | null
    actionSource: ActionSource2
    onMsg: (msg: Msg) => void
}
type Msg =
    | Extract<
          MsgOf<typeof FormComponent>,
          {
              type:
                  | 'close'
                  | 'import_keys_button_clicked'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'add_wallet_clicked'
                  | 'track_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_4337_gas_currency_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_address_scanned_and_add_label'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
          }
      >
    | MsgOf<typeof Submit>

type State =
    | { type: 'form'; initForm: Form }
    | { type: 'submit'; transactionRequest: TransactionRequest }

const calculateInitForm = ({
    fromWallet,
    toAddress,
    portfolioMap,
    networkMap,
    fromCurrencyId,
}: {
    fromWallet: Account
    toAddress: Web3.address.Address
    portfolioMap: PortfolioMap
    networkMap: NetworkMap
    fromCurrencyId: CurrencyId | null
}): Form => {
    const portfolio = unsafe_GetPortfolioCache2({
        address: fromWallet.address,
        portfolioMap,
    })
    if (!portfolio) {
        return {
            fromWallet,
            cryptoCurrency: GNOSIS_GNO,
            amount: null,
            stage: 'edit',
            toAddress,
        }
    }
    const currencies = portfolio.tokens.map((t) => t.balance.currency)

    const fromCurrency =
        fromCurrencyId && currencies.find(({ id }) => id === fromCurrencyId)

    return {
        fromWallet,
        cryptoCurrency:
            fromCurrency ||
            calculateInitialFromCurrency({
                portfolio,
                toCurrency: null,
                currencies,
                networkMap,
            }),
        amount: null,
        stage: 'edit',
        toAddress,
    }
}

export const SendERC20v2 = ({
    keyStoreMap,
    customCurrencies,
    cardConfig,
    sessionPassword,
    portfolioMap,
    currencyPinMap,
    currencyHiddenMap,
    accountsMap,
    gasCurrencyPresetMap,
    presetMap,
    defaultCurrencyConfig,
    networkMap,
    installationId,
    networkRPCMap,
    isEthereumNetworkFeeWarningSeen,
    fromWallet,
    toAddress,
    fromCurrencyId,
    actionSource,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'form',
        initForm: calculateInitForm({
            fromWallet,
            toAddress,
            portfolioMap,
            fromCurrencyId,
            networkMap,
        }),
    })

    switch (state.type) {
        case 'form':
            return (
                <FormComponent
                    initialForm={state.initForm}
                    keyStoreMap={keyStoreMap}
                    customCurrencies={customCurrencies}
                    cardConfig={cardConfig}
                    sessionPassword={sessionPassword}
                    portfolioMap={portfolioMap}
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    accountsMap={accountsMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    presetMap={presetMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networkMap={networkMap}
                    installationId={installationId}
                    networkRPCMap={networkRPCMap}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'import_keys_button_clicked':
                            case 'on_account_create_request':
                            case 'on_accounts_create_success_animation_finished':
                            case 'add_wallet_clicked':
                            case 'track_wallet_clicked':
                            case 'hardware_wallet_clicked':
                            case 'on_add_label_to_track_only_account_during_send':
                            case 'on_4337_gas_currency_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_address_scanned_and_add_label':
                            case 'on_ethereum_network_fee_warning_understand_clicked':
                                onMsg(msg)
                                break

                            case 'on_form_confirm_clicked':
                                setState({
                                    type: 'submit',
                                    transactionRequest: msg.transactionRequest,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit':
            return (
                <Submit
                    actionSource={actionSource}
                    accountsMap={accountsMap}
                    networkMap={networkMap}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    transactionRequest={state.transactionRequest}
                    onMsg={onMsg}
                />
            )

        default:
            return notReachable(state)
    }
}
