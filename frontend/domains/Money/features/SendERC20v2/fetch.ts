import { notReachable } from '@zeal/toolkit'
import { memoizeOne } from '@zeal/toolkit/Function/memoizeOne'
import * as Hex from '@zeal/toolkit/Hexadecimal'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    DefaultCurrency,
    GasCurrencyPresetMap,
    KnownCryptoCurrencies,
} from '@zeal/domains/Currency'
import { requestAllowance } from '@zeal/domains/Currency/api/fetchAllowance'
import { fetchShortStaticCurrencies } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import {
    PAYMASTER_ADDRESS,
    PAYMASTER_MAP,
} from '@zeal/domains/Currency/constants'
import { getKnownCryptoCurrenciesFromShortKnownCryptoCurrencies } from '@zeal/domains/Currency/helpers/getShortKnownCryptoCurrenciesfromKnownCryptoCurrencies'
import { FXRate2 } from '@zeal/domains/FXRate'
import { fetchCrossRates } from '@zeal/domains/FXRate/api/fetchCrossRates'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { EOA, KeyStoreMap, Safe4337, TrackOnly } from '@zeal/domains/KeyStore'
import { requestSafeOwners } from '@zeal/domains/KeyStore/api/fetchSafeOwners'
import { SafeInstance } from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { getSafeDeploymentInitCode } from '@zeal/domains/KeyStore/helpers/getSafeDeploymentInitCode'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { fetchServerPortfolioWithCache } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { fetchRPCBatch2 } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import {
    CustomSelectedPreset,
    FeeForecastResponse,
    FeePresetMap,
    PredefinedPreset,
} from '@zeal/domains/Transactions/domains/FeeForecast'
import { GasAbstractionTransactionFeeResponse2 } from '@zeal/domains/UserOperation'
import { requestCurrentEntrypointNonce2 } from '@zeal/domains/UserOperation/api/fetchCurrentEntrypointNonce'
import { fetchBiconomyFeeAndGasEstimates } from '@zeal/domains/UserOperation/api/fetchFeeAndGasEstimatesFromBundler'

import {
    createInitialUserOperation,
    fetchUserOperationFee,
    UserOperation,
} from './UserOperation'
import { SEND_SPONSORED_ON_NETWORKS } from './UserOperation/constants'

export type Pollable = PollableData<Data, Params>

export type Form = {
    cryptoCurrency: CryptoCurrency
    amount: string | null
    toAddress: Web3.address.Address
    fromWallet: Account
    stage: 'edit' | 'confirm'
}

export type Params = {
    currencyHiddenMap: CurrencyHiddenMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    presetMap: FeePresetMap
    keyStoreMap: KeyStoreMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    feePreset: PredefinedPreset | CustomSelectedPreset
    signal?: AbortSignal
    cacheKey: string
    form: Form
}

export type Data = {
    nativeCurrency: CryptoCurrency
    serverPortfolio: ServerPortfolio2
    fees:
        | {
              type: EOA['type']
              keystore: EOA
              fees: FeeForecastResponse
              maxBalance: CryptoMoney
          }
        | {
              type: Safe4337['type']
              keystore: Safe4337
              safeInstance: SafeInstance
              nativeFee: CryptoMoney
              nativeFeeInDefaultCurrency: FiatMoney | null
              erc20FeeInDefaultCurrency: FiatMoney | null

              fees: GasAbstractionTransactionFeeResponse2
              maxBalance: CryptoMoney
          }
        | {
              type: TrackOnly['type']
              keystore: TrackOnly
              maxBalance: CryptoMoney
          }

    nonce: bigint
}

export type SimulatedWithDeploymentBundleUserOperationRequest = {
    type: 'safe_deployment_bundle_user_operation_request'
    network: Network
    form: ValidatedForm
    keyStore: Safe4337
    userOperationWithSignature: UserOperation
}

export type SimulatedWithoutDeploymentBundleUserOperationRequest = {
    type: 'safe_without_deployment_bundle_user_operation_request'
    network: Network
    form: ValidatedForm
    keyStore: Safe4337
    userOperationWithSignature: UserOperation
}

export type ValidatedForm = {
    amount: CryptoMoney
    amountInDefaultCurrency: FiatMoney | null
    toAddress: Web3.address.Address
    fromWallet: Account
    fees: GasAbstractionTransactionFeeResponse2
    maxBalance: CryptoMoney
}

export type SimulatedWithAddOwnerUserOperationRequest = {
    type: 'safe_with_add_owner_user_operation_request'
    network: Network
    form: ValidatedForm
    keyStore: Safe4337
    userOperationWithSignature: UserOperation
}

type EOATransactionRequest = {
    type: 'eoa_transaction_request'
    form: ValidatedForm
}

export type SafeTransactionRequest = {
    type: 'safe_operation_request'
    network: Network
    form: ValidatedForm
    keyStore: Safe4337
    nonce: Hex.Hexadecimal
    safeInstance: SafeInstance
}

export type TransactionRequest = EOATransactionRequest | SafeTransactionRequest

type RPCDataParams = {
    fromWallet: Account
    currencies: KnownCryptoCurrencies
    network: Network
    keystore: Safe4337
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}

const fetchRPCData = async ({
    fromWallet,
    network,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    currencies,
    keystore,
    signal,
}: RPCDataParams): Promise<{
    safeInstance: SafeInstance
    crossRates: FXRate2<CryptoCurrency, CryptoCurrency>[]
    nonce: bigint
    allowances: CryptoMoney[]
    nativeToDefaultRate: FXRate2<CryptoCurrency, DefaultCurrency> | null
}> => {
    const fromAddress = fromWallet.address as Web3.address.Address
    const isSponsored = SEND_SPONSORED_ON_NETWORKS.includes(network.hexChainId)

    const isSmartWalletSupportedNetwork = (() => {
        switch (network.smartWalletSupport.type) {
            case 'supported':
                return true
            case 'not_supported':
                return false
            default:
                return notReachable(network.smartWalletSupport)
        }
    })()
    const shouldRequestRatesAndAllowance =
        !isSponsored || isSmartWalletSupportedNetwork

    const [nativeToDefaultRate, crossRates, [nonce, owners, ...allowances]] =
        await Promise.all([
            fetchRate({
                networkMap,
                networkRPCMap,
                cryptoCurrency: network.nativeCurrency,
                defaultCurrencyConfig,
                signal,
            }),

            !shouldRequestRatesAndAllowance
                ? []
                : fetchCrossRates({
                      network,
                      signal,
                      pairs: PAYMASTER_MAP[network.hexChainId]
                          .filter(
                              (currencyId) =>
                                  currencyId !== network.nativeCurrency.id
                          )
                          .map((currencyId) => ({
                              fromAddress: network.nativeCurrency.address,
                              toAddress: currencies[currencyId].address,
                          })),
                  }),

            fetchRPCBatch2(
                [
                    requestCurrentEntrypointNonce2({
                        entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
                        address: fromAddress,
                    }),
                    requestSafeOwners({ safeAddress: fromAddress }),

                    ...(!shouldRequestRatesAndAllowance
                        ? []
                        : PAYMASTER_MAP[network.hexChainId]
                              .filter(
                                  (currencyId) =>
                                      currencyId !== network.nativeCurrency.id
                              )
                              .map((currencyId) => {
                                  const currency = currencies[currencyId]
                                  return requestAllowance({
                                      currency,
                                      owner: fromWallet.address as Web3.address.Address,
                                      spender: PAYMASTER_ADDRESS,
                                  })
                              })),
                ],
                { networkRPCMap, network, signal }
            ),
        ])
    const safeInstance: SafeInstance = owners
        ? {
              type: 'deployed',
              safeAddress: fromAddress,
              entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
              owners: owners,
          }
        : {
              type: 'not_deployed',
              entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
              safeAddress: fromAddress,
              deploymentInitCode: getSafeDeploymentInitCode(
                  keystore.safeDeplymentConfig
              ),
          }
    return {
        safeInstance,
        allowances,
        nativeToDefaultRate,
        crossRates,
        nonce,
    }
}

const fetchRPCDataWithCache = memoizeOne(
    ({
        cacheKey: _,
        signal: __,
        ...rest
    }: RPCDataParams & { cacheKey: string }) => {
        return fetchRPCData(rest)
    },
    ({ cacheKey, fromWallet, network }) => {
        return `${cacheKey}${fromWallet.address}${network.hexChainId}`
    }
)

export const fetch = async ({
    form,
    defaultCurrencyConfig,
    networkMap,
    installationId,
    signal,
    feePreset,
    networkRPCMap,
    keyStoreMap,
    gasCurrencyPresetMap,
    currencyHiddenMap,
    cacheKey,
}: Params): Promise<Data> => {
    const keystore = getKeyStore({
        address: form.fromWallet.address,
        keyStoreMap,
    })
    const network = findNetworkByHexChainId(
        form.cryptoCurrency.networkHexChainId,
        networkMap
    )
    const fromAddress = form.fromWallet.address as Web3.address.Address
    switch (keystore.type) {
        case 'safe_4337':
            const nativeCurrency = network.nativeCurrency
            const staticShortCurrencies = await fetchShortStaticCurrencies()
            const currencies =
                getKnownCryptoCurrenciesFromShortKnownCryptoCurrencies(
                    staticShortCurrencies
                )

            // dirty way to optize fetching
            const portfolioPromise = fetchServerPortfolioWithCache({
                address: fromAddress,
                defaultCurrencyConfig,
                networkMap,
                installationId,
                currencyHiddenMap,
                cacheKey,
                signal,
                networkRPCMap,
            })

            const {
                crossRates,
                allowances,
                nonce,
                safeInstance,
                nativeToDefaultRate,
            } = await fetchRPCDataWithCache({
                fromWallet: form.fromWallet,
                cacheKey,
                defaultCurrencyConfig,
                network,
                networkMap,
                networkRPCMap,
                signal,
                keystore,
                currencies,
            })
            const [serverPortfolio, estimate] = await Promise.all([
                portfolioPromise,
                fetchBiconomyFeeAndGasEstimates({
                    entrypoint: safeInstance.entrypoint,
                    network,
                    initialUserOperation: createInitialUserOperation({
                        network,
                        nonce: Hex.fromBigInt(nonce),
                        safeInstance,
                        keystore,
                        form,
                    }),
                    signal,
                }),
            ])

            const fees = fetchUserOperationFee({
                portfolio: serverPortfolio,
                approvals: allowances,
                crossRates,
                network,
                safeInstance,
                keystore,
                gasCurrencyPresetMap,
                nativeCurrency,
                form,
                estimate,
                nativeToDefaultRate,
                currencies,
            })

            return {
                nativeCurrency,
                nonce,
                serverPortfolio,
                fees: {
                    type: 'safe_4337',
                    nativeFee: fees.feeInNativeTokenCurrency,
                    nativeFeeInDefaultCurrency: fees.feeInDefaultCurrency,
                    erc20FeeInDefaultCurrency: fees.erc20FeeInDefaultCurrency,
                    fees: fees.selectedFee,
                    maxBalance: fees.maxBalance,
                    keystore,
                    safeInstance,
                },
            }

        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
        case 'track_only':
            throw new Error('eoa not implemented yet')
        default:
            return notReachable(keystore)
    }
}
