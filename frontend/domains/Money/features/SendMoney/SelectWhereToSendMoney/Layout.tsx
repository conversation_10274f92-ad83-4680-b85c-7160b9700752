import { useState } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group, GroupHeader, Section } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Checkbox } from '@zeal/uikit/Icon/Checkbox'
import { OutlineSearch } from '@zeal/uikit/Icon/OutlineSearch'
import { QrCode } from '@zeal/uikit/Icon/QrCode'
import { SolidBank } from '@zeal/uikit/Icon/SolidBank'
import { Input } from '@zeal/uikit/Input'
import { ListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { ActiveAccountsSection } from '@zeal/domains/Account/components/ActiveAccountsSection'
import { EmptySearch } from '@zeal/domains/Account/components/EmptySearch'
import { EmptySearchForValidAddress } from '@zeal/domains/Account/components/EmptySearchForValidAddress'
import { TrackedAccountsSection } from '@zeal/domains/Account/components/TrackedAccountsSection'
import { UnlockedListItem } from '@zeal/domains/Account/components/UnlockedListItem'
import { validateAccountSearch } from '@zeal/domains/Account/helpers/validateAccountSearch'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    installationId: string
    toAddress: Web3.address.Address | null
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}
type Msg =
    | { type: 'on_bank_transfer_clicked' }
    | { type: 'close' }
    | { type: 'on_continue_clicked'; address: Web3.address.Address }
    | { type: 'on_qr_code_scanner_clicked' }
    | MsgOf<typeof ActiveAccountsSection>
    | MsgOf<typeof TrackedAccountsSection>
    | MsgOf<typeof UnlockedListItem>

export const Layout = ({
    accountsMap,
    keyStoreMap,
    portfolioMap,
    toAddress,
    currencyHiddenMap,
    installationId,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    const [search, setSearch] = useState<string>('')

    const searchResult = validateAccountSearch({
        accountsMap,
        keystoreMap: keyStoreMap,
        search,
        portfolioMap,
        currencyHiddenMap,
        defaultCurrencyConfig,
        selectedAccountAddress: null, // We don't want to have a separate section for selected account here
    })

    return (
        <Screen
            background="light"
            padding="form"
            aria-labelledby="send-to-layout"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={16} shrink fill>
                <Column spacing={0}>
                    <ActionBar
                        left={
                            <Clickable onClick={() => onMsg({ type: 'close' })}>
                                <Row spacing={4}>
                                    <BackIcon size={24} color="iconDefault" />
                                    <Text
                                        variant="title3"
                                        weight="semi_bold"
                                        color="textPrimary"
                                        id="send-to-layout"
                                    >
                                        <FormattedMessage
                                            id="SendERC20.send_to"
                                            defaultMessage="Send to"
                                        />
                                    </Text>
                                </Row>
                            </Clickable>
                        }
                    />
                </Column>

                <Input
                    keyboardType="default"
                    autoFocus={(() => {
                        switch (ZealPlatform.OS) {
                            case 'ios':
                            case 'android':
                                return false
                            case 'web':
                                return true
                            /* istanbul ignore next */
                            default:
                                return notReachable(ZealPlatform)
                        }
                    })()}
                    leftIcon={<OutlineSearch size={24} color="iconDefault" />}
                    rightIcon={<RightIcon searchResult={searchResult} />}
                    variant="regular"
                    value={search}
                    onChange={(e) => {
                        setSearch(e.nativeEvent.text)
                    }}
                    state="normal"
                    placeholder={formatMessage({
                        id: 'address_book.change_account.search_placeholder',
                        defaultMessage: 'Add or search address',
                    })}
                    onSubmitEditing={noop}
                />

                <ScrollContainer withFloatingActions={false}>
                    {(() => {
                        switch (searchResult.type) {
                            case 'accounts_not_found':
                                return <EmptySearch />

                            case 'accounts_not_found_search_valid_address':
                                return <EmptySearchForValidAddress />

                            case 'grouped_accounts': {
                                const { active, tracked } = searchResult

                                return (
                                    <Column spacing={16}>
                                        {(() => {
                                            switch (ZealPlatform.OS) {
                                                case 'ios':
                                                case 'android':
                                                    return (
                                                        <Section>
                                                            <Group variant="default">
                                                                <ListItem
                                                                    variant="default"
                                                                    size="regular"
                                                                    onClick={() => {
                                                                        postUserEvent(
                                                                            {
                                                                                type: 'SendRecipientQRCodeSelectedEvent',
                                                                                installationId,
                                                                            }
                                                                        )
                                                                        onMsg({
                                                                            type: 'on_qr_code_scanner_clicked',
                                                                        })
                                                                    }}
                                                                    aria-current={
                                                                        false
                                                                    }
                                                                    avatar={({
                                                                        size,
                                                                    }) => (
                                                                        <QrCode
                                                                            size={
                                                                                size
                                                                            }
                                                                            color="iconAccent2"
                                                                        />
                                                                    )}
                                                                    primaryText={
                                                                        <FormattedMessage
                                                                            id="send_to.qr_code.title"
                                                                            defaultMessage="Scan QR code"
                                                                        />
                                                                    }
                                                                    shortText={
                                                                        <FormattedMessage
                                                                            id="send_to.qr_code.description"
                                                                            defaultMessage="Scan a QR code to send to a wallet"
                                                                        />
                                                                    }
                                                                />
                                                            </Group>
                                                        </Section>
                                                    )
                                                case 'web':
                                                    break

                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        ZealPlatform
                                                    )
                                            }
                                        })()}
                                        <Section>
                                            <GroupHeader
                                                left={({
                                                    color,
                                                    textVariant,
                                                    textWeight,
                                                }) => (
                                                    <Text
                                                        color={color}
                                                        variant={textVariant}
                                                        weight={textWeight}
                                                    >
                                                        <FormattedMessage
                                                            id="address_book.change_account.bank_transfers.header"
                                                            defaultMessage="Bank recipients"
                                                        />
                                                    </Text>
                                                )}
                                                right={null}
                                            />

                                            <Group variant="default">
                                                <ListItem
                                                    size="regular"
                                                    aria-current={false}
                                                    avatar={({ size }) => (
                                                        <SolidBank
                                                            size={size}
                                                            color="iconAccent2"
                                                        />
                                                    )}
                                                    primaryText={
                                                        <FormattedMessage
                                                            id="address_book.change_account.bank_transfers.primary"
                                                            defaultMessage="Bank recipient"
                                                        />
                                                    }
                                                    onClick={() => {
                                                        postUserEvent({
                                                            type: 'SendRecipientSelectedEvent',
                                                            installationId,
                                                            recipientType:
                                                                'bank',
                                                        })
                                                        onMsg({
                                                            type: 'on_bank_transfer_clicked',
                                                        })
                                                    }}
                                                />
                                            </Group>
                                        </Section>

                                        <ActiveAccountsSection
                                            accounts={active}
                                            listItem={({ account }) => (
                                                <UnlockedListItem
                                                    defaultCurrencyConfig={
                                                        defaultCurrencyConfig
                                                    }
                                                    installationId={
                                                        installationId
                                                    }
                                                    currencyHiddenMap={
                                                        currencyHiddenMap
                                                    }
                                                    selectionVariant="background_color"
                                                    key={account.address}
                                                    account={account}
                                                    selected={
                                                        toAddress ===
                                                        account.address
                                                    }
                                                    keyStore={getKeyStore({
                                                        keyStoreMap,
                                                        address:
                                                            account.address,
                                                    })}
                                                    portfolio={unsafe_GetPortfolioCache2(
                                                        {
                                                            address:
                                                                account.address,
                                                            portfolioMap,
                                                        }
                                                    )}
                                                    onMsg={(msg) => {
                                                        switch (msg.type) {
                                                            case 'account_item_clicked':
                                                                postUserEvent({
                                                                    type: 'SendRecipientSelectedEvent',
                                                                    installationId,
                                                                    recipientType:
                                                                        'wallet',
                                                                })
                                                                onMsg(msg)
                                                                break

                                                            /* istanbul ignore next */
                                                            default:
                                                                notReachable(
                                                                    msg.type
                                                                )
                                                        }
                                                    }}
                                                />
                                            )}
                                            onMsg={onMsg}
                                        />

                                        <TrackedAccountsSection
                                            accounts={tracked}
                                            listItem={({ account }) => (
                                                <UnlockedListItem
                                                    defaultCurrencyConfig={
                                                        defaultCurrencyConfig
                                                    }
                                                    installationId={
                                                        installationId
                                                    }
                                                    currencyHiddenMap={
                                                        currencyHiddenMap
                                                    }
                                                    selectionVariant="background_color"
                                                    key={account.address}
                                                    account={account}
                                                    selected={
                                                        toAddress ===
                                                        account.address
                                                    }
                                                    keyStore={getKeyStore({
                                                        keyStoreMap,
                                                        address:
                                                            account.address,
                                                    })}
                                                    portfolio={unsafe_GetPortfolioCache2(
                                                        {
                                                            address:
                                                                account.address,
                                                            portfolioMap,
                                                        }
                                                    )}
                                                    onMsg={(msg) => {
                                                        switch (msg.type) {
                                                            case 'account_item_clicked':
                                                                postUserEvent({
                                                                    type: 'SendRecipientSelectedEvent',
                                                                    installationId,
                                                                    recipientType:
                                                                        'wallet',
                                                                })
                                                                onMsg(msg)
                                                                break

                                                            /* istanbul ignore next */
                                                            default:
                                                                notReachable(
                                                                    msg.type
                                                                )
                                                        }
                                                    }}
                                                />
                                            )}
                                            onMsg={onMsg}
                                        />
                                    </Column>
                                )
                            }

                            /* istanbul ignore next */
                            default:
                                return notReachable(searchResult)
                        }
                    })()}
                </ScrollContainer>
                <Actions variant="default">
                    <CTA onMsg={onMsg} searchResult={searchResult} />
                </Actions>
            </Column>
        </Screen>
    )
}

const CTA = ({
    searchResult,
    onMsg,
}: {
    searchResult: ReturnType<typeof validateAccountSearch>
    onMsg: (msg: Msg) => void
}) => {
    switch (searchResult.type) {
        case 'accounts_not_found':
        case 'grouped_accounts':
            return null
        case 'accounts_not_found_search_valid_address':
            return (
                <Button
                    size="regular"
                    variant="primary"
                    onClick={() => {
                        onMsg({
                            type: 'on_continue_clicked',
                            address: searchResult.address,
                        })
                    }}
                >
                    <FormattedMessage
                        id="actions.continue"
                        defaultMessage="Continue"
                    />
                </Button>
            )

        /* istanbul ignore next */
        default:
            return notReachable(searchResult)
    }
}

const RightIcon = ({
    searchResult,
}: {
    searchResult: ReturnType<typeof validateAccountSearch>
}) => {
    switch (searchResult.type) {
        case 'accounts_not_found':
        case 'grouped_accounts':
            return null

        case 'accounts_not_found_search_valid_address':
            return <Checkbox color="iconAccent2" size={24} />

        /* istanbul ignore next */
        default:
            return notReachable(searchResult)
    }
}
