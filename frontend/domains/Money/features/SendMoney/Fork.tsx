import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import {
    CurrencyHiddenMap,
    CurrencyId,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { BankTransferFork2 } from '@zeal/domains/Currency/domains/BankTransfer/features/BankTransferFork2'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { SendERC20 } from '@zeal/domains/Money/features/SendERC20'
import { SendERC20v2 } from '@zeal/domains/Money/features/SendERC20v2'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { SelectWhereToSendMoney } from './SelectWhereToSendMoney'

type Props = {
    currencyId: CurrencyId | null
    portfolio: ServerPortfolio2
    installationId: string
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    fromAccount: Account
    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    currencyPinMap: CurrencyPinMap
    bankTransferInfo: BankTransferInfo
    counterparties: Counterparty[]
    installationCampaign: string | null
    isEthereumNetworkFeeWarningSeen: boolean

    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof SelectWhereToSendMoney>,
          | { type: 'track_wallet_clicked' }
          | { type: 'hardware_wallet_clicked' }
          | { type: 'add_wallet_clicked' }
          | { type: 'on_add_label_to_track_only_account_during_send' }
          | { type: 'on_account_create_request' }
          | { type: 'on_address_scanned_and_add_label' }
          | { type: 'on_ethereum_network_fee_warning_understand_clicked' }
      >
    | Extract<
          MsgOf<typeof BankTransferFork2>,
          | { type: 'on_account_create_request' }
          | { type: 'monerium_deposit_on_enable_card_clicked' }
          | { type: 'import_keys_button_clicked' }
          | { type: 'on_4337_auto_gas_token_selection_clicked' }
          | { type: 'on_4337_gas_currency_selected' }
          | { type: 'on_predefined_fee_preset_selected' }
          | { type: 'on_latest_bank_transfer_owner_found' }
          | { type: 'on_import_latest_bank_transfer_owner_clicked' }
          | { type: 'on_user_login_to_unblock_success' }
          | { type: 'kyc_applicant_created' }
          | { type: 'bank_transfer_owner_successfully_changed' }
          | { type: 'on_withdrawal_monitor_fiat_transaction_start' }
          | { type: 'on_withdrawal_monitor_fiat_transaction_success' }
          | { type: 'on_contact_support_clicked' }
          | { type: 'on_on_ramp_transfer_success_close_click' }
          | { type: 'on_activate_existing_monerium_account_click' }
          | { type: 'on_monerium_deposit_success_go_to_wallet_clicked' }
          | { type: 'on_monerium_sign_delay_relay_success_close_clicked' }
          | {
                type:
                    | 'on_monerium_order_status_changed'
                    | 'on_save_counterparty_form_submitted'
                    | 'on_delete_counterparty_submitted'
                    | 'on_delete_last_counterparty_submitted'
                    | 'monerium_on_card_disconnected'
                    | 'on_create_smart_wallet_clicked'
                    | 'on_card_import_on_import_keys_clicked'
                    | 'on_card_imported_success_animation_complete'
                    | 'on_onboarded_card_imported_success_animation_complete'
                    | 'on_kyc_data_updated_close_clicked'
                    | 'on_gnosis_pay_kyc_submitted_animation_complete'
                    | 'on_gnosis_pay_account_created'
                    | 'on_gnosis_pay_onboarding_flow_closed'
                    | 'on_card_disconnected'
            }
      >
    | Extract<
          MsgOf<typeof SendERC20>,
          | { type: 'track_wallet_clicked' }
          | { type: 'on_account_create_request' }
          | { type: 'on_accounts_create_success_animation_finished' }
          | { type: 'add_wallet_clicked' }
          | { type: 'hardware_wallet_clicked' }
          | { type: 'on_add_label_to_track_only_account_during_send' }
          | { type: 'on_4337_auto_gas_token_selection_clicked' }
          | { type: 'on_4337_gas_currency_selected' }
          | {
                type: 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
            }
          | { type: 'import_keys_button_clicked' }
          | { type: 'on_predefined_fee_preset_selected' }
          | { type: 'cancel_submitted' }
          | {
                type: 'on_transaction_completed_splash_animation_screen_competed'
            }
          | { type: 'transaction_request_replaced' }
          | { type: 'transaction_submited' }
          | { type: 'on_address_scanned_and_add_label' }
          | { type: 'on_ethereum_network_fee_warning_understand_clicked' }
      >
    | Extract<
          MsgOf<typeof SendERC20v2>,
          {
              type:
                  | 'internal_send_transaction_submitted'
                  | 'internal_send_transaction_closed'
          }
      >

type State =
    | {
          type: 'select_where_to_send_money'
      }
    | {
          type: 'bank_transfer'
      }
    | { type: 'send_to_wallet'; address: Web3.address.Address }

export const Fork = ({
    currencyId,
    portfolio,
    defaultCurrencyConfig,
    portfolioMap,
    installationCampaign,
    accountsMap,
    fromAccount,
    cardConfig,
    currencyHiddenMap,
    customCurrencies,
    installationId,
    bankTransferInfo,
    currencyPinMap,
    feePresetMap,
    gasCurrencyPresetMap,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    sessionPassword,
    counterparties,
    isEthereumNetworkFeeWarningSeen,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'select_where_to_send_money',
    })
    switch (state.type) {
        case 'select_where_to_send_money':
            return (
                <SelectWhereToSendMoney
                    fromAccount={fromAccount}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolioMap={portfolioMap}
                    accountsMap={accountsMap}
                    cardConfig={cardConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    customCurrencies={customCurrencies}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    toAddress={null}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'track_wallet_clicked':
                            case 'hardware_wallet_clicked':
                            case 'add_wallet_clicked':
                            case 'on_ethereum_network_fee_warning_understand_clicked':
                                onMsg(msg)
                                break

                            case 'on_accounts_create_success_animation_finished':
                                captureError(
                                    "we get on account success animation but we don't know what to do as we should skip animations"
                                )
                                break
                            case 'account_item_clicked':
                                setState({
                                    type: 'send_to_wallet',
                                    address: msg.account
                                        .address as Web3.address.Address,
                                })
                                break
                            case 'on_address_scanned':
                                setState({
                                    type: 'send_to_wallet',
                                    address:
                                        msg.address as Web3.address.Address,
                                })
                                break
                            case 'on_add_label_skipped':
                                setState({
                                    type: 'send_to_wallet',
                                    address:
                                        msg.address as Web3.address.Address,
                                })
                                break
                            case 'on_address_scanned_and_add_label':
                            case 'on_add_label_to_track_only_account_during_send':
                            case 'on_account_create_request':
                                onMsg(msg)
                                setState({
                                    type: 'send_to_wallet',
                                    address: msg.accountsWithKeystores[0]
                                        .account
                                        .address as Web3.address.Address,
                                })
                                break
                            case 'on_bank_transfer_clicked':
                                setState({ type: 'bank_transfer' })
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'bank_transfer':
            return (
                <BankTransferFork2
                    counterparties={counterparties}
                    installationCampaign={installationCampaign}
                    portfolio={portfolio}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolioMap={portfolioMap}
                    accountsMap={accountsMap}
                    cardConfig={cardConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    customCurrencies={customCurrencies}
                    installationId={installationId}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    bankTransferInfo={bankTransferInfo}
                    currencyPinMap={currencyPinMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    keystoreMap={keyStoreMap}
                    selectedAddress={
                        fromAccount.address as Web3.address.Address
                    }
                    variant={{ type: 'withdraw' }}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({ type: 'select_where_to_send_money' })
                                break
                            case 'on_account_create_request':
                            case 'monerium_deposit_on_enable_card_clicked':
                            case 'import_keys_button_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_import_latest_bank_transfer_owner_clicked':
                            case 'on_user_login_to_unblock_success':
                            case 'kyc_applicant_created':
                            case 'bank_transfer_owner_successfully_changed':
                            case 'on_withdrawal_monitor_fiat_transaction_start':
                            case 'on_withdrawal_monitor_fiat_transaction_success':
                            case 'on_contact_support_clicked':
                            case 'on_on_ramp_transfer_success_close_click':
                            case 'on_activate_existing_monerium_account_click':
                            case 'on_monerium_deposit_success_go_to_wallet_clicked':
                            case 'on_monerium_order_status_changed':
                            case 'on_save_counterparty_form_submitted':
                            case 'on_delete_counterparty_submitted':
                            case 'monerium_on_card_disconnected':
                            case 'on_create_smart_wallet_clicked':
                            case 'on_monerium_sign_delay_relay_success_close_clicked':
                            case 'on_card_import_on_import_keys_clicked':
                            case 'on_card_imported_success_animation_complete':
                            case 'on_onboarded_card_imported_success_animation_complete':
                            case 'on_kyc_data_updated_close_clicked':
                            case 'on_gnosis_pay_kyc_submitted_animation_complete':
                            case 'on_gnosis_pay_account_created':
                            case 'on_gnosis_pay_onboarding_flow_closed':
                            case 'on_card_disconnected':
                                onMsg(msg)
                                break

                            case 'on_delete_last_counterparty_submitted':
                                setState({ type: 'select_where_to_send_money' })
                                onMsg(msg)
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'send_to_wallet':
            const keyStore = getKeyStore({
                address: fromAccount.address,
                keyStoreMap,
            })
            const actionSource: ActionSource2 = {
                type: 'internal',
                transactionEventSource: 'send',
            }
            switch (keyStore.type) {
                case 'private_key_store':
                case 'ledger':
                case 'secret_phrase_key':
                case 'trezor':
                case 'track_only':
                    return (
                        <SendERC20
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            portfolioMap={portfolioMap}
                            account={fromAccount}
                            accountsMap={accountsMap}
                            cardConfig={cardConfig}
                            currencyHiddenMap={currencyHiddenMap}
                            customCurrencies={customCurrencies}
                            installationId={installationId}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            sessionPassword={sessionPassword}
                            currencyPinMap={currencyPinMap}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            keyStoreMap={keyStoreMap}
                            currencyId={currencyId}
                            initialToAddress={state.address}
                            portfolio={portfolio}
                            actionSource={actionSource}
                            isEthereumNetworkFeeWarningSeen={
                                isEthereumNetworkFeeWarningSeen
                            }
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'close':
                                        setState({
                                            type: 'select_where_to_send_money',
                                        })
                                        break
                                    case 'on_completed_safe_transaction_close_click':
                                    case 'on_transaction_cancelled_successfully_close_clicked':
                                    case 'on_completed_transaction_close_click':
                                        onMsg({
                                            type: 'close',
                                        })
                                        break
                                    case 'track_wallet_clicked':
                                    case 'on_account_create_request':
                                    case 'on_accounts_create_success_animation_finished':
                                    case 'add_wallet_clicked':
                                    case 'hardware_wallet_clicked':
                                    case 'on_add_label_to_track_only_account_during_send':
                                    case 'on_4337_auto_gas_token_selection_clicked':
                                    case 'on_4337_gas_currency_selected':
                                    case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                    case 'import_keys_button_clicked':
                                    case 'on_predefined_fee_preset_selected':
                                    case 'cancel_submitted':
                                    case 'on_transaction_completed_splash_animation_screen_competed':
                                    case 'transaction_request_replaced':
                                    case 'transaction_submited':
                                    case 'on_address_scanned_and_add_label':
                                    case 'on_ethereum_network_fee_warning_understand_clicked':
                                        onMsg(msg)
                                        break
                                    default:
                                        return notReachable(msg)
                                }
                            }}
                        />
                    )

                case 'safe_4337':
                    return (
                        <SendERC20v2
                            actionSource={actionSource}
                            fromCurrencyId={currencyId}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            portfolioMap={portfolioMap}
                            fromWallet={fromAccount}
                            accountsMap={accountsMap}
                            cardConfig={cardConfig}
                            currencyHiddenMap={currencyHiddenMap}
                            customCurrencies={customCurrencies}
                            installationId={installationId}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            sessionPassword={sessionPassword}
                            currencyPinMap={currencyPinMap}
                            presetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            keyStoreMap={keyStoreMap}
                            isEthereumNetworkFeeWarningSeen={
                                isEthereumNetworkFeeWarningSeen
                            }
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'close':
                                        // https://linear.app/zeal/issue/ZEAL-3447/cancel-button-closes-back-to-home-screen
                                        onMsg(msg)
                                        break
                                    case 'track_wallet_clicked':
                                    case 'on_account_create_request':
                                    case 'on_accounts_create_success_animation_finished':
                                    case 'add_wallet_clicked':
                                    case 'hardware_wallet_clicked':
                                    case 'on_add_label_to_track_only_account_during_send':
                                    case 'on_4337_auto_gas_token_selection_clicked':
                                    case 'on_4337_gas_currency_selected':
                                    case 'import_keys_button_clicked':
                                    case 'on_address_scanned_and_add_label':
                                    case 'on_ethereum_network_fee_warning_understand_clicked':
                                    case 'internal_send_transaction_submitted':
                                    case 'internal_send_transaction_closed':
                                        onMsg(msg)
                                        break

                                    default:
                                        return notReachable(msg)
                                }
                            }}
                            toAddress={state.address}
                        />
                    )

                default:
                    return notReachable(keyStore)
            }

        default:
            return notReachable(state)
    }
}
