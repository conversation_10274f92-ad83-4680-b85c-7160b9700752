import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'
import { Address } from '@zeal/toolkit/Web3/address'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import {
    CurrencyHiddenMap,
    CurrencyId,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { fetchServerPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import {
    BankTransferInfo,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Fork } from './Fork'

type Props = {
    currencyId: CurrencyId | null
    installationId: string
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    fromAddress: Web3.address.Address
    bankTransferInfo: BankTransferInfo
    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    currencyPinMap: CurrencyPinMap
    counterparties: Counterparty[]
    isEthereumNetworkFeeWarningSeen: boolean
    installationCampaign: string | null
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof Fork>
type Data = {
    serverPortfolio: ServerPortfolio2
}
const fetch = async ({
    address,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    installationId,
    currencyHiddenMap,
    signal,
}: {
    signal?: AbortSignal
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    address: Address
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
}): Promise<Data> => {
    const [serverPortfolio] = await Promise.all([
        fetchServerPortfolio2({
            address,
            defaultCurrencyConfig,
            networkMap,
            currencyHiddenMap,
            signal,
            networkRPCMap,
            installationId,
        }),
    ])

    return { serverPortfolio }
}
const findAccount = (
    address: Web3.address.Address,
    accountsMap: AccountsMap
): Account => {
    const account = accountsMap[address]
    if (!account) {
        throw new ImperativeError(`[Send Money] account not found ${address}`)
    }
    return account
}

export const SendMoney = ({
    defaultCurrencyConfig,
    portfolioMap,
    cardConfig,
    currencyHiddenMap,
    customCurrencies,
    networkMap,
    networkRPCMap,
    onMsg,
    sessionPassword,
    currencyPinMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationCampaign,
    accountsMap,
    installationId,
    keyStoreMap,
    bankTransferInfo,
    currencyId,
    fromAddress,
    isEthereumNetworkFeeWarningSeen,
    counterparties,
}: Props) => {
    const fromAccount = findAccount(fromAddress, accountsMap)
    const initialPortfolio = unsafe_GetPortfolioCache2({
        portfolioMap,
        address: fromAddress,
    })
    const [loadable, setLoadable] = useReloadableData(
        fetch,
        initialPortfolio
            ? {
                  type: 'reloading',
                  params: {
                      defaultCurrencyConfig,
                      address: fromAddress,
                      networkMap,
                      currencyHiddenMap,
                      networkRPCMap,
                      installationId,
                  },
                  data: {
                      serverPortfolio: initialPortfolio,
                  },
              }
            : {
                  type: 'loading',
                  params: {
                      defaultCurrencyConfig,
                      address: fromAddress,
                      currencyHiddenMap,
                      networkMap,
                      networkRPCMap,
                      installationId,
                  },
              }
    )

    switch (loadable.type) {
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />

                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break

                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loading':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed': {
            return (
                <Fork
                    accountsMap={accountsMap}
                    cardConfig={cardConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    installationCampaign={installationCampaign}
                    currencyPinMap={currencyPinMap}
                    customCurrencies={customCurrencies}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    currencyId={currencyId}
                    bankTransferInfo={bankTransferInfo}
                    fromAccount={fromAccount}
                    portfolio={loadable.data.serverPortfolio}
                    onMsg={onMsg}
                    counterparties={counterparties}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                />
            )
        }

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
