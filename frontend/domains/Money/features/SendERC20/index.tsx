import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { isNonEmptyArray, NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyId,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { Token2 } from '@zeal/domains/Token'
import { filterByHideMap } from '@zeal/domains/Token/helpers/filterByHideMap'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { fetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { fetchTransactionResultByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'

import { EmptyPortfolio } from './EmptyPortfolio'
import { Form } from './Form'
import { Form as FormType } from './Form/validation'

type Props = {
    portfolio: ServerPortfolio2
    currencyId: CurrencyId | null
    initialToAddress: Web3.address.Address | null
    account: Account
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    customCurrencies: CustomCurrencyMap
    cardConfig: CardConfig
    actionSource: ActionSource2
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    isEthereumNetworkFeeWarningSeen: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Form>,
          {
              type:
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_address_scanned_and_add_label'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
          }
      >
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'transaction_submited'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'on_safe_transaction_completed_splash_animation_screen_competed'
                  | 'on_transaction_relayed'
                  | 'on_gas_currency_selected'
                  | 'transaction_request_replaced'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_completed_transaction_close_click'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_transaction_cancelled_successfully_close_clicked'
          }
      >

const getCurrencyWithHighestBalance = ({
    portfolioTokens,
}: {
    portfolioTokens: NonEmptyArray<Token2>
}): CryptoCurrency => {
    const maxToken = portfolioTokens.reduce((max, token) =>
        (token.priceInDefaultCurrency || 0n) >
        (max.priceInDefaultCurrency || 0n)
            ? token
            : max
    )

    return maxToken.balance.currency
}

const calculateCurrency2 = ({
    currencyId,
    portfolioTokens,
}: {
    currencyId: CurrencyId | null
    portfolioTokens: NonEmptyArray<Token2>
}): CryptoCurrency => {
    if (currencyId) {
        const token = portfolioTokens.find(
            (token) => token.balance.currency.id === currencyId
        )
        return token
            ? token.balance.currency
            : getCurrencyWithHighestBalance({
                  portfolioTokens,
              })
    }

    return getCurrencyWithHighestBalance({
        portfolioTokens,
    })
}

const calculateState = ({
    portfolio,
    currencyId,
    initialToAddress,
    currencyHiddenMap,
}: {
    portfolio: ServerPortfolio2
    currencyId: CurrencyId | null
    initialToAddress: Web3.address.Address | null
    currencyHiddenMap: CurrencyHiddenMap
}): State => {
    const tokens = portfolio.tokens.filter(filterByHideMap(currencyHiddenMap))
    if (isNonEmptyArray(tokens)) {
        return {
            type: 'form',
            form: {
                type: 'amount_in_crypto',
                cryptoCurrency: calculateCurrency2({
                    currencyId,
                    portfolioTokens: tokens,
                }),
                amount: null,
                toAddress: initialToAddress,
            },
        }
    }

    return { type: 'empty_portfolio' }
}

type State =
    | { type: 'empty_portfolio' }
    | { type: 'form'; form: FormType }
    | {
          type: 'submit_transaction'
          ethTransaction: EthSendTransaction
          network: Network
          form: FormType
      }

export const SendERC20 = ({
    account,
    portfolioMap,
    currencyId,
    accountsMap,
    sessionPassword,
    customCurrencies,
    onMsg,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    feePresetMap,
    currencyHiddenMap,
    currencyPinMap,
    portfolio,
    cardConfig,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
    initialToAddress,
    isEthereumNetworkFeeWarningSeen,
    actionSource,
}: Props) => {
    const [state, setState] = useState<State>(() =>
        calculateState({
            portfolio,
            currencyId,
            initialToAddress,
            currencyHiddenMap,
        })
    )

    switch (state.type) {
        case 'empty_portfolio':
            return <EmptyPortfolio onMsg={onMsg} />
        case 'form':
            return (
                <Form
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationId={installationId}
                    cardConfig={cardConfig}
                    initialForm={state.form}
                    portfolio={portfolio}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    customCurrencies={customCurrencies}
                    sessionPassword={sessionPassword}
                    portfolioMap={portfolioMap}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    account={account}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_submit_form':
                                setState({
                                    type: 'submit_transaction',
                                    ethTransaction: msg.request,
                                    network: msg.network,
                                    form: msg.form,
                                })
                                break
                            case 'close':
                            case 'on_add_label_to_track_only_account_during_send':
                            case 'on_account_create_request':
                            case 'on_accounts_create_success_animation_finished':
                            case 'track_wallet_clicked':
                            case 'add_wallet_clicked':
                            case 'hardware_wallet_clicked':
                            case 'on_address_scanned_and_add_label':
                            case 'on_ethereum_network_fee_warning_understand_clicked':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit_transaction':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={portfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    fetchSimulationByRequest={fetchSimulationByRequest}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    installationId={installationId}
                    accounts={accountsMap}
                    keystores={keyStoreMap}
                    state={{ type: 'maximised' }}
                    network={state.network}
                    networkRPCMap={networkRPCMap}
                    account={account}
                    sendTransactionRequests={[state.ethTransaction]}
                    sessionPassword={sessionPassword}
                    actionSource={actionSource}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_sign_cancel_button_clicked':
                            case 'transaction_failure_accepted':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_minimize_click':
                            case 'on_close_transaction_status_not_found_modal':
                                setState({
                                    type: 'form',
                                    form: state.form,
                                })
                                break

                            case 'on_user_operation_bundled':
                            case 'on_expand_request':
                            case 'drag':
                                break

                            case 'on_completed_transaction_close_click':
                            case 'on_completed_safe_transaction_close_click':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'import_keys_button_clicked':
                            case 'transaction_submited':
                            case 'cancel_submitted':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'transaction_request_replaced':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
