import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { LoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import {
    failure,
    required,
    RequiredError,
    Result,
    shape,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { CryptoCurrency, FiatCurrency } from '@zeal/domains/Currency'
import { FXRate2 } from '@zeal/domains/FXRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { revert2 } from '@zeal/domains/FXRate/helpers/revert'
import { CryptoMoney } from '@zeal/domains/Money'
import { Network, NetworkMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'

export type Form =
    | {
          type: 'amount_in_crypto'
          cryptoCurrency: CryptoCurrency
          amount: string | null
          toAddress: Web3.address.Address | null
      }
    | {
          type: 'amount_in_default_currency'
          cryptoCurrency: CryptoCurrency
          amount: string | null
          toAddress: Web3.address.Address | null
      }

export type MaxBalanceLoadable = LoadableData<
    CryptoMoney,
    {
        cryptoCurrency: CryptoCurrency
    }
>

export type Pollable = LoadedPollableData<
    FXRate2<CryptoCurrency, FiatCurrency> | null,
    {
        form: Form
        networkMap: NetworkMap
        signal?: AbortSignal
    }
>

type BalanceError = {
    type: 'not_enough_tokens'
}

type ToAddressError = RequiredError

type TokenError = RequiredError

type FormErrors = {
    amount?: BalanceError
    submit?:
        | BalanceError
        | { type: 'zero_amount' }
        | TokenError
        | ToAddressError
}

export const getMaxBalance = ({
    maxBalanceLoadable,
    portfolio,
}: {
    maxBalanceLoadable: MaxBalanceLoadable
    portfolio: ServerPortfolio2
}) => {
    switch (maxBalanceLoadable.type) {
        case 'loading':
        case 'error':
            const token = getTokenByCryptoCurrency3({
                currency: maxBalanceLoadable.params.cryptoCurrency,
                serverPortfolio: portfolio,
            })
            return token.balance
        case 'loaded':
            return maxBalanceLoadable.data
        /* istanbul ignore next */
        default:
            return notReachable(maxBalanceLoadable)
    }
}

const validateCryptoAmountBalance = ({
    portfolio,
    pollable,
    maxBalanceLoadable,
}: {
    pollable: Pollable
    maxBalanceLoadable: MaxBalanceLoadable
    portfolio: ServerPortfolio2
}): Result<BalanceError, CryptoMoney> => {
    const form = pollable.params.form

    switch (form.type) {
        case 'amount_in_crypto': {
            const cryptoCurrency = form.cryptoCurrency

            const inputAmount = fromFixedWithFraction(
                form.amount,
                cryptoCurrency.fraction
            )

            const balance = getMaxBalance({
                maxBalanceLoadable,
                portfolio,
            })

            return inputAmount <= balance.amount
                ? success({ amount: inputAmount, currency: cryptoCurrency })
                : failure({ type: 'not_enough_tokens' })
        }

        case 'amount_in_default_currency': {
            const rate = pollable.data
            const cryptoCurrency = pollable.params.form.cryptoCurrency

            if (!rate) {
                return success({
                    amount: 0n,
                    currency: cryptoCurrency,
                })
            }

            const defaultCurrency = rate.quote

            const amount = fromFixedWithFraction(
                pollable.params.form.amount,
                defaultCurrency.fraction
            )

            const reverted = revert2({ rate, extraPrecision: 0 })

            const cryptoAmount = applyRate2({
                baseAmount: { amount, currency: defaultCurrency },
                rate: reverted,
            })

            const balance = getMaxBalance({
                maxBalanceLoadable,
                portfolio,
            })

            return cryptoAmount.amount <= balance.amount
                ? success(cryptoAmount)
                : failure({ type: 'not_enough_tokens' })
        }
        /* istanbul ignore next */
        default:
            return notReachable(form)
    }
}

const validateZeroAmount = (
    amount: CryptoMoney
): Result<{ type: 'zero_amount' }, CryptoMoney> =>
    amount.amount > 0n ? success(amount) : failure({ type: 'zero_amount' })

export const validate = ({
    account,
    networkMap,
    pollable,
    portfolio,
    maxBalanceLoadable,
}: {
    pollable: Pollable
    maxBalanceLoadable: MaxBalanceLoadable
    portfolio: ServerPortfolio2
    account: Account
    networkMap: NetworkMap
}): Result<
    FormErrors,
    {
        request: EthSendTransaction
        network: Network
        form: Form
    }
> => {
    return shape({
        amount: validateCryptoAmountBalance({
            pollable,
            maxBalanceLoadable,
            portfolio,
        }),
        submit: validateCryptoAmountBalance({
            pollable,
            maxBalanceLoadable,
            portfolio,
        }).andThen((cryptoAmount) =>
            validateZeroAmount(cryptoAmount).andThen(() =>
                required(pollable.params.form.toAddress).map((toAddress) => ({
                    toAddress,
                    cryptoAmount,
                }))
            )
        ),
    }).map(({ submit: { cryptoAmount, toAddress } }) => {
        const network = findNetworkByHexChainId(
            cryptoAmount.currency.networkHexChainId,
            networkMap
        )

        const request = createTransferEthSendTransaction({
            amount: cryptoAmount,
            from: account.address,
            network,
            to: toAddress,
        })

        return {
            network,
            request,
            form: pollable.params.form,
        }
    })
}
