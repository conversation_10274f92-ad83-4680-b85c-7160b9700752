import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { But<PERSON> } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { CustomAccountAddress } from '@zeal/uikit/Icon/CustomAccountAddress'
import { ForwardIcon } from '@zeal/uikit/Icon/ForwardIcon'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { ListItemButton } from '@zeal/uikit/ListItem'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { NullableListItemButton } from '@zeal/domains/Account/components/NullableListItemButton'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { Network, NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'

import { SecondaryAmountButton } from './SecondaryAmountButton'

import {
    Form,
    getMaxBalance,
    MaxBalanceLoadable,
    Pollable,
    validate,
} from '../validation'

type Props = {
    accountsMap: AccountsMap
    pollable: Pollable
    networkMap: NetworkMap
    account: Account
    portfolio: ServerPortfolio2
    maxBalanceLoadable: MaxBalanceLoadable
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_select_token' }
    | { type: 'on_select_to_address' }
    | {
          type: 'on_submit_form'
          request: EthSendTransaction
          network: Network
          form: Form
      }
    | {
          type: 'on_amount_change'
          amount: string | null
      }
    | MsgOf<typeof SecondaryAmountButton>

const getPrefix = (pollable: Pollable): string => {
    const form = pollable.params.form
    switch (form.type) {
        case 'amount_in_crypto':
            return ''
        case 'amount_in_default_currency':
            return pollable.data?.quote.symbol || ''
        /* istanbul ignore next */
        default:
            return notReachable(form)
    }
}

export const Layout = ({
    pollable,
    networkMap,
    account,
    accountsMap,
    portfolio,
    maxBalanceLoadable,
    installationId,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    const validationResult = validate({
        pollable,
        portfolio,
        networkMap,
        account,
        maxBalanceLoadable,
    })

    const errors = validationResult.getFailureReason() || {}

    const prefix = getPrefix(pollable)

    const onSubmit = () => {
        validationResult.tap(({ network, request, form }) =>
            onMsg({
                type: 'on_submit_form',
                request,
                network,
                form,
            })
        )
    }

    const { form } = pollable.params

    const { cryptoCurrency } = form

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <UIActionBar
                top={<ActionBarAccountIndicator account={account} />}
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="textPrimary"
                            >
                                <FormattedMessage
                                    id="send_token.form.title"
                                    defaultMessage="Send"
                                />
                            </Text>
                        </Row>
                    </Clickable>
                }
            />

            <Column spacing={16} shrink alignY="stretch">
                <ScrollContainer withFloatingActions={false}>
                    <Column spacing={4}>
                        <AmountInput
                            label={cryptoCurrency.code}
                            content={{
                                topLeft: (
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => {
                                            onMsg({
                                                type: 'on_select_token',
                                            })
                                        }}
                                    >
                                        {({ color }) => (
                                            <Row spacing={4}>
                                                <CurrencyAvatar
                                                    key={cryptoCurrency.id}
                                                    currency={cryptoCurrency}
                                                    size={24}
                                                    rightBadge={({ size }) => (
                                                        <Badge
                                                            size={size}
                                                            network={findNetworkByHexChainId(
                                                                cryptoCurrency.networkHexChainId,
                                                                networkMap
                                                            )}
                                                        />
                                                    )}
                                                />
                                                <Text
                                                    variant="title3"
                                                    color="textPrimary"
                                                    weight="medium"
                                                >
                                                    {cryptoCurrency.code}
                                                </Text>
                                                <LightArrowDown2
                                                    size={18}
                                                    color={color}
                                                />
                                            </Row>
                                        )}
                                    </IconButton>
                                ),
                                topRight: ({ onBlur, onFocus }) => (
                                    <AmountInput.Input
                                        onFocus={onFocus}
                                        onBlur={onBlur}
                                        label={formatMessage({
                                            id: 'send_token.form.send-amount',
                                            defaultMessage: 'Send amount',
                                        })}
                                        fraction={cryptoCurrency.fraction}
                                        autoFocus
                                        readOnly={false}
                                        prefix={prefix}
                                        amount={form.amount}
                                        onChange={(value) => {
                                            onMsg({
                                                type: 'on_amount_change',
                                                amount: value,
                                            })
                                        }}
                                        onSubmitEditing={onSubmit}
                                    />
                                ),
                                bottomLeft: (
                                    <MaxButton
                                        installationId={installationId}
                                        location="send"
                                        balance={getMaxBalance({
                                            maxBalanceLoadable,
                                            portfolio,
                                        })}
                                        onMsg={onMsg}
                                        state={(() => {
                                            switch (maxBalanceLoadable.type) {
                                                case 'loading':
                                                    return 'loading'
                                                case 'error':
                                                case 'loaded':
                                                    return errors.amount
                                                        ? 'error'
                                                        : 'normal'
                                                default:
                                                    return notReachable(
                                                        maxBalanceLoadable
                                                    )
                                            }
                                        })()}
                                    />
                                ),
                                bottomRight: (
                                    <SecondaryAmountButton
                                        pollable={pollable}
                                        onMsg={onMsg}
                                    />
                                ),
                            }}
                            state={errors.amount ? 'error' : 'normal'}
                        />

                        <NextStepSeparator />

                        {form.toAddress ? (
                            <NullableListItemButton
                                disabled={false}
                                address={form.toAddress}
                                accountsMap={accountsMap}
                                onClick={() =>
                                    onMsg({ type: 'on_select_to_address' })
                                }
                            />
                        ) : (
                            <ListItemButton
                                variant="default"
                                disabled={false}
                                background="surface"
                                aria-current={false}
                                avatar={({ size }) => (
                                    <Avatar
                                        size={size}
                                        border="borderSecondary"
                                    >
                                        <CustomAccountAddress
                                            size={24}
                                            color="iconDefault"
                                        />
                                    </Avatar>
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="send_token.form.select-address"
                                        defaultMessage="Select address"
                                    />
                                }
                                side={{
                                    rightIcon: ({ size }) => (
                                        <ForwardIcon
                                            size={size}
                                            color="iconDefault"
                                        />
                                    ),
                                }}
                                onClick={() =>
                                    onMsg({
                                        type: 'on_select_to_address',
                                    })
                                }
                            />
                        )}
                    </Column>
                </ScrollContainer>

                <Actions variant="default">
                    <Button
                        size="regular"
                        variant="primary"
                        disabled={!!errors.submit}
                        onClick={onSubmit}
                    >
                        <FormattedMessage
                            id="actions.continue"
                            defaultMessage="Continue"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
