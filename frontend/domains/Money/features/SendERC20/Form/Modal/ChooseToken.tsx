import { FormattedMessage } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { SelectToken } from '@zeal/domains/Token/components/SelectToken'

type Props = {
    portfolio: ServerPortfolio2
    networkMap: NetworkMap
    selectedCurrency: CryptoCurrency | null
    account: Account
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    onMsg: (msg: Msg) => void
}
type Msg =
    | { type: 'close' }
    | { type: 'on_currency_selected'; currency: CryptoCurrency }

export const ChooseToken = ({
    portfolio,
    selectedCurrency,
    account,
    networkMap,
    currencyHiddenMap,
    currencyPinMap,
    onMsg,
}: Props) => {
    const cryptoCurrencies: CryptoCurrency[] = portfolio.tokens.map(
        (token) => token.balance.currency
    )

    return (
        <Screen
            background="light"
            padding="form"
            aria-labelledby="choose-tokens-label"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <UIActionBar
                top={<ActionBarAccountIndicator account={account} />}
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="textPrimary"
                                id="choose-tokens-label"
                            >
                                <FormattedMessage
                                    id="SendERC20.tokens"
                                    defaultMessage="Tokens"
                                />
                            </Text>
                        </Row>
                    </Clickable>
                }
            />

            <Column shrink spacing={16} fill>
                <SelectToken
                    cryptoCurrencies={cryptoCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    networkMap={networkMap}
                    serverPortfolio={portfolio}
                    selectedCurrency={selectedCurrency}
                    onCryptoCurrencySelected={(currency) =>
                        onMsg({
                            type: 'on_currency_selected',
                            currency,
                        })
                    }
                />
            </Column>
        </Screen>
    )
}
