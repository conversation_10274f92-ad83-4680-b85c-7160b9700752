import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { SelectToAddress } from '@zeal/domains/Account/features/SelectToAddress'
import { CardConfig } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { ChooseToken } from './ChooseToken'

type Props = {
    account: Account
    accountsMap: AccountsMap
    cardConfig: CardConfig
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    customCurrencies: CustomCurrencyMap
    installationId: string
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    portfolio: ServerPortfolio2
    portfolioMap: PortfolioMap
    selectedCurrency: CryptoCurrency | null
    sessionPassword: string
    state: State
    toAddress: Web3.address.Address | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    isEthereumNetworkFeeWarningSeen: boolean
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof ChooseToken> | MsgOf<typeof SelectToAddress>

export type State =
    | { type: 'closed' }
    | { type: 'select_token' }
    | { type: 'select_to_address' }

export const Modal = ({
    account,
    accountsMap,
    cardConfig,
    currencyHiddenMap,
    currencyPinMap,
    customCurrencies,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    onMsg,
    portfolio,
    portfolioMap,
    selectedCurrency,
    sessionPassword,
    state,
    toAddress,
    isEthereumNetworkFeeWarningSeen,
    defaultCurrencyConfig,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'select_token':
            return (
                <UIModal>
                    <ChooseToken
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        networkMap={networkMap}
                        portfolio={portfolio}
                        account={account}
                        selectedCurrency={selectedCurrency}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'select_to_address':
            return (
                <UIModal>
                    <SelectToAddress
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        customCurrencies={customCurrencies}
                        sessionPassword={sessionPassword}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        toAddress={toAddress}
                        accountsMap={accountsMap}
                        keyStoreMap={keyStoreMap}
                        portfolioMap={portfolioMap}
                        cardConfig={cardConfig}
                        account={account}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
