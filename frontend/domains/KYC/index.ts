export type SumSubAccessToken = string

export type SumSubStatus =
    | 'ActionCompleted'
    | 'Approved'
    | 'Failed'
    | 'FinallyRejected'
    | 'Incomplete'
    | 'Initial'
    | 'NetworkError'
    | 'Pending'
    | 'Ready'
    | 'TemporarilyDeclined'

export type SumSubStatusChangeEvent = {
    prevStatus: SumSubStatus
    newStatus: SumSubStatus
}

export type SumSubEvent =
    | {
          eventType: 'ApplicantLoaded'
          payload: { applicantId: string }
      }
    | {
          eventType: 'StepInitiated'
          payload: { idDocSetType: string }
      }
    | {
          eventType: 'StepCompleted'
          payload: { idDocSetType: string; isCancelled: boolean }
      }
    | {
          eventType: 'Analytics'
          payload: { eventName: string; eventPayload: object }
      }

export type SumSubWebSdkMsg =
    | 'idCheck.actionCompleted'
    | 'idCheck.applicantReviewComplete'
    | 'idCheck.applicantStatus'
    | 'idCheck.livenessCompleted'
    | 'idCheck.moduleResultPresented'
    | 'idCheck.onActionSubmitted'
    | 'idCheck.onApplicantLoaded'
    | 'idCheck.onApplicantResubmitted'
    | 'idCheck.onApplicantStatusChanged'
    | 'idCheck.onApplicantSubmitted'
    | 'idCheck.onError'
    | 'idCheck.onInitialized'
    | 'idCheck.onReady'
    | 'idCheck.onResize'
    | 'idCheck.onStepCompleted'
    | 'idCheck.onStepInitiated'
    | 'idCheck.onUploadError'
    | 'idCheck.onUploadWarning'
    | 'idCheck.onUserAction'
    | 'idCheck.onVideoIdentCallStarted'
    | 'idCheck.onVideoIdentCompleted'
    | 'idCheck.onVideoIdentModeratorJoined'
    | 'idCheck.stepCompleted'
    | 'idCheck.onApplicantActionLoaded'
    | 'idCheck.onApplicantActionSubmitted'
    | 'idCheck.onApplicantActionCompleted'
    | 'idCheck.onNavigationUiControlsStateChanged'
    | 'idCheck.onLanguageChanged'
    | 'idCheck.restoreScrollPosition'
