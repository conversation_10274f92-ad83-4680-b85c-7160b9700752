import * as Web3 from '@zeal/toolkit/Web3'

import { requestGetContractCode } from '@zeal/domains/RPCRequest/api/fetchIsContractDeployed'
import { Request } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'

import {
    P2pReceiverTypeCheckFailed,
    P2pReceiverTypeCheckPassed,
} from '../SafetyCheck'

export const createP2PReceiverSafetyCheckRequest = ({
    receiverAddress,
}: {
    receiverAddress: Web3.address.Address
}): Request<P2pReceiverTypeCheckPassed | P2pReceiverTypeCheckFailed> => {
    const { parser, request } = requestGetContractCode({
        address: receiverAddress,
    })

    return {
        request,
        parser: (input) =>
            parser(input)
                ? {
                      type: 'P2pReceiverTypeCheck',
                      severity: 'Danger',
                      state: 'Failed',
                  }
                : {
                      type: 'P2pReceiverTypeCheck',
                      severity: 'Danger',
                      state: 'Passed',
                  },
    }
}
