import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { requestGetContractCode } from '@zeal/domains/RPCRequest/api/fetchIsContractDeployed'
import { Request } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'

import {
    ApprovalSpenderTypeCheckFailed,
    ApprovalSpenderTypeCheckPassed,
} from '../SafetyCheck'

const SAFE_CODE_VERSION_1_3: Hexadecimal.Hexadecimal =
    '0x608060405273ffffffffffffffffffffffffffffffffffffffff600054167fa619486e0000000000000000000000000000000000000000000000000000000060003514156050578060005260206000f35b3660008037600080366000845af43d6000803e60008114156070573d6000fd5b3d6000f3fea2646970667358221220d1429297349653a4918076d650332de1a1068c5f3e07c5c82360c277770b955264736f6c63430007060033'
const SAFE_CODE_VERSION_1_4: Hexadecimal.Hexadecimal =
    '0x608060405273ffffffffffffffffffffffffffffffffffffffff600054167fa619486e0000000000000000000000000000000000000000000000000000000060003514156050578060005260206000f35b3660008037600080366000845af43d6000803e60008114156070573d6000fd5b3d6000f3fea264697066735822122003d1488ee65e08fa41e58e888a9865554c535f2c77126a82cb4c0f917f31441364736f6c63430007060033'

export const createApprovalSpenderSafetyCheckRequest = ({
    spenderAddress,
}: {
    spenderAddress: Web3.address.Address
}): Request<
    ApprovalSpenderTypeCheckPassed | ApprovalSpenderTypeCheckFailed
> => {
    const { parser, request } = requestGetContractCode({
        address: spenderAddress,
    })

    return {
        request,
        parser: (input) => {
            const code = parser(input)
            switch (code) {
                case SAFE_CODE_VERSION_1_3:
                case SAFE_CODE_VERSION_1_4:
                case null:
                    return {
                        type: 'ApprovalSpenderTypeCheck',
                        severity: 'Caution',
                        state: 'Failed',
                    }

                default:
                    return {
                        type: 'ApprovalSpenderTypeCheck',
                        severity: 'Caution',
                        state: 'Passed',
                    }
            }
        },
    }
}
