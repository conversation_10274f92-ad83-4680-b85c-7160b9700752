import { post } from '@zeal/api/requestBackend'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { failure, object, string, success } from '@zeal/toolkit/Result'

import { DAppSiteInfo } from '@zeal/domains/DApp'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { PredefinedNetwork } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { InitialUserOperation } from '@zeal/domains/UserOperation'

import { TransactionSafetyCheck } from '../SafetyCheck'

const BLOCKAID_NETWORK: Record<PredefinedNetwork['name'], string | null> = {
    Arbitrum: 'arbitrum',
    Avalanche: 'avalanche',
    BSC: 'bsc',
    Base: 'base',
    Blast: 'blast',
    Ethereum: 'ethereum',
    Gnosis: 'gnosis',
    Linea: 'linea',
    Optimism: 'optimism',
    Polygon: 'polygon',
    zkSync: 'zksync',

    Aurora: null,
    Celo: null,
    Cronos: null,
    Fantom: null,
    Manta: null,
    Mantle: null,
    OPBNB: null,
    PolygonZkevm: null,
}

const BLOCK_AID_RESULT_TYPE: Record<
    'Malicious' | 'Benign' | 'Warning' | 'Error',
    true
> = {
    Malicious: true,
    Benign: true,
    Warning: true,
    Error: true,
}

export const fetchBlockaidTransactionChecks = async ({
    requestToCheck,
    network,
    dApp,
    signal,
}: {
    requestToCheck:
        | { type: 'rpc_request'; rpcRequest: EthSendTransaction }
        | { type: 'user_operation'; initialUserOperation: InitialUserOperation }
    network: PredefinedNetwork
    dApp: DAppSiteInfo | null
    signal?: AbortSignal
}): Promise<TransactionSafetyCheck[]> => {
    try {
        const blockAidNetwork = BLOCKAID_NETWORK[network.name]

        if (!blockAidNetwork) {
            return []
        }

        const trx = (() => {
            switch (requestToCheck.type) {
                case 'rpc_request':
                    return requestToCheck.rpcRequest.params[0]
                case 'user_operation':
                    const { callData, entrypoint, sender } =
                        requestToCheck.initialUserOperation
                    return {
                        data: callData,
                        from: entrypoint,
                        to: sender,
                    }

                default:
                    return notReachable(requestToCheck)
            }
        })()

        const responseRaw = string(
            await post(
                `/proxy/ba/${blockAidNetwork}/v0/validate/transaction`,
                {
                    body: {
                        options: ['validation'],
                        metadata: dApp
                            ? { domain: dApp.hostname }
                            : { non_dapp: true },
                        data: {
                            from: trx.from,
                            data: trx.data,
                            value: trx.value,
                            to: trx.to,
                        },
                    },
                },
                signal
            )
        )
            .andThen(parseJSON)
            .andThen(object)
            .getSuccessResultOrThrow(
                'Failed to parse JSON response from blockaid transaction validation'
            )

        const resultType = object(responseRaw.validation)
            .andThen((obj) => string(obj.result_type))
            .andThen((resultType) =>
                BLOCK_AID_RESULT_TYPE[
                    resultType as keyof typeof BLOCK_AID_RESULT_TYPE
                ]
                    ? success(resultType as keyof typeof BLOCK_AID_RESULT_TYPE)
                    : failure({
                          type: 'unknown_blockaid_result_type',
                          value: resultType,
                      })
            )
            .getSuccessResultOrThrow('Failed to blockaid safety checks')

        switch (resultType) {
            case 'Benign':
            case 'Warning':
                return [
                    {
                        type: 'SmartContractBlacklistCheck',
                        severity: 'Danger',
                        state: 'Passed',
                        checkSource: { source: 'BlockAid', url: null },
                    },
                ]
            case 'Malicious':
                return [
                    {
                        type: 'SmartContractBlacklistCheck',
                        severity: 'Danger',
                        state: 'Failed',
                        checkSource: { source: 'BlockAid', url: null },
                    },
                ]

            case 'Error':
                captureError(
                    new ImperativeError('Error result type from blockaid', {
                        responseRaw,
                        trx,
                    })
                )
                return []

            default:
                return notReachable(resultType)
        }
    } catch (error) {
        captureError(error)
        return []
    }
}
