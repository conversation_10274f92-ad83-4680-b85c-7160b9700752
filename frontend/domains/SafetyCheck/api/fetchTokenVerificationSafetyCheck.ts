import { fetchStaticData } from '@zeal/api/fetchStaticData'

import { arrayOf, string } from '@zeal/toolkit/Result'

import { CurrencyId } from '@zeal/domains/Currency'

import {
    TokenVerificationCheckFailed,
    TokenVerificationCheckPassed,
} from '../SafetyCheck'

export const fetchTokenVerificationSafetyCheck = async ({
    currencyIds,
}: {
    currencyIds: CurrencyId[]
}): Promise<
    (TokenVerificationCheckFailed | TokenVerificationCheckPassed)[]
> => {
    const verifiedCurrencyIds = await fetchStaticData(
        '@zeal/assets/data/coingeck-verified-currency-ids.json',
        (input) => arrayOf(input, string).map((arr) => new Set<CurrencyId>(arr))
    )

    return currencyIds.map((currencyId) => {
        return verifiedCurrencyIds.has(currencyId)
            ? {
                  type: 'TokenVerificationCheck',
                  severity: 'Caution',
                  state: 'Passed',
                  currencyId,
                  checkSource: {
                      source: 'CoinGecko',
                      url: null,
                  },
              }
            : {
                  type: 'TokenVerificationCheck',
                  severity: 'Caution',
                  state: 'Failed',
                  currencyId,
                  checkSource: {
                      source: 'CoinGecko',
                      url: null,
                  },
              }
    })
}
