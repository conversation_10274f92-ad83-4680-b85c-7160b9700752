import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { SignedUserOperationRequest } from '@zeal/domains/TransactionRequest'
import { submitUserOperationRequest } from '@zeal/domains/TransactionRequest/api/submitUserOperationRequest'
import {
    SubmittedUserOperationCompleted,
    SubmittedUserOperationFailed,
    SubmittedUserOperationRejected,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { FetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'

import { fetchCurrentEntrypointNonce } from './fetchCurrentEntrypointNonce'
import { fetchBiconomySponsorshipPaymasterAndData } from './fetchPaymasterAndData'
import { fetchUserOperationHash } from './fetchUserOperationHash'
import { monitorSubmittedUserOperation } from './monitorSubmittedUserOperation'
import { simulateSafeTransaction } from './simulateSafeTransaction'

import { signUserOperationHashWithLocalSigner } from '../helpers/signUserOperationHashWithLocalSigner'
import { signUserOperationHashWithPassKey } from '../helpers/signUserOperationHashWithPassKey'
import {
    InitialUserOperation,
    UserOperationWithoutSignature,
    UserOperationWithSignature,
} from '../UserOperation'

type Params = {
    rpcRequestsToBundle: EthSendTransaction[]
    fetchSimulationByRequest: FetchSimulationByRequest
    network: Network
    networkMap: NetworkMap
    keyStore: Safe4337
    account: Account
    networkRPCMap: NetworkRPCMap
    dApp: DAppSiteInfo | null
    portfolio: ServerPortfolio2 | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    sessionPassword: string
    installationId: string
    signal?: AbortSignal
}

export const submitAndMonitorSponsoredUserOperation = async ({
    account,
    dApp,
    defaultCurrencyConfig,
    fetchSimulationByRequest,
    installationId,
    keyStore,
    network,
    networkMap,
    networkRPCMap,
    portfolio,
    rpcRequestsToBundle,
    sessionPassword,
    signal,
}: Params): Promise<
    | SubmittedUserOperationCompleted
    | SubmittedUserOperationRejected
    | SubmittedUserOperationFailed
> => {
    const { feeForecast, userOperationRequest } = await simulateSafeTransaction(
        {
            account,
            dApp,
            defaultCurrencyConfig,
            fetchSimulationByRequest,
            keyStore,
            network,
            networkMap,
            networkRPCMap,
            portfolio,
            rpcRequestsToBundle,
            signal,
            sponsored: true,
        }
    )

    const nonce = await fetchCurrentEntrypointNonce({
        network: userOperationRequest.network,
        address: userOperationRequest.account.address as Web3.address.Address,
        entrypoint: userOperationRequest.entrypoint as Web3.address.Address,
        networkRPCMap,
        signal,
    })

    const selectedFee = (() => {
        switch (feeForecast.type) {
            case 'sponsored_transaction':
                return feeForecast.fee
            case 'non_sponsored_transaction':
                throw new ImperativeError(
                    'Non-sponsored fee forecast returned for sponsored transaction'
                )

            default:
                return notReachable(feeForecast)
        }
    })()

    const initialUserOperation: InitialUserOperation = {
        type: 'initial_user_operation',
        sender: userOperationRequest.account.address,
        nonce,
        entrypoint: userOperationRequest.entrypoint,
        initCode: userOperationRequest.initCode,
        callData: selectedFee.callData,
    }
    const paymasterAndData = await fetchBiconomySponsorshipPaymasterAndData({
        network: userOperationRequest.network,
        initialUserOperation,
        bundlerGasPrice: selectedFee.gasPrice,
        gasEstimate: selectedFee.gasEstimate,
        signal,
    })

    const userOperationWithoutSignature: UserOperationWithoutSignature = {
        type: 'user_operation_without_signature',
        callData: initialUserOperation.callData,
        sender: initialUserOperation.sender,
        nonce,
        entrypoint: initialUserOperation.entrypoint,
        initCode: initialUserOperation.initCode,

        maxFeePerGas: selectedFee.gasPrice.maxFeePerGas,
        maxPriorityFeePerGas: selectedFee.gasPrice.maxPriorityFeePerGas,

        callGasLimit: selectedFee.gasEstimate.callGasLimit,
        preVerificationGas: selectedFee.gasEstimate.preVerificationGas,
        verificationGasLimit: selectedFee.gasEstimate.verificationGasLimit,

        paymasterAndData,
    }

    const userOperationHash = await fetchUserOperationHash({
        network,
        networkRPCMap,
        userOperation: userOperationWithoutSignature,
        signal,
    })

    const signedUserOperationRequest: SignedUserOperationRequest =
        await (async () => {
            switch (userOperationRequest.type) {
                case 'simulated_safe_with_add_owner_user_operation_request':
                case 'simulated_safe_deployment_bundle_user_operation_request': {
                    const signature = await signUserOperationHashWithPassKey({
                        passkey: keyStore.safeDeplymentConfig.passkeyOwner,
                        userOperationHash,
                        sessionPassword,
                    })

                    const userOperationWithSignature: UserOperationWithSignature =
                        {
                            type: 'user_operation_with_signature',
                            callData: userOperationWithoutSignature.callData,
                            callGasLimit:
                                userOperationWithoutSignature.callGasLimit,
                            initCode: userOperationWithoutSignature.initCode,
                            maxFeePerGas:
                                userOperationWithoutSignature.maxFeePerGas,
                            maxPriorityFeePerGas:
                                userOperationWithoutSignature.maxPriorityFeePerGas,
                            nonce: userOperationWithoutSignature.nonce,
                            entrypoint:
                                userOperationWithoutSignature.entrypoint,
                            paymasterAndData:
                                userOperationWithoutSignature.paymasterAndData,
                            preVerificationGas:
                                userOperationWithoutSignature.preVerificationGas,
                            sender: userOperationWithoutSignature.sender,
                            verificationGasLimit:
                                userOperationWithoutSignature.verificationGasLimit,
                            signature,
                        }

                    return {
                        ...userOperationRequest,
                        type: 'signed_safe_user_operation_request',
                        userOperationWithSignature,
                    }
                }

                case 'simulated_safe_without_deployment_bundle_user_operation_request': {
                    const signature =
                        await signUserOperationHashWithLocalSigner({
                            keyStore,
                            network,
                            sessionPassword,
                            userOperationHash,
                            dApp,
                        })

                    const userOperationWithSignature: UserOperationWithSignature =
                        {
                            type: 'user_operation_with_signature',
                            callData: userOperationWithoutSignature.callData,
                            callGasLimit:
                                userOperationWithoutSignature.callGasLimit,
                            initCode: userOperationWithoutSignature.initCode,
                            maxFeePerGas:
                                userOperationWithoutSignature.maxFeePerGas,
                            maxPriorityFeePerGas:
                                userOperationWithoutSignature.maxPriorityFeePerGas,
                            nonce: userOperationWithoutSignature.nonce,
                            entrypoint:
                                userOperationWithoutSignature.entrypoint,
                            paymasterAndData:
                                userOperationWithoutSignature.paymasterAndData,
                            preVerificationGas:
                                userOperationWithoutSignature.preVerificationGas,
                            sender: userOperationWithoutSignature.sender,
                            verificationGasLimit:
                                userOperationWithoutSignature.verificationGasLimit,
                            signature,
                        }

                    return {
                        ...userOperationRequest,
                        type: 'signed_safe_user_operation_request',
                        userOperationWithSignature,
                    }
                }
                /* istanbul ignore next */
                default:
                    return notReachable(userOperationRequest)
            }
        })()

    const submittedToBundlerUserOperationRequest =
        await submitUserOperationRequest({
            installationId,
            userOperationRequest: signedUserOperationRequest,
        })

    return monitorSubmittedUserOperation({
        installationId,
        network,
        submittedUserOperation:
            submittedToBundlerUserOperationRequest.submittedUserOperation,
        signal,
    })
}
