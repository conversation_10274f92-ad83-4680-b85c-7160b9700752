import { get } from '@zeal/api/requestBackend'

import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import {
    number,
    object,
    recordStrict,
    Result,
    string,
} from '@zeal/toolkit/Result'

import { DefaultCurrency, FiatCurrency } from '@zeal/domains/Currency'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { FiatRatesMap, FXRate2 } from '..'

const parseBTCRates = (
    input: unknown
): Result<unknown, Record<string, number>> =>
    object(input)
        .andThen((obj) => object(obj.rates))
        .andThen((obj) =>
            recordStrict(obj, {
                keyParser: string,
                valueParser: (item) =>
                    object(item).andThen((item) => number(item.value)),
            })
        )

const fetchCoinGeckoBtcRates = async ({ signal }: { signal?: AbortSignal }) =>
    get('/proxy/cgv3/exchange_rates', {}, signal).then((data) =>
        parseBTCRates(data).getSuccessResultOrThrow('failed to parse BTC rates')
    )

export const fetchDefaultCurrencyRateFromUSD = async ({
    defaultCurrencyConfig,
    signal,
}: {
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<FXRate2<FiatCurrency, DefaultCurrency> | null> => {
    const { defaultCurrency } = defaultCurrencyConfig
    try {
        const btcRates = await fetchCoinGeckoBtcRates({ signal })

        const btcDefaultCurrency =
            btcRates[defaultCurrency.code.toLowerCase()] || null

        const btcUsd = btcRates['usd'] || null

        if (!btcUsd || !btcDefaultCurrency) {
            throw new ImperativeError(
                'Failed to fetch BTC rates for default currency or USD',
                { defaultCurrency }
            )
        }

        return {
            base: FIAT_CURRENCIES.USD,
            quote: defaultCurrency,
            rate: fromFixedWithFraction(
                (btcDefaultCurrency / btcUsd).toFixed(6), // Double precision of coingecko
                defaultCurrency.rateFraction
            ),
        }
    } catch (error) {
        captureError(error)
        return null
    }
}

export const fetchDefaultCurrencyRateToFiatCurrency = async ({
    defaultCurrency,
    fiatCurrency,
    signal,
}: {
    defaultCurrency: DefaultCurrency
    fiatCurrency: FiatCurrency
    signal?: AbortSignal
}): Promise<FXRate2<FiatCurrency, DefaultCurrency> | null> => {
    try {
        const btcRates = await fetchCoinGeckoBtcRates({ signal })

        const btcDefaultCurrency =
            btcRates[defaultCurrency.code.toLowerCase()] || null

        const btcFiatCurrency =
            btcRates[fiatCurrency.code.toLowerCase()] || null

        if (!btcFiatCurrency || !btcDefaultCurrency) {
            throw new ImperativeError(
                'Failed to fetch BTC rates for default currency or fiat currency',
                { defaultCurrency, fiatCurrency }
            )
        }

        return {
            base: fiatCurrency,
            quote: defaultCurrency,
            rate: fromFixedWithFraction(
                (btcDefaultCurrency / btcFiatCurrency).toFixed(6), // Double precision of coingecko
                defaultCurrency.rateFraction
            ),
        }
    } catch (error) {
        captureError(error)
        return null
    }
}

export const fetchFiatCurrenciesRateToFiatCurrency = async ({
    currencies,
    quote,
    signal,
}: {
    currencies: FiatCurrency[]
    quote: FiatCurrency
    signal?: AbortSignal
}): Promise<FiatRatesMap> => {
    try {
        const btcRates = await fetchCoinGeckoBtcRates({ signal })
        const quoteRate = btcRates[quote.code.toLowerCase()] || null

        if (!quoteRate) {
            throw new ImperativeError(
                'Failed to fetch BTC rate for fiat currency',
                { quote }
            )
        }

        const errors: FiatCurrency[] = []
        const record = currencies.reduce(
            (acc, base) => {
                const baseRate = btcRates[base.code.toLowerCase()] || null

                if (!baseRate) {
                    errors.push(base)
                    return acc
                }

                acc[base.id] = {
                    base: base,
                    quote,
                    rate: fromFixedWithFraction(
                        (quoteRate / baseRate).toFixed(6), // Double precision of coingecko
                        base.rateFraction
                    ),
                }
                return acc
            },
            {} as Record<
                FiatCurrency['id'],
                FXRate2<FiatCurrency, FiatCurrency> | null
            >
        )

        if (errors.length) {
            captureError(
                new ImperativeError(
                    `Failed to fetch BTC rate for fiat currency`,
                    {
                        currencies: errors,
                    }
                )
            )
        }
        return record
    } catch (error) {
        captureError(error)
        return {}
    }
}
