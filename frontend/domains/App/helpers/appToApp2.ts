import { notReachable } from '@zeal/toolkit'

import {
    App,
    App2,
    AppProtocol,
    AppProtocol2,
    AppToken,
} from '@zeal/domains/App'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { Token2 } from '@zeal/domains/Token'

const appTokenToToken2 = (appToken: AppToken): Token2 => ({
    balance: appToken.balance,
    rate: null,
    priceInDefaultCurrency: appToken.priceInDefaultCurrency,
    marketData: null,
    scam: false,
})

const appProtocolToAppProtocol2 = (protocol: AppProtocol): AppProtocol2 => {
    switch (protocol.type) {
        case 'CommonAppProtocol':
            return {
                type: 'CommonAppProtocol',
                priceInDefaultCurrency: protocol.priceInDefaultCurrency,
                suppliedTokens: protocol.suppliedTokens.map(appTokenToToken2),
                borrowedTokens: protocol.borrowedTokens.map(appTokenToToken2),
                rewardTokens: protocol.rewardTokens.map(appTokenToToken2),
                category: protocol.category,
                description: protocol.description,
                priceInUsd: {
                    amount: 0n,
                    currency: FIAT_CURRENCIES.USD,
                },
            }

        case 'LockedTokenAppProtocol':
            return {
                priceInUsd: {
                    amount: 0n,
                    currency: FIAT_CURRENCIES.USD,
                },
                type: 'LockedTokenAppProtocol',
                priceInDefaultCurrency: protocol.priceInDefaultCurrency,
                lockedTokens: protocol.lockedTokens.map(appTokenToToken2),
                rewardTokens: protocol.rewardTokens.map(appTokenToToken2),
                unlockAt: protocol.unlockAt,
                category: protocol.category,
                description: protocol.description,
            }

        case 'LendingAppProtocol':
            return {
                priceInUsd: {
                    amount: 0n,
                    currency: FIAT_CURRENCIES.USD,
                },
                type: 'LendingAppProtocol',
                priceInDefaultCurrency: protocol.priceInDefaultCurrency,
                suppliedTokens: protocol.suppliedTokens.map(appTokenToToken2),
                borrowedTokens: protocol.borrowedTokens.map(appTokenToToken2),
                rewardTokens: protocol.rewardTokens.map(appTokenToToken2),
                category: protocol.category,
                healthFactor: protocol.healthFactor,
            }

        case 'VestingAppProtocol':
            return {
                priceInUsd: {
                    amount: 0n,
                    currency: FIAT_CURRENCIES.USD,
                },
                type: 'VestingAppProtocol',
                priceInDefaultCurrency: protocol.priceInDefaultCurrency,
                vestedToken: appTokenToToken2(protocol.vestedToken),
                claimableToken: appTokenToToken2(protocol.claimableToken),
                category: protocol.category,
            }

        case 'UnknownAppProtocol':
            return {
                priceInUsd: {
                    amount: 0n,
                    currency: FIAT_CURRENCIES.USD,
                },
                type: 'UnknownAppProtocol',
                priceInDefaultCurrency: protocol.priceInDefaultCurrency,
                tokens: protocol.tokens.map(appTokenToToken2),
                nfts: protocol.nfts,
                category: protocol.category,
            }

        default:
            return notReachable(protocol)
    }
}

export const appToApp2 = (app: App): App2 => ({
    name: app.name,
    icon: app.icon,
    networkHexId: app.networkHexId,
    priceInDefaultCurrency: app.priceInDefaultCurrency,
    priceInUsd: app.priceInUsd,
    url: app.url,
    protocols: app.protocols.map(appProtocolToAppProtocol2),
})
