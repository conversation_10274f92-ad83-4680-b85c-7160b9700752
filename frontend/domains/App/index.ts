import { ReactNode } from 'react'

import { Address } from '@zeal/domains/Address'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { NetworkHexId } from '@zeal/domains/Network'
import { Token2 } from '@zeal/domains/Token'

/**
 * @deprecated use App2 instead if you can
 */
export type App = {
    name: string
    icon: string
    networkHexId: NetworkHexId
    priceInDefaultCurrency: FiatMoney | null
    priceInUsd: FiatMoney
    url: string | null
    protocols: AppProtocol[]
}

export type AppProtocol =
    | CommonProtocol
    | LockedToken
    | Lending
    | Vesting
    | UnknownProtocol

export type CommonProtocol = {
    type: 'CommonAppProtocol'
    priceInDefaultCurrency: FiatMoney | null
    suppliedTokens: AppToken[]
    borrowedTokens: AppToken[]
    rewardTokens: AppToken[]
    category: string
    description: string | null
}

export type LockedToken = {
    type: 'LockedTokenAppProtocol'
    priceInDefaultCurrency: FiatMoney | null
    lockedTokens: AppToken[]
    rewardTokens: AppToken[]
    unlockAt: number
    category: string
    description: string | null
}

export type Lending = {
    type: 'LendingAppProtocol'
    priceInDefaultCurrency: FiatMoney | null
    suppliedTokens: AppToken[]
    borrowedTokens: AppToken[]
    rewardTokens: AppToken[]
    category: string
    healthFactor: number
}

export type Vesting = {
    type: 'VestingAppProtocol'
    priceInDefaultCurrency: FiatMoney | null
    vestedToken: AppToken
    claimableToken: AppToken
    category: string
}

export type UnknownProtocol = {
    type: 'UnknownAppProtocol'
    priceInDefaultCurrency: FiatMoney | null
    tokens: AppToken[]
    nfts: AppNft[]
    category: string
}

export type AppToken = {
    networkHexId: NetworkHexId
    name: string
    address: Address
    balance: CryptoMoney
    priceInDefaultCurrency: FiatMoney | null
}

export type AppNft = {
    tokenId: string
    name: string | null
    amount: string
    decimals: number
    priceInDefaultCurrency: FiatMoney | null
    uri: string | null
}

export type PlaceholderApp = {
    logo: (size: number) => ReactNode
    name: string
    description: ReactNode
    link: URL
}

export type App2 = {
    name: string
    icon: string | null
    networkHexId: NetworkHexId
    priceInDefaultCurrency: FiatMoney | null
    priceInUsd: FiatMoney
    url: string | null
    protocols: AppProtocol2[]
}

export type AppProtocol2 =
    | CommonProtocol2
    | LockedToken2
    | Lending2
    | Vesting2
    | UnknownProtocol2

export type CommonProtocol2 = {
    type: 'CommonAppProtocol'

    priceInDefaultCurrency: FiatMoney | null
    suppliedTokens: Token2[]
    priceInUsd: FiatMoney
    borrowedTokens: Token2[]
    rewardTokens: Token2[]
    category: string
    description: string | null
}

export type LockedToken2 = {
    type: 'LockedTokenAppProtocol'
    priceInDefaultCurrency: FiatMoney | null
    priceInUsd: FiatMoney
    lockedTokens: Token2[]
    rewardTokens: Token2[]
    unlockAt: number
    category: string
    description: string | null
}

export type Lending2 = {
    type: 'LendingAppProtocol'
    priceInUsd: FiatMoney
    priceInDefaultCurrency: FiatMoney | null
    suppliedTokens: Token2[]
    borrowedTokens: Token2[]
    rewardTokens: Token2[]
    category: string
    healthFactor: number
}

export type Vesting2 = {
    type: 'VestingAppProtocol'
    priceInUsd: FiatMoney
    priceInDefaultCurrency: FiatMoney | null
    vestedToken: Token2
    claimableToken: Token2
    category: string
}

export type UnknownProtocol2 = {
    type: 'UnknownAppProtocol'
    priceInUsd: FiatMoney
    priceInDefaultCurrency: FiatMoney | null
    tokens: Token2[]
    nfts: AppNft[]
    category: string
}

export type PlaceholderApp2 = {
    logo: (size: number) => ReactNode
    name: string
    description: ReactNode
    link: URL
}
