import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import {
    ConnectionState,
    Disconnected,
    NotInteracted,
} from '@zeal/domains/DApp/domains/ConnectionState'
import { KeyStore, KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DEFAULT_NETWORK } from '@zeal/domains/Network/constants'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { InteractionRequest } from '@zeal/domains/RPCRequest'
import { fetchSimulatedSignMessage } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation/api/fetchSimulatedSignMessage'
import { AddCustomNetwork } from '@zeal/domains/RPCRequest/features/AddCustomNetwork'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { Sign } from '@zeal/domains/RPCRequest/features/Sign'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { fetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { fetchTransactionResultByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'

import { Connect } from './Connect'

type Props = {
    interactionRequest: InteractionRequest
    sessionPassword: string
    customCurrencies: CustomCurrencyMap
    account: Account
    keyStore: KeyStore
    accounts: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    portfolioMap: PortfolioMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    connectionState: ConnectionState
    currencyHiddenMap: CurrencyHiddenMap
    cardConfig: CardConfig
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type UserInteractionResponseMsg =
    | MsgOf<typeof SendTransaction>
    | MsgOf<typeof Sign>
    | MsgOf<typeof AddCustomNetwork>
    | MsgOf<typeof Connect>

type Msg = {
    type: 'user_interaction_response'
    interactionRequest: InteractionRequest
    responseMsg: UserInteractionResponseMsg
}

const calculateNetworkToConnectTo = (
    connectionState: Disconnected | NotInteracted,
    networkMap: NetworkMap
) => {
    switch (connectionState.type) {
        case 'not_interacted':
            return DEFAULT_NETWORK
        case 'disconnected':
            return networkMap[connectionState.networkHexId] || DEFAULT_NETWORK
        /* istanbul ignore next */
        default:
            return notReachable(connectionState)
    }
}

export const UserInteractionRequired = ({
    interactionRequest,
    connectionState,
    onMsg,
    sessionPassword,
    networkMap,
    cardConfig,
    networkRPCMap,
    feePresetMap,
    portfolioMap,
    keystores,
    accounts,
    account,
    keyStore,
    gasCurrencyPresetMap,
    currencyHiddenMap,
    customCurrencies,
    installationId,
    defaultCurrencyConfig,
}: Props) => {
    switch (connectionState.type) {
        case 'not_interacted':
        case 'disconnected':
            const networkToConnectTo = calculateNetworkToConnectTo(
                connectionState,
                networkMap
            )
            return (
                <Connect
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    connectionState={connectionState}
                    network={networkToConnectTo}
                    account={account}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    cardConfig={cardConfig}
                    keystores={keystores}
                    accounts={accounts}
                    customCurrencyMap={customCurrencies}
                    networkMap={networkMap}
                    sessionPassword={sessionPassword}
                    currencyHiddenMap={currencyHiddenMap}
                    installationId={installationId}
                    onMsg={(msg) =>
                        onMsg({
                            type: 'user_interaction_response',
                            interactionRequest,
                            responseMsg: msg,
                        })
                    }
                />
            )
        case 'connected':
            const connectedAccount = accounts[connectionState.address]
            const connectedNetwork = networkMap[connectionState.networkHexId]

            if (!connectedAccount) {
                throw new ImperativeError('Connected account not found')
            }
            if (!connectedNetwork) {
                throw new ImperativeError('Connected network not found')
            }

            switch (interactionRequest.method) {
                case 'eth_requestAccounts':
                case 'wallet_requestPermissions':
                    return null
                case 'eth_sendTransaction':
                    return (
                        <SendTransaction
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            portfolio={unsafe_GetPortfolioCache2({
                                address: connectedAccount.address,
                                portfolioMap,
                            })}
                            feePresetMap={feePresetMap}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            fetchSimulationByRequest={fetchSimulationByRequest}
                            fetchTransactionResultByRequest={
                                fetchTransactionResultByRequest
                            }
                            installationId={installationId}
                            accounts={accounts}
                            keystores={keystores}
                            state={{ type: 'maximised' }}
                            account={connectedAccount}
                            network={connectedNetwork}
                            sendTransactionRequests={[interactionRequest]}
                            sessionPassword={sessionPassword}
                            actionSource={{
                                type: 'external',
                                dAppSiteInfo: connectionState.dApp,
                                transactionEventSource: 'browsertab',
                            }}
                            onMsg={(msg) =>
                                onMsg({
                                    type: 'user_interaction_response',
                                    interactionRequest,
                                    responseMsg: msg,
                                })
                            }
                        />
                    )

                case 'eth_signTypedData_v4':
                case 'eth_signTypedData_v3':
                case 'eth_signTypedData':
                case 'personal_sign':
                    return (
                        <Sign
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            accountsMap={accounts}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            installationId={installationId}
                            keyStoreMap={keystores}
                            portfolio={unsafe_GetPortfolioCache2({
                                address: account.address,
                                portfolioMap: portfolioMap,
                            })}
                            networkRPCMap={networkRPCMap}
                            networkMap={networkMap}
                            state={{ type: 'maximised' }}
                            sessionPassword={sessionPassword}
                            keyStore={keyStore}
                            request={interactionRequest}
                            account={connectedAccount}
                            network={connectedNetwork}
                            actionSource={{
                                type: 'external',
                                dAppSiteInfo: connectionState.dApp,
                                transactionEventSource: 'browsertab',
                            }}
                            fetchSimulatedSignMessage={
                                fetchSimulatedSignMessage
                            }
                            onMsg={(msg) =>
                                onMsg({
                                    type: 'user_interaction_response',
                                    interactionRequest,
                                    responseMsg: msg,
                                })
                            }
                        />
                    )

                case 'wallet_addEthereumChain':
                    return (
                        <AddCustomNetwork
                            installationId={installationId}
                            request={interactionRequest}
                            visualState={{ type: 'maximised' }}
                            account={connectedAccount}
                            dApp={connectionState.dApp}
                            network={connectedNetwork}
                            keyStore={keyStore}
                            onMsg={(msg) =>
                                onMsg({
                                    type: 'user_interaction_response',
                                    interactionRequest,
                                    responseMsg: msg,
                                })
                            }
                        />
                    )

                /* istanbul ignore next */
                default:
                    return notReachable(interactionRequest)
            }

        /* istanbul ignore next */
        default:
            return notReachable(connectionState)
    }
}
