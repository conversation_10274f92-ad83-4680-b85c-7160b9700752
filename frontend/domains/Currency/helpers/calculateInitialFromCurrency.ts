import { NetworkMap } from '@zeal/domains/Network'
import {
    findNetworkByHexChainId,
    GNOSIS,
} from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'

import { CryptoCurrency } from '..'
import { STABLE_COIN_TO_FIAT_CURRENCY_MAP } from '../constants'

export const calculateInitialFromCurrency = ({
    portfolio,
    toCurrency,
    currencies,
    networkMap,
}: {
    portfolio: ServerPortfolio2
    toCurrency: CryptoCurrency | null
    currencies: CryptoCurrency[]
    networkMap: NetworkMap
}): CryptoCurrency => {
    const currencyIds = new Set(currencies.map((c) => c.id))

    const filteredTokens = portfolio.tokens.filter(
        (token) =>
            (!toCurrency || token.balance.currency.id !== toCurrency.id) &&
            currencyIds.has(token.balance.currency.id)
    )

    if (!filteredTokens.length) {
        return GNOSIS.nativeCurrency
    }
    const tokens = filteredTokens.toSorted((a, b) => {
        const defaultCurrencyA = a.priceInDefaultCurrency?.amount || 0n
        const defaultCurrencyB = b.priceInDefaultCurrency?.amount || 0n

        if (defaultCurrencyA > defaultCurrencyB) return -1
        if (defaultCurrencyA < defaultCurrencyB) return 1
        return 0
    })

    const stableCoinToken = tokens.find(
        (token) => STABLE_COIN_TO_FIAT_CURRENCY_MAP[token.balance.currency.id]
    )
    if (stableCoinToken) {
        return stableCoinToken.balance.currency
    }
    const nativeToken = tokens.find(
        (token) =>
            findNetworkByHexChainId(
                token.balance.currency.networkHexChainId,
                networkMap
            ).nativeCurrency.id === token.balance.currency.id
    )
    if (nativeToken) {
        return nativeToken.balance.currency
    }
    return tokens[0].balance.currency
}
