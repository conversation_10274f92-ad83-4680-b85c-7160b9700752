import { FormattedMessage } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { SelectToken } from '@zeal/domains/Token/components/SelectToken'

type Props = {
    selected: CryptoCurrency | null
    cryptoCurrencies: CryptoCurrency[]

    portfolio: ServerPortfolio2
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap

    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_crypto_currency_selected'; currency: CryptoCurrency }

export const CryptoCurrencySelector = ({
    selected,
    portfolio,
    networkMap,
    currencyPinMap,
    currencyHiddenMap,
    cryptoCurrencies,
    onMsg,
}: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            aria-labelledby="select-crypto-currency"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <UIActionBar
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="textPrimary"
                                id="select-crypto-currency"
                            >
                                <FormattedMessage
                                    id="select-crypto-currency-title"
                                    defaultMessage="Select token"
                                />
                            </Text>
                        </Row>
                    </Clickable>
                }
            />

            <Column fill shrink spacing={16}>
                <SelectToken
                    cryptoCurrencies={cryptoCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    networkMap={networkMap}
                    serverPortfolio={portfolio}
                    selectedCurrency={selected}
                    onCryptoCurrencySelected={(currency) =>
                        onMsg({
                            type: 'on_crypto_currency_selected',
                            currency,
                        })
                    }
                />
            </Column>
        </Screen>
    )
}
