import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { ResignMsgAndLogin } from '@zeal/domains/Currency/domains/BankTransfer/features/ResignMsgAndLogin'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Flow } from './Flow'

type Props = {
    accountsMap: AccountsMap
    portfolioMap: PortfolioMap
    keyStoreMap: KeyStoreMap
    loginInfo: UnblockLoginInfo
    bankTransferInfo: BankTransferUnblockUserCreated
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    feePresetMap: FeePresetMap
    currencyHiddenMap: CurrencyHiddenMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    network: Network
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof Flow> | MsgOf<typeof ResignMsgAndLogin>

type State =
    | { type: 'flow' }
    | {
          type: 'login'
          newOwner: Account
          newOwnerKeyStore: SigningKeyStore
      }

export const ChangeOwner = ({
    bankTransferInfo,
    loginInfo,
    onMsg,
    network,
    portfolioMap,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    networkMap,
    networkRPCMap,
    sessionPassword,
    currencyHiddenMap,
    installationId,
    keyStoreMap,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'flow' })

    switch (state.type) {
        case 'flow':
            return (
                <Flow
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    accountsMap={accountsMap}
                    portfolioMap={portfolioMap}
                    keyStoreMap={keyStoreMap}
                    loginInfo={loginInfo}
                    currencyHiddenMap={currencyHiddenMap}
                    bankTransferInfo={bankTransferInfo}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'bank_transfer_owner_successfully_changed':
                                setState({
                                    type: 'login',
                                    newOwner: msg.newOwner,
                                    newOwnerKeyStore: msg.newOwnerKeyStore,
                                })
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'login':
            return (
                <ResignMsgAndLogin
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    keystore={state.newOwnerKeyStore}
                    account={state.newOwner}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    keyStoreMap={keyStoreMap}
                    sessionPassword={sessionPassword}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    portfolioMap={portfolioMap}
                    network={network}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
