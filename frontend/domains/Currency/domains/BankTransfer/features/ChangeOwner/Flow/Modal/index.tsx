import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { SigningKeyStore } from '@zeal/domains/KeyStore'

import { ConfirmChangeOwner } from './ConfirmChangeOwner'

type Props = {
    state: State
    loginInfo: UnblockLoginInfo
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof ConfirmChangeOwner>

export type State =
    | { type: 'closed' }
    | {
          type: 'confirm_change_owner'
          newOwner: Account
          keyStore: SigningKeyStore
      }

export const Modal = ({ onMsg, state, loginInfo, installationId }: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'confirm_change_owner':
            return (
                <ConfirmChangeOwner
                    newOwner={state.newOwner}
                    keyStore={state.keyStore}
                    loginInfo={loginInfo}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
