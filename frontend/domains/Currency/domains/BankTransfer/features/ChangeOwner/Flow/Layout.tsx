import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { values } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ListItem } from '@zeal/domains/Account/components/ListItem'
import { sortByBalance } from '@zeal/domains/Account/helpers/sortByBalance'
import { Address } from '@zeal/domains/Address'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStore, KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'

type Props = {
    accountsMap: AccountsMap
    portfolioMap: PortfolioMap
    keyStoreMap: KeyStoreMap
    bankTransferInfo: BankTransferUnblockUserCreated
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_account_selected'
          account: Account
          keyStore: SigningKeyStore
      }

export const Layout = ({
    accountsMap,
    portfolioMap,
    keyStoreMap,
    bankTransferInfo,
    currencyHiddenMap,
    installationId,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const signingKeyStoreAccounts = values(accountsMap)
        .map((account): [Account, KeyStore] => {
            const keystore = getKeyStore({
                keyStoreMap,
                address: account.address,
            })

            return [account, keystore] as const
        })
        .filter((item): item is [Account, SigningKeyStore] => {
            const [_, keystore] = item

            switch (keystore.type) {
                case 'private_key_store':
                case 'ledger':
                case 'secret_phrase_key':
                case 'trezor':
                case 'safe_4337':
                    return true

                case 'track_only':
                    return false

                /* istanbul ignore next */
                default:
                    return notReachable(keystore)
            }
        })

    const [currentOwnerAccount, currentOwnerKaystore] = findCurrentOwnerAccount(
        signingKeyStoreAccounts,
        bankTransferInfo.connectedWalletAddress
    )

    const restSigningKeyStoreAccounts = signingKeyStoreAccounts
        .filter(([account]) => {
            return account.address !== bankTransferInfo.connectedWalletAddress
        })
        .sort(([account1], [account2]) =>
            sortByBalance(
                portfolioMap,
                currencyHiddenMap,
                defaultCurrencyConfig
            )(account1, account2)
        )

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => {
                            onMsg({ type: 'close' })
                        }}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={20} shrink fill>
                <Header
                    title={
                        <FormattedMessage
                            id="bank-transfer.change-owner.title"
                            defaultMessage="Set account owner"
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="bank-transfer.change-owner.subtitle"
                            defaultMessage="This wallet is used to sign into and recover your bank transfer account"
                        />
                    }
                />
                <Column spacing={12} shrink fill>
                    <Group variant="default">
                        <ListItem
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'account_item_clicked':
                                        onMsg({
                                            type: 'on_account_selected',
                                            account: currentOwnerAccount,
                                            keyStore: currentOwnerKaystore,
                                        })
                                        break

                                    /* istanbul ignore next */
                                    default:
                                        notReachable(msg.type)
                                }
                            }}
                            installationId={installationId}
                            currencyHiddenMap={currencyHiddenMap}
                            account={currentOwnerAccount}
                            selected={true}
                            keystore={currentOwnerKaystore}
                            portfolio={unsafe_GetPortfolioCache2({
                                address: currentOwnerAccount.address,
                                portfolioMap,
                            })}
                        />
                    </Group>
                    <ScrollContainer withFloatingActions={false}>
                        <Group variant="default">
                            {restSigningKeyStoreAccounts.map(
                                ([account, keyStore]) => (
                                    <ListItem
                                        defaultCurrencyConfig={
                                            defaultCurrencyConfig
                                        }
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'account_item_clicked':
                                                    onMsg({
                                                        type: 'on_account_selected',
                                                        account,
                                                        keyStore,
                                                    })
                                                    break

                                                /* istanbul ignore next */
                                                default:
                                                    notReachable(msg.type)
                                            }
                                        }}
                                        installationId={installationId}
                                        currencyHiddenMap={currencyHiddenMap}
                                        key={account.address}
                                        account={account}
                                        selected={false}
                                        keystore={keyStore}
                                        portfolio={unsafe_GetPortfolioCache2({
                                            address: account.address,
                                            portfolioMap,
                                        })}
                                    />
                                )
                            )}
                        </Group>
                    </ScrollContainer>
                </Column>
            </Column>
        </Screen>
    )
}

const findCurrentOwnerAccount = (
    signingKeyStoreAccounts: [Account, SigningKeyStore][],
    ownerAddress: Address
): [Account, SigningKeyStore] => {
    const ownerSigninKeyStoreAccount = signingKeyStoreAccounts.find(
        ([account]) => account.address === ownerAddress
    )

    if (!ownerSigninKeyStoreAccount) {
        throw new ImperativeError(
            'Owner account does not have SigningKeyStore',
            { ownerAddress }
        )
    }

    return ownerSigninKeyStoreAccount
}
