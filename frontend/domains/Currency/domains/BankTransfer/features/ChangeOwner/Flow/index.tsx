import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    accountsMap: AccountsMap
    portfolioMap: PortfolioMap
    keyStoreMap: KeyStoreMap
    loginInfo: UnblockLoginInfo
    bankTransferInfo: BankTransferUnblockUserCreated
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof Layout>, { type: 'close' }>
    | MsgOf<typeof Modal>

export const Flow = ({
    onMsg,
    bankTransferInfo,
    portfolioMap,
    keyStoreMap,
    loginInfo,
    currencyHiddenMap,
    installationId,
    defaultCurrencyConfig,
    accountsMap,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Layout
                defaultCurrencyConfig={defaultCurrencyConfig}
                accountsMap={accountsMap}
                keyStoreMap={keyStoreMap}
                currencyHiddenMap={currencyHiddenMap}
                installationId={installationId}
                portfolioMap={portfolioMap}
                bankTransferInfo={bankTransferInfo}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            onMsg(msg)
                            break
                        case 'on_account_selected':
                            setModal({
                                type: 'confirm_change_owner',
                                newOwner: msg.account,
                                keyStore: msg.keyStore,
                            })
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                state={modal}
                loginInfo={loginInfo}
                onMsg={onMsg}
                installationId={installationId}
            />
        </>
    )
}
