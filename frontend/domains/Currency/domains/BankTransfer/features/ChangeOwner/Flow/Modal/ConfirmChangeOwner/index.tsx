import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Button } from '@zeal/uikit/Button'
import { Header } from '@zeal/uikit/Header'
import { BoldSwap } from '@zeal/uikit/Icon/BoldSwap'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'

import { noop, notReachable } from '@zeal/toolkit'
import { useLazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { UnblockLoginSignature } from '@zeal/domains/Currency/domains/BankTransfer'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { setUnblockSignInAddress } from '@zeal/domains/Currency/domains/BankTransfer/api/setUnblockSignInAddress'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { SigningKeyStore } from '@zeal/domains/KeyStore'

type Props = {
    newOwner: Account
    keyStore: SigningKeyStore
    loginInfo: UnblockLoginInfo
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'bank_transfer_owner_successfully_changed'
          newOwnerSignature: UnblockLoginSignature
          newOwner: Account
          newOwnerKeyStore: SigningKeyStore
      }
    | {
          type: 'close'
      }

export const ConfirmChangeOwner = ({
    newOwner,
    loginInfo,
    keyStore,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLazyLoadableData(
        setUnblockSignInAddress,
        {
            type: 'not_asked',
        }
    )

    const liveOnMsg = useLiveRef(onMsg)
    const liveNewOwner = useLiveRef(newOwner)
    const liveKeyStore = useLiveRef(keyStore)
    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'bank_transfer_owner_successfully_changed',
                    newOwner: liveNewOwner.current,
                    newOwnerKeyStore: liveKeyStore.current,
                    newOwnerSignature: loginInfo.usedSignature,
                })
                break
            case 'not_asked':
            case 'loading':
            case 'error':
                break
            /* istanbul ignore next */
            default:
                notReachable(loadable)
        }
    }, [liveOnMsg, loadable, liveKeyStore, liveNewOwner, loginInfo])

    switch (loadable.type) {
        case 'not_asked':
            return (
                <Popup.Layout
                    onMsg={() => {
                        onMsg({
                            type: 'close',
                        })
                    }}
                >
                    <ActionBar
                        right={
                            <IconButton
                                variant="on_light"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                {({ color }) => (
                                    <CloseCross size={24} color={color} />
                                )}
                            </IconButton>
                        }
                    />
                    <ConfirmChangeOwnerHeader />
                    <Popup.Actions direction="row">
                        <Button
                            variant="secondary"
                            size="regular"
                            onClick={() => {
                                onMsg({ type: 'close' })
                            }}
                        >
                            <FormattedMessage
                                id="action.cancel"
                                defaultMessage="Cancel"
                            />
                        </Button>
                        <Button
                            variant="primary"
                            size="regular"
                            onClick={() => {
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        unblockUserLoginInfo: loginInfo,
                                        signInAddress:
                                            newOwner.address as Web3.address.Address,
                                    },
                                })
                            }}
                        >
                            <FormattedMessage
                                id="action.confirm"
                                defaultMessage="Confirm"
                            />
                        </Button>
                    </Popup.Actions>
                </Popup.Layout>
            )

        case 'loading':
        case 'loaded':
            return (
                <Popup.Layout
                    onMsg={() => {
                        onMsg({
                            type: 'close',
                        })
                    }}
                >
                    <ActionBar
                        right={
                            <IconButton
                                variant="on_light"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                {({ color }) => (
                                    <CloseCross size={24} color={color} />
                                )}
                            </IconButton>
                        }
                    />
                    <ConfirmChangeOwnerHeader />
                    <Popup.Actions direction="row">
                        <Button
                            variant="secondary"
                            disabled={true}
                            size="regular"
                            onClick={noop}
                        >
                            <FormattedMessage
                                id="action.cancel"
                                defaultMessage="Cancel"
                            />
                        </Button>
                        <Button
                            variant="primary"
                            loading={true}
                            disabled={true}
                            size="regular"
                            onClick={noop}
                        />
                    </Popup.Actions>
                </Popup.Layout>
            )
        case 'error': {
            const error = parseAppError(loadable.error)

            return (
                <AppErrorPopup
                    error={error}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg({ type: 'close' })
                                break
                            case 'try_again_clicked':
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        unblockUserLoginInfo: loginInfo,
                                        signInAddress:
                                            newOwner.address as Web3.address.Address,
                                    },
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        }

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}

const ConfirmChangeOwnerHeader = () => {
    return (
        <Header
            icon={({ size }) => <BoldSwap size={size} color="teal40" />}
            title={
                <FormattedMessage
                    id="bank-transfer.configrm-change-owner.title"
                    defaultMessage="Change account owner"
                />
            }
            subtitle={
                <FormattedMessage
                    id="bank-transfer.configrm-change-owner.subtitle"
                    defaultMessage="Are you sure you want to change account owner. This wallet is used to sign into and recover your bank transfer account."
                />
            }
        />
    )
}
