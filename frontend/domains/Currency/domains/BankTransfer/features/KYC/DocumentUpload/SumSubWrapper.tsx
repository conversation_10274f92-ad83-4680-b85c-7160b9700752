import SumSubWebSdk from '@sumsub/websdk-react'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { UnexpectedResultFailureError } from '@zeal/toolkit/Result'

import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { SumSubAccessToken } from '@zeal/domains/KYC'
import { parseSumsubWebSdkMessage } from '@zeal/domains/KYC/parsers/parseSumsubSdkMessage'

type Props = {
    sumSubAccessToken: SumSubAccessToken
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'application_submitted' } | { type: 'close' }

export const SumSubWrapper = ({ sumSubAccessToken, onMsg }: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />
            <Spacer />

            <Column alignX="center" spacing={0}>
                <SumSubWebSdk
                    accessToken={sumSubAccessToken}
                    onMessage={(msg: unknown) => {
                        const parsed = parseSumsubWebSdkMessage(msg)

                        switch (parsed.type) {
                            case 'Failure':
                                captureError(
                                    new UnexpectedResultFailureError(
                                        'Failed to parse SumSub web sdk message',
                                        parsed.reason
                                    )
                                )
                                break
                            case 'Success':
                                switch (parsed.data) {
                                    case 'idCheck.onApplicantSubmitted':
                                    case 'idCheck.onApplicantResubmitted':
                                        onMsg({
                                            type: 'application_submitted',
                                        })
                                        break
                                    case 'idCheck.actionCompleted':
                                    case 'idCheck.applicantReviewComplete':
                                    case 'idCheck.applicantStatus':
                                    case 'idCheck.livenessCompleted':
                                    case 'idCheck.moduleResultPresented':
                                    case 'idCheck.onActionSubmitted':
                                    case 'idCheck.onApplicantLoaded':
                                    case 'idCheck.onApplicantStatusChanged':
                                    case 'idCheck.onError':
                                    case 'idCheck.onInitialized':
                                    case 'idCheck.onReady':
                                    case 'idCheck.onResize':
                                    case 'idCheck.onStepCompleted':
                                    case 'idCheck.onStepInitiated':
                                    case 'idCheck.onUploadError':
                                    case 'idCheck.onUploadWarning':
                                    case 'idCheck.onUserAction':
                                    case 'idCheck.onVideoIdentCallStarted':
                                    case 'idCheck.onVideoIdentCompleted':
                                    case 'idCheck.onVideoIdentModeratorJoined':
                                    case 'idCheck.stepCompleted':
                                    case 'idCheck.onApplicantActionLoaded':
                                    case 'idCheck.onApplicantActionSubmitted':
                                    case 'idCheck.onApplicantActionCompleted':
                                    case 'idCheck.onNavigationUiControlsStateChanged':
                                    case 'idCheck.onLanguageChanged':
                                    case 'idCheck.restoreScrollPosition':
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(parsed.data)
                                }
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(parsed)
                        }
                    }}
                    expirationHandler={() => {
                        // TODO @Nicvaniek: Refresh token if this becomes a problem
                        captureError(
                            new ImperativeError(
                                'SumSub access token expired [web SDK]'
                            )
                        )
                        return sumSubAccessToken
                    }}
                />
            </Column>

            <Spacer />
        </Screen>
    )
}
