import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { UnblockUser } from '@zeal/domains/Currency/domains/BankTransfer'
import {
    KycApplication,
    PersonalDetails,
    ResidenceDetails,
} from '@zeal/domains/Currency/domains/BankTransfer/api/submitUnblockKycApplication'
import { UNBLOCK_SUPPORTED_COUNTRIES } from '@zeal/domains/Currency/domains/BankTransfer/constants'

import {
    InitialPersonalDetails,
    PersonalDetailsForm,
} from './PersonalDetailsForm'
import { SourceOfFundsForm } from './SourceOfFundsForm'

import {
    InitialResidenceDetails,
    ResidenceDetailsForm,
} from '../ResidenceDetailsForm'

type Props = {
    unblockUser: UnblockUser
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'form_submitted'; form: KycApplication }
    | Extract<
          MsgOf<typeof PersonalDetailsForm>,
          {
              type: 'close'
          }
      >

type State =
    | { type: 'personal_details'; initialForm: InitialPersonalDetails }
    | {
          type: 'residence_details'
          initialForm: InitialResidenceDetails
          personalDetails: PersonalDetails
      }
    | {
          type: 'source_of_funds'
          personalDetails: PersonalDetails
          residenceDetails: ResidenceDetails
      }

export const Form = ({ onMsg, unblockUser }: Props) => {
    const [state, setState] = useState<State>({
        type: 'personal_details',
        initialForm: {
            firstName: unblockUser.firstName,
            lastName: unblockUser.lastName,
            dateOfBirth: null,
        },
    })

    switch (state.type) {
        case 'personal_details':
            return (
                <PersonalDetailsForm
                    initialPersonalDetails={state.initialForm}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_form_submitted':
                                setState({
                                    type: 'residence_details',
                                    personalDetails: msg.completedForm,
                                    initialForm:
                                        UNBLOCK_SUPPORTED_COUNTRIES.some(
                                            ({ code }) =>
                                                code ===
                                                unblockUser.residenceDetails
                                                    .country
                                        )
                                            ? {
                                                  city: unblockUser
                                                      .residenceDetails.city,
                                                  address:
                                                      unblockUser
                                                          .residenceDetails
                                                          .address,
                                                  country:
                                                      unblockUser
                                                          .residenceDetails
                                                          .country,
                                                  postCode:
                                                      unblockUser
                                                          .residenceDetails
                                                          .postCode,
                                              }
                                            : {
                                                  address: null,
                                                  city: null,
                                                  country: null,
                                                  postCode: null,
                                              },
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'residence_details':
            return (
                <ResidenceDetailsForm
                    initialResidenceDetails={state.initialForm}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({
                                    type: 'personal_details',
                                    initialForm: state.personalDetails,
                                })
                                break
                            case 'on_form_submitted':
                                setState({
                                    type: 'source_of_funds',
                                    personalDetails: state.personalDetails,
                                    residenceDetails: msg.completedForm,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'source_of_funds':
            return (
                <SourceOfFundsForm
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_source_of_funds_selected':
                                onMsg({
                                    type: 'form_submitted',
                                    form: {
                                        personalDetails: state.personalDetails,
                                        residenceDetails:
                                            state.residenceDetails,
                                        sourceOfFunds: msg.source,
                                    },
                                })
                                break
                            case 'close':
                                setState({
                                    type: 'residence_details',
                                    personalDetails: state.personalDetails,
                                    initialForm: state.residenceDetails,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
