import { useRef, useState } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'
import { TextInput } from 'react-native'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { But<PERSON> } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { ArrowDown } from '@zeal/uikit/Icon/ArrowDown'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { QuestionCircle } from '@zeal/uikit/Icon/QuestionCircle'
import { IconButton } from '@zeal/uikit/IconButton'
import { Input } from '@zeal/uikit/Input'
import { InputButton } from '@zeal/uikit/InputButton'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import {
    parse as parsePostalCode,
    PostalCodeValidationError,
} from '@zeal/toolkit/PostalCode'
import {
    EmptyStringError,
    matchRegExp,
    nonEmptyString,
    nonNull,
    Result,
    shape,
} from '@zeal/toolkit/Result'

import { CountryISOCode } from '@zeal/domains/Country'
import { Avatar as CountryIcon } from '@zeal/domains/Country/components/Avatar'
import { COUNTRIES_MAP } from '@zeal/domains/Country/constants'
import { ResidenceDetails } from '@zeal/domains/Currency/domains/BankTransfer/api/submitUnblockKycApplication'

export type InitialResidenceDetails = {
    country: CountryISOCode | null
    address: string | null
    postCode: string | null
    city: string | null
}

type CountryError = { type: 'country_required' }
type AddressError = EmptyStringError | { type: 'unsupported_character' }

type PostCodeError = EmptyStringError | PostalCodeValidationError | CountryError
type CityError = EmptyStringError | { type: 'unsupported_character' }

type FormErrors = {
    address?: AddressError
    postCode?: PostCodeError
    city?: CityError
    submit?:
        | AddressError
        | PostCodeError
        | CityError
        | { type: 'country_required' }
}

type Props = {
    form: InitialResidenceDetails
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_form_submitted'; completedForm: ResidenceDetails }
    | { type: 'on_select_country_click' }
    | { type: 'on_form_change'; form: InitialResidenceDetails }
    | {
          type: 'close'
      }

const formatPostCode = (postcode: string): string => {
    try {
        const ukPostCodeMatch: RegExpExecArray | null =
            [
                ...postcode.matchAll(
                    /^([A-Z]{1,2}\d[A-Z\d]?) ?(\d[A-Z]{2})$/gim
                ),
            ][0] || null

        if (ukPostCodeMatch) {
            const matches = Array.from(ukPostCodeMatch)
            return `${matches[1]} ${matches[2]}`
        }

        return postcode
    } catch {
        return postcode
    }
}

const UNBLOCK_ADDRESS_REGEXP = /^[0-9a-zA-Z,;'\-\\ ]*$/

const validateAddress = (
    form: InitialResidenceDetails
): Result<AddressError, string> =>
    nonEmptyString(form.address).andThen((address) =>
        matchRegExp(address, UNBLOCK_ADDRESS_REGEXP, {
            type: 'unsupported_character',
        })
    )

const UNBLOCK_CITY_REGEXP = /^[0-9a-zA-Z-.,&()' ]*$/

const validateCity = (input: unknown): Result<CityError, string> =>
    nonEmptyString(input).andThen((city) =>
        matchRegExp(city, UNBLOCK_CITY_REGEXP, {
            type: 'unsupported_character',
        })
    )

const validatePostCode = (
    input: unknown,
    country: CountryISOCode
): Result<PostCodeError, string> =>
    nonEmptyString(input)
        .andThen((code) => parsePostalCode(country, code))
        .map(formatPostCode)

const validateCountry = (
    input: CountryISOCode | null
): Result<CountryError, CountryISOCode> =>
    nonNull(input).mapError(() => ({
        type: 'country_required' as const,
    }))

const validateAsYouType = (
    form: InitialResidenceDetails
): Result<FormErrors, unknown> =>
    shape({
        country: nonNull(form.country).mapError(() => ({
            type: 'country_required' as const,
        })),
        address: nonEmptyString(form.address),
        postCode: nonEmptyString(form.postCode),
        city: nonEmptyString(form.city),
    }).mapError(({ country, address, postCode, city }) => ({
        submit: country || address || postCode || city,
    }))

const validateOnSubmit = (
    form: InitialResidenceDetails
): Result<FormErrors, ResidenceDetails> =>
    shape({
        country: validateCountry(form.country),
        address: validateAddress(form),
        postCode: validateCountry(form.country).andThen((country) =>
            validatePostCode(form.postCode, country)
        ),
        city: validateCity(form.city),
        submit: nonNull(form.country)
            .mapError(() => ({
                type: 'country_required' as const,
            }))
            .andThen(() =>
                validateAddress(form).andThen(() =>
                    nonEmptyString(form.postCode).andThen(() =>
                        nonEmptyString(form.city)
                    )
                )
            ),
    })

export const Layout = ({ form, onMsg }: Props) => {
    const [isSubmitted, setIsSubmitted] = useState<boolean>(false)
    const errors = isSubmitted
        ? validateOnSubmit(form).getFailureReason() || {}
        : validateAsYouType(form).getFailureReason() || {}

    const postalCodeInput = useRef<TextInput>(null)
    const cityInput = useRef<TextInput>(null)

    const { formatMessage } = useIntl()

    const onSubmit = () => {
        setIsSubmitted(true)
        const validation = validateOnSubmit(form)

        switch (validation.type) {
            case 'Failure':
                break

            case 'Success': {
                onMsg({
                    type: 'on_form_submitted',
                    completedForm: validation.data,
                })
                break
            }

            default:
                notReachable(validation)
        }
    }

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />

            <Column spacing={8} fill alignY="stretch">
                <Column spacing={24} fill>
                    <Header
                        title={
                            <FormattedMessage
                                id="bank_transfer.residence_details.title"
                                defaultMessage="Your residence"
                            />
                        }
                    />
                    <ScrollContainer withFloatingActions={false}>
                        <Column spacing={8}>
                            <Column spacing={8}>
                                <Text
                                    variant="paragraph"
                                    weight="regular"
                                    color="textSecondary"
                                >
                                    <FormattedMessage
                                        id="bank_transfer.residence_details.title"
                                        defaultMessage="Country of residence"
                                    />
                                </Text>

                                <InputButton
                                    leftIcon={
                                        form.country ? (
                                            <CountryIcon
                                                countryCode={form.country}
                                                size={28}
                                            />
                                        ) : (
                                            <QuestionCircle
                                                size={28}
                                                color="iconDefault"
                                            />
                                        )
                                    }
                                    rightIcon={
                                        <ArrowDown
                                            color="iconDisabled"
                                            size={24}
                                        />
                                    }
                                    onClick={() => {
                                        onMsg({
                                            type: 'on_select_country_click',
                                        })
                                    }}
                                >
                                    {form.country ? (
                                        COUNTRIES_MAP[form.country].name
                                    ) : (
                                        <FormattedMessage
                                            id="bank_transfer.residence_details.country_placeholder"
                                            defaultMessage="Country"
                                        />
                                    )}
                                </InputButton>
                            </Column>
                            <Column spacing={8}>
                                <Text
                                    variant="paragraph"
                                    weight="regular"
                                    color="textSecondary"
                                >
                                    <FormattedMessage
                                        id="bank_transfer.residence_details.address"
                                        defaultMessage="Your address"
                                    />
                                </Text>

                                <Input
                                    keyboardType="default"
                                    returnKeyType="next"
                                    autoComplete="street-address"
                                    blurOnSubmit={false} // prevent keyboard flashing when pressing "next"
                                    onSubmitEditing={() => {
                                        switch (ZealPlatform.OS) {
                                            case 'ios':
                                            case 'android':
                                                postalCodeInput.current?.focus()
                                                break
                                            case 'web':
                                                onSubmit()
                                                break
                                            /* istanbul ignore next */
                                            default:
                                                return notReachable(
                                                    ZealPlatform
                                                )
                                        }
                                    }}
                                    onChange={(e) =>
                                        onMsg({
                                            type: 'on_form_change',
                                            form: {
                                                ...form,
                                                address: e.nativeEvent.text,
                                            },
                                        })
                                    }
                                    state={errors.address ? 'error' : 'normal'}
                                    message={
                                        errors.address && (
                                            <AddressErrorMessage
                                                error={errors.address}
                                            />
                                        )
                                    }
                                    placeholder={formatMessage({
                                        id: 'bank_transfer.residence_details.street',
                                        defaultMessage: 'Street',
                                    })}
                                    variant="regular"
                                    value={form.address ?? ''}
                                />
                            </Column>
                            <Column spacing={8}>
                                <Text
                                    variant="paragraph"
                                    weight="regular"
                                    color="textSecondary"
                                >
                                    <FormattedMessage
                                        id="bank_transfer.residence_details.postcode"
                                        defaultMessage="Postcode"
                                    />
                                </Text>

                                <Input
                                    ref={postalCodeInput}
                                    keyboardType="default"
                                    returnKeyType="next"
                                    autoComplete="postal-code"
                                    blurOnSubmit={false} // prevent keyboard flashing when pressing "next"
                                    onSubmitEditing={() => {
                                        switch (ZealPlatform.OS) {
                                            case 'ios':
                                            case 'android':
                                                cityInput.current?.focus()
                                                break
                                            case 'web':
                                                onSubmit()
                                                break
                                            /* istanbul ignore next */
                                            default:
                                                return notReachable(
                                                    ZealPlatform
                                                )
                                        }
                                    }}
                                    onChange={(e) =>
                                        onMsg({
                                            type: 'on_form_change',
                                            form: {
                                                ...form,
                                                postCode: e.nativeEvent.text,
                                            },
                                        })
                                    }
                                    state={errors.postCode ? 'error' : 'normal'}
                                    message={
                                        errors.postCode && (
                                            <PostCodeErrorMessage
                                                error={errors.postCode}
                                            />
                                        )
                                    }
                                    placeholder="..."
                                    variant="regular"
                                    value={form.postCode ?? ''}
                                />
                            </Column>
                            <Column spacing={8}>
                                <Text
                                    variant="paragraph"
                                    weight="regular"
                                    color="textSecondary"
                                >
                                    <FormattedMessage
                                        id="bank_transfer.residence_details.city"
                                        defaultMessage="City"
                                    />
                                </Text>

                                <Input
                                    ref={cityInput}
                                    keyboardType="default"
                                    onSubmitEditing={onSubmit}
                                    onChange={(e) =>
                                        onMsg({
                                            type: 'on_form_change',
                                            form: {
                                                ...form,
                                                city: e.nativeEvent.text,
                                            },
                                        })
                                    }
                                    state={errors.city ? 'error' : 'normal'}
                                    message={
                                        errors.city && (
                                            <CityErrorMessage
                                                error={errors.city}
                                            />
                                        )
                                    }
                                    placeholder="London"
                                    variant="regular"
                                    value={form.city ?? ''}
                                />
                            </Column>
                        </Column>
                    </ScrollContainer>
                </Column>
                <Actions variant="default">
                    <Button
                        variant="primary"
                        size="regular"
                        disabled={!!errors.submit}
                        onClick={onSubmit}
                    >
                        <FormattedMessage
                            id="actions.continue"
                            defaultMessage="Continue"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}

const AddressErrorMessage = ({ error }: { error: AddressError }) => {
    switch (error.type) {
        case 'value_is_not_a_string':
        case 'string_is_empty':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.residence-form.address-required"
                    defaultMessage="Required"
                />
            )
        case 'unsupported_character':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.residence-form.address-unsupported-char"
                    defaultMessage="Only letters, numbers, spaces, and , ; ’ - \ allowed."
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}

const PostCodeErrorMessage = ({ error }: { error: PostCodeError }) => {
    switch (error.type) {
        case 'value_is_not_a_string':
        case 'string_is_empty':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.residence-form.postcode-required"
                    defaultMessage="Required"
                />
            )
        case 'postal_code_invalid':
        case 'country_for_validate_postal_code_not_found':
        case 'country_required':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.residence-form.postcode-invalid"
                    defaultMessage="Invalid postcode"
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}

const CityErrorMessage = ({ error }: { error: CityError }) => {
    switch (error.type) {
        case 'value_is_not_a_string':
        case 'string_is_empty':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.residence-form.city-required"
                    defaultMessage="Required"
                />
            )
        case 'unsupported_character':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.residence-form.address-unsupported-char"
                    defaultMessage="Only letters, numbers, spaces, and . , -  & ( ) ’ allowed."
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}
