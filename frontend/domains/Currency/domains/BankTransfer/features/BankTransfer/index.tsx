import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { useReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Address } from '@zeal/toolkit/Web3/address'

import { AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { BankTransferFork2 } from '@zeal/domains/Currency/domains/BankTransfer/features/BankTransferFork2'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { fetchServerPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import {
    BankTransferInfo,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    cardConfig: CardConfig
    bankTransferInfo: BankTransferInfo
    selectedAddress: Address

    variant: DepositWithdrawVariant
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationCampaign: string | null

    counterparties: Counterparty[]
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof BankTransferFork2>
type Data = {
    serverPortfolio: ServerPortfolio2
}
const fetch = async ({
    address,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    installationId,
    currencyHiddenMap,
}: {
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    address: Address

    installationId: string
}): Promise<Data> => {
    const [serverPortfolio] = await Promise.all([
        fetchServerPortfolio2({
            currencyHiddenMap,
            address,
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            installationId,
        }),
    ])

    return { serverPortfolio }
}

export const BankTransfer = ({
    bankTransferInfo,
    onMsg,
    gasCurrencyPresetMap,
    feePresetMap,
    currencyHiddenMap,
    currencyPinMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    keystoreMap,
    installationId,
    variant,
    installationCampaign,
    customCurrencies,
    sessionPassword,
    selectedAddress,
    accountsMap,
    cardConfig,
    defaultCurrencyConfig,
    counterparties,
}: Props) => {
    const initialPortfolio = unsafe_GetPortfolioCache2({
        portfolioMap,
        address: selectedAddress,
    })
    const [loadable, setLoadable] = useReloadableData(
        fetch,
        initialPortfolio
            ? {
                  type: 'reloading',
                  params: {
                      defaultCurrencyConfig,
                      address: selectedAddress,
                      networkMap,
                      networkRPCMap,
                      currencyHiddenMap,
                      installationId,
                  },
                  data: {
                      serverPortfolio: initialPortfolio,
                  },
              }
            : {
                  type: 'loading',
                  params: {
                      defaultCurrencyConfig,
                      address: selectedAddress,
                      networkMap,
                      currencyHiddenMap,
                      networkRPCMap,
                      installationId,
                  },
              }
    )

    switch (loadable.type) {
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />

                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break

                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loading':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed': {
            return (
                <BankTransferFork2
                    counterparties={counterparties}
                    installationCampaign={installationCampaign}
                    portfolio={loadable.data.serverPortfolio}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolioMap={portfolioMap}
                    accountsMap={accountsMap}
                    cardConfig={cardConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    customCurrencies={customCurrencies}
                    installationId={installationId}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    bankTransferInfo={bankTransferInfo}
                    currencyPinMap={currencyPinMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    keystoreMap={keystoreMap}
                    selectedAddress={selectedAddress}
                    variant={variant}
                    onMsg={onMsg}
                />
            )
        }

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
