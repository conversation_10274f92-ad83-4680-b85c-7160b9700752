import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    OffRampAccount,
    UnblockUser,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { OnRampFeeParams } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchTransactionFee'
import { OnRampFeeResponse } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockOnRampFee'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Form } from './Form'
import { ShowOnRampAccount } from './ShowOnRampAccount'
import { UpdateUnblockUserPreferences } from './UpdateUnblockUserPreferences'

type Props = {
    offRampAccounts: OffRampAccount[]
    userPreferredCryptoCurrency: CryptoCurrency
    initialCurrency: CryptoCurrency | null
    accountsMap: AccountsMap
    networkMap: NetworkMap
    keyStoreMap: KeyStoreMap
    unblockUser: UnblockUser
    depositReceiverPortfolio: ServerPortfolio2
    bankTransferInfo: BankTransferUnblockUserCreated
    unblockLoginInfo: UnblockLoginInfo
    currencies: BankTransferCurrencies
    owner: Account
    portfolioMap: PortfolioMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    network: Network
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | Extract<
          MsgOf<typeof Form>,
          {
              type:
                  | 'on_withdraw_tab_click'
                  | 'user_bank_verification_number_successfully_set'
                  | 'bank_transfer_owner_successfully_changed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_logged_in'
                  | 'on_unblock_login_success'
                  | 'on_deposit_receiver_changed_successfully'
                  | 'on_deposit_receiver_changed_successfully_from_form'
                  | 'close'
                  | 'on_withdrawal_bank_account_successfully_deleted'
                  | 'on_withdrawal_bank_account_successfully_set_as_default'
                  | 'on_withdrawal_bank_account_created'
          }
      >
    | Extract<
          MsgOf<typeof ShowOnRampAccount>,
          {
              type:
                  | 'on_on_ramp_transfer_success_close_click'
                  | 'on_contact_support_clicked'
          }
      >

type State =
    | { type: 'form' }
    | {
          type: 'update_unblock_user_preferences'
          depositFeesPollable: PollableData<OnRampFeeResponse, OnRampFeeParams>
      }
    | {
          type: 'show_on_ramp_account'
          depositFeesPollable: PollableData<OnRampFeeResponse, OnRampFeeParams>
      }

export const DepositForm = ({
    offRampAccounts,
    unblockUser,
    depositReceiverPortfolio,
    keyStoreMap,
    initialCurrency,
    networkMap,
    bankTransferInfo,
    currencies,
    network,
    accountsMap,
    unblockLoginInfo,
    userPreferredCryptoCurrency,
    currencyPinMap,
    currencyHiddenMap,
    gasCurrencyPresetMap,
    feePresetMap,
    portfolioMap,
    networkRPCMap,
    sessionPassword,
    owner,
    installationId,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'form' })
    switch (state.type) {
        case 'form':
            return (
                <Form
                    network={network}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialCurrency={initialCurrency}
                    accountsMap={accountsMap}
                    portfolioMap={portfolioMap}
                    owner={owner}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    userPreferredCryptoCurrency={userPreferredCryptoCurrency}
                    installationId={installationId}
                    loginInfo={unblockLoginInfo}
                    currencies={currencies}
                    unblockUser={unblockUser}
                    keyStoreMap={keyStoreMap}
                    bankTransferInfo={bankTransferInfo}
                    offRampAccounts={offRampAccounts}
                    depositReceiverPortfolio={depositReceiverPortfolio}
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_withdraw_tab_click':
                            case 'bank_transfer_owner_successfully_changed':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_logged_in':
                            case 'on_deposit_receiver_changed_successfully':
                            case 'on_deposit_receiver_changed_successfully_from_form':
                            case 'on_withdrawal_bank_account_successfully_deleted':
                            case 'on_withdrawal_bank_account_successfully_set_as_default':
                            case 'on_withdrawal_bank_account_created':
                                onMsg(msg)
                                break
                            case 'on_submit_form_click':
                                setState({
                                    type: 'update_unblock_user_preferences',
                                    depositFeesPollable:
                                        msg.depositFeesPollable,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'update_unblock_user_preferences':
            return (
                <UpdateUnblockUserPreferences
                    cryptoCurrency={
                        state.depositFeesPollable.params.outputCurrency
                    }
                    unblockUserLoginInfo={unblockLoginInfo}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({ type: 'form' })
                                break

                            case 'on_preferences_updated_successfully':
                                setState({
                                    type: 'show_on_ramp_account',
                                    depositFeesPollable:
                                        state.depositFeesPollable,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'show_on_ramp_account':
            return (
                <ShowOnRampAccount
                    depositFeesPollable={state.depositFeesPollable}
                    installationId={installationId}
                    networkMap={networkMap}
                    unblockUser={unblockUser}
                    currencies={currencies}
                    network={findNetworkByHexChainId(
                        state.depositFeesPollable.params.outputCurrency
                            .networkHexChainId,
                        networkMap
                    )}
                    keyStoreMap={keyStoreMap}
                    accountsMap={accountsMap}
                    unblockLoginInfo={unblockLoginInfo}
                    bankTransferInfo={bankTransferInfo}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_on_ramp_transfer_success_close_click':
                            case 'on_contact_support_clicked':
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_minimize_click':
                                onMsg({ type: 'close' })
                                break

                            case 'on_back_button_clicked':
                                setState({ type: 'form' })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
