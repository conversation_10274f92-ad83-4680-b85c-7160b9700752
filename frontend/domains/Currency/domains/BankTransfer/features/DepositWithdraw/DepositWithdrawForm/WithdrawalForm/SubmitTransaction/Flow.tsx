import { useCallback, useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { Address } from '@zeal/domains/Address'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { WithdrawalRequest } from '@zeal/domains/Currency/domains/BankTransfer'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import {
    FetchSimulationByRequest,
    fetchSimulationByRequest,
    SimulationResult,
} from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'

import { MonitorFiatTransaction } from './MonitorFiatTransaction'

type Props = {
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    bankTransferInfo: BankTransferUnblockUserCreated
    installationId: string
    sessionPassword: string
    owner: Account
    withdrawalRequest: WithdrawalRequest
    network: Network
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    feePresetMap: FeePresetMap
    ownerPortfolio: ServerPortfolio2
    toAddress: Address
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'crypto_transaction_submitted' }
    | MsgOf<typeof MonitorFiatTransaction>
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'on_minimize_click'
                  | 'import_keys_button_clicked'
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_wrong_network_accepted'
                  | 'transaction_failure_accepted'
                  | 'on_safe_transaction_failure_accepted'
                  | 'on_sign_cancel_button_clicked'
                  | 'on_transaction_cancelled_successfully_close_clicked'
                  | 'transaction_cancel_failure_accepted'
                  | 'transaction_submited'
                  | 'cancel_submitted'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_gas_currency_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'transaction_request_replaced'
                  | 'on_close_transaction_status_not_found_modal'
          }
      >

type State =
    | { type: 'send_crypto_transaction'; rpcRequest: EthSendTransaction }
    | { type: 'monitor_fiat_transaction'; transactionHash: string }

export const Flow = ({
    accountsMap,
    keystoreMap,
    installationId,
    withdrawalRequest,
    network,
    networkMap,
    networkRPCMap,
    owner,
    toAddress,
    sessionPassword,
    onMsg,
    bankTransferInfo,
    feePresetMap,
    gasCurrencyPresetMap,
    ownerPortfolio,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'send_crypto_transaction',
        rpcRequest: createTransferEthSendTransaction({
            amount: withdrawalRequest.fromAmount,
            from: owner.address,
            to: toAddress,
            network,
        }),
    })

    const fetchWithdrawalSimulation = useCallback<FetchSimulationByRequest>(
        async ({ network, requestToSimulate }): Promise<SimulationResult> => {
            const resp = await fetchSimulationByRequest({
                network,
                requestToSimulate,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                dApp: null,
            })
            switch (resp.type) {
                case 'failed':
                case 'not_supported':
                    return resp
                case 'simulated':
                    return {
                        ...resp,
                        simulation: {
                            ...resp.simulation,
                            currencies: {
                                ...resp.simulation.currencies,

                                ...(() => {
                                    switch (withdrawalRequest.type) {
                                        case 'full_withdrawal_request':
                                            return {
                                                [withdrawalRequest.fromAmount
                                                    .currency.id]:
                                                    withdrawalRequest.fromAmount
                                                        .currency,
                                                [withdrawalRequest.toAmount
                                                    .currency.id]:
                                                    withdrawalRequest.toAmount
                                                        .currency,
                                            }
                                        case 'incomplete_withdrawal_request':
                                            return {
                                                [withdrawalRequest.fromAmount
                                                    .currency.id]:
                                                    withdrawalRequest.fromAmount
                                                        .currency,
                                                [withdrawalRequest.toCurrency
                                                    .id]:
                                                    withdrawalRequest.toCurrency,
                                            }
                                        default:
                                            return notReachable(
                                                withdrawalRequest
                                            )
                                    }
                                })(),
                            },
                            transaction: {
                                type: 'WithdrawalTrx' as const,
                                withdrawalRequest,
                            },
                        },
                    }
                /* istanbul ignore next */
                default:
                    return notReachable(resp)
            }
        },
        [withdrawalRequest, defaultCurrencyConfig, networkMap, networkRPCMap]
    )

    switch (state.type) {
        case 'send_crypto_transaction':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={ownerPortfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    fetchSimulationByRequest={fetchWithdrawalSimulation}
                    fetchTransactionResultByRequest={async () => ({
                        transaction: {
                            type: 'WithdrawalTrx' as const,
                            withdrawalRequest,
                        },
                        currencies: {},
                    })}
                    key={state.rpcRequest.id}
                    sendTransactionRequests={[state.rpcRequest]}
                    sessionPassword={sessionPassword}
                    account={owner}
                    network={network}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    installationId={installationId}
                    state={{ type: 'maximised' }}
                    actionSource={{
                        type: 'internal',
                        transactionEventSource: 'offramp',
                    }}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in off-ramp $${msg.type}`
                                    )
                                )
                                break

                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'on_minimize_click':
                            case 'import_keys_button_clicked':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'transaction_submited':
                            case 'cancel_submitted':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'transaction_request_replaced':
                            case 'on_close_transaction_status_not_found_modal':
                                onMsg(msg)
                                break

                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                setState({
                                    type: 'monitor_fiat_transaction',
                                    transactionHash:
                                        msg.userOperation.bundleTransactionHash,
                                })
                                break

                            case 'on_completed_safe_transaction_close_click':
                                setState({
                                    type: 'monitor_fiat_transaction',
                                    transactionHash:
                                        msg.completedTransaction
                                            .bundleTransactionHash,
                                })
                                break

                            case 'on_transaction_completed_splash_animation_screen_competed':
                                setState({
                                    type: 'monitor_fiat_transaction',
                                    transactionHash: msg.transaction.hash,
                                })
                                break
                            case 'on_completed_transaction_close_click':
                                setState({
                                    type: 'monitor_fiat_transaction',
                                    transactionHash:
                                        msg.completedTransaction.hash,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'monitor_fiat_transaction':
            return (
                <MonitorFiatTransaction
                    installationId={installationId}
                    network={network}
                    networkMap={networkMap}
                    account={owner}
                    keyStoreMap={keystoreMap}
                    bankTransferInfo={bankTransferInfo}
                    transactionHash={state.transactionHash}
                    withdrawalRequest={withdrawalRequest}
                    onMsg={onMsg}
                />
            )

        default:
            return notReachable(state)
    }
}
