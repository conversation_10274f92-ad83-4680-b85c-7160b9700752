import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    OffRampAccount,
    UnblockUser,
    WithdrawalRequest,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Form } from './Form'
import { SubmitTransaction } from './SubmitTransaction'

type Props = {
    currencies: BankTransferCurrencies
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    networkMap: NetworkMap
    initialCurrency: CryptoCurrency | null
    feePresetMap: FeePresetMap
    installationId: string
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    loginInfo: UnblockLoginInfo
    network: Network
    bankTransferInfo: BankTransferUnblockUserCreated
    offRampAccounts: OffRampAccount[]
    unblockUser: UnblockUser
    ownerPortfolio: ServerPortfolio2
    owner: Account
    gasCurrencyPresetMap: GasCurrencyPresetMap
    userPreferredCryptoCurrency: CryptoCurrency
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    portfolioMap: PortfolioMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | Extract<
          MsgOf<typeof Form>,
          {
              type:
                  | 'close'
                  | 'on_deposit_tab_click'
                  | 'on_off_ramp_account_become_default'
                  | 'unblock_offramp_account_created'
                  | 'on_unblock_login_success'
                  | 'on_logged_in'
                  | 'bank_transfer_owner_successfully_changed'
                  | 'on_4337_gas_currency_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_deposit_receiver_changed_successfully'
                  | 'on_withdrawal_bank_account_successfully_deleted'
                  | 'on_withdrawal_bank_account_successfully_set_as_default_from_form'
                  | 'on_withdrawal_bank_account_successfully_set_as_default'
                  | 'on_withdrawal_bank_account_created'
          }
      >
    | Extract<
          MsgOf<typeof SubmitTransaction>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'kyc_applicant_created'
                  | 'on_contact_support_clicked'
                  | 'on_gas_currency_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_withdrawal_monitor_fiat_transaction_start'
                  | 'on_withdrawal_monitor_fiat_transaction_success'
          }
      >

type State =
    | { type: 'form' }
    | { type: 'submit_transaction'; form: WithdrawalRequest }

export const WithdrawalForm = ({
    currencies,
    loginInfo,
    bankTransferInfo,
    onMsg,
    initialCurrency,
    owner,
    ownerPortfolio,
    offRampAccounts,
    accountsMap,
    keystoreMap,
    networkMap,
    networkRPCMap,
    feePresetMap,
    network,
    installationId,
    sessionPassword,
    unblockUser,
    gasCurrencyPresetMap,
    userPreferredCryptoCurrency,
    currencyPinMap,
    currencyHiddenMap,
    portfolioMap,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'form' })

    switch (state.type) {
        case 'form':
            return (
                <Form
                    network={network}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialCurrency={initialCurrency}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    accountsMap={accountsMap}
                    portfolioMap={portfolioMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    unblockUser={unblockUser}
                    currencies={currencies}
                    unblockLoginInfo={loginInfo}
                    bankTransferInfo={bankTransferInfo}
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    offRampAccounts={offRampAccounts}
                    userPreferredCryptoCurrency={userPreferredCryptoCurrency}
                    ownerPortfolio={ownerPortfolio}
                    owner={owner}
                    keyStoreMap={keystoreMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_withdrawal_bank_account_successfully_deleted':
                            case 'on_withdrawal_bank_account_successfully_set_as_default_from_form':
                            case 'on_withdrawal_bank_account_successfully_set_as_default':
                            case 'on_withdrawal_bank_account_created':
                            case 'close':
                            case 'on_deposit_tab_click':
                            case 'on_logged_in':
                            case 'bank_transfer_owner_successfully_changed':
                            case 'on_4337_gas_currency_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_deposit_receiver_changed_successfully':
                                onMsg(msg)
                                break
                            case 'on_submit_form_click':
                                postUserEvent({
                                    type: 'BankTransferOffRampSubmittedEvent',
                                    installationId,
                                    inputToken: msg.form.fromAmount.currency.id,
                                    outputCurrency: (() => {
                                        switch (msg.form.type) {
                                            case 'incomplete_withdrawal_request':
                                                return msg.form.toCurrency.id
                                            case 'full_withdrawal_request':
                                                return msg.form.toAmount
                                                    .currency.id
                                            default:
                                                return notReachable(msg.form)
                                        }
                                    })(),
                                })
                                setState({
                                    type: 'submit_transaction',
                                    form: msg.form,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'submit_transaction':
            return (
                <SubmitTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    ownerPortfolio={ownerPortfolio}
                    feePresetMap={feePresetMap}
                    bankTransferInfo={bankTransferInfo}
                    unblockLoginInfo={loginInfo}
                    accountsMap={accountsMap}
                    keystoreMap={keystoreMap}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    owner={owner}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    withdrawalRequest={state.form}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_withdrawal_monitor_fiat_transaction_start':
                            case 'on_withdrawal_monitor_fiat_transaction_success':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_contact_support_clicked':
                                onMsg(msg)
                                break

                            case 'on_minimize_click':
                                onMsg({ type: 'close' })
                                break

                            case 'crypto_transaction_submitted':
                                // TODO @resetko-zeal :: do we want to do something here?
                                break

                            case 'transaction_failure_accepted':
                            case 'on_safe_transaction_failure_accepted':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_close_transaction_status_not_found_modal':
                                setState({ type: 'form' })
                                break

                            case 'transaction_submited':
                            case 'cancel_submitted':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_request_replaced':
                                // We do not want a widget for the crypto transaction, since the withdrawal itself is tracked already
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
