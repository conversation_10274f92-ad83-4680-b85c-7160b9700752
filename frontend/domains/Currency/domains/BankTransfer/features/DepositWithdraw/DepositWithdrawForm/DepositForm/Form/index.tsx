import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    OffRampAccount,
    UnblockUser,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { OnRampFeeParams } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchTransactionFee'
import { fetchUnblockOnRampFee } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockOnRampFee'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    currencies: BankTransferCurrencies
    initialCurrency: CryptoCurrency | null
    userPreferredCryptoCurrency: CryptoCurrency
    offRampAccounts: OffRampAccount[]
    depositReceiverPortfolio: ServerPortfolio2
    unblockUser: UnblockUser
    keyStoreMap: KeyStoreMap
    loginInfo: UnblockLoginInfo
    bankTransferInfo: BankTransferUnblockUserCreated
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    installationId: string
    owner: Account
    network: Network
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Layout>,
          {
              type: 'on_withdraw_tab_click' | 'on_submit_form_click'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'kyc_applicant_created'
                  | 'user_bank_verification_number_successfully_set'
                  | 'bank_transfer_owner_successfully_changed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_logged_in'
                  | 'on_unblock_login_success'
                  | 'on_deposit_receiver_changed_successfully'
                  | 'on_deposit_receiver_changed_successfully_from_form'
                  | 'on_withdrawal_bank_account_successfully_deleted'
                  | 'on_withdrawal_bank_account_successfully_set_as_default'
                  | 'on_withdrawal_bank_account_created'
          }
      >

const calculateInitialForm = ({
    offRampAccounts,
    userPreferredCryptoCurrency,
    initialCurrency,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
}: {
    userPreferredCryptoCurrency: CryptoCurrency
    initialCurrency: CryptoCurrency | null
    offRampAccounts: OffRampAccount[]
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
}): OnRampFeeParams => {
    const defaultOffRampAccount = offRampAccounts.find(
        (account) => account.mainBeneficiary
    )

    if (!defaultOffRampAccount) {
        throw new ImperativeError(
            'At least one off-ramp account is needed to access the Deposit form'
        )
    }

    return {
        type: 'fiatToCrypto',
        amount: null,
        outputCurrency: initialCurrency || userPreferredCryptoCurrency,
        inputCurrency: defaultOffRampAccount.currency,
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
    }
}

export const Form = ({
    currencies,
    userPreferredCryptoCurrency,
    offRampAccounts,
    depositReceiverPortfolio,
    unblockUser,
    loginInfo,
    keyStoreMap,
    bankTransferInfo,
    gasCurrencyPresetMap,
    feePresetMap,
    portfolioMap,
    accountsMap,
    networkRPCMap,
    sessionPassword,
    owner,
    installationId,
    initialCurrency,
    networkMap,
    currencyPinMap,
    currencyHiddenMap,
    defaultCurrencyConfig,
    network,
    onMsg,
}: Props) => {
    const [state, setState] = useState<ModalState>({ type: 'closed' })

    const [pollable, setPollable] = usePollableData(
        fetchUnblockOnRampFee,
        {
            type: 'loading',
            params: calculateInitialForm({
                offRampAccounts,
                userPreferredCryptoCurrency,
                initialCurrency,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
            }),
        },
        {
            stopIf: () => false,
            pollIntervalMilliseconds: 30000,
        }
    )

    return (
        <>
            <Layout
                accountsMap={accountsMap}
                unblockUser={unblockUser}
                networkMap={networkMap}
                pollable={pollable}
                depositReceiverPortfolio={depositReceiverPortfolio}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            onMsg(msg)
                            break
                        case 'on_amount_change':
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    amount: msg.amount,
                                },
                            })
                            break
                        case 'on_withdraw_tab_click':
                            onMsg(msg)
                            break
                        case 'on_fiat_currency_selector_click':
                            setState({ type: 'fiat_currency_selector' })
                            break
                        case 'on_provider_info_click':
                            setState({ type: 'bank_transfer_provider_info' })
                            break
                        case 'on_crypto_currency_selector_click':
                        case 'on_network_selector_click':
                            setState({ type: 'crypto_currency_selector' })
                            break
                        case 'on_submit_form_click':
                            onMsg(msg)
                            break
                        case 'on_settings_icon_click':
                            setState({ type: 'settings' })
                            break
                        case 'on_deposit_receiver_click':
                            setState({ type: 'change_deposit_receiver' })
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                network={network}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                portfolioMap={portfolioMap}
                offRampAccounts={offRampAccounts}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                networkRPCMap={networkRPCMap}
                sessionPassword={sessionPassword}
                owner={owner}
                installationId={installationId}
                currencyPinMap={currencyPinMap}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                depositReceiverPortfolio={depositReceiverPortfolio}
                keyStoreMap={keyStoreMap}
                loginInfo={loginInfo}
                bankTransferInfo={bankTransferInfo}
                unblockUser={unblockUser}
                currencies={currencies}
                state={state}
                pollable={pollable}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setState({ type: 'closed' })
                            break
                        case 'on_fiat_currency_selected':
                            setState({ type: 'closed' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    inputCurrency: msg.currency,
                                },
                            })
                            break
                        case 'on_crypto_currency_selected':
                            setState({ type: 'closed' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    outputCurrency: msg.currency,
                                },
                            })
                            break
                        case 'bank_transfer_owner_successfully_changed':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'on_logged_in':
                        case 'on_deposit_receiver_changed_successfully':
                        case 'on_withdrawal_bank_account_successfully_deleted':
                        case 'on_withdrawal_bank_account_successfully_set_as_default':
                        case 'on_withdrawal_bank_account_created':
                            onMsg(msg)
                            break
                        case 'on_deposit_receiver_changed_successfully_from_form':
                            setState({ type: 'closed' })
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
