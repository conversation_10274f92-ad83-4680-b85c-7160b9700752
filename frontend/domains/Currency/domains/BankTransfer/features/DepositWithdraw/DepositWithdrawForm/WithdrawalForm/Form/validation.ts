import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { CryptoCurrency } from '@zeal/domains/Currency'
import {
    WithdrawalRequest,
    WithdrawPollable,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { OffRampFeeParams } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchTransactionFee'
import {
    MINIMUM_TRANSFER_AMOUNT_IN_USD,
    POST_KYC_TRANSFER_LIMIT_IN_USD,
} from '@zeal/domains/Currency/domains/BankTransfer/constants'
import { applyRate2, mergeRates } from '@zeal/domains/FXRate/helpers/applyRate'
import { revert2 } from '@zeal/domains/FXRate/helpers/revert'
import { CryptoMoney } from '@zeal/domains/Money'
import { isGreaterThan2 } from '@zeal/domains/Money/helpers/compare'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

export type MaxBalanceLoadable = LoadableData<
    CryptoMoney,
    {
        cryptoCurrency: CryptoCurrency
        portfolio: ServerPortfolio2
    }
>

export type FormErrors = {
    input?: InsufficientBalanceError | MinimumAmountError
    balance?: InsufficientBalanceError
    banner?: BannerError
    submit?:
        | InsufficientBalanceError
        | BannerError
        | MinimumAmountError
        | MaxBalanceStillLoading
        | { type: 'amount_is_zero' }
}
type MaxBalanceStillLoading = { type: 'max_balance_still_loading' }

export type InsufficientBalanceError = { type: 'insufficient_balance' }

export type MinimumAmountError = {
    type: 'minimum_amount'
    limit: CryptoMoney
}

export type BannerError = MaxWithdrawError

export type MaxWithdrawError =
    | { type: 'post_kyc_limit_reached' }
    | { type: 'pollable_loading' }

export const getMaxBalance = ({
    maxBalanceLoadable,
    ownerPortfolio,
}: {
    maxBalanceLoadable: MaxBalanceLoadable
    ownerPortfolio: ServerPortfolio2
}) => {
    switch (maxBalanceLoadable.type) {
        case 'loading':
        case 'error':
            return getBalanceByCryptoCurrency2({
                currency: maxBalanceLoadable.params.cryptoCurrency,
                serverPortfolio: ownerPortfolio,
            })

        case 'loaded':
            return maxBalanceLoadable.data
        /* istanbul ignore next */
        default:
            return notReachable(maxBalanceLoadable)
    }
}

export const validateAsUserTypes = (
    pollable: WithdrawPollable
): Result<FormErrors, unknown> => {
    return shape({
        banner: validateKYCWithdrawalLimit(pollable),

        submit: validateKYCWithdrawalLimit(pollable).andThen(() =>
            validateAmountNotZero(pollable)
        ),
    })
}

export const validateOnSubmit = ({
    pollable,
    maxBalanceLoadable,
    ownerPortfolio,
}: {
    pollable: WithdrawPollable
    maxBalanceLoadable: MaxBalanceLoadable
    ownerPortfolio: ServerPortfolio2
}): Result<FormErrors, WithdrawalRequest> =>
    shape({
        input: validateBalanceAmount({
            pollable,
            maxBalanceLoadable,
            ownerPortfolio,
        }).andThen(() => validateMinimumAmount(pollable)),

        balance: validateBalanceAmount({
            pollable,
            maxBalanceLoadable,
            ownerPortfolio,
        }),

        banner: validateKYCWithdrawalLimit(pollable),
        submitText: validateBalanceAmount({
            pollable,
            maxBalanceLoadable,
            ownerPortfolio,
        }).andThen(() => validateMinimumAmount(pollable)),

        submit: validateBalanceAmount({
            pollable,
            maxBalanceLoadable,
            ownerPortfolio,
        })
            .andThen(() => validateMinimumAmount(pollable))
            .andThen(() => validateKYCWithdrawalLimit(pollable))
            .andThen(() => validateAmountNotZero(pollable))
            .andThen(() => validateMaxBalanceLoadable({ maxBalanceLoadable })),
    }).map(() => {
        const fromAmount = {
            amount: fromFixedWithFraction(
                pollable.params.amount,
                pollable.params.inputCurrency.fraction
            ),
            currency: pollable.params.inputCurrency,
        }

        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
            case 'subsequent_failed':
                return {
                    type: 'full_withdrawal_request',
                    fromAmount,
                    toAmount: applyRate2({
                        baseAmount: {
                            amount: fromFixedWithFraction(
                                pollable.params.amount,
                                pollable.params.inputCurrency.fraction
                            ),
                            currency: pollable.params.inputCurrency,
                        },
                        rate: pollable.data.rate,
                    }),

                    fee: pollable.data.fee,
                }

            case 'loading':
            case 'error':
                return {
                    type: 'incomplete_withdrawal_request',
                    fromAmount,
                    toCurrency: pollable.params.outputCurrency,
                }

            /* istanbul ignore next */
            default:
                return notReachable(pollable)
        }
    })

const validateAmountNotZero = (
    pollable: WithdrawPollable
): Result<{ type: 'amount_is_zero' }, unknown> => {
    return fromFixedWithFraction(
        pollable.params.amount,
        pollable.params.inputCurrency.fraction
    ) > 0n
        ? success(undefined)
        : failure({ type: 'amount_is_zero' })
}

const validateMinimumAmount = (
    pollable: WithdrawPollable
): Result<MinimumAmountError, void> => {
    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed': {
            const inputAmount: CryptoMoney = {
                amount: fromFixedWithFraction(
                    pollable.params.amount,
                    pollable.params.inputCurrency.fraction
                ),
                currency: pollable.params.inputCurrency,
            }

            const minimumAmountInInputCurrency =
                pollable.data.defaultCurrencyRateToUSD &&
                pollable.data.defaultCurrencyRateToInputCurrency &&
                applyRate2({
                    baseAmount: MINIMUM_TRANSFER_AMOUNT_IN_USD,
                    rate: mergeRates({
                        rateA: pollable.data.defaultCurrencyRateToUSD,
                        rateB: revert2({
                            rate: pollable.data
                                .defaultCurrencyRateToInputCurrency,
                            extraPrecision: Math.abs(
                                pollable.data.defaultCurrencyRateToInputCurrency
                                    .base.fraction -
                                    pollable.data
                                        .defaultCurrencyRateToInputCurrency
                                        .quote.fraction
                            ),
                        }),
                    }),
                })

            return minimumAmountInInputCurrency &&
                isGreaterThan2(minimumAmountInInputCurrency, inputAmount)
                ? failure({
                      type: 'minimum_amount',
                      limit: minimumAmountInInputCurrency,
                  })
                : success(undefined)
        }
        case 'loading':
        case 'error':
            return success(undefined)

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}

const validateBalanceAmount = ({
    pollable,
    maxBalanceLoadable,
    ownerPortfolio,
}: {
    pollable: WithdrawPollable
    maxBalanceLoadable: MaxBalanceLoadable
    ownerPortfolio: ServerPortfolio2
}): Result<{ type: 'insufficient_balance' }, void> => {
    const balance = getMaxBalance({
        maxBalanceLoadable,
        ownerPortfolio,
    })

    const inputAmount: CryptoMoney = {
        amount: fromFixedWithFraction(
            pollable.params.amount,
            pollable.params.inputCurrency.fraction
        ),
        currency: pollable.params.inputCurrency,
    }

    return isGreaterThan2(inputAmount, balance)
        ? failure({ type: 'insufficient_balance' })
        : success(undefined)
}

const validateMaxBalanceLoadable = ({
    maxBalanceLoadable,
}: {
    maxBalanceLoadable: MaxBalanceLoadable
}): Result<MaxBalanceStillLoading, unknown> => {
    switch (maxBalanceLoadable.type) {
        case 'loading':
            return failure({ type: 'max_balance_still_loading' })
        case 'error':
        case 'loaded':
            return success(undefined)

        /* istanbul ignore next */
        default:
            return notReachable(maxBalanceLoadable)
    }
}

const validateKYCWithdrawalLimit = (
    pollable: WithdrawPollable
): Result<MaxWithdrawError, OffRampFeeParams> => {
    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed': {
            const imputAmount: CryptoMoney = {
                amount: fromFixedWithFraction(
                    pollable.params.amount,
                    pollable.params.inputCurrency.fraction
                ),
                currency: pollable.params.inputCurrency,
            }

            const inputAmountInUsd =
                pollable.data.defaultCurrencyRateToUSD &&
                pollable.data.defaultCurrencyRateToInputCurrency
                    ? applyRate2({
                          baseAmount: imputAmount,
                          rate: mergeRates({
                              rateA: pollable.data
                                  .defaultCurrencyRateToInputCurrency,
                              rateB: revert2({
                                  rate: pollable.data.defaultCurrencyRateToUSD,
                                  extraPrecision: 0,
                              }),
                          }),
                      })
                    : null

            return inputAmountInUsd &&
                isGreaterThan2(inputAmountInUsd, POST_KYC_TRANSFER_LIMIT_IN_USD)
                ? failure({ type: 'post_kyc_limit_reached' })
                : success(pollable.params)
        }
        case 'loading':
            return failure({ type: 'pollable_loading' })
        case 'error':
            return success(pollable.params)
        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}
