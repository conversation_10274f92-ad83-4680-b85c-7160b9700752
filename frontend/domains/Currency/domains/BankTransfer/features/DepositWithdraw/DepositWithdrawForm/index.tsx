import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    DepositWithdrawVariant,
    OffRampAccount,
    UnblockUser,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { fetchUserPreferredCryptoCurrency } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUserPreferredCryptoCurrency'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { BankTransferLoadingLayout } from '@zeal/domains/Currency/domains/BankTransfer/components/BankTransferLoadingLayout'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { fetchServerPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import {
    BankTransferUnblockUserCreated,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Form } from './Form'

type Props = {
    customCurrencies: CustomCurrencyMap
    unblockUser: UnblockUser
    bankTransferCurrencies: BankTransferCurrencies
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    offRampAccounts: OffRampAccount[]
    installationId: string
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    portfolioMap: PortfolioMap
    owner: Account
    loginInfo: UnblockLoginInfo
    bankTransferInfo: BankTransferUnblockUserCreated
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    cardConfig: CardConfig
    initialCurrency: CryptoCurrency | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    network: Network
    variant: DepositWithdrawVariant
    onMsg: (msg: Msg) => void
}

export type Msg = Extract<
    MsgOf<typeof Form>,
    {
        type:
            | 'close'
            | 'unblock_offramp_account_created'
            | 'on_off_ramp_account_become_default'
            | 'on_withdrawal_monitor_fiat_transaction_start'
            | 'on_withdrawal_monitor_fiat_transaction_success'
            | 'import_keys_button_clicked'
            | 'on_predefined_fee_preset_selected'
            | 'on_on_ramp_transfer_success_close_click'
            | 'on_gas_currency_selected'
            | 'on_4337_auto_gas_token_selection_clicked'
            | 'on_4337_gas_currency_selected'
            | 'on_contact_support_clicked'
            | 'on_kyc_update_details_clicked'
            | 'on_kyc_start_verification_clicked'
            | 'bank_transfer_owner_successfully_changed'
            | 'on_unblock_login_success'
            | 'on_logged_in'
            | 'on_withdrawal_bank_account_successfully_deleted'
            | 'on_withdrawal_bank_account_successfully_set_as_default_from_form'
            | 'on_withdrawal_bank_account_successfully_set_as_default'
            | 'on_withdrawal_bank_account_created'
            | 'on_deposit_receiver_changed_successfully'
            | 'on_deposit_receiver_changed_successfully_from_form'
    }
>

type Data = {
    ownerPortfolio: ServerPortfolio2
    depositReceiverPortfolio: ServerPortfolio2
    userPreferredCryptoCurrency: CryptoCurrency
}

const fetch = async ({
    bankTransferInfo,
    unblockLoginInfo,
    bankTransferCurrencies,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    installationId,
    unblockUser,
    currencyHiddenMap,
}: {
    bankTransferInfo: BankTransferUnblockUserCreated
    currencyHiddenMap: CurrencyHiddenMap
    unblockLoginInfo: UnblockLoginInfo
    bankTransferCurrencies: BankTransferCurrencies
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    unblockUser: UnblockUser
    installationId: string
}): Promise<Data> => {
    const owner = Web3.address
        .fromString(bankTransferInfo.connectedWalletAddress)
        .getSuccessResultOrThrow('Failed to parse owner from') // TODO @resetko-zeal remove parsing from here and do correct type parsing in the original parser

    const [
        userPreferredCryptoCurrency,
        ownerPortfolio,
        depositReceiverPortfolio,
    ] = await Promise.all([
        fetchUserPreferredCryptoCurrency({
            unblockLoginInfo,
            bankTransferCryptoCurrencies:
                bankTransferCurrencies.cryptoCurrencies,
        }),
        fetchServerPortfolio2({
            address: bankTransferInfo.connectedWalletAddress,
            defaultCurrencyConfig,
            currencyHiddenMap,
            networkMap,
            networkRPCMap,
            installationId,
        }),
        owner !== unblockUser.depositReceiverAddress
            ? fetchServerPortfolio2({
                  currencyHiddenMap,
                  address: unblockUser.depositReceiverAddress,
                  defaultCurrencyConfig,
                  networkMap,
                  networkRPCMap,
                  installationId,
              })
            : null,
    ])

    return {
        ownerPortfolio,
        depositReceiverPortfolio: depositReceiverPortfolio || ownerPortfolio,
        userPreferredCryptoCurrency,
    }
}

export const DepositWithdrawForm = ({
    customCurrencies,
    bankTransferCurrencies,
    accountsMap,
    keystoreMap,
    networkMap,
    installationId,
    network,
    sessionPassword,
    initialCurrency,
    offRampAccounts,
    networkRPCMap,
    loginInfo,
    unblockUser,
    bankTransferInfo,
    onMsg,
    owner,
    portfolioMap,
    feePresetMap,
    currencyPinMap,
    currencyHiddenMap,
    cardConfig,
    variant,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
}: Props) => {
    const [loadable, setLoadable] = useReloadableData(fetch, {
        type: 'loading',
        params: {
            portfolioMap,
            currencyHiddenMap,
            bankTransferInfo,
            unblockLoginInfo: loginInfo,
            customCurrencies,
            bankTransferCurrencies,
            networkMap,
            networkRPCMap,
            cardConfig,
            unblockUser,
            defaultCurrencyConfig,
            installationId,
        },
    })

    useEffect(() => {
        switch (loadable.type) {
            case 'subsequent_failed':
                captureError(loadable.error)
                break
            case 'loaded':
            case 'reloading':
            case 'loading':
                break
            case 'error':
                // Should be reported by component
                break
            default:
                return notReachable(loadable)
        }
    }, [loadable])

    switch (loadable.type) {
        case 'loading':
            return <BankTransferLoadingLayout onMsg={onMsg} />
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed':
            return (
                <Form
                    variant={variant}
                    network={network}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialCurrency={initialCurrency}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    ownerPortfolio={loadable.data.ownerPortfolio}
                    depositReceiverPortfolio={
                        loadable.data.depositReceiverPortfolio
                    }
                    feePresetMap={feePresetMap}
                    accountsMap={accountsMap}
                    keystoreMap={keystoreMap}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    unblockLoginInfo={loginInfo}
                    bankTransferInfo={bankTransferInfo}
                    offRampAccounts={offRampAccounts}
                    unblockUser={unblockUser}
                    userPreferredCryptoCurrency={
                        loadable.data.userPreferredCryptoCurrency
                    }
                    currencies={bankTransferCurrencies}
                    owner={owner}
                    portfolioMap={portfolioMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_withdrawal_monitor_fiat_transaction_start':
                            case 'on_withdrawal_monitor_fiat_transaction_success':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_on_ramp_transfer_success_close_click':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_contact_support_clicked':
                            case 'bank_transfer_owner_successfully_changed':
                            case 'on_logged_in':
                            case 'on_withdrawal_bank_account_successfully_deleted':
                            case 'on_withdrawal_bank_account_successfully_set_as_default_from_form':
                            case 'on_withdrawal_bank_account_successfully_set_as_default':
                            case 'on_withdrawal_bank_account_created':
                            case 'on_deposit_receiver_changed_successfully':
                            case 'on_deposit_receiver_changed_successfully_from_form':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'error':
            return (
                <>
                    <BankTransferLoadingLayout onMsg={onMsg} />
                    <AppErrorPopup
                        installationId={installationId}
                        error={parseAppError(loadable.error)}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
