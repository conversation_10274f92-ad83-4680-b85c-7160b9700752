import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    OffRampAccount,
    UnblockUser,
    WithdrawPollable,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { CryptoCurrencySelector } from '@zeal/domains/Currency/domains/BankTransfer/components/CryptoCurrencySelector'
import { UnblockProviderInfoPopup } from '@zeal/domains/Currency/domains/BankTransfer/components/UnblockProviderInfoPopup'
import { ChangeWithdrawalBankAccount } from '@zeal/domains/Currency/domains/BankTransfer/features/ChangeWithdrawalBankAccount'
import { Settings } from '@zeal/domains/Currency/domains/BankTransfer/features/Settings'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    pollable: WithdrawPollable
    state: State
    currencies: BankTransferCurrencies
    offRampAccounts: OffRampAccount[]
    bankTransferInfo: BankTransferUnblockUserCreated
    unblockUser: UnblockUser
    unblockLoginInfo: UnblockLoginInfo
    owner: Account
    keyStoreMap: KeyStoreMap
    installationId: string

    ownerPortfolio: ServerPortfolio2
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    network: Network
    sessionPassword: string
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | {
          type: 'on_withdrawal_bank_account_successfully_set_as_default_from_form'
          bankAccount: OffRampAccount
      }
    | MsgOf<typeof CryptoCurrencySelector>
    | MsgOf<typeof Settings>

export type State =
    | { type: 'closed' }
    | { type: 'crypto_currency_selector' }
    | { type: 'bank_transfer_provider_info' }
    | { type: 'settings' }
    | { type: 'change_withdrawal_receiver' }

export const Modal = ({
    state,
    network,
    pollable,
    currencies,
    unblockLoginInfo,
    bankTransferInfo,
    owner,
    keyStoreMap,
    offRampAccounts,
    unblockUser,
    ownerPortfolio,
    currencyPinMap,
    currencyHiddenMap,
    networkMap,
    installationId,
    sessionPassword,
    feePresetMap,
    gasCurrencyPresetMap,
    portfolioMap,
    accountsMap,
    networkRPCMap,

    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'crypto_currency_selector':
            return (
                <UIModal>
                    <CryptoCurrencySelector
                        selected={pollable.params.inputCurrency}
                        portfolio={ownerPortfolio}
                        currencyPinMap={currencyPinMap}
                        currencyHiddenMap={currencyHiddenMap}
                        networkMap={networkMap}
                        cryptoCurrencies={currencies.cryptoCurrencies}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'bank_transfer_provider_info':
            return <UnblockProviderInfoPopup onMsg={onMsg} />
        case 'settings':
            return (
                <UIModal>
                    <Settings
                        network={network}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        currencies={currencies}
                        owner={owner}
                        unblockLoginInfo={unblockLoginInfo}
                        portfolioMap={portfolioMap}
                        unblockUser={unblockUser}
                        offRampAccounts={offRampAccounts}
                        accountsMap={accountsMap}
                        keyStoreMap={keyStoreMap}
                        bankTransferInfo={bankTransferInfo}
                        installationId={installationId}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        sessionPassword={sessionPassword}
                        feePresetMap={feePresetMap}
                        currenciesHiddenMap={currencyHiddenMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'change_withdrawal_receiver':
            return (
                <UIModal>
                    <ChangeWithdrawalBankAccount
                        offRampAccounts={offRampAccounts}
                        bankTransferInfo={bankTransferInfo}
                        currencies={currencies}
                        unblockLoginInfo={unblockLoginInfo}
                        location="bank_withdrawal_screen"
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                case 'on_withdrawal_bank_account_created':
                                case 'on_withdrawal_bank_account_successfully_deleted':
                                    onMsg(msg)
                                    break
                                case 'on_withdrawal_bank_account_successfully_set_as_default':
                                    onMsg({
                                        type: 'on_withdrawal_bank_account_successfully_set_as_default_from_form', // we re-map because in this case modal should be closed, but if changed in Settings modal should stay open
                                        bankAccount: msg.bankAccount,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
