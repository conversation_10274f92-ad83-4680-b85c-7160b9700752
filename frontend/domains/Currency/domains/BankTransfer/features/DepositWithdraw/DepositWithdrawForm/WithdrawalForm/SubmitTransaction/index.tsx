import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { WithdrawalRequest } from '@zeal/domains/Currency/domains/BankTransfer'
import { fetchUserOffRampAddress } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUserOffRampAddress'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { BankTransferLoadingLayout } from '@zeal/domains/Currency/domains/BankTransfer/components/BankTransferLoadingLayout'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Flow } from './Flow'

type Props = {
    unblockLoginInfo: UnblockLoginInfo
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    installationId: string
    sessionPassword: string
    owner: Account
    withdrawalRequest: WithdrawalRequest
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    bankTransferInfo: BankTransferUnblockUserCreated
    ownerPortfolio: ServerPortfolio2
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg = MsgOf<typeof Flow>

export const SubmitTransaction = ({
    unblockLoginInfo,
    accountsMap,
    keystoreMap,
    installationId,
    sessionPassword,
    owner,
    networkMap,
    networkRPCMap,
    onMsg,
    bankTransferInfo,
    withdrawalRequest,
    feePresetMap,
    ownerPortfolio,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
}: Props) => {
    const network = findNetworkByHexChainId(
        withdrawalRequest.fromAmount.currency.networkHexChainId,
        networkMap
    )

    const [loadable, setLoadable] = useLoadableData(fetchUserOffRampAddress, {
        type: 'loading',
        params: {
            unblockLoginInfo,
            bankTransferInfo,
            network,
        },
    })

    switch (loadable.type) {
        case 'loading':
            return <BankTransferLoadingLayout onMsg={onMsg} />
        case 'loaded':
            return (
                <Flow
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    ownerPortfolio={ownerPortfolio}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    feePresetMap={feePresetMap}
                    bankTransferInfo={bankTransferInfo}
                    accountsMap={accountsMap}
                    keystoreMap={keystoreMap}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    owner={owner}
                    withdrawalRequest={withdrawalRequest}
                    network={network}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    toAddress={loadable.data}
                    onMsg={onMsg}
                />
            )
        case 'error':
            return (
                <>
                    <BankTransferLoadingLayout onMsg={onMsg} />
                    <AppErrorPopup
                        installationId={installationId}
                        error={parseAppError(loadable.error)}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break

                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break

                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
