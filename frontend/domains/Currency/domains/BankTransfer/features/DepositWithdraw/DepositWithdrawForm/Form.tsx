import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    DepositWithdrawVariant,
    OffRampAccount,
    UnblockUser,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { DepositForm } from './DepositForm'
import { WithdrawalForm } from './WithdrawalForm'

type Props = {
    variant: DepositWithdrawVariant
    ownerPortfolio: ServerPortfolio2
    initialCurrency: CryptoCurrency | null
    depositReceiverPortfolio: ServerPortfolio2
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    installationId: string
    sessionPassword: string
    unblockLoginInfo: UnblockLoginInfo
    bankTransferInfo: BankTransferUnblockUserCreated
    offRampAccounts: OffRampAccount[]
    currencies: BankTransferCurrencies
    unblockUser: UnblockUser
    userPreferredCryptoCurrency: CryptoCurrency
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    owner: Account
    network: Network
    portfolioMap: PortfolioMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | Extract<
          MsgOf<typeof WithdrawalForm>,
          {
              type:
                  | 'close'
                  | 'import_keys_button_clicked'
                  | 'on_off_ramp_account_become_default'
                  | 'unblock_offramp_account_created'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_withdrawal_monitor_fiat_transaction_success'
                  | 'on_withdrawal_monitor_fiat_transaction_start'
                  | 'on_gas_currency_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'bank_transfer_owner_successfully_changed'
                  | 'on_logged_in'
                  | 'on_unblock_login_success'
                  | 'on_deposit_receiver_changed_successfully'
                  | 'on_withdrawal_bank_account_successfully_deleted'
                  | 'on_withdrawal_bank_account_successfully_set_as_default_from_form'
                  | 'on_withdrawal_bank_account_successfully_set_as_default'
                  | 'on_withdrawal_bank_account_created'
          }
      >
    | Extract<
          MsgOf<typeof DepositForm>,
          {
              type:
                  | 'close'
                  | 'on_on_ramp_transfer_success_close_click'
                  | 'user_bank_verification_number_successfully_set'
                  | 'on_contact_support_clicked'
                  | 'bank_transfer_owner_successfully_changed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_logged_in'
                  | 'on_unblock_login_success'
                  | 'on_deposit_receiver_changed_successfully'
                  | 'on_deposit_receiver_changed_successfully_from_form'
                  | 'on_withdrawal_bank_account_successfully_deleted'
                  | 'on_withdrawal_bank_account_successfully_set_as_default'
                  | 'on_withdrawal_bank_account_created'
          }
      >

export const Form = ({
    accountsMap,
    ownerPortfolio,
    depositReceiverPortfolio,
    keystoreMap,
    networkMap,
    initialCurrency,
    networkRPCMap,
    installationId,
    sessionPassword,
    unblockLoginInfo,
    bankTransferInfo,
    offRampAccounts,
    currencies,
    onMsg,
    network,
    feePresetMap,
    unblockUser,
    userPreferredCryptoCurrency,
    gasCurrencyPresetMap,
    currencyPinMap,
    currencyHiddenMap,
    portfolioMap,
    owner,
    defaultCurrencyConfig,
    variant,
}: Props) => {
    const [state, setState] = useState<DepositWithdrawVariant>(variant)

    switch (state.type) {
        case 'deposit':
            return (
                <DepositForm
                    network={network}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialCurrency={initialCurrency}
                    installationId={installationId}
                    networkMap={networkMap}
                    currencies={currencies}
                    unblockUser={unblockUser}
                    userPreferredCryptoCurrency={userPreferredCryptoCurrency}
                    keyStoreMap={keystoreMap}
                    unblockLoginInfo={unblockLoginInfo}
                    bankTransferInfo={bankTransferInfo}
                    offRampAccounts={offRampAccounts}
                    accountsMap={accountsMap}
                    depositReceiverPortfolio={depositReceiverPortfolio}
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    feePresetMap={feePresetMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    owner={owner}
                    portfolioMap={portfolioMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_on_ramp_transfer_success_close_click':
                            case 'on_contact_support_clicked':
                            case 'bank_transfer_owner_successfully_changed':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_logged_in':
                            case 'on_deposit_receiver_changed_successfully':
                            case 'on_deposit_receiver_changed_successfully_from_form':
                            case 'on_withdrawal_bank_account_successfully_deleted':
                            case 'on_withdrawal_bank_account_successfully_set_as_default':
                            case 'on_withdrawal_bank_account_created':
                                onMsg(msg)
                                break
                            case 'on_withdraw_tab_click':
                                setState({ type: 'withdraw' })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'withdraw':
            return (
                <WithdrawalForm
                    network={network}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialCurrency={initialCurrency}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    ownerPortfolio={ownerPortfolio}
                    owner={owner}
                    portfolioMap={portfolioMap}
                    unblockUser={unblockUser}
                    feePresetMap={feePresetMap}
                    currencies={currencies}
                    accountsMap={accountsMap}
                    keystoreMap={keystoreMap}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    loginInfo={unblockLoginInfo}
                    bankTransferInfo={bankTransferInfo}
                    offRampAccounts={offRampAccounts}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    userPreferredCryptoCurrency={userPreferredCryptoCurrency}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'import_keys_button_clicked':
                            case 'close':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_withdrawal_monitor_fiat_transaction_start':
                            case 'on_withdrawal_monitor_fiat_transaction_success':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_contact_support_clicked':
                            case 'on_logged_in':
                            case 'bank_transfer_owner_successfully_changed':
                            case 'on_deposit_receiver_changed_successfully':
                            case 'on_withdrawal_bank_account_successfully_deleted':
                            case 'on_withdrawal_bank_account_successfully_set_as_default_from_form':
                            case 'on_withdrawal_bank_account_successfully_set_as_default':
                            case 'on_withdrawal_bank_account_created':
                                onMsg(msg)
                                break
                            case 'on_deposit_tab_click':
                                setState({ type: 'deposit' })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
