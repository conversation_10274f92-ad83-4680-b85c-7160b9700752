import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { values } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    DepositPollable,
    OffRampAccount,
    UnblockUser,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { CryptoCurrencySelector } from '@zeal/domains/Currency/domains/BankTransfer/components/CryptoCurrencySelector'
import { FiatCurrencySelector } from '@zeal/domains/Currency/domains/BankTransfer/components/FiatCurrencySelector'
import { UnblockProviderInfoPopup } from '@zeal/domains/Currency/domains/BankTransfer/components/UnblockProviderInfoPopup'
import { ChangeDepositReceiverAddress } from '@zeal/domains/Currency/domains/BankTransfer/features/ChangeDepositReceiverAddress'
import { Settings } from '@zeal/domains/Currency/domains/BankTransfer/features/Settings'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferUnblockUserCreated,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    state: State
    pollable: DepositPollable
    currencies: BankTransferCurrencies
    keyStoreMap: KeyStoreMap
    unblockUser: UnblockUser
    bankTransferInfo: BankTransferUnblockUserCreated
    loginInfo: UnblockLoginInfo
    installationId: string

    depositReceiverPortfolio: ServerPortfolio2
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    owner: Account
    network: Network
    portfolioMap: PortfolioMap
    offRampAccounts: OffRampAccount[]
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | {
          type: 'on_deposit_receiver_changed_successfully_from_form'
          newDepositReceiver: Account
      }
    | MsgOf<typeof FiatCurrencySelector>
    | MsgOf<typeof CryptoCurrencySelector>
    | MsgOf<typeof Settings>

export type State =
    | { type: 'closed' }
    | { type: 'fiat_currency_selector' }
    | { type: 'crypto_currency_selector' }
    | { type: 'bank_transfer_provider_info' }
    | { type: 'settings' }
    | { type: 'change_deposit_receiver' }

export const Modal = ({
    state,
    pollable,
    keyStoreMap,
    currencies,
    unblockUser,
    loginInfo,
    installationId,
    bankTransferInfo,
    currencyPinMap,
    currencyHiddenMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    accountsMap,
    feePresetMap,
    network,
    gasCurrencyPresetMap,
    offRampAccounts,
    sessionPassword,
    owner,
    depositReceiverPortfolio,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'fiat_currency_selector':
            return (
                <UIModal>
                    <FiatCurrencySelector
                        selected={pollable.params.inputCurrency}
                        fiatCurrencies={values(currencies.fiatCurrencies)}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'crypto_currency_selector':
            return (
                <UIModal>
                    <CryptoCurrencySelector
                        selected={pollable.params.outputCurrency}
                        portfolio={depositReceiverPortfolio}
                        currencyPinMap={currencyPinMap}
                        currencyHiddenMap={currencyHiddenMap}
                        networkMap={networkMap}
                        cryptoCurrencies={currencies.cryptoCurrencies}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'bank_transfer_provider_info':
            return <UnblockProviderInfoPopup onMsg={onMsg} />

        case 'settings':
            return (
                <UIModal>
                    <Settings
                        network={network}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        currencies={currencies}
                        owner={owner}
                        unblockLoginInfo={loginInfo}
                        portfolioMap={portfolioMap}
                        unblockUser={unblockUser}
                        offRampAccounts={offRampAccounts}
                        accountsMap={accountsMap}
                        keyStoreMap={keyStoreMap}
                        bankTransferInfo={bankTransferInfo}
                        installationId={installationId}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        sessionPassword={sessionPassword}
                        feePresetMap={feePresetMap}
                        currenciesHiddenMap={currencyHiddenMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'change_deposit_receiver':
            return (
                <UIModal>
                    <ChangeDepositReceiverAddress
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        accountsMap={accountsMap}
                        keyStoreMap={keyStoreMap}
                        portfolioMap={portfolioMap}
                        unblockUser={unblockUser}
                        unblockLoginInfo={loginInfo}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        location="bank_deposit_screen"
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'on_deposit_receiver_changed_successfully':
                                    onMsg({
                                        type: 'on_deposit_receiver_changed_successfully_from_form', // we re-map because in this case modal should be closed, but if changed in Settings modal should stay open
                                        newDepositReceiver:
                                            msg.newDepositReceiver,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
