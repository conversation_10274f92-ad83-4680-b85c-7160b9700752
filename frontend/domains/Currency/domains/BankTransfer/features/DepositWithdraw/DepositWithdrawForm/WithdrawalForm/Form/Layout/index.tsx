import { useState } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { BannerLimit } from '@zeal/uikit/BannerLimit'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { FeeInputButton } from '@zeal/uikit/FeeInputButton'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldDangerTriangle } from '@zeal/uikit/Icon/BoldDangerTriangle'
import { BoldGeneralBank } from '@zeal/uikit/Icon/BoldGeneralBank'
import { BoldSetting } from '@zeal/uikit/Icon/BoldSetting'
import { ForwardIcon } from '@zeal/uikit/Icon/ForwardIcon'
import { InfoCircleOutline } from '@zeal/uikit/Icon/InfoCircleOutline'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { Unblock } from '@zeal/uikit/Icon/Providers/Unblock'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { ListItemButton } from '@zeal/uikit/ListItem'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Spacer } from '@zeal/uikit/Spacer'
import { TabHeader } from '@zeal/uikit/TabHeader'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'

import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import {
    BankAccountDetails,
    OffRampAccount,
    UnblockUser,
    WithdrawalRequest,
    WithdrawPollable,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { WithdrawalFees } from '@zeal/domains/Currency/domains/BankTransfer/components/WithdrawalFees'
import { formatIBAN } from '@zeal/domains/Currency/domains/BankTransfer/helpers/formatIBAN'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { sub2 } from '@zeal/domains/Money/helpers/sub'
import { NetworkMap } from '@zeal/domains/Network'
import { FancyButton as NetworkFancyButton } from '@zeal/domains/Network/components/FancyButton'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'

import {
    BannerError,
    FormErrors,
    getMaxBalance,
    MaxBalanceLoadable,
    validateAsUserTypes,
    validateOnSubmit,
} from '../validation'

type Props = {
    pollable: WithdrawPollable
    ownerPortfolio: ServerPortfolio2
    networkMap: NetworkMap
    offRampAccounts: OffRampAccount[]
    unblockUser: UnblockUser
    maxBalanceLoadable: MaxBalanceLoadable
    installationId: string
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'on_amount_change'; amount: string | null }
    | { type: 'on_deposit_tab_click' }
    | { type: 'on_fiat_currency_selector_click' }
    | { type: 'on_crypto_currency_selector_click' }
    | { type: 'on_network_selector_click' }
    | { type: 'on_provider_info_click' }
    | { type: 'on_submit_form_click'; form: WithdrawalRequest }
    | { type: 'on_settings_icon_click' }
    | { type: 'on_withdrawal_receiver_click' }

const getAmounts = (
    pollable: WithdrawPollable
): {
    cryptoInputAmountInDefaultCurrency: FiatMoney | null
    fiatNetOutputAmount: FiatMoney | null
    fiatNetOutputAmountInDefaultCurrency: FiatMoney | null
} => {
    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed': {
            const cryptoInputAmount: CryptoMoney = {
                amount: fromFixedWithFraction(
                    pollable.params.amount,
                    pollable.params.inputCurrency.fraction
                ),
                currency: pollable.params.inputCurrency,
            }

            const fiatOutputAmount = applyRate2({
                baseAmount: cryptoInputAmount,
                rate: pollable.data.rate,
            })
            const feeAmount = pollable.data.fee.amount

            const fiatNetOutputAmount = sub2(fiatOutputAmount, feeAmount)

            const fiatNetOutputAmountInDefaultCurrency =
                pollable.data.defaultCurrencyRateToOutputCurrency &&
                applyRate2({
                    baseAmount: fiatNetOutputAmount,
                    rate: pollable.data.defaultCurrencyRateToOutputCurrency,
                })

            const cryptoInputAmountInDefaultCurrency =
                pollable.data.defaultCurrencyRateToInputCurrency &&
                applyRate2({
                    baseAmount: cryptoInputAmount,
                    rate: pollable.data.defaultCurrencyRateToInputCurrency,
                })

            return {
                cryptoInputAmountInDefaultCurrency,
                fiatNetOutputAmount,
                fiatNetOutputAmountInDefaultCurrency,
            }
        }
        case 'loading':
        case 'error':
            return {
                cryptoInputAmountInDefaultCurrency: null,
                fiatNetOutputAmount: null,
                fiatNetOutputAmountInDefaultCurrency: null,
            }

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}

export const Layout = ({
    pollable,
    ownerPortfolio,
    offRampAccounts,
    unblockUser,
    networkMap,
    installationId,
    maxBalanceLoadable,
    onMsg,
}: Props) => {
    const [submitAttempted, setSubmitAttempted] = useState<boolean>(false)
    const { formatMessage } = useIntl()

    const { amount, inputCurrency, outputCurrency } = pollable.params

    const network = findNetworkByHexChainId(
        pollable.params.inputCurrency.networkHexChainId,
        networkMap
    )

    const result = submitAttempted
        ? validateOnSubmit({
              maxBalanceLoadable,
              pollable,
              ownerPortfolio,
          })
        : validateAsUserTypes(pollable)

    const errors = result.getFailureReason() || {}

    const {
        cryptoInputAmountInDefaultCurrency,
        fiatNetOutputAmount,
        fiatNetOutputAmountInDefaultCurrency,
    } = getAmounts(pollable)

    const onSubmit = () => {
        setSubmitAttempted(true)
        const result = validateOnSubmit({
            maxBalanceLoadable,
            pollable,
            ownerPortfolio,
        })

        switch (result.type) {
            case 'Failure':
                break
            case 'Success':
                onMsg({
                    type: 'on_submit_form_click',
                    form: result.data,
                })
                break
            /* istanbul ignore next */
            default:
                return notReachable(result)
        }
    }

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={16} fill>
                <ActionBar
                    center={
                        <Row spacing={12} grow shrink>
                            <TabHeader
                                selected={false}
                                onClick={() =>
                                    onMsg({ type: 'on_deposit_tab_click' })
                                }
                            >
                                <FormattedMessage
                                    id="bank_transfers.deposit-header"
                                    defaultMessage="Deposit"
                                />
                            </TabHeader>
                            <TabHeader selected>
                                <FormattedMessage
                                    id="bank_transfers.withdraw-header"
                                    defaultMessage="Withdraw"
                                />
                            </TabHeader>
                        </Row>
                    }
                    left={
                        <IconButton
                            variant="on_light"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            {({ color }) => (
                                <BackIcon size={24} color={color} />
                            )}
                        </IconButton>
                    }
                    right={
                        <IconButton
                            variant="on_light"
                            onClick={() =>
                                onMsg({ type: 'on_settings_icon_click' })
                            }
                        >
                            {({ color }) => (
                                <BoldSetting size={24} color={color} />
                            )}
                        </IconButton>
                    }
                />
                <Column spacing={8} fill alignY="stretch">
                    <Column spacing={16} fill>
                        <ScrollContainer
                            contentFill
                            withFloatingActions={false}
                        >
                            <Column spacing={12} fill>
                                <Column spacing={4}>
                                    <AmountInput
                                        state={
                                            errors.input ? 'error' : 'normal'
                                        }
                                        top={
                                            <NetworkFancyButton
                                                fill
                                                rounded={false}
                                                network={network}
                                                onClick={() =>
                                                    onMsg({
                                                        type: 'on_network_selector_click',
                                                    })
                                                }
                                            />
                                        }
                                        content={{
                                            topLeft: (
                                                <IconButton
                                                    variant="on_light"
                                                    onClick={() =>
                                                        onMsg({
                                                            type: 'on_crypto_currency_selector_click',
                                                        })
                                                    }
                                                >
                                                    {({ color }) => (
                                                        <Row spacing={4}>
                                                            <CurrencyAvatar
                                                                key={
                                                                    inputCurrency.id
                                                                }
                                                                currency={
                                                                    inputCurrency
                                                                }
                                                                size={24}
                                                                rightBadge={() =>
                                                                    null
                                                                }
                                                            />
                                                            <Text
                                                                variant="title3"
                                                                color="textPrimary"
                                                                weight="medium"
                                                            >
                                                                {
                                                                    inputCurrency.code
                                                                }
                                                            </Text>
                                                            <LightArrowDown2
                                                                size={18}
                                                                color={color}
                                                            />
                                                        </Row>
                                                    )}
                                                </IconButton>
                                            ),
                                            topRight: ({ onBlur, onFocus }) => (
                                                <AmountInput.Input
                                                    onFocus={onFocus}
                                                    onBlur={onBlur}
                                                    label={formatMessage({
                                                        id: 'bank_transfers.withdraw.amount-input',
                                                        defaultMessage:
                                                            'Amount to withdraw',
                                                    })}
                                                    amount={amount}
                                                    fraction={
                                                        inputCurrency.fraction
                                                    }
                                                    onChange={(value) =>
                                                        onMsg({
                                                            type: 'on_amount_change',
                                                            amount: value,
                                                        })
                                                    }
                                                    autoFocus
                                                    prefix=""
                                                    onSubmitEditing={onSubmit}
                                                />
                                            ),
                                            bottomRight:
                                                cryptoInputAmountInDefaultCurrency && (
                                                    <Text
                                                        variant="footnote"
                                                        color="textSecondary"
                                                        weight="regular"
                                                    >
                                                        <FormattedMoneyPrecise
                                                            withSymbol
                                                            sign={null}
                                                            money={
                                                                cryptoInputAmountInDefaultCurrency
                                                            }
                                                        />
                                                    </Text>
                                                ),
                                            bottomLeft: (
                                                <MaxButton
                                                    installationId={
                                                        installationId
                                                    }
                                                    location="unblock_withdrawal"
                                                    balance={getMaxBalance({
                                                        maxBalanceLoadable,
                                                        ownerPortfolio,
                                                    })}
                                                    onMsg={onMsg}
                                                    state={(() => {
                                                        switch (
                                                            maxBalanceLoadable.type
                                                        ) {
                                                            case 'loading':
                                                                return 'loading'
                                                            case 'error':
                                                            case 'loaded':
                                                                return errors.balance
                                                                    ? 'error'
                                                                    : 'normal'
                                                            default:
                                                                return notReachable(
                                                                    maxBalanceLoadable
                                                                )
                                                        }
                                                    })()}
                                                />
                                            ),
                                        }}
                                    />

                                    <NextStepSeparator />

                                    <AmountInput
                                        state={(() => {
                                            switch (pollable.type) {
                                                case 'error':
                                                    return 'error'
                                                case 'loaded':
                                                case 'reloading':
                                                case 'subsequent_failed':
                                                case 'loading':
                                                    return 'normal'
                                                default:
                                                    return notReachable(
                                                        pollable
                                                    )
                                            }
                                        })()}
                                        content={{
                                            topLeft: (
                                                <IconButton
                                                    variant="on_light"
                                                    onClick={() => {
                                                        onMsg({
                                                            type: 'on_fiat_currency_selector_click',
                                                        })
                                                    }}
                                                >
                                                    {({ color }) => (
                                                        <Row spacing={4}>
                                                            <CurrencyAvatar
                                                                key={
                                                                    outputCurrency.id
                                                                }
                                                                currency={
                                                                    outputCurrency
                                                                }
                                                                size={24}
                                                                rightBadge={() =>
                                                                    null
                                                                }
                                                            />
                                                            <Text
                                                                variant="title3"
                                                                color="textPrimary"
                                                                weight="medium"
                                                            >
                                                                {
                                                                    outputCurrency.code
                                                                }
                                                            </Text>

                                                            <LightArrowDown2
                                                                size={18}
                                                                color="iconDefault"
                                                            />
                                                        </Row>
                                                    )}
                                                </IconButton>
                                            ),
                                            topRight: () =>
                                                (() => {
                                                    switch (pollable.type) {
                                                        case 'loading':
                                                        case 'reloading':
                                                            return (
                                                                <AmountInput.InputSkeleton />
                                                            )
                                                        case 'loaded':
                                                        case 'subsequent_failed':
                                                            return (
                                                                <Column
                                                                    spacing={0}
                                                                    alignX="end"
                                                                >
                                                                    <Text
                                                                        variant="title3"
                                                                        color="textPrimary"
                                                                        weight="medium"
                                                                    >
                                                                        {fiatNetOutputAmount ? (
                                                                            <FormattedMoneyPrecise
                                                                                withSymbol
                                                                                sign={
                                                                                    null
                                                                                }
                                                                                money={
                                                                                    fiatNetOutputAmount
                                                                                }
                                                                            />
                                                                        ) : (
                                                                            '0'
                                                                        )}
                                                                    </Text>
                                                                </Column>
                                                            )

                                                        case 'error':
                                                            return (
                                                                <Column
                                                                    spacing={0}
                                                                    alignX="end"
                                                                >
                                                                    <Text
                                                                        variant="title3"
                                                                        color="textDisabled"
                                                                        weight="regular"
                                                                    >
                                                                        <FormattedMessage
                                                                            id="bank_transfers.deposit.amount-output.error"
                                                                            defaultMessage="error"
                                                                        />
                                                                    </Text>
                                                                </Column>
                                                            )
                                                        /* istanbul ignore next */
                                                        default:
                                                            return notReachable(
                                                                pollable
                                                            )
                                                    }
                                                })(),
                                            bottomRight: (() => {
                                                switch (pollable.type) {
                                                    case 'loading':
                                                    case 'reloading':
                                                        return (
                                                            <Skeleton
                                                                variant="default"
                                                                width={40}
                                                                height={16}
                                                            />
                                                        )
                                                    case 'loaded':
                                                    case 'subsequent_failed':
                                                        return (
                                                            <Text
                                                                variant="footnote"
                                                                color="textSecondary"
                                                                weight="regular"
                                                            >
                                                                {fiatNetOutputAmountInDefaultCurrency && (
                                                                    <FormattedMoneyPrecise
                                                                        withSymbol
                                                                        sign={
                                                                            null
                                                                        }
                                                                        money={
                                                                            fiatNetOutputAmountInDefaultCurrency
                                                                        }
                                                                    />
                                                                )}
                                                            </Text>
                                                        )

                                                    case 'error':
                                                        return null
                                                    /* istanbul ignore next */
                                                    default:
                                                        return notReachable(
                                                            pollable
                                                        )
                                                }
                                            })(),
                                        }}
                                    />

                                    <NextStepSeparator />

                                    <BankAccountInformation
                                        unblockUser={unblockUser}
                                        offRampAccounts={offRampAccounts}
                                        onMsg={onMsg}
                                    />
                                </Column>
                                <Spacer />
                                <Column spacing={12}>
                                    <Text
                                        variant="footnote"
                                        weight="regular"
                                        color="textSecondary"
                                    >
                                        <FormattedMessage
                                            id="currency.bridge.bridge_provider"
                                            defaultMessage="Transfer provider"
                                        />
                                    </Text>
                                    <FeeInputButton
                                        left={
                                            <Row spacing={4}>
                                                <Avatar
                                                    variant="squared"
                                                    size={20}
                                                >
                                                    <Unblock size={20} />
                                                </Avatar>

                                                <Text
                                                    variant="paragraph"
                                                    weight="regular"
                                                    color="textPrimary"
                                                >
                                                    Unblock
                                                </Text>
                                            </Row>
                                        }
                                        right={
                                            <Row spacing={4}>
                                                <Row
                                                    spacing={4}
                                                    alignY="center"
                                                >
                                                    <Text
                                                        variant="paragraph"
                                                        weight="regular"
                                                        color="textPrimary"
                                                    >
                                                        <FormattedMessage
                                                            id="bank_transfers.fees"
                                                            defaultMessage="Fees"
                                                        />
                                                    </Text>
                                                    <WithdrawalFees
                                                        withdrawalFeesPollable={
                                                            pollable
                                                        }
                                                    />
                                                </Row>
                                                <InfoCircleOutline
                                                    size={20}
                                                    color="iconDefault"
                                                />
                                            </Row>
                                        }
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_provider_info_click',
                                            })
                                        }
                                    />
                                </Column>
                                <Column spacing={12}>
                                    {errors.banner && (
                                        <ErrorBanner error={errors.banner} />
                                    )}
                                </Column>
                            </Column>
                        </ScrollContainer>
                    </Column>
                    <Actions variant="default">
                        <Button
                            size="regular"
                            variant="primary"
                            disabled={!!errors.submit}
                            onClick={onSubmit}
                        >
                            {errors.submit ? (
                                <SubmitButtonErrorText error={errors.submit} />
                            ) : (
                                <FormattedMessage
                                    id="bank_transfers.continue"
                                    defaultMessage="Continue"
                                />
                            )}
                        </Button>
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}

const BankAccountInformation = ({
    offRampAccounts,
    unblockUser,
    onMsg,
}: {
    offRampAccounts: OffRampAccount[]
    unblockUser: UnblockUser
    onMsg: (msg: Msg) => void
}) => {
    const bankAccount = offRampAccounts.find(
        (account) => account.mainBeneficiary
    )

    if (!bankAccount) {
        throw new ImperativeError(
            'Bank account information not available in list of off-ramp accounts'
        )
    }

    return (
        <ListItemButton
            variant="default"
            background="surface"
            onClick={() => onMsg({ type: 'on_withdrawal_receiver_click' })}
            disabled={false}
            aria-current={false}
            primaryText={`${unblockUser.firstName} ${unblockUser.lastName}`}
            avatar={({ size }) => (
                <Avatar size={size}>
                    <BoldGeneralBank size={size} color="textPrimary" />
                </Avatar>
            )}
            side={{
                rightIcon: ({ size }) => (
                    <ForwardIcon size={size} color="iconDefault" />
                ),
            }}
            shortText={
                <BankAccountDetailsInfo bankDetails={bankAccount.bankDetails} />
            }
        />
    )
}

const BankAccountDetailsInfo = ({
    bankDetails: bankAccount,
}: {
    bankDetails: BankAccountDetails
}) => {
    switch (bankAccount.type) {
        case 'uk':
            return (
                <Row spacing={8}>
                    <Text
                        variant="paragraph"
                        color="textSecondary"
                        weight="regular"
                    >
                        {bankAccount.accountNumber}
                    </Text>
                    <Text
                        variant="paragraph"
                        color="textSecondary"
                        weight="regular"
                    >
                        {formattedSortCode(bankAccount.sortCode)}
                    </Text>
                </Row>
            )

        case 'iban':
            return (
                <Text
                    variant="paragraph"
                    color="textSecondary"
                    weight="regular"
                >
                    {formatIBAN(bankAccount.iban)}
                </Text>
            )

        default:
            return notReachable(bankAccount)
    }
}

const formattedSortCode = (sortCode: string) => {
    const parts = sortCode.match(/.{1,2}/g) || []
    return parts.join('-')
}

const SubmitButtonErrorText = ({
    error,
}: {
    error: NonNullable<FormErrors['submit']>
}) => {
    switch (error.type) {
        case 'post_kyc_limit_reached':
            return (
                <FormattedMessage
                    id="bank_transfers.withdrawal.verify-id"
                    defaultMessage="Reduce amount"
                />
            )
        case 'pollable_loading':
        case 'amount_is_zero':
        case 'max_balance_still_loading':
            return (
                <FormattedMessage
                    id="bank_transfers.continue"
                    defaultMessage="Continue"
                />
            )
        case 'minimum_amount':
            return (
                <FormattedMessage
                    id="bank_transfers.increase-amount"
                    defaultMessage="Minimum transfer is {limit}"
                    values={{
                        limit: (
                            <FormattedMoneyPrecise
                                withSymbol
                                sign={null}
                                money={error.limit}
                            />
                        ),
                    }}
                />
            )

        case 'insufficient_balance':
            return (
                <FormattedMessage
                    id="bank_transfers.insufficient-funds"
                    defaultMessage="Insufficient funds"
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}

const ErrorBanner = ({ error }: { error: BannerError }) => {
    switch (error.type) {
        case 'post_kyc_limit_reached':
            return (
                <BannerLimit
                    variant="warning"
                    onClick={null}
                    icon={({ size, color }) => (
                        <BoldDangerTriangle size={size} color={color} />
                    )}
                    title={
                        <FormattedMessage
                            id="bank_transfers.withdraw.max-limit-reached"
                            defaultMessage="Amount exceeds max transfer limit"
                        />
                    }
                />
            )
        case 'pollable_loading':
            return null
        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}
