import { FormattedMessage } from 'react-intl'

import { notReachable } from '@zeal/toolkit'
import { useLazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'

import { OffRampAccount } from '@zeal/domains/Currency/domains/BankTransfer'
import {
    createOffRampAccount,
    CreateOffRampAccountRequest,
} from '@zeal/domains/Currency/domains/BankTransfer/api/createOffRampAccount'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'
import { BankTransferLoadingLayout } from '@zeal/domains/Currency/domains/BankTransfer/components/BankTransferLoadingLayout'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { SuccessLayoutWithNotificationsPrompt } from '@zeal/domains/Notification/features/SuccessLayoutWithNotificationsPrompt'
import { BankTransferUnblockUserCreated } from '@zeal/domains/Storage'

import { BankDetailsDoNoMatch } from './BankDetailsDoNoMatch'
import { Form } from './Form'
import { InvalidFasterPayments } from './InvalidFasterPayments'
import { InvalidIBAN } from './InvalidIBAN'

type Props = {
    bankTransferInfo: BankTransferUnblockUserCreated
    currencies: BankTransferCurrencies
    unblockLoginInfo: UnblockLoginInfo
    installationId: string
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | {
          type: 'on_withdrawal_bank_account_created'
          bankAccount: OffRampAccount
      }

export const updateUserAndCreateOffRampAccount = async ({
    account,
    unblockLoginInfo,
    signal,
}: {
    account: CreateOffRampAccountRequest
    unblockLoginInfo: UnblockLoginInfo
    signal?: AbortSignal
}): Promise<OffRampAccount> => {
    switch (account.bankDetails.type) {
        case 'iban':
        case 'uk':
            return createOffRampAccount({
                account,
                unblockLoginInfo,
                signal,
            })
        /* istanbul ignore next */
        default:
            return notReachable(account.bankDetails)
    }
}

export const CreateWithdrawalBankAccount = ({
    bankTransferInfo,
    currencies,
    unblockLoginInfo,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLazyLoadableData(
        updateUserAndCreateOffRampAccount
    )

    switch (loadable.type) {
        case 'not_asked':
            return (
                <Form
                    currencies={currencies}
                    bankTransferInfo={bankTransferInfo}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'form_submitted':
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        account: msg.form,
                                        unblockLoginInfo,
                                    },
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'loading':
            return <BankTransferLoadingLayout onMsg={onMsg} />
        case 'loaded':
            return (
                <SuccessLayoutWithNotificationsPrompt
                    title={
                        <FormattedMessage
                            id="currency.bank_transfer.create_unblock_withdraw_account.success"
                            defaultMessage="Account set up"
                        />
                    }
                    installationId={installationId}
                    location="bank_transfers_setup"
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_success_layout_animation_completed':
                                onMsg({
                                    type: 'on_withdrawal_bank_account_created',
                                    bankAccount: loadable.data,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg.type)
                        }
                    }}
                />
            )

        case 'error':
            const parsed = parseAppError(loadable.error)

            switch (parsed.type) {
                case 'unblock_account_number_and_sort_code_mismatch':
                    return (
                        <>
                            <BankTransferLoadingLayout onMsg={onMsg} />
                            <BankDetailsDoNoMatch
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            setLoadable({
                                                type: 'not_asked',
                                            })
                                            break

                                        default:
                                            notReachable(msg.type)
                                    }
                                }}
                            />
                        </>
                    )

                case 'unblock_invalid_faster_payment_configuration':
                    return (
                        <>
                            <BankTransferLoadingLayout onMsg={onMsg} />
                            <InvalidFasterPayments
                                error={parsed}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            setLoadable({
                                                type: 'not_asked',
                                            })
                                            break

                                        default:
                                            notReachable(msg.type)
                                    }
                                }}
                            />
                        </>
                    )

                case 'unblock_invalid_iban':
                    return (
                        <>
                            <BankTransferLoadingLayout onMsg={onMsg} />
                            <InvalidIBAN
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            setLoadable({
                                                type: 'not_asked',
                                            })
                                            break

                                        default:
                                            notReachable(msg.type)
                                    }
                                }}
                            />
                        </>
                    )

                default:
                    return (
                        <>
                            <BankTransferLoadingLayout onMsg={onMsg} />
                            <AppErrorPopup
                                installationId={installationId}
                                error={parsed}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            setLoadable({ type: 'not_asked' })
                                            break

                                        case 'try_again_clicked':
                                            setLoadable({
                                                type: 'loading',
                                                params: loadable.params,
                                            })
                                            break

                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        </>
                    )
            }

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
