import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { values } from '@zeal/toolkit/Object'

import { FiatCurrency } from '@zeal/domains/Currency'
import {
    BankTransferCurrencies,
    UnblockFiatCurrencyCode,
} from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { FiatCurrencySelector } from '@zeal/domains/Currency/domains/BankTransfer/components/FiatCurrencySelector'

type Props = {
    currencies: BankTransferCurrencies
    currentCurrency: FiatCurrency<UnblockFiatCurrencyCode> | null
    state: State
    onMsg: (msg: Msg) => void
}

export type Msg = MsgOf<typeof FiatCurrencySelector<UnblockFiatCurrencyCode>>

export type State = { type: 'closed' } | { type: 'select_currency' }

export const Modal = ({ currencies, currentCurrency, state, onMsg }: Props) => {
    switch (state.type) {
        case 'select_currency':
            return (
                <UIModal>
                    <FiatCurrencySelector
                        fiatCurrencies={values(currencies.fiatCurrencies)}
                        selected={currentCurrency}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break

                                case 'on_fiat_currency_selected':
                                    onMsg(msg)
                                    onMsg({ type: 'close' })
                                    break

                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </UIModal>
            )

        case 'closed':
            return null

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
