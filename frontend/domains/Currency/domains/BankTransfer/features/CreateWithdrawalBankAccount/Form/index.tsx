import { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { ArrowDown } from '@zeal/uikit/Icon/ArrowDown'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { QuestionCircle } from '@zeal/uikit/Icon/QuestionCircle'
import { IconButton } from '@zeal/uikit/IconButton'
import { Input } from '@zeal/uikit/Input'
import { InputButton } from '@zeal/uikit/InputButton'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import {
    EmptyStringError,
    failure,
    nonEmptyString,
    required,
    RequiredError,
    Result,
    shape,
    success,
} from '@zeal/toolkit/Result'

import { tryToGetUserCurrentCountry } from '@zeal/domains/Country/helpers/tryToGetUserCurrentCountry'
import { FiatCurrency } from '@zeal/domains/Currency'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { CreateOffRampAccountRequest } from '@zeal/domains/Currency/domains/BankTransfer/api/createOffRampAccount'
import {
    BankTransferCurrencies,
    UnblockFiatCurrencyCode,
} from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { formatSortCode } from '@zeal/domains/Currency/domains/BankTransfer/helpers/formatSortCode'
import { defaultCurrencyForCountryCode } from '@zeal/domains/Currency/domains/BankTransfer/helpers/getDefaultCurrencyForCountry'
import {
    IbanInvalid,
    parseIbanString,
} from '@zeal/domains/Currency/domains/BankTransfer/helpers/parseIbanString'
import { sanitiseIbanInput } from '@zeal/domains/Currency/domains/BankTransfer/helpers/sanitiseIbanInput'
import { BankTransferUnblockUserCreated } from '@zeal/domains/Storage'

import { Modal, State as ModalState } from './Modal'

type Props = {
    currencies: BankTransferCurrencies
    bankTransferInfo: BankTransferUnblockUserCreated
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'form_submitted'; form: CreateOffRampAccountRequest }

type InitialForm = {
    bankDetails: BankDetailsInput
}

type BankDetailsInput =
    | {
          type: 'uk'
          accountNumber?: string
          sortCode?: string
          currency: FiatCurrency<'GBP'>
      }
    | { type: 'iban'; iban?: string; currency: FiatCurrency<'EUR'> }

type FormErrors = {
    accountNumber?: AccountNumberError
    sortCode?: SortCodeError
    iban?: IBANError

    submit?: RequiredError
}

type AccountNumberError = RequiredError | UKBankAccountNumberStringError

type SortCodeError = RequiredError | UKBankSortCodeStringError

type IBANError = RequiredError | IBANStringError

const validateAsYouType = (form: InitialForm): Result<FormErrors, unknown> => {
    return shape({
        submit: required(form.bankDetails).andThen((bankDetails) => {
            switch (bankDetails.type) {
                case 'uk':
                    return required(bankDetails.sortCode).andThen(() =>
                        required(bankDetails.accountNumber)
                    )
                case 'iban':
                    return required(bankDetails.iban)
                /* istanbul ignore next */
                default:
                    return notReachable(bankDetails)
            }
        }),
    })
}

const validateOnSubmit = (
    form: InitialForm
): Result<FormErrors, CreateOffRampAccountRequest> => {
    const { bankDetails } = form
    switch (bankDetails.type) {
        case 'uk':
            return shape({
                accountNumber: validateAccountNumber(form),
                sortCode: validateSortCode(form),
                iban: success(''),
            }).map(({ accountNumber, sortCode }) => ({
                bankDetails: {
                    type: 'uk',
                    accountNumber,
                    sortCode,
                    currency: bankDetails.currency,
                },
            }))
        case 'iban':
            return shape({
                accountNuber: success(''),
                sortCode: success(''),
                iban: validateIBAN(form),
            }).map(({ iban }) => ({
                bankDetails: {
                    type: 'iban',
                    iban: iban,
                    currency: bankDetails.currency,
                },
            }))
        /* istanbul ignore next */
        default:
            return notReachable(bankDetails)
    }
}

const validateAccountNumber = (
    form: InitialForm
): Result<AccountNumberError, string> => {
    return required(form.bankDetails).andThen((bankDetails) => {
        switch (bankDetails.type) {
            case 'uk':
                return ukBankAccountNumberString(bankDetails.accountNumber)

            case 'iban':
                return success('')

            default:
                return notReachable(bankDetails)
        }
    })
}

const validateSortCode = (form: InitialForm): Result<SortCodeError, string> => {
    return required(form.bankDetails).andThen((bankDetails) => {
        switch (bankDetails.type) {
            case 'uk':
                return ukBankSortCodeString(bankDetails.sortCode)

            case 'iban':
                return success('')

            default:
                return notReachable(bankDetails)
        }
    })
}

const validateIBAN = (form: InitialForm): Result<IBANError, string> => {
    return required(form.bankDetails).andThen((bankDetails) => {
        switch (bankDetails.type) {
            case 'uk':
                return success('')

            case 'iban':
                return ibanString(bankDetails.iban)

            default:
                return notReachable(bankDetails)
        }
    })
}

const changeCurrency = (
    currency: FiatCurrency<UnblockFiatCurrencyCode>,
    form: InitialForm
): InitialForm => {
    const newBankDetails = defaultBankDetails(currency)

    return {
        ...form,
        bankDetails:
            form.bankDetails?.type === newBankDetails?.type
                ? form.bankDetails
                : newBankDetails,
    }
}

const calculateInitialForm = (
    bankTransferInfo: BankTransferUnblockUserCreated
): InitialForm => {
    const detectedCountryCode =
        tryToGetUserCurrentCountry().getSuccessResult()?.code

    const countryCode = bankTransferInfo.countryCode || detectedCountryCode

    const defaultCurrency = countryCode
        ? defaultCurrencyForCountryCode(countryCode)
        : null

    return {
        bankDetails: defaultBankDetails(defaultCurrency),
    }
}

const defaultBankDetails = (
    currency: FiatCurrency<UnblockFiatCurrencyCode> | null
): InitialForm['bankDetails'] => {
    if (!currency) {
        return {
            type: 'iban',
            currency: FIAT_CURRENCIES.EUR,
        }
    }
    switch (currency.code) {
        case 'GBP':
            return {
                type: 'uk',
                currency: currency as FiatCurrency<typeof currency.code>,
            }

        case 'EUR':
            return {
                type: 'iban',
                currency: currency as FiatCurrency<typeof currency.code>,
            }
        /* istanbul ignore next */
        default:
            return notReachable(currency.code)
    }
}

const parsedSortCode = (newValue: string, bankDetails: BankDetailsInput) => {
    switch (bankDetails.type) {
        case 'uk':
            return newValue.replace(/[^0-9]/g, '').substring(0, 6)

        case 'iban':
            throw new ImperativeError(
                `Bank details type does not have a sort code`,
                { type: bankDetails.type }
            )

        default:
            notReachable(bankDetails)
    }
}

const sanitiseSpecialCharacters = (value: string) => {
    return value.replace(/[^0-9A-Za-z]/g, '')
}

export const Form = ({ currencies, bankTransferInfo, onMsg }: Props) => {
    const [form, setForm] = useState<InitialForm>(() =>
        calculateInitialForm(bankTransferInfo)
    )

    const [modalState, setModalState] = useState<ModalState>({ type: 'closed' })

    const [isSubmitted, setIsSubmitted] = useState<boolean>(false)

    const error = isSubmitted
        ? validateOnSubmit(form).getFailureReason() || {}
        : validateAsYouType(form).getFailureReason() || {}

    const onSubmit = () => {
        setIsSubmitted(true)
        const result = validateOnSubmit(form)
        switch (result.type) {
            case 'Failure':
                break
            case 'Success':
                onMsg({
                    type: 'form_submitted',
                    form: result.data,
                })

                break
            /* istanbul ignore next */
            default:
                return notReachable(result)
        }
    }

    return (
        <>
            <Screen
                background="light"
                padding="form"
                onNavigateBack={() => onMsg({ type: 'close' })}
            >
                <ActionBar
                    left={
                        <IconButton
                            variant="on_light"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            {({ color }) => (
                                <BackIcon size={24} color={color} />
                            )}
                        </IconButton>
                    }
                />
                <Column spacing={8} fill>
                    <Column spacing={24} fill>
                        <Header
                            title={
                                <FormattedMessage
                                    id="currency.bank_transfer.create_unblock_withdraw_account.title"
                                    defaultMessage="Link your bank account"
                                />
                            }
                        />
                        <ScrollContainer withFloatingActions={false}>
                            <Column spacing={8} fill>
                                <Column spacing={8}>
                                    <Text
                                        variant="paragraph"
                                        weight="regular"
                                        color="textSecondary"
                                    >
                                        <FormattedMessage
                                            id="currency.bank_transfer.create_unblock_withdraw_account.preferred_currency"
                                            defaultMessage="Preferred currency"
                                        />
                                    </Text>

                                    <InputButton
                                        leftIcon={
                                            form.bankDetails?.currency ? (
                                                <Avatar
                                                    size={28}
                                                    border="borderSecondary"
                                                >
                                                    <Text
                                                        variant="caption1"
                                                        weight="medium"
                                                        color="textPrimary"
                                                        align="center"
                                                    >
                                                        {
                                                            form.bankDetails
                                                                .currency.symbol
                                                        }
                                                    </Text>
                                                </Avatar>
                                            ) : (
                                                <QuestionCircle
                                                    size={28}
                                                    color="iconDefault"
                                                />
                                            )
                                        }
                                        rightIcon={
                                            <ArrowDown
                                                color="iconDisabled"
                                                size={24}
                                            />
                                        }
                                        onClick={() => {
                                            setModalState({
                                                type: 'select_currency',
                                            })
                                        }}
                                    >
                                        {form.bankDetails?.currency?.name ||
                                            'Currency'}
                                    </InputButton>
                                </Column>

                                {form.bankDetails?.type === 'uk' && (
                                    <Column spacing={8}>
                                        <Text
                                            variant="paragraph"
                                            weight="regular"
                                            color="textSecondary"
                                        >
                                            <FormattedMessage
                                                id="currency.bank_transfer.create_unblock_withdraw_account.account_number"
                                                defaultMessage="Account number"
                                            />
                                        </Text>

                                        <Input
                                            keyboardType="number-pad"
                                            onSubmitEditing={onSubmit}
                                            onChange={(e) =>
                                                setForm((form) => ({
                                                    ...form,
                                                    bankDetails:
                                                        form.bankDetails && {
                                                            ...form.bankDetails,
                                                            accountNumber:
                                                                sanitiseSpecialCharacters(
                                                                    e
                                                                        .nativeEvent
                                                                        .text
                                                                ),
                                                        },
                                                }))
                                            }
                                            state={
                                                error.accountNumber
                                                    ? 'error'
                                                    : 'normal'
                                            }
                                            placeholder="********"
                                            variant="regular"
                                            value={(() => {
                                                switch (
                                                    form.bankDetails?.type
                                                ) {
                                                    case 'uk':
                                                        return (
                                                            form.bankDetails
                                                                .accountNumber ||
                                                            ''
                                                        )

                                                    default:
                                                        return ''
                                                }
                                            })()}
                                            message={
                                                error.accountNumber && (
                                                    <AccountNumberErrorMessage
                                                        error={
                                                            error.accountNumber
                                                        }
                                                    />
                                                )
                                            }
                                        />
                                    </Column>
                                )}

                                {form.bankDetails?.type === 'uk' && (
                                    <Column spacing={8}>
                                        <Text
                                            variant="paragraph"
                                            weight="regular"
                                            color="textSecondary"
                                        >
                                            <FormattedMessage
                                                id="currency.bank_transfer.create_unblock_withdraw_account.sort_code"
                                                defaultMessage="Sort code"
                                            />
                                        </Text>

                                        <Input
                                            keyboardType="number-pad"
                                            onSubmitEditing={onSubmit}
                                            onChange={(e) =>
                                                setForm((form) => ({
                                                    ...form,
                                                    bankDetails:
                                                        form.bankDetails && {
                                                            ...form.bankDetails,
                                                            sortCode:
                                                                parsedSortCode(
                                                                    e
                                                                        .nativeEvent
                                                                        .text,
                                                                    form.bankDetails
                                                                ),
                                                        },
                                                }))
                                            }
                                            state={
                                                error.sortCode
                                                    ? 'error'
                                                    : 'normal'
                                            }
                                            placeholder="00-00-00"
                                            variant="regular"
                                            value={(() => {
                                                switch (
                                                    form.bankDetails?.type
                                                ) {
                                                    case 'uk':
                                                        return formatSortCode(
                                                            form.bankDetails
                                                                .sortCode
                                                        )

                                                    default:
                                                        return ''
                                                }
                                            })()}
                                            message={
                                                error.sortCode && (
                                                    <SortCodeErrorMessage
                                                        error={error.sortCode}
                                                    />
                                                )
                                            }
                                        />
                                    </Column>
                                )}

                                {form.bankDetails?.type === 'iban' && (
                                    <Column spacing={8}>
                                        <Text
                                            variant="paragraph"
                                            weight="regular"
                                            color="textSecondary"
                                        >
                                            <FormattedMessage
                                                id="currency.bank_transfer.create_unblock_withdraw_account.iban"
                                                defaultMessage="IBAN"
                                            />
                                        </Text>

                                        <Input
                                            keyboardType="default"
                                            onSubmitEditing={onSubmit}
                                            onChange={(e) =>
                                                setForm((form) => ({
                                                    ...form,
                                                    bankDetails:
                                                        form.bankDetails && {
                                                            ...form.bankDetails,
                                                            iban: sanitiseIbanInput(
                                                                e.nativeEvent
                                                                    .text
                                                            ),
                                                        },
                                                }))
                                            }
                                            state={
                                                error.iban ? 'error' : 'normal'
                                            }
                                            placeholder="***********************"
                                            variant="regular"
                                            value={(() => {
                                                switch (
                                                    form.bankDetails?.type
                                                ) {
                                                    case 'iban':
                                                        return (
                                                            form.bankDetails
                                                                .iban || ''
                                                        )

                                                    default:
                                                        return ''
                                                }
                                            })()}
                                            message={
                                                error.iban && (
                                                    <IBANErrorMessage
                                                        error={error.iban}
                                                    />
                                                )
                                            }
                                        />
                                    </Column>
                                )}
                            </Column>
                        </ScrollContainer>
                    </Column>
                    <Actions variant="default">
                        <Button
                            size="regular"
                            variant="primary"
                            disabled={!!error.submit}
                            onClick={onSubmit}
                        >
                            <FormattedMessage
                                id="action.continue"
                                defaultMessage="Continue"
                            />
                        </Button>
                    </Actions>
                </Column>
            </Screen>

            <Modal
                currencies={currencies}
                currentCurrency={form.bankDetails?.currency || null}
                state={modalState}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModalState({ type: 'closed' })
                            break

                        case 'on_fiat_currency_selected':
                            setForm(changeCurrency(msg.currency, form))
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}

const AccountNumberErrorMessage = ({
    error,
}: {
    error: AccountNumberError
}) => {
    switch (error.type) {
        case 'value_is_required':
        case 'string_is_empty':
        case 'value_is_not_a_string':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.create_unblock_user.account_number_missing"
                    defaultMessage="Required"
                />
            )

        case 'uk_bank_account_number_invalid':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.create_unblock_user.account_number_invalid"
                    defaultMessage="Invalid account number"
                />
            )
        default:
            return notReachable(error)
    }
}

const SortCodeErrorMessage = ({ error }: { error: SortCodeError }) => {
    switch (error.type) {
        case 'value_is_required':
        case 'string_is_empty':
        case 'value_is_not_a_string':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.create_unblock_user.sort_code_missing"
                    defaultMessage="Required"
                />
            )

        case 'uk_bank_sort_code_invalid':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.create_unblock_user.sort_code_invalid"
                    defaultMessage="Invalid sort code"
                />
            )

        default:
            return notReachable(error)
    }
}

const IBANErrorMessage = ({ error }: { error: IBANError }) => {
    switch (error.type) {
        case 'value_is_required':
        case 'string_is_empty':
        case 'value_is_not_a_string':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.create_unblock_user.sort_code_missing"
                    defaultMessage="Required"
                />
            )

        case 'iban_invalid':
            return (
                <FormattedMessage
                    id="currency.bank_transfer.create_unblock_user.sort_code_invalid"
                    defaultMessage="Invalid IBAN"
                />
            )

        default:
            return notReachable(error)
    }
}

type UKBankAccountNumberStringError =
    | EmptyStringError
    | { type: 'uk_bank_account_number_invalid'; value: unknown }

const ukBankAccountNumberString = (
    value: unknown
): Result<UKBankAccountNumberStringError, string> =>
    nonEmptyString(value).andThen((str) =>
        str.match(/^\d{8}$/)
            ? success(str)
            : failure({
                  type: 'uk_bank_account_number_invalid',
                  value: str,
              })
    )

type UKBankSortCodeStringError =
    | EmptyStringError
    | { type: 'uk_bank_sort_code_invalid'; value: unknown }

const ukBankSortCodeString = (
    value: unknown
): Result<UKBankSortCodeStringError, string> =>
    nonEmptyString(value).andThen((str) =>
        str.match(/^\d{6}$/)
            ? success(str)
            : failure({
                  type: 'uk_bank_sort_code_invalid',
                  value: str,
              })
    )

type IBANStringError = EmptyStringError | IbanInvalid

const ibanString = (value: unknown): Result<IBANStringError, string> =>
    nonEmptyString(value).andThen((str) => parseIbanString(str))
