import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Deposit } from './Deposit'
import { Withdraw } from './Withdrawal'

type Msg = Extract<
    MsgOf<typeof Deposit>,
    { type: 'close' | 'on_setup_bank_transfer_click' }
>

type Props = {
    currencies: BankTransferCurrencies
    serverPortfolio: ServerPortfolio2

    initialCurrency: CryptoCurrency | null
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

type State = {
    type: 'deposit' | 'withdraw'
}

export const Form = ({
    currencies,
    serverPortfolio,
    currencyPinMap,
    initialCurrency,
    installationId,
    currencyHiddenMap,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'deposit' })

    switch (state.type) {
        case 'deposit':
            return (
                <Deposit
                    networkRPCMap={networkRPCMap}
                    initialCurrency={initialCurrency}
                    currencies={currencies}
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    serverPortfolio={serverPortfolio}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_setup_bank_transfer_click':
                                onMsg(msg)
                                break
                            case 'on_withdraw_tab_click':
                                setState({ type: 'withdraw' })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'withdraw':
            return (
                <Withdraw
                    installationId={installationId}
                    networkRPCMap={networkRPCMap}
                    initialCurrency={initialCurrency}
                    currencies={currencies}
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    serverPortfolio={serverPortfolio}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_setup_bank_transfer_click':
                                onMsg(msg)
                                break
                            case 'on_deposit_tab_click':
                                setState({ type: 'deposit' })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state.type)
    }
}
