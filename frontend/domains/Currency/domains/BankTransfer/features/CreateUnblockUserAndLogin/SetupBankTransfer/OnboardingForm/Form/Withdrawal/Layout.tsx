import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { FeeInputButton } from '@zeal/uikit/FeeInputButton'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldGeneralBank } from '@zeal/uikit/Icon/BoldGeneralBank'
import { InfoCircleOutline } from '@zeal/uikit/Icon/InfoCircleOutline'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { Unblock } from '@zeal/uikit/Icon/Providers/Unblock'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { ListItem } from '@zeal/uikit/ListItem'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Spacer } from '@zeal/uikit/Spacer'
import { TabHeader } from '@zeal/uikit/TabHeader'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { WithdrawPollable } from '@zeal/domains/Currency/domains/BankTransfer'
import { OffRampFeeParams } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchTransactionFee'
import { WithdrawalFees } from '@zeal/domains/Currency/domains/BankTransfer/components/WithdrawalFees'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { sub2 } from '@zeal/domains/Money/helpers/sub'
import { NetworkMap } from '@zeal/domains/Network'
import { FancyButton as NetworkFancyButton } from '@zeal/domains/Network/components/FancyButton'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

type Msg =
    | { type: 'close' }
    | { type: 'on_amount_change'; amount: string | null }
    | { type: 'on_deposit_tab_click' }
    | { type: 'on_fiat_currency_selector_click' }
    | { type: 'on_crypto_currency_selector_click' }
    | { type: 'on_network_selector_click' }
    | { type: 'on_provider_info_click' }
    | {
          type: 'on_setup_bank_transfer_click'
      }

type Props = {
    pollable: WithdrawPollable
    portfolio: ServerPortfolio2
    networkMap: NetworkMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type FormError = { submit?: { type: 'zero_amount' } }

const validate = ({
    pollableParams,
}: {
    pollableParams: OffRampFeeParams
}): Result<FormError, unknown> =>
    shape({
        submit:
            fromFixedWithFraction(
                pollableParams.amount,
                pollableParams.inputCurrency.fraction
            ) > 0n
                ? success(undefined)
                : failure({ type: 'zero_amount' as const }),
    })

const getAmounts = (
    pollable: WithdrawPollable
): {
    cryptoInputAmountInDefaultCurrency: FiatMoney | null
    fiatNetOutputAmount: FiatMoney | null
    fiatNetOutputAmountInDefaultCurrency: FiatMoney | null
} => {
    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed': {
            const cryptoInputAmount: CryptoMoney = {
                amount: fromFixedWithFraction(
                    pollable.params.amount,
                    pollable.params.inputCurrency.fraction
                ),
                currency: pollable.params.inputCurrency,
            }

            const fiatOutputAmount = applyRate2({
                baseAmount: cryptoInputAmount,
                rate: pollable.data.rate,
            })
            const feeAmount = pollable.data.fee.amount

            const fiatNetOutputAmount = sub2(fiatOutputAmount, feeAmount)

            const fiatNetOutputAmountInDefaultCurrency =
                pollable.data.defaultCurrencyRateToOutputCurrency &&
                applyRate2({
                    baseAmount: fiatNetOutputAmount,
                    rate: pollable.data.defaultCurrencyRateToOutputCurrency,
                })

            const cryptoInputAmountInDefaultCurrency =
                pollable.data.defaultCurrencyRateToInputCurrency &&
                applyRate2({
                    baseAmount: cryptoInputAmount,
                    rate: pollable.data.defaultCurrencyRateToInputCurrency,
                })

            return {
                cryptoInputAmountInDefaultCurrency,
                fiatNetOutputAmount,
                fiatNetOutputAmountInDefaultCurrency,
            }
        }
        case 'loading':
        case 'error':
            return {
                cryptoInputAmountInDefaultCurrency: null,
                fiatNetOutputAmount: null,
                fiatNetOutputAmountInDefaultCurrency: null,
            }

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}

export const Layout = ({
    pollable,
    portfolio,
    installationId,
    networkMap,
    onMsg,
}: Props) => {
    const errors =
        validate({ pollableParams: pollable.params }).getFailureReason() || {}

    const { formatMessage } = useIntl()

    const { amount, inputCurrency, outputCurrency } = pollable.params

    const inputCryptoCurrencyBalance = getBalanceByCryptoCurrency2({
        serverPortfolio: portfolio,
        currency: pollable.params.inputCurrency,
    })

    const network = findNetworkByHexChainId(
        pollable.params.inputCurrency.networkHexChainId,
        networkMap
    )

    const {
        cryptoInputAmountInDefaultCurrency,
        fiatNetOutputAmount,
        fiatNetOutputAmountInDefaultCurrency,
    } = getAmounts(pollable)

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={16} fill>
                <ActionBar
                    center={
                        <Row spacing={12} grow shrink>
                            <TabHeader
                                selected={false}
                                onClick={() =>
                                    onMsg({ type: 'on_deposit_tab_click' })
                                }
                            >
                                <FormattedMessage
                                    id="bank_transfers.deposit-header"
                                    defaultMessage="Deposit"
                                />
                            </TabHeader>
                            <TabHeader selected>
                                <FormattedMessage
                                    id="bank_transfers.withdraw-header"
                                    defaultMessage="Withdraw"
                                />
                            </TabHeader>
                        </Row>
                    }
                    left={
                        <IconButton
                            variant="on_light"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            {({ color }) => (
                                <BackIcon size={24} color={color} />
                            )}
                        </IconButton>
                    }
                />
                <Column spacing={8} fill alignY="stretch">
                    <Column spacing={16} fill>
                        <ScrollContainer
                            contentFill
                            withFloatingActions={false}
                        >
                            <Column spacing={12} fill>
                                <Column spacing={4}>
                                    <AmountInput
                                        state="normal"
                                        top={
                                            <NetworkFancyButton
                                                fill
                                                rounded={false}
                                                network={network}
                                                onClick={() =>
                                                    onMsg({
                                                        type: 'on_network_selector_click',
                                                    })
                                                }
                                            />
                                        }
                                        content={{
                                            topLeft: (
                                                <IconButton
                                                    variant="on_light"
                                                    onClick={() =>
                                                        onMsg({
                                                            type: 'on_crypto_currency_selector_click',
                                                        })
                                                    }
                                                >
                                                    {({ color }) => (
                                                        <Row spacing={4}>
                                                            <CurrencyAvatar
                                                                key={
                                                                    inputCurrency.id
                                                                }
                                                                currency={
                                                                    inputCurrency
                                                                }
                                                                size={24}
                                                                rightBadge={() =>
                                                                    null
                                                                }
                                                            />
                                                            <Text
                                                                variant="title3"
                                                                color="textPrimary"
                                                                weight="medium"
                                                            >
                                                                {
                                                                    inputCurrency.code
                                                                }
                                                            </Text>
                                                            <LightArrowDown2
                                                                size={18}
                                                                color={color}
                                                            />
                                                        </Row>
                                                    )}
                                                </IconButton>
                                            ),
                                            topRight: ({ onBlur, onFocus }) => (
                                                <AmountInput.Input
                                                    onFocus={onFocus}
                                                    onBlur={onBlur}
                                                    label={formatMessage({
                                                        id: 'bank_transfers.withdraw.amount-input',
                                                        defaultMessage:
                                                            'Amount to withdraw',
                                                    })}
                                                    amount={amount}
                                                    fraction={
                                                        inputCurrency.fraction
                                                    }
                                                    onChange={(value) =>
                                                        onMsg({
                                                            type: 'on_amount_change',
                                                            amount: value,
                                                        })
                                                    }
                                                    autoFocus
                                                    prefix=""
                                                    onSubmitEditing={noop}
                                                />
                                            ),
                                            bottomRight:
                                                cryptoInputAmountInDefaultCurrency && (
                                                    <Text
                                                        variant="footnote"
                                                        color="textSecondary"
                                                        weight="regular"
                                                    >
                                                        <FormattedMoneyPrecise
                                                            withSymbol
                                                            sign={null}
                                                            money={
                                                                cryptoInputAmountInDefaultCurrency
                                                            }
                                                        />
                                                    </Text>
                                                ),
                                            bottomLeft: (
                                                <MaxButton
                                                    installationId={
                                                        installationId
                                                    }
                                                    location="unblock_withdrawal"
                                                    balance={
                                                        inputCryptoCurrencyBalance
                                                    }
                                                    onMsg={onMsg}
                                                    state="normal"
                                                />
                                            ),
                                        }}
                                    />

                                    <NextStepSeparator />

                                    <AmountInput
                                        state={(() => {
                                            switch (pollable.type) {
                                                case 'error':
                                                    return 'error'
                                                case 'loaded':
                                                case 'reloading':
                                                case 'subsequent_failed':
                                                case 'loading':
                                                    return 'normal'
                                                default:
                                                    return notReachable(
                                                        pollable
                                                    )
                                            }
                                        })()}
                                        content={{
                                            topLeft: (
                                                <IconButton
                                                    variant="on_light"
                                                    onClick={() => {
                                                        onMsg({
                                                            type: 'on_fiat_currency_selector_click',
                                                        })
                                                    }}
                                                >
                                                    {({ color }) => (
                                                        <Row spacing={4}>
                                                            <CurrencyAvatar
                                                                key={
                                                                    outputCurrency.id
                                                                }
                                                                currency={
                                                                    outputCurrency
                                                                }
                                                                size={24}
                                                                rightBadge={() =>
                                                                    null
                                                                }
                                                            />
                                                            <Text
                                                                variant="title3"
                                                                color="textPrimary"
                                                                weight="medium"
                                                            >
                                                                {
                                                                    outputCurrency.code
                                                                }
                                                            </Text>

                                                            <LightArrowDown2
                                                                size={18}
                                                                color="iconDefault"
                                                            />
                                                        </Row>
                                                    )}
                                                </IconButton>
                                            ),
                                            topRight: () =>
                                                (() => {
                                                    switch (pollable.type) {
                                                        case 'loading':
                                                        case 'reloading':
                                                            return (
                                                                <AmountInput.InputSkeleton />
                                                            )
                                                        case 'loaded':
                                                        case 'subsequent_failed':
                                                            return (
                                                                <Column
                                                                    spacing={0}
                                                                    alignX="end"
                                                                >
                                                                    <Text
                                                                        variant="title3"
                                                                        color="textPrimary"
                                                                        weight="medium"
                                                                    >
                                                                        {fiatNetOutputAmount ? (
                                                                            <FormattedMoneyPrecise
                                                                                withSymbol
                                                                                sign={
                                                                                    null
                                                                                }
                                                                                money={
                                                                                    fiatNetOutputAmount
                                                                                }
                                                                            />
                                                                        ) : (
                                                                            '0'
                                                                        )}
                                                                    </Text>
                                                                </Column>
                                                            )

                                                        case 'error':
                                                            return (
                                                                <Column
                                                                    spacing={0}
                                                                    alignX="end"
                                                                >
                                                                    <Text
                                                                        variant="title3"
                                                                        color="textDisabled"
                                                                        weight="regular"
                                                                    >
                                                                        <FormattedMessage
                                                                            id="bank_transfers.deposit.amount-output.error"
                                                                            defaultMessage="error"
                                                                        />
                                                                    </Text>
                                                                </Column>
                                                            )
                                                        /* istanbul ignore next */
                                                        default:
                                                            return notReachable(
                                                                pollable
                                                            )
                                                    }
                                                })(),
                                            bottomRight: (() => {
                                                switch (pollable.type) {
                                                    case 'loading':
                                                    case 'reloading':
                                                        return (
                                                            <Skeleton
                                                                variant="default"
                                                                width={40}
                                                                height={16}
                                                            />
                                                        )
                                                    case 'loaded':
                                                    case 'subsequent_failed':
                                                        return (
                                                            <Text
                                                                variant="footnote"
                                                                color="textSecondary"
                                                                weight="regular"
                                                            >
                                                                {fiatNetOutputAmountInDefaultCurrency ? (
                                                                    <FormattedMoneyPrecise
                                                                        withSymbol
                                                                        sign={
                                                                            null
                                                                        }
                                                                        money={
                                                                            fiatNetOutputAmountInDefaultCurrency
                                                                        }
                                                                    />
                                                                ) : (
                                                                    '$0'
                                                                )}
                                                            </Text>
                                                        )

                                                    case 'error':
                                                        return null
                                                    /* istanbul ignore next */
                                                    default:
                                                        return notReachable(
                                                            pollable
                                                        )
                                                }
                                            })(),
                                        }}
                                    />

                                    <NextStepSeparator />

                                    <Group variant="default">
                                        <ListItem
                                            aria-current={false}
                                            size="regular"
                                            avatar={({ size }) => (
                                                <BoldGeneralBank
                                                    size={size}
                                                    color="textPrimary"
                                                />
                                            )}
                                            primaryText={
                                                <FormattedMessage
                                                    id="bank-transfers.setup.bank-account"
                                                    defaultMessage="Bank account"
                                                />
                                            }
                                        />
                                    </Group>
                                </Column>
                                <Spacer />
                                <Column spacing={12}>
                                    <Text
                                        variant="footnote"
                                        weight="regular"
                                        color="textSecondary"
                                    >
                                        <FormattedMessage
                                            id="currency.bridge.bridge_provider"
                                            defaultMessage="Transfer provider"
                                        />
                                    </Text>
                                    <FeeInputButton
                                        left={
                                            <Row spacing={4}>
                                                <Avatar
                                                    variant="squared"
                                                    size={20}
                                                >
                                                    <Unblock size={20} />
                                                </Avatar>

                                                <Text
                                                    variant="paragraph"
                                                    weight="regular"
                                                    color="textPrimary"
                                                >
                                                    Unblock
                                                </Text>
                                            </Row>
                                        }
                                        right={
                                            <Row spacing={4}>
                                                <Row
                                                    spacing={4}
                                                    alignY="center"
                                                >
                                                    <Text
                                                        variant="paragraph"
                                                        weight="regular"
                                                        color="textPrimary"
                                                    >
                                                        <FormattedMessage
                                                            id="bank_transfers.fees"
                                                            defaultMessage="Fees"
                                                        />
                                                    </Text>
                                                    <WithdrawalFees
                                                        withdrawalFeesPollable={
                                                            pollable
                                                        }
                                                    />
                                                </Row>
                                                <InfoCircleOutline
                                                    size={20}
                                                    color="iconDefault"
                                                />
                                            </Row>
                                        }
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_provider_info_click',
                                            })
                                        }
                                    />
                                </Column>
                            </Column>
                        </ScrollContainer>
                    </Column>
                    <Actions variant="default">
                        <CTA formError={errors} onMsg={onMsg} />
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}

const CTA = ({
    formError,
    onMsg,
}: {
    formError: FormError
    onMsg: (msg: Msg) => void
}) => {
    if (!formError.submit) {
        return (
            <Button
                size="regular"
                variant="primary"
                onClick={() => onMsg({ type: 'on_setup_bank_transfer_click' })}
            >
                <FormattedMessage
                    id="bank_transfers.setup.cta"
                    defaultMessage="Set up bank transfers"
                />
            </Button>
        )
    }

    switch (formError.submit.type) {
        case 'zero_amount':
            return (
                <Button size="regular" variant="primary" disabled>
                    <FormattedMessage
                        id="bank_transfers.setup.enter-amount"
                        defaultMessage="Enter amount"
                    />
                </Button>
            )
        /* istanbul ignore next */
        default:
            return notReachable(formError.submit.type)
    }
}
