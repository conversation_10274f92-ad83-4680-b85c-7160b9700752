import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { values } from '@zeal/toolkit/Object'

import { CurrencyHiddenMap, CurrencyPinMap } from '@zeal/domains/Currency'
import { DepositPollable } from '@zeal/domains/Currency/domains/BankTransfer'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { CryptoCurrencySelector } from '@zeal/domains/Currency/domains/BankTransfer/components/CryptoCurrencySelector'
import { FiatCurrencySelector } from '@zeal/domains/Currency/domains/BankTransfer/components/FiatCurrencySelector'
import { UnblockProviderInfoPopup } from '@zeal/domains/Currency/domains/BankTransfer/components/UnblockProviderInfoPopup'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'

type Props = {
    state: State
    pollable: DepositPollable
    currencies: BankTransferCurrencies
    portfolio: ServerPortfolio2
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap

    onMsg: (msg: Msg) => void
}

export type Msg =
    | MsgOf<typeof FiatCurrencySelector>
    | MsgOf<typeof CryptoCurrencySelector>

export type State =
    | { type: 'closed' }
    | { type: 'fiat_currency_selector' }
    | { type: 'crypto_currency_selector' }
    | { type: 'bank_transfer_provider_info' }

export const Modal = ({
    state,
    pollable,
    portfolio,
    currencies,
    currencyPinMap,
    currencyHiddenMap,
    networkMap,

    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'bank_transfer_provider_info':
            return <UnblockProviderInfoPopup onMsg={onMsg} />

        case 'fiat_currency_selector':
            return (
                <UIModal>
                    <FiatCurrencySelector
                        selected={pollable.params.inputCurrency}
                        fiatCurrencies={values(currencies.fiatCurrencies)}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'crypto_currency_selector':
            return (
                <UIModal>
                    <CryptoCurrencySelector
                        selected={pollable.params.outputCurrency}
                        portfolio={portfolio}
                        currencyPinMap={currencyPinMap}
                        currencyHiddenMap={currencyHiddenMap}
                        networkMap={networkMap}
                        cryptoCurrencies={currencies.cryptoCurrencies}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
