import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { OnRampFeeParams } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchTransactionFee'
import { fetchUnblockOnRampFee } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockOnRampFee'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { OPTIMISM } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Msg = Extract<
    MsgOf<typeof Layout>,
    { type: 'close' | 'on_withdraw_tab_click' | 'on_setup_bank_transfer_click' }
>

type Props = {
    currencies: BankTransferCurrencies
    initialCurrency: CryptoCurrency | null
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    serverPortfolio: ServerPortfolio2
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

const calculateInitialForm = ({
    currencies,
    defaultCurrencyConfig,
    initialCurrency,
    networkMap,
    networkRPCMap,
}: {
    currencies: BankTransferCurrencies
    defaultCurrencyConfig: DefaultCurrencyConfig
    initialCurrency: CryptoCurrency | null
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
}): OnRampFeeParams => {
    const inputCurrency = currencies.fiatCurrencies.EUR
    const defaultOutputCurrency = currencies.cryptoCurrencies.filter(
        (currency) => currency.networkHexChainId === OPTIMISM.hexChainId
    )[0]

    return {
        type: 'fiatToCrypto',
        amount: null,
        outputCurrency: initialCurrency || defaultOutputCurrency,
        inputCurrency,
        networkMap,
        networkRPCMap,
        defaultCurrencyConfig,
    }
}

export const Deposit = ({
    currencies,
    serverPortfolio,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    initialCurrency,
    currencyPinMap,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<ModalState>({ type: 'closed' })
    const [pollable, setPollable] = usePollableData(
        fetchUnblockOnRampFee,
        {
            type: 'loading',
            params: calculateInitialForm({
                currencies,
                initialCurrency,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
            }),
        },
        {
            stopIf: () => false,
            pollIntervalMilliseconds: 30000,
        }
    )

    return (
        <>
            <Layout
                pollable={pollable}
                networkMap={networkMap}
                portfolio={serverPortfolio}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_withdraw_tab_click':
                        case 'on_setup_bank_transfer_click':
                            onMsg(msg)
                            break
                        case 'on_amount_change':
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    amount: msg.amount,
                                },
                            })
                            break
                        case 'on_fiat_currency_selector_click':
                            setState({ type: 'fiat_currency_selector' })
                            break
                        case 'on_crypto_currency_selector_click':
                        case 'on_network_selector_click':
                            setState({ type: 'crypto_currency_selector' })
                            break
                        case 'on_provider_info_click':
                            setState({ type: 'bank_transfer_provider_info' })
                            break
                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />

            <Modal
                state={state}
                currencies={currencies}
                pollable={pollable}
                portfolio={serverPortfolio}
                networkMap={networkMap}
                currencyPinMap={currencyPinMap}
                currencyHiddenMap={currencyHiddenMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setState({ type: 'closed' })
                            break
                        case 'on_fiat_currency_selected':
                            setState({ type: 'closed' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    inputCurrency: msg.currency,
                                },
                            })
                            break
                        case 'on_crypto_currency_selected':
                            setState({ type: 'closed' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    outputCurrency: msg.currency,
                                },
                            })
                            break
                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
