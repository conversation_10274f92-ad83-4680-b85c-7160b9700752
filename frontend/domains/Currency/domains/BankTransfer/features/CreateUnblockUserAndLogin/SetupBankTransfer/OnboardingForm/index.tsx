import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Address } from '@zeal/domains/Address'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import {
    BankTransferCurrencies,
    fetchUnblockSupportedCurrencies,
} from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { BankTransferLoadingLayout } from '@zeal/domains/Currency/domains/BankTransfer/components/BankTransferLoadingLayout'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { fetchServerPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Form } from './Form'

type Msg = MsgOf<typeof Form>

type Data = {
    currencies: BankTransferCurrencies
    serverPortfolio: ServerPortfolio2
}

const fetch = async ({
    address,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    installationId,
    currencyHiddenMap,
    signal,
}: {
    defaultCurrencyConfig: DefaultCurrencyConfig
    address: Address
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap

    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    signal?: AbortSignal
}): Promise<Data> => {
    const [bankTransferCurrencies, serverPortfolio] = await Promise.all([
        fetchUnblockSupportedCurrencies({ networkMap, signal }),
        fetchServerPortfolio2({
            address,
            signal,
            currencyHiddenMap,
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            installationId,
        }),
    ])

    return {
        currencies: bankTransferCurrencies,
        serverPortfolio,
    }
}

type Props = {
    address: Address

    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    initialCurrency: CryptoCurrency | null
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

export const OnboardingForm = ({
    address,
    networkMap,
    networkRPCMap,
    currencyPinMap,
    currencyHiddenMap,
    initialCurrency,
    installationId,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            address,
            currencyHiddenMap,
            networkMap,
            networkRPCMap,
            defaultCurrencyConfig,
            installationId,
        },
    })

    switch (loadable.type) {
        case 'loading':
            return <BankTransferLoadingLayout onMsg={onMsg} />

        case 'loaded':
            return (
                <Form
                    installationId={installationId}
                    networkRPCMap={networkRPCMap}
                    serverPortfolio={loadable.data.serverPortfolio}
                    currencies={loadable.data.currencies}
                    initialCurrency={initialCurrency}
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    onMsg={onMsg}
                />
            )

        case 'error': {
            const error = parseAppError(loadable.error)

            return (
                <>
                    <BankTransferLoadingLayout onMsg={onMsg} />
                    <AppErrorPopup
                        error={error}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            currencyHiddenMap,
                                            address,
                                            networkMap,
                                            networkRPCMap,
                                            defaultCurrencyConfig,
                                            installationId,
                                        },
                                    })
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        }

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
