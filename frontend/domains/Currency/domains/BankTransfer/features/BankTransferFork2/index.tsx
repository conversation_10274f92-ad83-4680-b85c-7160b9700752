import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Address } from '@zeal/toolkit/Web3/address'

import { AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { Layout } from '@zeal/domains/Currency/domains/BankTransfer/features/BankTransferFork2/Layout'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Modal, State as ModalState } from './Modal'

type Props = {
    cardConfig: CardConfig
    bankTransferInfo: BankTransferInfo
    selectedAddress: Address

    variant: DepositWithdrawVariant
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    portfolio: ServerPortfolio2
    installationCampaign: string | null
    counterparties: Counterparty[]
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_gnosis_pay_kyc_submitted_animation_complete'
                  | 'on_gnosis_pay_account_created'
                  | 'on_gnosis_pay_onboarding_flow_closed'
          }
      >
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_account_create_request'
                  | 'monerium_deposit_on_enable_card_clicked'
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_import_latest_bank_transfer_owner_clicked'
                  | 'on_user_login_to_unblock_success'
                  | 'kyc_applicant_created'
                  | 'bank_transfer_owner_successfully_changed'
                  | 'on_withdrawal_monitor_fiat_transaction_start'
                  | 'on_withdrawal_monitor_fiat_transaction_success'
                  | 'on_contact_support_clicked'
                  | 'on_on_ramp_transfer_success_close_click'
                  | 'on_activate_existing_monerium_account_click'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'on_monerium_order_status_changed'
                  | 'on_save_counterparty_form_submitted'
                  | 'on_delete_counterparty_submitted'
                  | 'monerium_on_card_disconnected'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_monerium_sign_delay_relay_success_close_clicked'
                  | 'on_kyc_data_updated_close_clicked'
                  | 'on_delete_last_counterparty_submitted'
                  | 'close'
                  | 'on_card_disconnected'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
          }
      >

export const BankTransferFork2 = ({
    bankTransferInfo,
    onMsg,
    gasCurrencyPresetMap,
    installationCampaign,
    feePresetMap,
    currencyHiddenMap,
    currencyPinMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    keystoreMap,
    installationId,
    variant,
    customCurrencies,
    sessionPassword,
    selectedAddress,
    accountsMap,
    cardConfig,
    defaultCurrencyConfig,
    portfolio,
    counterparties,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Layout
                cardConfig={cardConfig}
                installationCampaign={installationCampaign}
                bankTransferInfo={bankTransferInfo}
                selectedAddress={selectedAddress}
                variant={variant}
                accountsMap={accountsMap}
                keystoreMap={keystoreMap}
                portfolioMap={portfolioMap}
                sessionPassword={sessionPassword}
                customCurrencies={customCurrencies}
                currencyHiddenMap={currencyHiddenMap}
                currencyPinMap={currencyPinMap}
                networkMap={networkMap}
                feePresetMap={feePresetMap}
                installationId={installationId}
                networkRPCMap={networkRPCMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                portfolio={portfolio}
                counterparties={counterparties}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_login_to_gnosis_pay_clicked':
                            setModal({
                                type: 'order_card_flow',
                                userSelected: 'import',
                            })
                            break
                        case 'on_create_gnosis_pay_account_clicked':
                            setModal({
                                type: 'order_card_flow',
                                userSelected: 'create',
                            })
                            break
                        case 'close':
                        case 'on_account_create_request':
                        case 'monerium_deposit_on_enable_card_clicked':
                        case 'import_keys_button_clicked':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_import_latest_bank_transfer_owner_clicked':
                        case 'on_user_login_to_unblock_success':
                        case 'kyc_applicant_created':
                        case 'bank_transfer_owner_successfully_changed':
                        case 'on_withdrawal_monitor_fiat_transaction_start':
                        case 'on_withdrawal_monitor_fiat_transaction_success':
                        case 'on_contact_support_clicked':
                        case 'on_on_ramp_transfer_success_close_click':
                        case 'on_activate_existing_monerium_account_click':
                        case 'on_monerium_deposit_success_go_to_wallet_clicked':
                        case 'on_monerium_order_status_changed':
                        case 'on_save_counterparty_form_submitted':
                        case 'on_delete_counterparty_submitted':
                        case 'monerium_on_card_disconnected':
                        case 'on_create_smart_wallet_clicked':
                        case 'on_monerium_sign_delay_relay_success_close_clicked':
                        case 'on_kyc_data_updated_close_clicked':
                        case 'on_delete_last_counterparty_submitted':
                        case 'on_card_disconnected':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                installationId={installationId}
                installationCampaign={installationCampaign}
                accountsMap={accountsMap}
                keystoreMap={keystoreMap}
                portfolioMap={portfolioMap}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                sessionPassword={sessionPassword}
                defaultCurrencyConfig={defaultCurrencyConfig}
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_gnosis_pay_not_available_accepted':
                            setModal({ type: 'closed' })
                            break
                        case 'on_onboarded_card_imported_success_animation_complete':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_create_smart_wallet_clicked':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_gnosis_pay_kyc_submitted_animation_complete':
                        case 'on_gnosis_pay_onboarding_flow_closed':
                        case 'on_gnosis_pay_account_created':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
