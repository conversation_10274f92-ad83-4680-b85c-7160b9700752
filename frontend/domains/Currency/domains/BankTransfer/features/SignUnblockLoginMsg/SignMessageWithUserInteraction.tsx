import { useMemo } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { generateRandomNumber } from '@zeal/toolkit/Number'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { UnblockLoginSignature } from '@zeal/domains/Currency/domains/BankTransfer'
import { FAKE_UNBLOCK_DAPP } from '@zeal/domains/DApp/constants'
import { KeyStoreMap, LEDGER, Safe4337, Trezor } from '@zeal/domains/KeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { PersonalSign } from '@zeal/domains/RPCRequest'
import { fetchSimulatedSignMessage } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation/api/fetchSimulatedSignMessage'
import { Sign } from '@zeal/domains/RPCRequest/features/Sign'
import { generateUnblockLoginRequest } from '@zeal/domains/RPCRequest/helpers/generateUnblockLoginRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    account: Account
    keyStore: LEDGER | Trezor | Safe4337
    sessionPassword: string

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    network: Network
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_unblock_login_message_signed'
          unblockLoginSignature: UnblockLoginSignature
      }
    | Extract<
          MsgOf<typeof Sign>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >

export const SignMessageWithUserInteraction = ({
    keyStore,
    sessionPassword,
    account,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    network,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const request = useMemo(() => {
        const message = generateUnblockLoginRequest({
            account,
            network,
        })
        const request: PersonalSign = {
            id: generateRandomNumber(),
            jsonrpc: '2.0',
            method: 'personal_sign',
            params: [message.message],
        }
        return request
    }, [account, network])

    return (
        <Sign
            defaultCurrencyConfig={defaultCurrencyConfig}
            state={{ type: 'maximised' }}
            actionSource={{
                type: 'internal_sign',
                transactionEventSource: 'unblockLogin',
                dAppSiteInfo: FAKE_UNBLOCK_DAPP,
            }}
            account={account}
            accountsMap={accountsMap}
            feePresetMap={feePresetMap}
            gasCurrencyPresetMap={gasCurrencyPresetMap}
            installationId={installationId}
            keyStore={keyStore}
            keyStoreMap={keyStoreMap}
            network={network}
            networkMap={networkMap}
            networkRPCMap={networkRPCMap}
            fetchSimulatedSignMessage={fetchSimulatedSignMessage}
            portfolio={unsafe_GetPortfolioCache2({
                address: account.address,
                portfolioMap,
            })}
            sessionPassword={sessionPassword}
            request={request}
            onMsg={(msg) => {
                switch (msg.type) {
                    case 'on_4337_auto_gas_token_selection_clicked':
                    case 'on_4337_gas_currency_selected':
                        onMsg(msg)
                        break

                    case 'drag':
                    case 'import_keys_button_clicked':
                    case 'on_minimize_click':
                    case 'on_expand_request':
                        throw new ImperativeError(
                            `Message cannot be fired in UnblockLoginMsg SignMessageWithUserInteraction flow`,
                            { msg }
                        )

                    case 'cancel_button_click':
                    case 'on_cancel_confirm_transaction_clicked':
                    case 'on_safe_transaction_failure_accepted':
                    case 'on_wrong_network_accepted':
                    case 'on_safe_deployemnt_cancelled':
                    case 'close':
                    case 'on_safe_deployment_error_popup_cancel_clicked':
                        onMsg({ type: 'close' })
                        break

                    case 'message_signed':
                        onMsg({
                            type: 'on_unblock_login_message_signed',
                            unblockLoginSignature: {
                                message: request.params[0],
                                signature: msg.signature,
                            },
                        })
                        break

                    /* istanbul ignore next */
                    default:
                        notReachable(msg)
                }
            }}
        />
    )
}
