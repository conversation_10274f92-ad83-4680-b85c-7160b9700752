import * as requestBackend from '@zeal/api/requestBackend'

import { notReachable } from '@zeal/toolkit'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { object, string } from '@zeal/toolkit/Result'

import {
    OffRampAccount,
    UserInputForBankDetails,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { fetchOffRampAccount } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockOffRampAccount'
import { UnblockLoginInfo } from '@zeal/domains/Currency/domains/BankTransfer/api/loginToUnblock'

export type CreateOffRampAccountRequest = {
    bankDetails: UserInputForBankDetails
}

const DEFAULT_OFFRAMP_ACCOUNT_NAME = 'Default'

export const createOffRampAccount = ({
    account,
    unblockLoginInfo,
    signal,
}: {
    account: CreateOffRampAccountRequest
    unblockLoginInfo: UnblockLoginInfo
    signal?: AbortSignal
}): Promise<OffRampAccount> =>
    requestBackend
        .post(
            '/proxy/unblock/user/bank-account/remote',
            {
                auth: {
                    type: 'unblock_session_id',
                    sessionId: unblockLoginInfo.unblockSessionId,
                },
                body: {
                    main_beneficiary: true,
                    account_name: DEFAULT_OFFRAMP_ACCOUNT_NAME,
                    account_details: (() => {
                        switch (account.bankDetails.type) {
                            case 'uk':
                                return {
                                    currency: account.bankDetails.currency.code,
                                    account_number:
                                        account.bankDetails.accountNumber,
                                    sort_code: account.bankDetails.sortCode,
                                }
                            case 'iban':
                                return {
                                    currency: account.bankDetails.currency.code,
                                    iban: account.bankDetails.iban,
                                }
                            default:
                                return notReachable(account.bankDetails)
                        }
                    })(),
                },
            },
            signal
        )
        .then((data) =>
            string(data)
                .andThen(parseJSON)
                .andThen(object)
                .andThen((obj) => string(obj.uuid))
                .getSuccessResultOrThrow(
                    'Failed to parse offramp account uuid after creation'
                )
        )
        .then((uuid) =>
            fetchOffRampAccount({
                accountUuid: uuid,
                unblockLoginInfo,
                signal,
            })
        )
