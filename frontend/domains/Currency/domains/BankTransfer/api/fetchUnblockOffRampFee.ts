import {
    CryptoCurrency,
    DefaultCurrency,
    FiatCurrency,
} from '@zeal/domains/Currency'
import { FXRate2 } from '@zeal/domains/FXRate'
import {
    fetchDefaultCurrencyRateFromUSD,
    fetchDefaultCurrencyRateToFiatCurrency,
} from '@zeal/domains/FXRate/api/fetchDefaultCurrencyRateToUSD'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'

import { fetchTransactionFee, OffRampFeeParams } from './fetchTransactionFee'
import { fetchUnblockWithdrawRate } from './fetchUnblockFXRate'

import { UnblockTransferFee } from '..'

export type OffRampFeeResponse = {
    rate: FXRate2<CryptoCurrency, FiatCurrency>
    fee: UnblockTransferFee
    defaultCurrencyRateToUSD: FXRate2<FiatCurrency, DefaultCurrency> | null
    defaultCurrencyRateToInputCurrency: FXRate2<
        CryptoCurrency,
        DefaultCurrency
    > | null
    defaultCurrencyRateToOutputCurrency: FXRate2<
        FiatCurrency,
        DefaultCurrency
    > | null
}

export type Params = OffRampFeeParams & { signal?: AbortSignal }

export const fetchUnblockOffRampFee = async ({
    signal,
    ...feeParams
}: Params): Promise<OffRampFeeResponse> => {
    const [
        defaultCurrencyRateToOutputCurrency,
        defaultCurrencyRateToInputCurrency,
        defaultCurrencyRateToUSD,
        rate,
        fee,
    ] = await Promise.all([
        fetchDefaultCurrencyRateToFiatCurrency({
            defaultCurrency: feeParams.defaultCurrencyConfig.defaultCurrency,
            fiatCurrency: feeParams.outputCurrency,
            signal,
        }),
        fetchRate({
            defaultCurrencyConfig: feeParams.defaultCurrencyConfig,
            cryptoCurrency: feeParams.inputCurrency,
            networkMap: feeParams.networkMap,
            networkRPCMap: feeParams.networkRPCMap,
            signal,
        }),
        fetchDefaultCurrencyRateFromUSD({
            defaultCurrencyConfig: feeParams.defaultCurrencyConfig,
            signal,
        }),
        fetchUnblockWithdrawRate({
            feeParams,
            signal,
        }),
        fetchTransactionFee({ feeParams, signal }),
    ])

    return {
        defaultCurrencyRateToOutputCurrency,
        defaultCurrencyRateToInputCurrency,
        defaultCurrencyRateToUSD,
        rate,
        fee,
    }
}
