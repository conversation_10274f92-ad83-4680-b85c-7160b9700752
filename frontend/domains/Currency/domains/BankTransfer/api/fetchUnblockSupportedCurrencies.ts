import { values } from '@zeal/toolkit/Object'

import {
    CryptoCurrency,
    Currency,
    CurrencyId,
    FiatCurrency,
    KnownCurrencies,
} from '@zeal/domains/Currency'
import { INITIAL_DEFAULT_CURRENCY } from '@zeal/domains/Currency/constants'
import { NetworkMap } from '@zeal/domains/Network'

import {
    UNBLOCK_FIAT_CURRENCIES_MAP,
    UNBLOCK_SUPPORTED_CRYPTO_CURRENCIES,
} from '../constants'

export type UnblockFiatCurrencyCode = 'GBP' | 'EUR'

export type BankTransferFiatCurrencies = Record<
    UnblockFiatCurrencyCode,
    FiatCurrency<UnblockFiatCurrencyCode>
>

// TODO :: @Nicvaniek rename to be unblock specific?
export type BankTransferCurrencies = {
    fiatCurrencies: BankTransferFiatCurrencies
    cryptoCurrencies: CryptoCurrency[]
    knownCurrencies: KnownCurrencies
}

/**
 * @deprecated FIXME @resetko-zeal kill
 */
export const fetchUnblockSupportedCurrencies = async ({
    networkMap,
    signal,
}: {
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<BankTransferCurrencies> => {
    const cryptoCurrencies = UNBLOCK_SUPPORTED_CRYPTO_CURRENCIES

    const knownCurrencies = [
        INITIAL_DEFAULT_CURRENCY,
        ...cryptoCurrencies,
        ...values(UNBLOCK_FIAT_CURRENCIES_MAP),
    ].reduce(
        (record, currency) => {
            record[currency.id] = currency
            return record
        },
        {} as Record<CurrencyId, Currency>
    )

    return {
        fiatCurrencies: UNBLOCK_FIAT_CURRENCIES_MAP,
        cryptoCurrencies,
        knownCurrencies,
    }
}
