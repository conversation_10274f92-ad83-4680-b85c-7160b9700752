import {
    CryptoCurrency,
    DefaultCurrency,
    FiatCurrency,
} from '@zeal/domains/Currency'
import { FXRate2 } from '@zeal/domains/FXRate'
import {
    fetchDefaultCurrencyRateFromUSD,
    fetchDefaultCurrencyRateToFiatCurrency,
} from '@zeal/domains/FXRate/api/fetchDefaultCurrencyRateToUSD'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'

import { fetchTransactionFee, OnRampFeeParams } from './fetchTransactionFee'
import { fetchUnblockDepositRate } from './fetchUnblockFXRate'

import { UnblockTransferFee } from '..'

export type OnRampFeeResponse = {
    rate: FXRate2<FiatCurrency, CryptoCurrency>
    defaultCurrencyRateToUSD: FXRate2<FiatCurrency, DefaultCurrency> | null
    defaultCurrencyRateToInputCurrency: FXRate2<
        FiatCurrency,
        DefaultCurrency
    > | null
    defaultCurrencyRateToOutputCurrency: FXRate2<
        CryptoCurrency,
        DefaultCurrency
    > | null
    fee: UnblockTransferFee
}

type Params = OnRampFeeParams & { signal?: AbortSignal }

export const fetchUnblockOnRampFee = async ({
    signal,
    ...feeParams
}: Params): Promise<OnRampFeeResponse> => {
    const [
        defaultCurrencyRateToOutputCurrency,
        defaultCurrencyRateToInputCurrency,
        defaultCurrencyRateToUSD,
        rate,
        fee,
    ] = await Promise.all([
        fetchRate({
            cryptoCurrency: feeParams.outputCurrency,
            defaultCurrencyConfig: feeParams.defaultCurrencyConfig,
            networkMap: feeParams.networkMap,
            networkRPCMap: feeParams.networkRPCMap,
            signal,
        }),
        fetchDefaultCurrencyRateToFiatCurrency({
            defaultCurrency: feeParams.defaultCurrencyConfig.defaultCurrency,
            fiatCurrency: feeParams.inputCurrency,
            signal,
        }),
        fetchDefaultCurrencyRateFromUSD({
            defaultCurrencyConfig: feeParams.defaultCurrencyConfig,
            signal,
        }),
        fetchUnblockDepositRate({ feeParams, signal }),
        fetchTransactionFee({ feeParams, signal }),
    ])

    return {
        rate,
        fee,
        defaultCurrencyRateToInputCurrency,
        defaultCurrencyRateToOutputCurrency,
        defaultCurrencyRateToUSD,
    }
}
