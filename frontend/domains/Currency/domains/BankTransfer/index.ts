import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import * as Web3 from '@zeal/toolkit/Web3'

import { CountryISOCode } from '@zeal/domains/Country'
import { FiatCurrency } from '@zeal/domains/Currency'
import { <PERSON>ptoMoney, FiatMoney } from '@zeal/domains/Money'

import { OffRampFeeParams, OnRampFeeParams } from './api/fetchTransactionFee'
import { OffRampFeeResponse } from './api/fetchUnblockOffRampFee'
import { OnRampFeeResponse } from './api/fetchUnblockOnRampFee'

export type DepositWithdrawVariant = { type: 'deposit' } | { type: 'withdraw' }

export type KycNotStarted = {
    type: 'not_started'
}

export type KycApproved = {
    type: 'approved'
}

export type KycPaused = {
    type: 'paused'
}

export type KycFailed = {
    type: 'failed'
}

export type KycInProgress = {
    type: 'in_progress'
}

// https://zeal-hzn3129.slack.com/archives/C06FCGTHA8N/p1741640796870899
export type KycReverificationNeeded = {
    type: 'reverification_needed'
    lastSuccessfulCheck: Date
}

export type KycStatusBasedOnUserStatus =
    | KycNotStarted
    | KycApproved
    | KycPaused
    | KycFailed
    | KycInProgress

export type KycStatus = KycStatusBasedOnUserStatus | KycReverificationNeeded

export type UnblockUser = {
    firstName: string
    lastName: string
    kycStatus: KycStatus
    signInCryptoAddress: Web3.address.Address
    depositReceiverAddress: Web3.address.Address
    emailAddress: string
    residenceDetails: {
        country: CountryISOCode
        address: string | null
        postCode: string | null
        city: string | null
    }
}

export type OnRampAccount =
    | OnRampActiveAccount
    | OnRampPendingAccount
    | OnRampDisabledAccount

export type OnRampActiveAccount = {
    type: 'on_ramp_active_account'
    uuid: string
    currency: FiatCurrency
    bankDetails: BankAccountDetails
}

export type OnRampPendingAccount = {
    type: 'on_ramp_pending_account'
    uuid: string
}

export type OnRampDisabledAccount = {
    type: 'on_ramp_disabled_account'
    uuid: string
}

export type OffRampAccount = {
    type: 'off_ramp_account'
    uuid: string
    mainBeneficiary: boolean
    currency: FiatCurrency
    bankDetails: BankAccountDetails
}

export type DepositPollable = PollableData<OnRampFeeResponse, OnRampFeeParams>

export type WithdrawPollable = PollableData<
    OffRampFeeResponse,
    OffRampFeeParams
>

export type BankAccountDetails =
    | {
          type: 'uk'
          accountNumber: string
          sortCode: string
      }
    | { type: 'iban'; iban: string; bic: string | null }

export type UserInputForBankDetails =
    | {
          type: 'uk'
          accountNumber: string
          sortCode: string
          currency: FiatCurrency<'GBP'>
      }
    | { type: 'iban'; iban: string; currency: FiatCurrency<'EUR'> }

export type UnblockTransferFee = {
    amount: FiatMoney
    percentageFee: number
}

export type UnblockLoginSignature = {
    message: string
    signature: string
}

export type WithdrawalRequest =
    | {
          type: 'full_withdrawal_request'
          fromAmount: CryptoMoney
          toAmount: FiatMoney
          fee: UnblockTransferFee | null
      }
    | {
          type: 'incomplete_withdrawal_request'
          fromAmount: CryptoMoney
          toCurrency: FiatCurrency
      }

export type SubmittedOfframpTransaction = {
    transactionHash: string
    withdrawalRequest: WithdrawalRequest
}

export type UnblockChain = 'polygon' | 'optimism' | 'arbitrum' | 'base'

export type UnblockEvent =
    | KYCEventStatusChangedEvent
    | UnblockOfframpEvent
    | UnblockOnrampEvent
    | UnblockBankAccountCreateEvent
    | UnblockBankAccountStatusUpdateEvent

export type UnblockOfframpEvent =
    | UnblockOfframpFailedEvent
    | UnblockOfframpFiatTransferIssuedEvent
    | UnblockOfframpInProgressEvent
    | UnblockOfframpLimitBreachedEvent
    | UnblockOfframpOnHoldComplianceEvent
    | UnblockOfframpOnHoldReviewEvent
    | UnblockOfframpOutsideTransferReceivedEvent
    | UnblockOfframpPendingEvent
    | UnblockOfframpSuccessEvent

export type UnblockOnrampEvent =
    | UnblockOnrampCryptoTransferCompletedEvent
    | UnblockOnrampCryptoTransferCompletedNoInfoEvent
    | UnblockOnrampCryptoTransferIssuedEvent
    | UnblockOnrampFailedEvent
    | UnblockOnrampPendingEvent
    | UnblockOnrampProcessCompletedEvent
    | UnblockOnrampTransferApprovedEvent
    | UnblockOnrampTransferInProgress
    | UnblockOnrampTransferInReviewEvent
    | UnblockOnrampTransferOnHoldComplianceEvent
    | UnblockOnrampTransferOnHoldReviewEvent
    | UnblockOnrampTransferReceivedEvent

type OnrampEventData = {
    userIdHash: string
    transactionUuid: string
    createdAt: number
}

export type UnblockOnrampTransferInProgress = {
    type: 'unblock_onramp_transfer_in_progress'
    fiatMoney: FiatMoney
} & OnrampEventData

export type UnblockOnrampTransferReceivedEvent = {
    type: 'unblock_onramp_transfer_received'
    fiatMoney: FiatMoney | null
} & OnrampEventData

export type UnblockOnrampTransferApprovedEvent = {
    type: 'unblock_onramp_transfer_approved'
    fiatMoney: FiatMoney
} & OnrampEventData

export type UnblockOnrampTransferInReviewEvent = {
    type: 'unblock_onramp_transfer_in_review'
    fiatMoney: FiatMoney
} & OnrampEventData

export type UnblockOnrampTransferOnHoldComplianceEvent = {
    type: 'unblock_onramp_transfer_on_hold_compliance'
    fiatMoney: FiatMoney
} & OnrampEventData

export type UnblockOnrampTransferOnHoldReviewEvent = {
    type: 'unblock_onramp_transfer_on_hold_review'
    fiatMoney: FiatMoney
} & OnrampEventData

export type UnblockOnrampCryptoTransferIssuedEvent = {
    type: 'unblock_onramp_crypto_transfer_issued'
    fiatMoney: FiatMoney
    cryptoMoney: CryptoMoney
    transactionHash: string
} & OnrampEventData

export type UnblockOnrampCryptoTransferCompletedEvent = {
    type: 'unblock_onramp_crypto_transfer_completed'
    fiatMoney: FiatMoney
    cryptoMoney: CryptoMoney
    transactionHash: string
} & OnrampEventData

export type UnblockOnrampCryptoTransferCompletedNoInfoEvent = {
    type: 'unblock_onramp_crypto_transfer_completed_no_info'
} & OnrampEventData

export type UnblockOnrampProcessCompletedEvent = {
    type: 'unblock_onramp_process_completed'
    fiatMoney: FiatMoney
    cryptoMoney: CryptoMoney
    transactionHash: string
} & OnrampEventData

export type UnblockOnrampFailedEvent = {
    type: 'unblock_onramp_failed'
    fiatMoney: FiatMoney
} & OnrampEventData

export type UnblockOnrampPendingEvent = {
    type: 'unblock_onramp_pending'
    fiatMoney: FiatMoney
} & OnrampEventData

type OffRampEventData = {
    userIdHash: string
    transactionUuid: string
    transactionHash: string
    createdAt: number
}

export type UnblockOfframpInProgressEvent = {
    type: 'unblock_offramp_in_progress'
    cryptoMoney: CryptoMoney
} & OffRampEventData

export type UnblockOfframpOutsideTransferReceivedEvent = {
    type: 'unblock_offramp_outside_transfer_received'
    cryptoMoney: CryptoMoney
} & OffRampEventData

export type UnblockOfframpFiatTransferIssuedEvent = {
    type: 'unblock_offramp_fiat_transfer_issued'
    cryptoMoney: CryptoMoney
    fiatMoney: FiatMoney
} & OffRampEventData

export type UnblockOfframpSuccessEvent = {
    type: 'unblock_offramp_success'
    cryptoMoney: CryptoMoney
    fiatMoney: FiatMoney
} & OffRampEventData

export type UnblockOfframpOnHoldComplianceEvent = {
    type: 'unblock_offramp_on_hold_compliance'
    cryptoMoney: CryptoMoney
} & OffRampEventData

export type UnblockOfframpOnHoldReviewEvent = {
    type: 'unblock_offramp_on_hold_review'
    cryptoMoney: CryptoMoney
} & OffRampEventData

export type UnblockOfframpFailedEvent = {
    type: 'unblock_offramp_failed'
    fiatMoney: FiatMoney | null
} & OffRampEventData

// This should never happen through Zeal, only if users manually send small amounts to their off-ramp address. If we run into this case we can create proper designs, but for now we just treat it as in-progress. https://docs.getunblock.com/docs/webhooks-standards#limit_breached
export type UnblockOfframpLimitBreachedEvent = {
    type: 'unblock_offramp_limit_breached'
} & Omit<OffRampEventData, 'transactionUuid'>

export type UnblockOfframpPendingEvent = {
    type: 'unblock_offramp_pending'
} & OffRampEventData

export type UnblockBankAccountCreateEvent = {
    type: 'unblock_bank_account_create'
    userIdHash: string
    createdAt: number
}

export type UnblockBankAccountStatusUpdateEvent = {
    type: 'unblock_bank_account_status_update'
    userIdHash: string
    createdAt: number
}

export type KYCEventStatusChangedEvent = {
    type: 'kyc_event_status_changed'
    userIdHash: string
    status: KycStatusBasedOnUserStatus
    createdAt: number
}
