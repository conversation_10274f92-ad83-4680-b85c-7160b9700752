import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { SwapsIOQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    OrderBuySignMessage,
    OrderCardTopupSignMessage,
    OrderEarnDepositBridge,
} from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { SwapsioEventSource } from '@zeal/domains/UserEvents'

import { Erc20TokenSwap } from './ERC20TokenSwap'
import { NativeTokenSwap } from './NativeTokenSwap'

type Props = {
    quote: SwapsIOQuote
    simulation:
        | OrderCardTopupSignMessage
        | OrderEarnDepositBridge
        | OrderBuySignMessage

    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    sessionPassword: string
    senderPortfolio: ServerPortfolio2
    defaultCurrencyConfig: DefaultCurrencyConfig
    source: SwapsioEventSource
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof NativeTokenSwap> | MsgOf<typeof Erc20TokenSwap>

export const SubmitSwap = ({
    quote,
    keyStoreMap,
    networkMap,
    senderPortfolio,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    networkRPCMap,
    onMsg,
    sessionPassword,
    simulation,
    installationId,
    defaultCurrencyConfig,
    source,
}: Props) => {
    switch (quote.type) {
        case 'swaps_io_quote_erc20_swap':
            return (
                <Erc20TokenSwap
                    quote={quote}
                    simulation={simulation}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    senderPortfolio={senderPortfolio}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    source={source}
                    onMsg={onMsg}
                />
            )
        case 'swaps_io_quote_native_swap':
            return (
                <NativeTokenSwap
                    quote={quote}
                    simulation={simulation}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    senderPortfolio={senderPortfolio}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    source={source}
                    onMsg={onMsg}
                />
            )
        default:
            return notReachable(quote)
    }
}
