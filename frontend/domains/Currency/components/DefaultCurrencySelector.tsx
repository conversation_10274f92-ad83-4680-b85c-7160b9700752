import { useMemo } from 'react'
import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Chain } from '@zeal/uikit/Chain'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { GroupList } from '@zeal/uikit/GroupList'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { ListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'

import { Country } from '@zeal/domains/Country'
import {
    COUNTRY_TO_CURRENCY_MAP,
    EEA_COUNTRIES,
} from '@zeal/domains/Country/constants'
import { tryToGetUserTimeZoneCountries } from '@zeal/domains/Country/helpers/tryToGetUserTimeZoneCountries'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Avatar } from './Avatar'

import { DefaultCurrency, FiatCurrency, FiatCurrencyCode } from '..'
import { DEFAULT_CURRENCIES_LIST, FIAT_CURRENCIES } from '../constants'

type Props = {
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_default_currency_selected'; currency: DefaultCurrency }

const sortCurrenciesByCountries = ({
    localCountries,
}: {
    localCountries: Country[]
}) => {
    const localCurrencies = new Set(
        localCountries
            .map((country) => COUNTRY_TO_CURRENCY_MAP[country.code] || null)
            .filter(excludeNullValues)
    )

    const eeaCurrencies = new Set(
        EEA_COUNTRIES.map(
            (country) => COUNTRY_TO_CURRENCY_MAP[country] || null
        ).filter(excludeNullValues)
    )

    const topPrioCurrencies = new Set<FiatCurrencyCode>([
        FIAT_CURRENCIES.EUR.code,
        FIAT_CURRENCIES.USD.code,
        FIAT_CURRENCIES.GBP.code,
    ])

    return (a: FiatCurrency, b: FiatCurrency) => {
        const aIsLocal = localCurrencies.has(a.code)
        const bIsLocal = localCurrencies.has(b.code)

        const aIsEEA = eeaCurrencies.has(a.code)
        const bIsEEA = eeaCurrencies.has(b.code)

        const aIsTopPrio = topPrioCurrencies.has(a.code)
        const bIsTopPrio = topPrioCurrencies.has(b.code)

        if (aIsTopPrio && !bIsTopPrio) return -1
        if (!aIsTopPrio && bIsTopPrio) return 1

        if (aIsLocal && !bIsLocal) return -1
        if (!aIsLocal && bIsLocal) return 1

        if (aIsEEA && !bIsEEA) return -1
        if (!aIsEEA && bIsEEA) return 1

        return a.code.localeCompare(b.code)
    }
}

export const DefaultCurrencySelector = ({
    defaultCurrencyConfig,
    installationId,
    onMsg,
}: Props) => {
    const filteredList = useMemo(
        () =>
            DEFAULT_CURRENCIES_LIST.filter(
                ({ id }) => id !== defaultCurrencyConfig.defaultCurrency.id
            ).toSorted(
                sortCurrenciesByCountries({
                    localCountries:
                        tryToGetUserTimeZoneCountries().getSuccessResult() ||
                        [],
                })
            ),
        [defaultCurrencyConfig.defaultCurrency.id]
    )

    useEffect(() => {
        postUserEvent({
            type: 'DefaultCurrencySettingsEnteredEvent',
            installationId,
        })
    }, [installationId])

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={16}>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="textPrimary"
                                >
                                    <FormattedMessage
                                        id="settings.default_currency_selector.title"
                                        defaultMessage="Currency"
                                    />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                />
                <Column spacing={16}>
                    <Group variant="default">
                        <ListItem
                            size="regular"
                            aria-current={false}
                            avatar={({ size }) => (
                                <Avatar
                                    currency={
                                        defaultCurrencyConfig.defaultCurrency
                                    }
                                    size={size}
                                />
                            )}
                            primaryText={
                                defaultCurrencyConfig.defaultCurrency.name
                            }
                            shortText={
                                <Chain>
                                    {
                                        defaultCurrencyConfig.defaultCurrency
                                            .symbol
                                    }
                                    {defaultCurrencyConfig.defaultCurrency.code}
                                </Chain>
                            }
                            onClick={() => {
                                onMsg({
                                    type: 'close',
                                })
                            }}
                        />
                    </Group>

                    <GroupList
                        variant="default"
                        data={filteredList}
                        emptyState={null}
                        renderItem={({ item: currency }) => (
                            <ListItem
                                key={currency.id}
                                size="regular"
                                aria-current={false}
                                avatar={({ size }) => (
                                    <Avatar currency={currency} size={size} />
                                )}
                                primaryText={currency.name}
                                shortText={
                                    <Chain>
                                        {currency.symbol}
                                        {currency.code}
                                    </Chain>
                                }
                                onClick={() => {
                                    onMsg({
                                        type: 'on_default_currency_selected',
                                        currency: currency as DefaultCurrency,
                                    })
                                }}
                            />
                        )}
                    />
                </Column>
            </Column>
        </Screen>
    )
}
