import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { SwapRoute } from '@zeal/domains/Currency/domains/SwapQuote'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { fetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { fetchTransactionResultByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'

type Props = {
    route: SwapRoute

    sessionPassword: string
    account: Account
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolio: Portfolio2
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string

    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'on_all_transaction_success' }
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_wrong_network_accepted'
                  | 'transaction_failure_accepted'
                  | 'on_safe_transaction_failure_accepted'
                  | 'on_sign_cancel_button_clicked'
                  | 'transaction_submited'
                  | 'cancel_submitted'
                  | 'on_transaction_cancelled_successfully_close_clicked'
                  | 'transaction_cancel_failure_accepted'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'on_safe_transaction_completed_splash_animation_screen_competed'
                  | 'on_gas_currency_selected'
                  | 'on_close_transaction_status_not_found_modal'
                  | 'transaction_request_replaced'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >

type State =
    | { type: 'submit_approval'; approvalTransaction: EthSendTransaction }
    | { type: 'submit_swap_trx' }

export const SubmitSwapEOA = ({
    sessionPassword,
    account,
    accountsMap,
    keystoreMap,
    installationId,
    route,
    networkMap,
    portfolio,
    networkRPCMap,
    feePresetMap,
    onMsg,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>(() =>
        route.approvalTransaction
            ? {
                  type: 'submit_approval',
                  approvalTransaction: route.approvalTransaction,
              }
            : { type: 'submit_swap_trx' }
    )

    switch (state.type) {
        case 'submit_swap_trx':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={portfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    fetchSimulationByRequest={fetchSimulationByRequest}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    key={route.swapTransaction.id}
                    sendTransactionRequests={[route.swapTransaction]}
                    sessionPassword={sessionPassword}
                    account={account}
                    network={route.network}
                    networkRPCMap={networkRPCMap}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    installationId={installationId}
                    state={{ type: 'maximised' }}
                    actionSource={{
                        type: 'internal',
                        transactionEventSource: 'swap',
                        dAppSiteInfo: {
                            title: route.protocolDisplayName,
                            avatar: route.protocolIcon,
                            hostname: '',
                        },
                    }}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                                onMsg({ type: 'close' })
                                break
                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in swap $${msg.type}`
                                    )
                                )
                                break

                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'import_keys_button_clicked':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'transaction_submited':
                            case 'cancel_submitted':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'transaction_request_replaced':
                                onMsg(msg)
                                break

                            case 'on_completed_safe_transaction_close_click':
                            case 'on_completed_transaction_close_click':
                                onMsg({ type: 'on_all_transaction_success' })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit_approval':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolio={portfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    fetchSimulationByRequest={fetchSimulationByRequest}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    key={state.approvalTransaction.id}
                    sendTransactionRequests={[state.approvalTransaction]}
                    sessionPassword={sessionPassword}
                    account={account}
                    network={route.network}
                    networkRPCMap={networkRPCMap}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    installationId={installationId}
                    state={{ type: 'maximised' }}
                    actionSource={{
                        type: 'internal',
                        transactionEventSource: 'swapApprove',
                        dAppSiteInfo: {
                            title: route.protocolDisplayName,
                            avatar: route.protocolIcon,
                            hostname: '',
                        },
                    }}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                                onMsg({ type: 'close' })
                                break
                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in swap $${msg.type}`
                                    )
                                )
                                break

                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'import_keys_button_clicked':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'transaction_submited':
                            case 'cancel_submitted':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'transaction_request_replaced':
                            case 'on_close_transaction_status_not_found_modal':
                                onMsg(msg)
                                break

                            case 'on_completed_transaction_close_click':
                            case 'on_completed_safe_transaction_close_click':
                                setState({ type: 'submit_swap_trx' })
                                break

                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                onMsg(msg)
                                setState({ type: 'submit_swap_trx' })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
