import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { SwapRoute } from '@zeal/domains/Currency/domains/SwapQuote'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { fetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { fetchTransactionResultByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'

import { SubmitSwapEOA } from './SubmitSwapEOA'

type Props = {
    route: SwapRoute

    sessionPassword: string
    account: Account
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolio: Portfolio2
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    gasCurrencyPresetMap: GasCurrencyPresetMap
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'on_all_transaction_success' }
    | MsgOf<typeof SubmitSwapEOA>
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_safe_transaction_failure_accepted'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_wrong_network_accepted'
          }
      >

export const SubmitSwap = ({
    sessionPassword,
    account,
    accountsMap,
    keystoreMap,
    installationId,
    route,
    networkMap,
    portfolio,
    networkRPCMap,
    feePresetMap,
    onMsg,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
}: Props) => {
    const keyStore = getKeyStore({
        keyStoreMap: keystoreMap,
        address: account.address,
    })

    switch (keyStore.type) {
        case 'safe_4337':
            const transactionsToBundle: NonEmptyArray<EthSendTransaction> =
                route.approvalTransaction
                    ? [route.approvalTransaction, route.swapTransaction]
                    : [route.swapTransaction]
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    fetchSimulationByRequest={fetchSimulationByRequest}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    network={route.network}
                    networkRPCMap={networkRPCMap}
                    account={account}
                    accounts={accountsMap}
                    keystores={keystoreMap}
                    networkMap={networkMap}
                    sessionPassword={sessionPassword}
                    portfolio={portfolio}
                    state={{ type: 'maximised' }}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    actionSource={{
                        type: 'internal',
                        transactionEventSource: 'swap',
                        dAppSiteInfo: {
                            title: route.protocolDisplayName,
                            avatar: route.protocolIcon,
                            hostname: '',
                        },
                    }}
                    sendTransactionRequests={transactionsToBundle}
                    feePresetMap={feePresetMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'cancel_submitted':
                            case 'on_completed_transaction_close_click':
                            case 'transaction_failure_accepted':
                            case 'transaction_submited':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'transaction_request_replaced':
                                noop() // Not relevant to smart wallet
                                break
                            case 'on_minimize_click':
                                onMsg({ type: 'close' })
                                break
                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in swap 4337 $${msg.type}`
                                    )
                                )
                                break
                            case 'on_user_operation_bundled':
                                noop()
                                break
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_wrong_network_accepted':
                                onMsg(msg)
                                break
                            case 'on_completed_safe_transaction_close_click':
                                onMsg({ type: 'on_all_transaction_success' })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'track_only':
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
            return (
                <SubmitSwapEOA
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    route={route}
                    sessionPassword={sessionPassword}
                    account={account}
                    accountsMap={accountsMap}
                    keystoreMap={keystoreMap}
                    portfolio={portfolio}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}
