import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { QuestionCircle } from '@zeal/uikit/Icon/QuestionCircle'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Spacer } from '@zeal/uikit/Spacer'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { LazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { SwapRoute } from '@zeal/domains/Currency/domains/SwapQuote'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { FiatMoney } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { formattedCryptoMoneyPrecise } from '@zeal/domains/Money/helpers/formattedMoneyPrecise'
import { NetworkMap } from '@zeal/domains/Network'
import { FancyButton } from '@zeal/domains/Network/components/FancyButton'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'

import { ErrorMessage } from './ErrorMessage'
import { ErrorMessageBanner } from './ErrorMessageBanner'
import { Route } from './Route'

import {
    getMaxBalance,
    getRoute,
    MaxBalanceLoadable,
    Pollable,
    validate,
} from '../validation'

type Props = {
    pollable: Pollable
    maxBalanceLoadable: MaxBalanceLoadable
    gasFeeLoadable: LazyLoadableData<SwapRoute, unknown>

    portfolio: Portfolio2
    networkMap: NetworkMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_select_network_click' }
    | { type: 'on_to_currency_click' }
    | { type: 'on_from_currency_click' }
    | { type: 'on_amount_change'; amount: string | null }
    | { type: 'on_swap_continue_clicked'; route: SwapRoute }
    | { type: 'on_try_again_clicked' }
    | {
          type: 'on_fields_switch_clicked'
          fromCurrency: CryptoCurrency
          toCurrency: CryptoCurrency
          toAmount: string | null
      }
    | { type: 'on_slippage_clicked' }
    | MsgOf<typeof Route>
    | MsgOf<typeof ErrorMessageBanner>

export const Layout = ({
    pollable,
    maxBalanceLoadable,
    installationId,
    gasFeeLoadable,
    portfolio,
    networkMap,
    onMsg,
}: Props) => {
    const { formatMessage, formatNumber } = useIntl()
    const validationResult = validate({
        pollable,
        maxBalanceLoadable,
        portfolio,
    })

    const errors =
        validate({
            pollable,
            maxBalanceLoadable,
            portfolio,
        }).getFailureReason() || {}

    const { fromCurrency, toCurrency } = pollable.params

    const route: SwapRoute | null = getRoute(pollable)

    const toAmount =
        route && toCurrency
            ? formattedCryptoMoneyPrecise({
                  money: { amount: route.to.amount, currency: toCurrency },
                  withSymbol: false,
                  sign: null,
              })
            : null

    const fromToken = getTokenByCryptoCurrency3({
        currency: fromCurrency,
        serverPortfolio: portfolio,
    })

    const fromAmountInDefaultCurrency: FiatMoney | null =
        fromToken.rate && pollable.params.amount
            ? applyRate2({
                  baseAmount: {
                      amount: fromFixedWithFraction(
                          pollable.params.amount,
                          fromCurrency.fraction
                      ),
                      currency: fromCurrency,
                  },
                  rate: fromToken.rate,
              })
            : null

    const network = findNetworkByHexChainId(
        fromCurrency.networkHexChainId,
        networkMap
    )

    const networkSelector = (
        <FancyButton
            fill
            rounded={false}
            network={network}
            onClick={() => onMsg({ type: 'on_select_network_click' })}
        />
    )

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={0} fill>
                <ActionBar
                    top={
                        <ActionBarAccountIndicator
                            account={pollable.params.fromAccount}
                        />
                    }
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <ActionBar.Header>
                                    <FormattedMessage
                                        id="currency.swap.header"
                                        defaultMessage="Swap"
                                    />
                                </ActionBar.Header>
                            </Row>
                        </Clickable>
                    }
                />

                <Column spacing={12} fill alignY="stretch">
                    <ScrollContainer contentFill withFloatingActions={false}>
                        <Column spacing={4} fill>
                            <AmountInput
                                top={networkSelector}
                                content={{
                                    topLeft: (
                                        <IconButton
                                            variant="on_light"
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_from_currency_click',
                                                })
                                            }}
                                        >
                                            {({ color }) => (
                                                <Row spacing={4}>
                                                    <CurrencyAvatar
                                                        key={fromCurrency.id}
                                                        rightBadge={() => null}
                                                        currency={fromCurrency}
                                                        size={24}
                                                    />
                                                    <Text
                                                        variant="title3"
                                                        color="textPrimary"
                                                        weight="medium"
                                                    >
                                                        {fromCurrency.code}
                                                    </Text>

                                                    <LightArrowDown2
                                                        size={18}
                                                        color={color}
                                                    />
                                                </Row>
                                            )}
                                        </IconButton>
                                    ),
                                    topRight: ({ onBlur, onFocus }) => (
                                        <AmountInput.Input
                                            onBlur={onBlur}
                                            onFocus={onFocus}
                                            onChange={(value) =>
                                                onMsg({
                                                    type: 'on_amount_change',
                                                    amount: value,
                                                })
                                            }
                                            label={formatMessage({
                                                id: 'currency.swap.amount_to_swap',
                                                defaultMessage:
                                                    'Amount to swap',
                                            })}
                                            prefix=""
                                            fraction={fromCurrency.fraction}
                                            autoFocus
                                            amount={
                                                pollable.params.amount || null
                                            }
                                            onSubmitEditing={noop}
                                        />
                                    ),
                                    bottomRight:
                                        fromAmountInDefaultCurrency && (
                                            <Text
                                                variant="footnote"
                                                color="textSecondary"
                                                weight="regular"
                                            >
                                                <FormattedMoneyPrecise
                                                    withSymbol
                                                    sign={null}
                                                    money={
                                                        fromAmountInDefaultCurrency
                                                    }
                                                />
                                            </Text>
                                        ),
                                    bottomLeft: (
                                        <MaxButton
                                            installationId={installationId}
                                            location="swap"
                                            balance={getMaxBalance({
                                                maxBalanceLoadable,
                                                portfolio,
                                            })}
                                            onMsg={onMsg}
                                            state={(() => {
                                                switch (
                                                    maxBalanceLoadable.type
                                                ) {
                                                    case 'loading':
                                                        return 'loading'
                                                    case 'error':
                                                    case 'loaded':
                                                        return errors.fromToken
                                                            ? 'error'
                                                            : 'normal'
                                                    default:
                                                        return notReachable(
                                                            maxBalanceLoadable
                                                        )
                                                }
                                            })()}
                                        />
                                    ),
                                }}
                                state={errors.fromToken ? 'error' : 'normal'}
                            />

                            <NextStepSeparator
                                onClick={
                                    toCurrency
                                        ? () =>
                                              onMsg({
                                                  type: 'on_fields_switch_clicked',
                                                  fromCurrency,
                                                  toAmount,
                                                  toCurrency,
                                              })
                                        : undefined
                                }
                            />

                            <AmountInput
                                top={networkSelector}
                                content={{
                                    topRight: ({ onBlur, onFocus }) => (
                                        <AmountInput.Input
                                            onBlur={onBlur}
                                            onFocus={onFocus}
                                            label={formatMessage({
                                                id: 'currency.swap.destination_amount',
                                                defaultMessage:
                                                    'Destination amount',
                                            })}
                                            prefix=""
                                            readOnly
                                            onChange={noop}
                                            amount={toAmount}
                                            fraction={toCurrency?.fraction ?? 0}
                                            onSubmitEditing={noop}
                                        />
                                    ),
                                    topLeft: (
                                        <IconButton
                                            variant="on_light"
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_to_currency_click',
                                                })
                                            }}
                                        >
                                            {({ color }) => (
                                                <Row spacing={4}>
                                                    {toCurrency ? (
                                                        <>
                                                            <CurrencyAvatar
                                                                key={
                                                                    toCurrency.id
                                                                }
                                                                currency={
                                                                    toCurrency
                                                                }
                                                                size={24}
                                                                rightBadge={() =>
                                                                    null
                                                                }
                                                            />
                                                            <Text
                                                                variant="title3"
                                                                color="textPrimary"
                                                                weight="medium"
                                                            >
                                                                {
                                                                    toCurrency.code
                                                                }
                                                            </Text>
                                                            <LightArrowDown2
                                                                size={18}
                                                                color={color}
                                                            />
                                                        </>
                                                    ) : (
                                                        <>
                                                            <Avatar size={24}>
                                                                <QuestionCircle
                                                                    size={24}
                                                                    color={
                                                                        color
                                                                    }
                                                                />
                                                            </Avatar>
                                                            <Text
                                                                variant="title3"
                                                                color="textPrimary"
                                                                weight="medium"
                                                            >
                                                                <FormattedMessage
                                                                    id="currency.swap.select_to_token"
                                                                    defaultMessage="Select token"
                                                                />
                                                            </Text>
                                                            <LightArrowDown2
                                                                size={18}
                                                                color={color}
                                                            />
                                                        </>
                                                    )}
                                                </Row>
                                            )}
                                        </IconButton>
                                    ),
                                    bottomRight: route?.toInDefaultCurrency && (
                                        <Text
                                            variant="footnote"
                                            color="textSecondary"
                                            weight="regular"
                                        >
                                            <FormattedMoneyPrecise
                                                withSymbol
                                                sign={null}
                                                money={
                                                    route.toInDefaultCurrency
                                                }
                                            />
                                        </Text>
                                    ),
                                }}
                                state="normal"
                            />
                            <Spacer />

                            <Column spacing={8}>
                                <Row spacing={4} alignX="stretch">
                                    <Text
                                        variant="footnote"
                                        weight="regular"
                                        color="textSecondary"
                                    >
                                        <FormattedMessage
                                            id="currency.swap.swap_settings"
                                            defaultMessage="Swap settings"
                                        />
                                    </Text>

                                    <Tertiary
                                        color="on_light"
                                        size="small"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_slippage_clicked',
                                            })
                                        }
                                    >
                                        {({
                                            color,
                                            textVariant,
                                            textWeight,
                                        }) => (
                                            <Row spacing={4}>
                                                <Text
                                                    color={color}
                                                    variant={textVariant}
                                                    weight={textWeight}
                                                >
                                                    <FormattedMessage
                                                        id="SelectRoute.slippage"
                                                        defaultMessage="Slippage {slippage}"
                                                        values={{
                                                            slippage:
                                                                formatNumber(
                                                                    pollable
                                                                        .params
                                                                        .swapSlippagePercent /
                                                                        100,
                                                                    {
                                                                        // TODO @resetko-zeal use getFormattedPercentage
                                                                        style: 'percent',
                                                                        minimumFractionDigits: 0,
                                                                        maximumFractionDigits: 2,
                                                                    }
                                                                ),
                                                        }}
                                                    />
                                                </Text>
                                                <LightArrowDown2
                                                    size={16}
                                                    color={color}
                                                />
                                            </Row>
                                        )}
                                    </Tertiary>
                                </Row>
                                <Route pollable={pollable} onMsg={onMsg} />
                            </Column>
                        </Column>
                    </ScrollContainer>

                    <ErrorMessageBanner
                        gasFeeLoadable={gasFeeLoadable}
                        onMsg={onMsg}
                    />

                    <Actions variant="default">
                        <Button
                            variant="primary"
                            size="regular"
                            disabled={(() => {
                                if (!!errors.submit) {
                                    return true
                                }
                                switch (gasFeeLoadable.type) {
                                    case 'not_asked':
                                    case 'loaded':
                                    case 'error':
                                        return false
                                    case 'loading':
                                        return true
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(gasFeeLoadable)
                                }
                            })()}
                            onClick={() => {
                                switch (validationResult.type) {
                                    case 'Failure':
                                        break
                                    case 'Success':
                                        onMsg({
                                            type: 'on_swap_continue_clicked',
                                            route: validationResult.data,
                                        })
                                        break

                                    /* istanbul ignore next */
                                    default:
                                        notReachable(validationResult)
                                }
                            }}
                        >
                            {(() => {
                                switch (validationResult.type) {
                                    case 'Failure':
                                        return (
                                            <ErrorMessage
                                                error={
                                                    validationResult.reason
                                                        .submit
                                                }
                                            />
                                        )
                                    case 'Success':
                                        return (
                                            <FormattedMessage
                                                id="action.continue"
                                                defaultMessage="Continue"
                                            />
                                        )
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(validationResult)
                                }
                            })()}
                        </Button>
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}
