import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { LoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import {
    SwapQuote,
    SwapQuoteRequest,
    SwapRoute,
} from '@zeal/domains/Currency/domains/SwapQuote'
import { CryptoMoney } from '@zeal/domains/Money'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

export type Pollable = LoadedPollableData<SwapQuote, SwapQuoteRequest>

export type MaxBalanceLoadable = LoadableData<
    CryptoMoney,
    {
        swapQuoteRequest: SwapQuoteRequest
        portfolio: Portfolio2
    }
>

type NoRoutesFoundError = { type: 'no_routes_found' }
type SelectedRouteIsNoMoreAvailable = {
    type: 'selected_route_is_no_more_available'
}

type PollableReloading = { type: 'pollable_reloading' }
type PollableSubsequentFailed = { type: 'pollable_subsequent_failed' }

type NotEnoughBalanceError = { type: 'not_enough_balance' }
type MaxBalanceStillLoading = { type: 'max_balance_still_loading' }
type AmountRequired = { type: 'amount_required' }

export type FromTokenError =
    | NotEnoughBalanceError
    | AmountRequired
    | MaxBalanceStillLoading

type SubmitError =
    | PollableReloading
    | PollableSubsequentFailed
    | NoRoutesFoundError
    | FromTokenError
    | SelectedRouteIsNoMoreAvailable

export type FormError = {
    fromToken?: FromTokenError
    submit?: SubmitError
}

export const getRoute = (
    pollable: LoadedReloadableData<SwapQuote, SwapQuoteRequest>
): SwapRoute | null =>
    pollable.data.routes.find(
        (route) => pollable.params.usedDexName === route.dexName
    ) ||
    pollable.data.routes[0] ||
    null

export const getMaxBalance = ({
    maxBalanceLoadable,
    portfolio,
}: {
    maxBalanceLoadable: MaxBalanceLoadable
    portfolio: Portfolio2
}) => {
    switch (maxBalanceLoadable.type) {
        case 'loading':
        case 'error':
            return getBalanceByCryptoCurrency2({
                currency:
                    maxBalanceLoadable.params.swapQuoteRequest.fromCurrency,
                serverPortfolio: portfolio,
            })

        case 'loaded':
            return maxBalanceLoadable.data
        /* istanbul ignore next */
        default:
            return notReachable(maxBalanceLoadable)
    }
}

export const validate = ({
    pollable,
    portfolio,
    maxBalanceLoadable,
}: {
    pollable: Pollable
    portfolio: Portfolio2
    maxBalanceLoadable: MaxBalanceLoadable
}): Result<FormError, SwapRoute> =>
    shape({
        fromToken: validateBalance({ maxBalanceLoadable, portfolio, pollable }),
        submit: validateFromAmountRequired({
            pollable,
        })
            .andThen(() =>
                validateBalance({ maxBalanceLoadable, portfolio, pollable })
            )
            .andThen(() => validateMaxBalanceLoadable({ maxBalanceLoadable }))
            .andThen(() => validatePollable({ pollable }))
            .andThen(() => validateRoute({ pollable })),
    }).map(({ submit }) => submit)

const validateMaxBalanceLoadable = ({
    maxBalanceLoadable,
}: {
    maxBalanceLoadable: MaxBalanceLoadable
}): Result<MaxBalanceStillLoading, unknown> => {
    switch (maxBalanceLoadable.type) {
        case 'loading':
            return failure({ type: 'max_balance_still_loading' })
        case 'error':
        case 'loaded':
            return success(undefined)

        /* istanbul ignore next */
        default:
            return notReachable(maxBalanceLoadable)
    }
}

const validateFromAmountRequired = ({
    pollable,
}: {
    pollable: Pollable
}): Result<AmountRequired, unknown> => {
    const currency = pollable.params.fromCurrency
    const amount = fromFixedWithFraction(
        pollable.params.amount,
        currency.fraction
    )

    if (amount === 0n) {
        return failure({ type: 'amount_required' })
    }

    return success(undefined)
}

const validateBalance = ({
    maxBalanceLoadable,
    pollable,
    portfolio,
}: {
    portfolio: Portfolio2
    pollable: Pollable
    maxBalanceLoadable: MaxBalanceLoadable
}): Result<NotEnoughBalanceError, unknown> => {
    const maxBalance = getMaxBalance({ maxBalanceLoadable, portfolio })

    const currency = pollable.params.fromCurrency
    const amount = fromFixedWithFraction(
        pollable.params.amount,
        currency.fraction
    )

    if (maxBalance.amount < amount) {
        return failure({ type: 'not_enough_balance' })
    }

    return success(undefined)
}

const validatePollable = ({
    pollable,
}: {
    pollable: Pollable
}): Result<PollableReloading | PollableSubsequentFailed, unknown> => {
    switch (pollable.type) {
        case 'loaded':
            return success(pollable)

        case 'reloading':
            return failure({ type: 'pollable_reloading' })

        case 'subsequent_failed':
            return failure({ type: 'pollable_subsequent_failed' })

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}

const validateRoute = ({
    pollable,
}: {
    pollable: Pollable
}): Result<NoRoutesFoundError | SelectedRouteIsNoMoreAvailable, SwapRoute> => {
    const routes = pollable.data.routes
    if (!routes.length) {
        return failure({ type: 'no_routes_found' })
    }

    const selectedRoute = getRoute(pollable)

    if (!selectedRoute) {
        return failure({ type: 'selected_route_is_no_more_available' })
    }

    return success(selectedRoute)
}
