import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyId,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { CurrenciesMatrix } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import {
    SwapQuoteRequest,
    SwapRoute,
} from '@zeal/domains/Currency/domains/SwapQuote'
import { fetchMaxBalance } from '@zeal/domains/Currency/domains/SwapQuote/api/fetchMaxBalance'
import { fetchSwapQuote } from '@zeal/domains/Currency/domains/SwapQuote/api/fetchSwapQuote'
import { DEFAULT_SWAP_SLIPPAGE_PERCENT } from '@zeal/domains/Currency/domains/SwapQuote/constants'
import {
    getCryptoCurrency,
    getCryptoCurrencyOrThrow,
} from '@zeal/domains/Currency/helpers/getCryptoCurrency'
import { searchCryptoCurrencies } from '@zeal/domains/Currency/helpers/searchCryptoCurrencies'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap, Safe4337 } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR,
    findNetworkByHexChainId,
    OPTIMISM,
} from '@zeal/domains/Network/constants'
import { Portfolio2, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { fetchGasAbstractionTransactionFeesForMetaTransactions } from '@zeal/domains/UserOperation/api/fetchGasAbstractionTransactionFeesForMetaTransactions'
import { ethSendTransactionToMetaTransactionData } from '@zeal/domains/UserOperation/helpers/ethSendTransactionToMetaTransactionData'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

const DEFAULT_SWAP_NETWORK = OPTIMISM

type Props = {
    currenciesMatrix: CurrenciesMatrix
    portfolio: Portfolio2
    fromAccount: Account

    fromCurrencyId: CurrencyId | null
    toCurrencyId: CurrencyId | null

    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap

    swapSlippagePercent: number | null

    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof Layout>, { type: 'close' }>
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_all_transaction_success'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'transaction_submited'
                  | 'cancel_submitted'
                  | 'on_set_slippage_percent'
                  | 'on_rpc_change_confirmed'
                  | 'on_select_rpc_click'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'on_safe_transaction_completed_splash_animation_screen_competed'
                  | 'on_gas_currency_selected'
                  | 'transaction_request_replaced'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >

const getInitialNetworkByPortfolio = ({
    portfolio,
    currenciesMatrix,
    currencyHiddenMap,
    currencyPinMap,
    networkMap,
}: {
    portfolio: Portfolio2
    currenciesMatrix: CurrenciesMatrix
    networkMap: NetworkMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
}): Network | null => {
    const currenciesToSelectFrom = portfolio.tokens
        .map((token) => token.balance.currency)
        .filter(
            (currency) =>
                currenciesMatrix.knownCurrencies[currency.id] &&
                currenciesMatrix.currencies[currency.networkHexChainId]?.[
                    currency.networkHexChainId
                ]
        )

    const currencies = sortCurrencies({
        currencies: currenciesToSelectFrom,
        currencyHiddenMap,
        currencyPinMap,
        networkMap,
        portfolio,
    })

    const topCurrencyNetworkHexId = currencies[0]?.networkHexChainId

    return topCurrencyNetworkHexId
        ? findNetworkByHexChainId(topCurrencyNetworkHexId, networkMap)
        : null
}

const initSwapCurrencies = ({
    fromCurrencyId,
    toCurrencyId,
    currenciesMatrix,
    networkMap,
    fallbackNetwork,
    currencyHiddenMap,
    currencyPinMap,
    portfolio,
}: {
    fromCurrencyId: CurrencyId | null
    toCurrencyId: CurrencyId | null
    fallbackNetwork: Network
    networkMap: NetworkMap
    portfolio: Portfolio2
    currenciesMatrix: CurrenciesMatrix
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
}): {
    fromCurrency: CryptoCurrency
    toCurrency: CryptoCurrency
} => {
    const knownCurrencies = currenciesMatrix.knownCurrencies

    const fromCurrency = fromCurrencyId
        ? getCryptoCurrency({
              cryptoCurrencyId: fromCurrencyId,
              knownCurrencies,
          })
        : null

    const toCurrency = toCurrencyId
        ? getCryptoCurrency({
              cryptoCurrencyId: toCurrencyId,
              knownCurrencies,
          })
        : null

    const currenciesState:
        | { type: 'none' }
        | { type: 'only_from'; fromCurrency: CryptoCurrency }
        | { type: 'only_to'; toCurrency: CryptoCurrency }
        | {
              type: 'both'
              fromCurrency: CryptoCurrency
              toCurrency: CryptoCurrency
          } = (() => {
        if (
            fromCurrency &&
            toCurrency &&
            currenciesMatrix.currencies[fromCurrency.networkHexChainId] &&
            currenciesMatrix.currencies[toCurrency.networkHexChainId]
        ) {
            return { type: 'both', fromCurrency, toCurrency }
        } else if (
            fromCurrency &&
            !toCurrency &&
            currenciesMatrix.currencies[fromCurrency.networkHexChainId]
        ) {
            return { type: 'only_from', fromCurrency }
        } else if (
            !fromCurrency &&
            toCurrency &&
            currenciesMatrix.currencies[toCurrency.networkHexChainId]
        ) {
            return { type: 'only_to', toCurrency }
        } else {
            return { type: 'none' }
        }
    })()

    switch (currenciesState.type) {
        case 'none': {
            const network = fallbackNetwork

            const fromCurrencyIds =
                currenciesMatrix.currencies[network.hexChainId]?.[
                    network.hexChainId
                ]?.from || []

            const fromCurrencies = fromCurrencyIds
                .map((id) =>
                    getCryptoCurrency({
                        cryptoCurrencyId: id,
                        knownCurrencies: currenciesMatrix.knownCurrencies,
                    })
                )
                .filter((currency) => currency !== null)

            const fromCurrenciesSorted = sortCurrencies({
                currencies: fromCurrencies,
                currencyHiddenMap,
                currencyPinMap,
                networkMap,
                portfolio,
            })

            const fromCurrency = fromCurrenciesSorted[0] || null

            if (!fromCurrency) {
                throw new ImperativeError(
                    'Failed to find from currency for swap based on network',
                    {
                        network,
                        fromCurrencies,
                    }
                )
            }

            const matrixItem =
                currenciesMatrix.currencies[fromCurrency.networkHexChainId]?.[
                    fromCurrency.networkHexChainId
                ] || null

            const toCurrencyIds = matrixItem?.to || []

            const toCurrencies = toCurrencyIds
                .map((id) =>
                    getCryptoCurrency({
                        cryptoCurrencyId: id,
                        knownCurrencies: currenciesMatrix.knownCurrencies,
                    })
                )
                .filter((currency) => currency !== null)
                .filter((currency) => currency.id !== fromCurrency.id)

            const toCurrenciesSorted = sortCurrencies({
                currencies: toCurrencies,
                currencyHiddenMap,
                currencyPinMap,
                networkMap,
                portfolio,
            })

            const toCurrency = toCurrenciesSorted[0] || null

            if (!toCurrency) {
                captureError(
                    new ImperativeError('Failed to find to currency for swap', {
                        fromCurrency,
                    })
                )
                return { fromCurrency, toCurrency: fromCurrency }
            }

            return { toCurrency, fromCurrency }
        }

        case 'only_from': {
            const { fromCurrency } = currenciesState
            const matrixItem =
                currenciesMatrix.currencies[fromCurrency.networkHexChainId]?.[
                    fromCurrency.networkHexChainId
                ] || null

            const toCurrencyIds = matrixItem?.to || []

            const toCurrencies = toCurrencyIds
                .map((id) =>
                    getCryptoCurrency({
                        cryptoCurrencyId: id,
                        knownCurrencies: currenciesMatrix.knownCurrencies,
                    })
                )
                .filter((currency) => currency !== null)
                .filter((currency) => currency.id !== fromCurrency.id)

            const toCurrenciesSorted = sortCurrencies({
                currencies: toCurrencies,
                currencyHiddenMap,
                currencyPinMap,
                networkMap,
                portfolio,
            })

            const toCurrency = toCurrenciesSorted[0] || null

            if (!toCurrency) {
                captureError(
                    new ImperativeError('Failed to find to currency for swap', {
                        fromCurrency,
                    })
                )
                return { fromCurrency, toCurrency: fromCurrency }
            }

            return { toCurrency, fromCurrency }
        }

        case 'only_to': {
            const { toCurrency } = currenciesState

            const network = fallbackNetwork

            const matrixItem =
                currenciesMatrix.currencies[toCurrency.networkHexChainId]?.[
                    toCurrency.networkHexChainId
                ] || null

            const fromCurrencyIds = matrixItem?.from || []

            const fromCurrencies = fromCurrencyIds
                .map((id) =>
                    getCryptoCurrency({
                        cryptoCurrencyId: id,
                        knownCurrencies: currenciesMatrix.knownCurrencies,
                    })
                )
                .filter((currency) => currency !== null)
                .filter((currency) => currency.id !== toCurrency.id)

            const fromCurrenciesSorted = sortCurrencies({
                currencies: fromCurrencies,
                currencyHiddenMap,
                currencyPinMap,
                networkMap,
                portfolio,
            })

            const fromCurrency = fromCurrenciesSorted[0] || null

            if (!fromCurrency) {
                throw new ImperativeError(
                    'Failed to find from currency for swap based on network',
                    {
                        network,
                        fromCurrencies,
                    }
                )
            }

            if (!fromCurrency) {
                captureError(
                    new ImperativeError(
                        'Failed to find from currency for swap',
                        { toCurrency }
                    )
                )
                return { toCurrency, fromCurrency: toCurrency }
            }

            return { toCurrency, fromCurrency }
        }

        case 'both':
            return {
                fromCurrency: currenciesState.fromCurrency,
                toCurrency: currenciesState.toCurrency,
            }

        default:
            return notReachable(currenciesState)
    }
}

const sortCurrencies = ({
    currencies,
    currencyHiddenMap,
    currencyPinMap,
    networkMap,
    portfolio,
}: {
    currencies: CryptoCurrency[]
    portfolio: Portfolio2
    networkMap: NetworkMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
}): CryptoCurrency[] => {
    const result = searchCryptoCurrencies({
        currencies,
        currencyHiddenMap,
        currencyPinMap,
        networkMap,
        portfolio,
        searchTerm: '',
    })

    switch (result.type) {
        case 'grouped_results':
            return [
                ...result.portfolioCurrencies,
                ...result.nonPortfolioCurrencies,
            ]
        case 'no_currencies_found':
            return []

        default:
            return notReachable(result)
    }
}

const fetchSwapRouteGasFee = async ({
    swapRoute,
    keyStore,
    networkRPCMap,
    portfolio,
    defaultCurrencyConfig,
    networkMap,
    signal,
}: {
    swapRoute: SwapRoute
    keyStore: Safe4337
    networkRPCMap: NetworkRPCMap
    portfolio: ServerPortfolio2 | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<SwapRoute> => {
    return fetchGasAbstractionTransactionFeesForMetaTransactions({
        metaTransactionDatas: [
            swapRoute.approvalTransaction,
            swapRoute.swapTransaction,
        ]
            .filter(excludeNullValues)
            .map(ethSendTransactionToMetaTransactionData),
        network: swapRoute.network,
        keyStore,
        sponsored: DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR.includes(
            swapRoute.network.hexChainId
        ),
        networkRPCMap,
        serverPortfolio: portfolio,
        defaultCurrencyConfig,
        networkMap,
        callGasLimitBuffer: 0n,
        signal,
    }).then(() => swapRoute)
}

const POLL_INTERVAL_MS = 60_000

export const Form = ({
    portfolio,
    fromAccount,
    currenciesMatrix,
    fromCurrencyId,
    accountsMap,
    installationId,
    keystoreMap,
    sessionPassword,
    swapSlippagePercent,
    networkMap,
    networkRPCMap,
    feePresetMap,
    currencyHiddenMap,
    currencyPinMap,
    onMsg,
    gasCurrencyPresetMap,
    toCurrencyId,
    defaultCurrencyConfig,
}: Props) => {
    const [modalState, setModalState] = useState<ModalState>({ type: 'closed' })

    const [pollable, setPollable] = useLoadedPollableData(
        fetchSwapQuote,
        () => {
            const fallbackNetwork =
                getInitialNetworkByPortfolio({
                    currenciesMatrix,
                    currencyHiddenMap,
                    currencyPinMap,
                    networkMap,
                    portfolio,
                }) || DEFAULT_SWAP_NETWORK

            const { fromCurrency, toCurrency } = initSwapCurrencies({
                fromCurrencyId,
                networkMap,
                toCurrencyId,
                portfolio,
                currenciesMatrix,
                currencyHiddenMap,
                currencyPinMap,
                fallbackNetwork,
            })

            return {
                type: 'reloading',
                params: {
                    networkRPCMap,
                    swapSlippagePercent:
                        swapSlippagePercent || DEFAULT_SWAP_SLIPPAGE_PERCENT,
                    amount: null,
                    fromAccount,
                    fromCurrency,
                    toCurrency,
                    usedDexName: null,
                    networkMap,
                    recipient: fromAccount.address as Web3.address.Address,
                    defaultCurrencyConfig,
                    keyStoreMap: keystoreMap,
                } as SwapQuoteRequest,
                data: {
                    bestReturnRoute: null,
                    routes: [],
                },
            }
        },
        { pollIntervalMilliseconds: POLL_INTERVAL_MS }
    )

    const fromCurrencyBalance = getTokenByCryptoCurrency3({
        currency: pollable.params.fromCurrency,
        serverPortfolio: portfolio,
    })

    const [maxBalanceLoadable, setMaxBalanceLoadable] = useLoadableData(
        fetchMaxBalance,
        {
            type: 'loading',
            params: {
                swapQuoteRequest: {
                    ...pollable.params,
                    amount: toFixedWithFraction(
                        fromCurrencyBalance.balance.amount,
                        fromCurrencyBalance.balance.currency.fraction
                    ),
                    keyStoreMap: keystoreMap,
                },
                portfolio,
                gasCurrencyPresetMap,
                keyStore: getKeyStore({
                    address: fromAccount.address,
                    keyStoreMap: keystoreMap,
                }),
            },
        }
    )

    const [gasFeeLoadable, setGasFeeLoadable] = useLazyLoadableData(
        fetchSwapRouteGasFee,
        {
            type: 'not_asked',
        }
    )

    useEffect(() => {
        switch (gasFeeLoadable.type) {
            case 'not_asked':
            case 'loading':
            case 'error':
                break
            case 'loaded':
                setModalState({
                    type: 'execute_swap',
                    route: gasFeeLoadable.data,
                })
                break

            /* istanbul ignore next */
            default:
                notReachable(gasFeeLoadable)
        }
    }, [gasFeeLoadable])

    useEffect(() => {
        const fromBalance = getTokenByCryptoCurrency3({
            currency: pollable.params.fromCurrency,
            serverPortfolio: portfolio,
        })

        setMaxBalanceLoadable((old) => ({
            ...old,
            type: 'loading',
            params: {
                ...old.params,
                portfolio,
                swapQuoteRequest: {
                    amount: toFixedWithFraction(
                        fromBalance.balance.amount,
                        fromBalance.balance.currency.fraction
                    ),
                    defaultCurrencyConfig:
                        pollable.params.defaultCurrencyConfig,
                    fromAccount: pollable.params.fromAccount,
                    networkMap: pollable.params.networkMap,
                    networkRPCMap: pollable.params.networkRPCMap,
                    recipient: pollable.params.recipient,
                    swapSlippagePercent: pollable.params.swapSlippagePercent,
                    usedDexName: pollable.params.usedDexName,
                    toCurrency: pollable.params.toCurrency,
                    fromCurrency: pollable.params.fromCurrency,
                    keyStoreMap: keystoreMap,
                },
            },
        }))
    }, [
        pollable.params.defaultCurrencyConfig,
        pollable.params.fromAccount,
        pollable.params.networkMap,
        pollable.params.networkRPCMap,
        pollable.params.recipient,
        pollable.params.swapSlippagePercent,
        pollable.params.usedDexName,
        pollable.params.toCurrency,
        pollable.params.fromCurrency,
        setMaxBalanceLoadable,
        portfolio,
        keystoreMap,
    ])

    useEffect(() => {
        switch (maxBalanceLoadable.type) {
            case 'error':
                captureError(maxBalanceLoadable.error, {
                    extra: { context: 'maxButton balance correction on swap' },
                })
                break

            case 'loaded':
            case 'loading':
                break

            /* istanbul ignore next */
            default:
                notReachable(maxBalanceLoadable)
        }
    }, [maxBalanceLoadable])

    useEffect(() => {
        switch (pollable.type) {
            case 'subsequent_failed':
                captureError(pollable.error)
                break

            case 'reloading':
            case 'loaded':
                break

            /* istanbul ignore next */
            default:
                notReachable(pollable)
        }
    }, [pollable])

    switch (pollable.type) {
        case 'reloading':
        case 'loaded':
        case 'subsequent_failed':
            return (
                <>
                    <Layout
                        installationId={installationId}
                        maxBalanceLoadable={maxBalanceLoadable}
                        gasFeeLoadable={gasFeeLoadable}
                        portfolio={portfolio}
                        networkMap={networkMap}
                        pollable={pollable}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break

                                case 'on_swap_continue_clicked':
                                    const keyStore = getKeyStore({
                                        address:
                                            fromAccount.address as Web3.address.Address,
                                        keyStoreMap: keystoreMap,
                                    })

                                    switch (keyStore.type) {
                                        case 'private_key_store':
                                        case 'ledger':
                                        case 'secret_phrase_key':
                                        case 'trezor':
                                        case 'track_only':
                                            setModalState({
                                                type: 'execute_swap',
                                                route: msg.route,
                                            })
                                            break
                                        case 'safe_4337':
                                            setGasFeeLoadable({
                                                type: 'loading',
                                                params: {
                                                    swapRoute: msg.route,
                                                    keyStore,
                                                    networkRPCMap,
                                                    portfolio,
                                                    defaultCurrencyConfig,
                                                    networkMap,
                                                },
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            notReachable(keyStore)
                                    }
                                    break

                                case 'on_select_network_click':
                                    setModalState({ type: 'select_network' })
                                    break

                                case 'on_to_currency_click':
                                    setModalState({
                                        type: 'select_to_currency',
                                    })
                                    break

                                case 'on_from_currency_click':
                                    setModalState({
                                        type: 'select_from_currency',
                                    })
                                    break

                                case 'on_amount_change':
                                    setPollable({
                                        type: 'reloading',
                                        params: {
                                            ...pollable.params,
                                            amount: msg.amount,
                                        },
                                        data: pollable.data,
                                    })
                                    setGasFeeLoadable({ type: 'not_asked' })
                                    break

                                case 'on_try_again_clicked':
                                    setPollable({
                                        type: 'reloading',
                                        params: pollable.params,
                                        data: pollable.data,
                                    })
                                    setGasFeeLoadable({ type: 'not_asked' })
                                    break

                                case 'on_route_click':
                                    setModalState({ type: 'select_route' })
                                    break

                                case 'on_slippage_clicked':
                                    setModalState({ type: 'set_slippage' })
                                    break

                                case 'on_fields_switch_clicked':
                                    const {
                                        toAmount,
                                        fromCurrency,
                                        toCurrency,
                                    } = msg

                                    setPollable({
                                        type: 'reloading',
                                        params: {
                                            defaultCurrencyConfig,
                                            networkRPCMap,
                                            fromCurrency: toCurrency,
                                            amount: toAmount,
                                            toCurrency: fromCurrency,
                                            keyStoreMap: keystoreMap,
                                            usedDexName:
                                                pollable.params.usedDexName,
                                            swapSlippagePercent:
                                                pollable.params
                                                    .swapSlippagePercent,
                                            fromAccount:
                                                pollable.params.fromAccount,
                                            networkMap:
                                                pollable.params.networkMap,
                                            recipient:
                                                pollable.params.recipient,
                                        },
                                        data: {
                                            bestReturnRoute: null,
                                            routes: [],
                                        },
                                    })
                                    setGasFeeLoadable({ type: 'not_asked' })
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />

                    <Modal
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        portfolio={portfolio}
                        feePresetMap={feePresetMap}
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        fromAccount={fromAccount}
                        accountsMap={accountsMap}
                        installationId={installationId}
                        keystoreMap={keystoreMap}
                        sessionPassword={sessionPassword}
                        state={modalState}
                        currenciesMatrix={currenciesMatrix}
                        pollable={pollable}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'on_all_transaction_success':
                                case 'import_keys_button_clicked':
                                case 'transaction_submited':
                                case 'cancel_submitted':
                                case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                case 'on_transaction_completed_splash_animation_screen_competed':
                                case 'on_predefined_fee_preset_selected':
                                case 'on_rpc_change_confirmed':
                                case 'on_select_rpc_click':
                                case 'on_4337_auto_gas_token_selection_clicked':
                                case 'on_4337_gas_currency_selected':
                                case 'transaction_request_replaced':
                                    onMsg(msg)
                                    break

                                case 'on_cancel_confirm_transaction_clicked':
                                case 'on_wrong_network_accepted':
                                case 'transaction_failure_accepted':
                                case 'on_safe_transaction_failure_accepted':
                                case 'on_sign_cancel_button_clicked':
                                case 'on_transaction_cancelled_successfully_close_clicked':
                                case 'transaction_cancel_failure_accepted':
                                case 'close':
                                case 'on_close_transaction_status_not_found_modal':
                                    setModalState({ type: 'closed' })
                                    break

                                case 'on_network_item_click': {
                                    switch (msg.network.type) {
                                        case 'all_networks':
                                            throw new ImperativeError(
                                                'All networks cannot be selected on swap'
                                            )
                                        case 'specific_network': {
                                            setModalState({ type: 'closed' })
                                            const { fromCurrency, toCurrency } =
                                                initSwapCurrencies({
                                                    fromCurrencyId: null,
                                                    toCurrencyId: null,
                                                    fallbackNetwork:
                                                        msg.network.network,
                                                    currenciesMatrix,
                                                    currencyHiddenMap,
                                                    currencyPinMap,
                                                    networkMap,
                                                    portfolio,
                                                })

                                            setGasFeeLoadable({
                                                type: 'not_asked',
                                            })
                                            setPollable({
                                                type: 'reloading',
                                                params: {
                                                    ...pollable.params,
                                                    fromCurrency,
                                                    toCurrency,
                                                    amount: null,
                                                },
                                                data: pollable.data,
                                            })
                                            break
                                        }

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg.network)
                                    }
                                    break
                                }

                                case 'on_from_currency_selected':
                                    setModalState({ type: 'closed' })
                                    setGasFeeLoadable({ type: 'not_asked' })
                                    setPollable({
                                        type: 'reloading',
                                        params: {
                                            ...pollable.params,
                                            fromCurrency:
                                                getCryptoCurrencyOrThrow({
                                                    cryptoCurrencyId:
                                                        msg.currencyId,
                                                    knownCurrencies:
                                                        currenciesMatrix.knownCurrencies,
                                                }),
                                        },
                                        data: pollable.data,
                                    })
                                    break

                                case 'on_to_currency_selected':
                                    setModalState({ type: 'closed' })
                                    setGasFeeLoadable({ type: 'not_asked' })
                                    setPollable({
                                        type: 'reloading',
                                        params: {
                                            ...pollable.params,
                                            toCurrency:
                                                getCryptoCurrencyOrThrow({
                                                    cryptoCurrencyId:
                                                        msg.currencyId,
                                                    knownCurrencies:
                                                        currenciesMatrix.knownCurrencies,
                                                }),
                                        },
                                        data: pollable.data,
                                    })
                                    break

                                case 'on_route_selected':
                                    setModalState({ type: 'closed' })
                                    setGasFeeLoadable({ type: 'not_asked' })
                                    setPollable({
                                        type: 'reloading',
                                        params: {
                                            ...pollable.params,
                                            usedDexName: msg.route.dexName,
                                        },
                                        data: pollable.data,
                                    })
                                    break

                                case 'on_set_slippage_percent':
                                    setGasFeeLoadable({ type: 'not_asked' })
                                    setPollable({
                                        type: 'reloading',
                                        params: {
                                            ...pollable.params,
                                            swapSlippagePercent:
                                                msg.slippagePercent,
                                        },
                                        data: pollable.data,
                                    })
                                    setModalState({ type: 'closed' })
                                    onMsg(msg)
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        default:
            return notReachable(pollable)
    }
}
