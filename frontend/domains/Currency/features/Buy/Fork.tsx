import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { AddFunds } from '@zeal/domains/Account/features/AddFunds'
import { CardConfig } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { PricesMap } from '@zeal/domains/Currency/api/fetchPriceChange'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { RatesMap } from '@zeal/domains/FXRate'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { DataLoader } from './DataLoader'
import { SelectToCurrency } from './SelectToCurrency'

type Props = {
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    fromAddress: Web3.address.Address
    currencies: CryptoCurrency[]
    ratesMap: RatesMap
    pricesMap: PricesMap

    installationId: string
    networkMap: NetworkMap
    keyStoreMap: KeyStoreMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    sessionPassword: string
    isEthereumNetworkFeeWarningSeen: boolean
    contractsMap: SwapsIOContractsMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof SelectToCurrency>, { type: 'close' }>
    | MsgOf<typeof AddFunds>
    | Extract<
          MsgOf<typeof DataLoader>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_buy_success'
                  | 'on_swaps_io_swap_request_created'
          }
      >

type State =
    | {
          type: 'select_to_currency'
      }
    | {
          type: 'add_funds'
      }
    | { type: 'flow'; toCurrency: CryptoCurrency }

const calculateNextStep = ({
    portfolioMap,
    selectedCurrency,
    fromAddress,
    currencies,
}: {
    portfolioMap: PortfolioMap
    selectedCurrency: CryptoCurrency
    fromAddress: Web3.address.Address
    currencies: CryptoCurrency[]
}):
    | {
          type: 'add_funds'
      }
    | { type: 'flow'; toCurrency: CryptoCurrency } => {
    const currencyIds = new Set(currencies.map((c) => c.id))
    const portfolio = unsafe_GetPortfolioCache2({
        portfolioMap,
        address: fromAddress,
    })
    const portfolioTokens = portfolio?.tokens || []
    const filteredTokens = portfolioTokens.filter(
        (token) =>
            token.balance.currency.id !== selectedCurrency.id &&
            currencyIds.has(token.balance.currency.id)
    )

    const allEmpty =
        !filteredTokens.length ||
        filteredTokens.every((token) => token.balance.amount <= 0n)

    if (allEmpty) {
        return { type: 'add_funds' }
    }
    return { type: 'flow', toCurrency: selectedCurrency }
}

export const Fork = ({
    currencies,
    ratesMap,
    pricesMap,
    portfolioMap,
    accountsMap,
    fromAddress,
    customCurrencies,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    feePresetMap,
    currencyHiddenMap,
    currencyPinMap,
    cardConfig,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
    sessionPassword,
    isEthereumNetworkFeeWarningSeen,
    contractsMap,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'select_to_currency' })
    switch (state.type) {
        case 'select_to_currency':
            return (
                <SelectToCurrency
                    installationId={installationId}
                    pricesMap={pricesMap}
                    ratesMap={ratesMap}
                    currencies={currencies}
                    selectedCurrency={null}
                    networkMap={networkMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_to_currency_selected':
                                setState(
                                    calculateNextStep({
                                        portfolioMap,
                                        currencies,
                                        selectedCurrency: msg.currency,
                                        fromAddress,
                                    })
                                )
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'add_funds':
            return (
                <AddFunds
                    accountsMap={accountsMap}
                    address={fromAddress}
                    installationId={installationId}
                    networkMap={networkMap}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    networkRPCMap={networkRPCMap}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    feePresetMap={feePresetMap}
                    customCurrencies={customCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    cardConfig={cardConfig}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    sessionPassword={sessionPassword}
                    onMsg={onMsg}
                />
            )
        case 'flow':
            return (
                <DataLoader
                    toCurrency={state.toCurrency}
                    fromAddress={fromAddress}
                    currencies={currencies}
                    ratesMap={ratesMap}
                    pricesMap={pricesMap}
                    contractsMap={contractsMap}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolioMap={portfolioMap}
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({ type: 'select_to_currency' })
                                break
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_swaps_io_swap_request_created':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_buy_success':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
