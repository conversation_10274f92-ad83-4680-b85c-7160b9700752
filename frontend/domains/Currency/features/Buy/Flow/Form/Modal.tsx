import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Result } from '@zeal/toolkit/Result'

import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { PricesMap } from '@zeal/domains/Currency/api/fetchPriceChange'
import { SwapsIOQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import {
    SwapsIOQuoteRequest,
    SwapsIOQuoteValidationsErrors,
} from '@zeal/domains/Currency/domains/SwapsIO/api/fetchQuote'
import { RatesMap } from '@zeal/domains/FXRate'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'

import { SelectFromCurrency } from './SelectFromCurrency'

import { SelectToCurrency } from '../../SelectToCurrency'

type Props = {
    state: State
    pollable: PollableData<
        Result<SwapsIOQuoteValidationsErrors, SwapsIOQuote>,
        SwapsIOQuoteRequest
    >
    currencies: CryptoCurrency[]
    ratesMap: RatesMap
    pricesMap: PricesMap
    portfolio: ServerPortfolio2
    networkMap: NetworkMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    installationId: string
    onMsg: (msg: Msg) => void
}
export type State =
    | { type: 'closed' }
    | { type: 'from_currency' }
    | { type: 'to_currency' }

type Msg = MsgOf<typeof SelectToCurrency> | MsgOf<typeof SelectFromCurrency>

export const Modal = ({
    state,
    currencies,
    ratesMap,
    pricesMap,
    pollable,
    portfolio,
    networkMap,
    currencyHiddenMap,
    currencyPinMap,
    installationId,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'from_currency': {
            return (
                <UIModal>
                    <SelectFromCurrency
                        networkMap={networkMap}
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        onMsg={onMsg}
                        fromAccount={pollable.params.sender}
                        fromAccountPortfolio={portfolio}
                        fromCurrency={pollable.params.fromCurrency}
                        fromCurrencies={currencies}
                    />
                </UIModal>
            )
        }

        case 'to_currency':
            return (
                <UIModal>
                    <SelectToCurrency
                        installationId={installationId}
                        selectedCurrency={pollable.params.toCurrency}
                        networkMap={networkMap}
                        onMsg={onMsg}
                        currencies={currencies}
                        ratesMap={ratesMap}
                        pricesMap={pricesMap}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
