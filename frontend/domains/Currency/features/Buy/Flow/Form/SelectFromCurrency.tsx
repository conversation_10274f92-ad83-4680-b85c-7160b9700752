import { FormattedMessage } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { Account } from '@zeal/domains/Account'
import { AvatarWithoutBadge as AccountAvatar } from '@zeal/domains/Account/components/Avatar'
import { format as formatAddress } from '@zeal/domains/Address/helpers/format'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { SelectToken as SelectTokenLayout } from '@zeal/domains/Token/components/SelectToken'

type Props = {
    fromAccount: Account
    fromAccountPortfolio: ServerPortfolio2
    fromCurrency: CryptoCurrency | null
    fromCurrencies: CryptoCurrency[]

    networkMap: NetworkMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_from_currency_selected'; fromCurrency: CryptoCurrency }

export const SelectFromCurrency = ({
    fromAccount,
    fromCurrencies,
    currencyHiddenMap,
    currencyPinMap,
    networkMap,
    onMsg,
    fromCurrency,
    fromAccountPortfolio,
}: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            aria-labelledby="select-currency-label"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <UIActionBar
                top={
                    <Row spacing={8}>
                        <AccountAvatar size={24} account={fromAccount} />
                        <Text
                            variant="footnote"
                            color="textPrimary"
                            weight="regular"
                            ellipsis
                        >
                            {fromAccount.label}
                        </Text>

                        <Text
                            variant="footnote"
                            color="textSecondary"
                            weight="regular"
                        >
                            {formatAddress(fromAccount.address)}
                        </Text>
                    </Row>
                }
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="textPrimary"
                            >
                                <FormattedMessage
                                    id="buy.select-currency.title"
                                    defaultMessage="Select token"
                                />
                            </Text>
                        </Row>
                    </Clickable>
                }
            />

            <Column fill shrink spacing={16}>
                <SelectTokenLayout
                    cryptoCurrencies={fromCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    networkMap={networkMap}
                    serverPortfolio={fromAccountPortfolio}
                    selectedCurrency={fromCurrency}
                    onCryptoCurrencySelected={(fromCurrency) =>
                        onMsg({
                            type: 'on_from_currency_selected',
                            fromCurrency,
                        })
                    }
                />
            </Column>
        </Screen>
    )
}
