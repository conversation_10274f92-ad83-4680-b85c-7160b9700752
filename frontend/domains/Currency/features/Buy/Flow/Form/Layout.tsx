import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Divider } from '@zeal/uikit/Divider'
import { FancyButton } from '@zeal/uikit/FancyButton'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import {
    fromFixedWithFraction,
    unsafe_toNumberWithFraction,
} from '@zeal/toolkit/BigInt'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Result } from '@zeal/toolkit/Result'

import { AvatarWithoutBadge as AccountAvatarWithoutBadge } from '@zeal/domains/Account/components/Avatar'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { SwapsIOQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import {
    SwapsIOQuoteRequest,
    SwapsIOQuoteValidationsErrors,
} from '@zeal/domains/Currency/domains/SwapsIO/api/fetchQuote'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney, FiatMoney, Money2 } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'

import { Banner } from './Banner'
import { FormError, MaxBalanceLoadable, validate } from './validate'

type Props = {
    networkMap: NetworkMap
    pollable: PollableData<
        Result<SwapsIOQuoteValidationsErrors, SwapsIOQuote>,
        SwapsIOQuoteRequest
    >
    maxBalanceLoadable: MaxBalanceLoadable
    portfolio: ServerPortfolio2
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof MaxButton>
    | { type: 'close' }
    | { type: 'on_from_currency_clicked' }
    | { type: 'on_to_currency_clicked' }
    | { type: 'on_amount_change'; amount: string | null }
    | { type: 'on_form_submitted'; quote: SwapsIOQuote }

const getMaxBalance = ({
    maxBalanceLoadable,
    senderPortfolio,
}: {
    maxBalanceLoadable: MaxBalanceLoadable
    senderPortfolio: ServerPortfolio2
}) => {
    switch (maxBalanceLoadable.type) {
        case 'loading':
        case 'error':
            return getBalanceByCryptoCurrency2({
                currency:
                    maxBalanceLoadable.params.swapQuoteRequest.fromCurrency,
                serverPortfolio: senderPortfolio,
            })

        case 'loaded':
            return maxBalanceLoadable.data
        /* istanbul ignore next */
        default:
            return notReachable(maxBalanceLoadable)
    }
}

export const Layout = ({
    maxBalanceLoadable,
    pollable,
    portfolio,
    installationId,
    networkMap,
    onMsg,
}: Props) => {
    const { formatMessage, formatNumber } = useIntl()

    const validationResult = validate({
        pollable,
        maxBalanceLoadable,
        portfolio,
    })

    const errors = validationResult.getFailureReason() || {}

    const { fromCurrency, toCurrency } = pollable.params

    const fromToken = getTokenByCryptoCurrency3({
        serverPortfolio: portfolio,
        currency: fromCurrency,
    })

    const from: CryptoMoney = {
        amount: fromFixedWithFraction(
            pollable.params.amount,
            fromCurrency.fraction
        ),
        currency: fromCurrency,
    }

    const maxBalance = getMaxBalance({
        maxBalanceLoadable,
        senderPortfolio: portfolio,
    })

    const isMaxAmount = from.amount === maxBalance.amount

    const fromAmountInDefaultCurrency: FiatMoney | null =
        fromToken.rate && pollable.params.amount
            ? applyRate2({
                  baseAmount: {
                      amount: fromFixedWithFraction(
                          pollable.params.amount,
                          fromCurrency.fraction
                      ),
                      currency: fromCurrency,
                  },
                  rate: fromToken.rate,
              })
            : null

    const quote = (() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
                return pollable.data.getSuccessResult() || null

            case 'subsequent_failed':
            case 'loading':
            case 'error':
                return null
            /* istanbul ignore next */
            default:
                return notReachable(pollable)
        }
    })()

    const toToken = portfolio.tokens.find(
        (token) => token.balance.currency.id === pollable.params.toCurrency.id
    )

    const toTokenBalance: Money2 | null = toToken?.balance
        ? {
              amount: toToken.balance.amount,
              currency: pollable.params.toCurrency,
          }
        : null

    const formatedAmount = formatNumber(
        unsafe_toNumberWithFraction(from.amount, fromCurrency.fraction),
        {
            maximumFractionDigits: 6,
        }
    )

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} fill>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <ActionBar.Header>
                                    <FormattedMessage
                                        id="buy_form.title"
                                        defaultMessage="Buy token"
                                    />
                                </ActionBar.Header>
                            </Row>
                        </Clickable>
                    }
                />

                <Column spacing={0} fill alignY="stretch">
                    <Column spacing={4} fill>
                        <AmountInput
                            top={
                                <Column spacing={0}>
                                    <FancyButton
                                        color="secondary"
                                        rounded
                                        onClick={noop}
                                        right={null}
                                        left={() => (
                                            <Row grow shrink spacing={4}>
                                                <AccountAvatarWithoutBadge
                                                    size={12}
                                                    account={
                                                        pollable.params.sender
                                                    }
                                                />
                                                <Text
                                                    variant="caption1"
                                                    color="gray40"
                                                    weight="medium"
                                                    ellipsis
                                                >
                                                    {
                                                        pollable.params.sender
                                                            .label
                                                    }
                                                </Text>
                                            </Row>
                                        )}
                                    />
                                    <Divider variant="secondary" />
                                </Column>
                            }
                            content={{
                                topLeft: (
                                    <IconButton
                                        variant="on_light"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_from_currency_clicked',
                                            })
                                        }
                                    >
                                        {({ color }) => (
                                            <Row spacing={12}>
                                                <CurrencyAvatar
                                                    key={fromCurrency.id}
                                                    rightBadge={({ size }) => (
                                                        <Badge
                                                            size={size}
                                                            network={findNetworkByHexChainId(
                                                                fromCurrency.networkHexChainId,
                                                                networkMap
                                                            )}
                                                        />
                                                    )}
                                                    currency={fromCurrency}
                                                    size={32}
                                                />
                                                <Row spacing={4}>
                                                    <Text
                                                        variant="title3"
                                                        color="textPrimary"
                                                        weight="medium"
                                                        ellipsis
                                                    >
                                                        {fromCurrency.code}
                                                    </Text>

                                                    <LightArrowDown2
                                                        size={18}
                                                        color={color}
                                                    />
                                                </Row>
                                            </Row>
                                        )}
                                    </IconButton>
                                ),
                                topRight: ({ onBlur, onFocus, isFocused }) => (
                                    <AmountInput.Input
                                        onBlur={onBlur}
                                        onFocus={onFocus}
                                        onChange={(value) =>
                                            onMsg({
                                                type: 'on_amount_change',
                                                amount: value,
                                            })
                                        }
                                        label={formatMessage({
                                            id: 'amount',
                                            defaultMessage: 'amount',
                                        })}
                                        prefix=""
                                        fraction={fromCurrency.fraction}
                                        autoFocus
                                        amount={
                                            !isFocused && isMaxAmount
                                                ? formatedAmount
                                                : pollable.params.amount
                                        }
                                        onSubmitEditing={noop}
                                    />
                                ),
                                bottomRight: fromAmountInDefaultCurrency && (
                                    <Text
                                        variant="footnote"
                                        color="textSecondary"
                                        weight="regular"
                                    >
                                        <FormattedMoneyPrecise
                                            withSymbol
                                            sign={null}
                                            money={fromAmountInDefaultCurrency}
                                        />
                                    </Text>
                                ),
                                bottomLeft: (
                                    <MaxButton
                                        installationId={installationId}
                                        location="buy"
                                        balance={maxBalance}
                                        onMsg={onMsg}
                                        state={(() => {
                                            switch (maxBalanceLoadable.type) {
                                                case 'loading':
                                                    return 'loading'
                                                case 'error':
                                                case 'loaded':
                                                    return errors.fromToken
                                                        ? 'error'
                                                        : 'normal'
                                                default:
                                                    return notReachable(
                                                        maxBalanceLoadable
                                                    )
                                            }
                                        })()}
                                    />
                                ),
                            }}
                            state={errors.fromToken ? 'error' : 'normal'}
                        />

                        <NextStepSeparator />

                        <AmountInput
                            content={{
                                topRight: () => (
                                    <Row spacing={0}>
                                        <Spacer />
                                        <Text
                                            variant="title3"
                                            weight="medium"
                                            color="textPrimary"
                                        >
                                            <FormattedMoneyPrecise
                                                withSymbol={false}
                                                sign={null}
                                                money={
                                                    quote?.to || {
                                                        amount: 0n,
                                                        currency:
                                                            pollable.params
                                                                .toCurrency,
                                                    }
                                                }
                                            />
                                        </Text>
                                    </Row>
                                ),
                                topLeft: (
                                    <IconButton
                                        variant="on_light"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_to_currency_clicked',
                                            })
                                        }
                                    >
                                        {({ color }) => (
                                            <Row spacing={12}>
                                                <CurrencyAvatar
                                                    key={toCurrency.id}
                                                    rightBadge={({ size }) => (
                                                        <Badge
                                                            size={size}
                                                            network={findNetworkByHexChainId(
                                                                toCurrency.networkHexChainId,
                                                                networkMap
                                                            )}
                                                        />
                                                    )}
                                                    currency={toCurrency}
                                                    size={32}
                                                />
                                                <Row spacing={4}>
                                                    <Text
                                                        ellipsis
                                                        variant="title3"
                                                        color="textPrimary"
                                                        weight="medium"
                                                    >
                                                        {toCurrency.name}
                                                    </Text>

                                                    <LightArrowDown2
                                                        size={18}
                                                        color={color}
                                                    />
                                                </Row>
                                            </Row>
                                        )}
                                    </IconButton>
                                ),
                                bottomRight: quote &&
                                    quote.toInDefaultCurrency && (
                                        <Text
                                            variant="footnote"
                                            color="textSecondary"
                                            weight="regular"
                                        >
                                            <FormattedMoneyPrecise
                                                withSymbol
                                                sign={null}
                                                money={
                                                    quote.toInDefaultCurrency
                                                }
                                            />
                                        </Text>
                                    ),
                                bottomLeft: (
                                    <Text
                                        color="textSecondary"
                                        variant="paragraph"
                                        weight="regular"
                                    >
                                        <FormattedMessage
                                            id="currency.swap.max_label"
                                            defaultMessage="Balance: {amount}"
                                            values={{
                                                amount: (
                                                    <FormattedMoneyPrecise
                                                        withSymbol={false}
                                                        sign={null}
                                                        money={
                                                            toTokenBalance || {
                                                                amount: 0n,
                                                                currency:
                                                                    pollable
                                                                        .params
                                                                        .toCurrency,
                                                            }
                                                        }
                                                    />
                                                ),
                                            }}
                                        />
                                    </Text>
                                ),
                            }}
                            state="normal"
                        />
                    </Column>

                    <Column spacing={16}>
                        <Banner pollable={pollable} error={errors.banner} />
                        <Actions variant="default">
                            <Button
                                variant="primary"
                                size="regular"
                                disabled={!!errors.submit}
                                onClick={() => {
                                    validationResult.tap((quote) => {
                                        onMsg({
                                            type: 'on_form_submitted',
                                            quote,
                                        })
                                    })
                                }}
                            >
                                <CTAMessage error={errors.submit} />
                            </Button>
                        </Actions>
                    </Column>
                </Column>
            </Column>
        </Screen>
    )
}

const CTAMessage = ({ error }: { error?: FormError['submit'] }) => {
    if (!error) {
        return <FormattedMessage id="buy" defaultMessage="Buy" />
    }

    switch (error.type) {
        case 'pollable_reloading':
        case 'max_balance_still_loading':
            return (
                <FormattedMessage
                    id="buy.loading"
                    defaultMessage="Loading..."
                />
            )
        case 'pollable_subsequent_failed':
            return <FormattedMessage id="buy" defaultMessage="Buy" />
        case 'amount_required':
            return (
                <FormattedMessage
                    id="buy.enter_amount"
                    defaultMessage="Enter Amount"
                />
            )
        case 'not_enough_balance':
            return (
                <FormattedMessage
                    id="buy.not_enough_balance"
                    defaultMessage="Not enough balance"
                />
            )
        case 'selected_route_is_no_more_available':
        case 'no_routes_found':
            return (
                <FormattedMessage
                    id="buy.no_routes_found"
                    defaultMessage="No routes found"
                />
            )

        case 'above_maximum_limit':
            return (
                <FormattedMessage
                    id="submit.error.amount_high"
                    defaultMessage="Amount too high"
                />
            )

        case 'below_minimum_limit':
            return (
                <FormattedMessage
                    id="submit.error.amount_low"
                    defaultMessage="Amount too low"
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}
