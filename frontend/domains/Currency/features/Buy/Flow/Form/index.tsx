import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { PricesMap } from '@zeal/domains/Currency/api/fetchPriceChange'
import { fetchMaxBalance } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchMaxBalance'
import {
    fetchQuote,
    SwapsIOQuoteRequest,
} from '@zeal/domains/Currency/domains/SwapsIO/api/fetchQuote'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { RatesMap } from '@zeal/domains/FXRate'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    fromAccountPortfolio: ServerPortfolio2
    initialRequest: SwapsIOQuoteRequest
    keyStoreMap: KeyStoreMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencies: CryptoCurrency[]
    ratesMap: RatesMap
    networkMap: NetworkMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    pricesMap: PricesMap
    onMsg: (msg: Msg) => void
}

type Msg = Extract<
    MsgOf<typeof Layout>,
    { type: 'close' | 'on_form_submitted' }
>

const QUOTE_REFRESH_INTERVAL_MS = 60000

export const Form = ({
    onMsg,
    fromAccountPortfolio,
    initialRequest,
    installationId,
    keyStoreMap,
    gasCurrencyPresetMap,
    currencies,
    pricesMap,
    ratesMap,
    currencyHiddenMap,
    currencyPinMap,
    networkMap,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    const [pollable, setPollable] = usePollableData(
        fetchQuote,
        {
            type: 'loading',
            params: initialRequest,
        },
        {
            pollIntervalMilliseconds: QUOTE_REFRESH_INTERVAL_MS,
        }
    )
    const swapsIOQuoteRequest = pollable.params
    const fromCurrencyBalance = getTokenByCryptoCurrency3({
        currency: swapsIOQuoteRequest.fromCurrency,
        serverPortfolio: fromAccountPortfolio,
    })
    const [maxBalanceLoadable, setMaxBalanceLoadable] = useLoadableData(
        fetchMaxBalance,
        {
            type: 'loading',
            params: {
                swapQuoteRequest: {
                    ...swapsIOQuoteRequest,
                    amount: toFixedWithFraction(
                        fromCurrencyBalance.balance.amount,
                        fromCurrencyBalance.balance.currency.fraction
                    ),
                },
                serverPortfolio: fromAccountPortfolio,
                gasCurrencyPresetMap,
                installationId,
                keyStore: getKeyStore({
                    address: pollable.params.sender.address,
                    keyStoreMap: keyStoreMap,
                }),
            },
        }
    )

    useEffect(() => {
        const fromBalance = getTokenByCryptoCurrency3({
            currency: swapsIOQuoteRequest.fromCurrency,
            serverPortfolio: fromAccountPortfolio,
        })

        setMaxBalanceLoadable((old) => ({
            type: 'loading',
            params: {
                ...old.params,
                swapQuoteRequest: {
                    ...old.params.swapQuoteRequest,
                    fromCurrency: swapsIOQuoteRequest.fromCurrency,
                    amount: toFixedWithFraction(
                        fromBalance.balance.amount,
                        fromBalance.balance.currency.fraction
                    ),
                },
                serverPortfolio: fromAccountPortfolio,
            },
        }))
    }, [
        swapsIOQuoteRequest.fromCurrency,
        setMaxBalanceLoadable,
        fromAccountPortfolio,
    ])

    useEffect(() => {
        switch (maxBalanceLoadable.type) {
            case 'error':
                captureError(maxBalanceLoadable.error, {
                    extra: { context: 'maxButton balance correction on swap' },
                })
                break

            case 'loaded':
            case 'loading':
                break

            /* istanbul ignore next */
            default:
                notReachable(maxBalanceLoadable)
        }
    }, [maxBalanceLoadable])

    return (
        <>
            <Layout
                installationId={installationId}
                pollable={pollable}
                maxBalanceLoadable={maxBalanceLoadable}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_form_submitted':
                            onMsg(msg)
                            break
                        case 'on_amount_change':
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    amount: msg.amount,
                                },
                            })
                            break
                        case 'on_from_currency_clicked':
                            setModal({
                                type: 'from_currency',
                            })
                            break
                        case 'on_to_currency_clicked':
                            setModal({
                                type: 'to_currency',
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
                networkMap={networkMap}
                portfolio={fromAccountPortfolio}
            />
            <Modal
                installationId={installationId}
                networkMap={networkMap}
                pollable={pollable}
                currencies={currencies}
                ratesMap={ratesMap}
                pricesMap={pricesMap}
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_to_currency_selected':
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    toCurrency: msg.currency,
                                },
                            })
                            setModal({ type: 'closed' })
                            break

                        case 'on_from_currency_selected':
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    fromCurrency: msg.fromCurrency,
                                },
                            })
                            setModal({ type: 'closed' })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
                portfolio={fromAccountPortfolio}
                currencyHiddenMap={currencyHiddenMap}
                currencyPinMap={currencyPinMap}
            />
        </>
    )
}
