import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { failure, Result, shape, success } from '@zeal/toolkit/Result'

import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { SwapsIOQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import {
    SwapsIOQuoteRequest,
    SwapsIOQuoteValidationsErrors,
} from '@zeal/domains/Currency/domains/SwapsIO/api/fetchQuote'
import { KeyStore } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

export type Pollable = PollableData<
    Result<SwapsIOQuoteValidationsErrors, SwapsIOQuote>,
    SwapsIOQuoteRequest
>

export type BannerError =
    | AmountRequired
    | LimitsError
    | NoRoutesFoundError
    | PollableReloading
    | PollableSubsequentFailed

export type MaxBalanceLoadable = LoadableData<
    CryptoMoney,
    {
        swapQuoteRequest: SwapsIOQuoteRequest
        serverPortfolio: ServerPortfolio2
        keyStore: KeyStore
        installationId: string
        gasCurrencyPresetMap: GasCurrencyPresetMap
    }
>

type NoRoutesFoundError = { type: 'no_routes_found' }
type SelectedRouteIsNoMoreAvailable = {
    type: 'selected_route_is_no_more_available'
}

type PollableReloading = { type: 'pollable_reloading' }
type PollableSubsequentFailed = { type: 'pollable_subsequent_failed' }

type NotEnoughBalanceError = { type: 'not_enough_balance' }
type MaxBalanceStillLoading = { type: 'max_balance_still_loading' }
type AmountRequired = { type: 'amount_required' }

export type FromTokenError =
    | NotEnoughBalanceError
    | AmountRequired
    | MaxBalanceStillLoading

type LimitsError =
    | {
          type: 'above_maximum_limit'
          limit: CryptoMoney | null
      }
    | { type: 'below_minimum_limit'; limit: CryptoMoney | null }

type SubmitError =
    | PollableReloading
    | PollableSubsequentFailed
    | NoRoutesFoundError
    | FromTokenError
    | SelectedRouteIsNoMoreAvailable
    | LimitsError

export type FormError = {
    fromToken?: FromTokenError
    banner?: BannerError
    submit?: SubmitError
}

export const getMaxBalance = ({
    maxBalanceLoadable,
    portfolio,
}: {
    maxBalanceLoadable: MaxBalanceLoadable
    portfolio: ServerPortfolio2
}) => {
    switch (maxBalanceLoadable.type) {
        case 'loading':
        case 'error':
            return getBalanceByCryptoCurrency2({
                currency:
                    maxBalanceLoadable.params.swapQuoteRequest.fromCurrency,
                serverPortfolio: portfolio,
            })

        case 'loaded':
            return maxBalanceLoadable.data
        /* istanbul ignore next */
        default:
            return notReachable(maxBalanceLoadable)
    }
}

export const validate = ({
    pollable,
    portfolio,
    maxBalanceLoadable,
}: {
    pollable: Pollable
    portfolio: ServerPortfolio2
    maxBalanceLoadable: MaxBalanceLoadable
}): Result<FormError, SwapsIOQuote> =>
    shape({
        fromToken: validateBalance({ maxBalanceLoadable, portfolio, pollable }),
        banner: validateBridgeQuote({ pollable }).andThen(validateErrors),
        submit: validateFromAmountRequired({
            pollable,
        })
            .andThen(() =>
                validateBalance({ maxBalanceLoadable, portfolio, pollable })
            )
            .andThen(() => validateMaxBalanceLoadable({ maxBalanceLoadable }))
            .andThen(() => validateBridgeQuote({ pollable }))
            .andThen(validateErrors),
    }).map(({ submit }) => submit)

const validateMaxBalanceLoadable = ({
    maxBalanceLoadable,
}: {
    maxBalanceLoadable: MaxBalanceLoadable
}): Result<MaxBalanceStillLoading, unknown> => {
    switch (maxBalanceLoadable.type) {
        case 'loading':
            return failure({ type: 'max_balance_still_loading' })
        case 'error':
        case 'loaded':
            return success(undefined)

        /* istanbul ignore next */
        default:
            return notReachable(maxBalanceLoadable)
    }
}

const validateFromAmountRequired = ({
    pollable,
}: {
    pollable: Pollable
}): Result<AmountRequired, unknown> => {
    const currency = pollable.params.fromCurrency
    const amount = fromFixedWithFraction(
        pollable.params.amount,
        currency.fraction
    )

    if (amount === 0n) {
        return failure({ type: 'amount_required' })
    }

    return success(undefined)
}

const validateBalance = ({
    maxBalanceLoadable,
    pollable,
    portfolio,
}: {
    portfolio: ServerPortfolio2
    pollable: Pollable
    maxBalanceLoadable: MaxBalanceLoadable
}): Result<NotEnoughBalanceError, unknown> => {
    const maxBalance = getMaxBalance({ maxBalanceLoadable, portfolio })

    const currency = pollable.params.fromCurrency
    const amount = fromFixedWithFraction(
        pollable.params.amount,
        currency.fraction
    )

    if (maxBalance.amount < amount) {
        return failure({ type: 'not_enough_balance' })
    }

    return success(undefined)
}

const validateErrors = (
    data: Result<SwapsIOQuoteValidationsErrors, SwapsIOQuote>
): Result<LimitsError | NoRoutesFoundError, SwapsIOQuote> => {
    return data.mapError((errors) => {
        switch (errors.type) {
            case 'above_maximum':
                return {
                    type: 'above_maximum_limit',
                    limit: errors.max,
                }
            case 'below_minimum':
                return {
                    type: 'below_minimum_limit',
                    limit: errors.min,
                }
            case 'currency_not_supported':
            case 'amount_required':
                return { type: 'no_routes_found' }
            default:
                return notReachable(errors)
        }
    })
}

const validateBridgeQuote = ({
    pollable,
}: {
    pollable: Pollable
}): Result<
    PollableReloading | PollableSubsequentFailed | NoRoutesFoundError,
    Result<SwapsIOQuoteValidationsErrors, SwapsIOQuote>
> => {
    switch (pollable.type) {
        case 'loaded': {
            if (!pollable.data) {
                return failure({ type: 'no_routes_found' })
            }

            return success(pollable.data)
        }

        case 'loading':
        case 'reloading':
            return failure({ type: 'pollable_reloading' })

        case 'error':
        case 'subsequent_failed':
            return failure({ type: 'pollable_subsequent_failed' })

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}
