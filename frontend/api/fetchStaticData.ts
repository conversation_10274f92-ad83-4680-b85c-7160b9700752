import memoize from 'lodash/memoize'

import { notReachable } from '@zeal/toolkit'
import { Result } from '@zeal/toolkit/Result'

type Path =
    | '@zeal/assets/data/currencies2.json'
    | '@zeal/assets/data/matrix2.json'
    | '@zeal/assets/data/coingeck-verified-currency-ids.json'

const getData = async (path: Path): Promise<unknown> => {
    switch (path) {
        case '@zeal/assets/data/currencies2.json':
            return (await import('@zeal/assets/data/currencies2.json' as any))
                .default
        case '@zeal/assets/data/matrix2.json':
            return (await import('@zeal/assets/data/matrix2.json' as any))
                .default
        case '@zeal/assets/data/coingeck-verified-currency-ids.json':
            return (
                await import(
                    '@zeal/assets/data/coingeck-verified-currency-ids.json' as any
                )
            ).default

        default:
            return notReachable(path)
    }
}

export const fetchStaticData = memoize(
    async <T>(
        path: Path,
        parser: (input: unknown) => Result<unknown, T>
    ): Promise<T> => {
        const data = parser(await getData(path)).getSuccessResultOrThrow(
            `failed to parse static asset ${path}`
        )
        return data
    }
)
