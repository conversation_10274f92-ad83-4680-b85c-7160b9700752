export type paths = {
    [key: `/wallet/portfolio/${string}/`]: {
        get: {
            parameters: {
                path: { address: string }
                query: { forceRefresh?: boolean }
            }
            responses: {
                200: { content: { 'application/json': unknown } }
                500: unknown
            }
        }
    }
    [key: `/wallet/transaction/activity/${string}/`]: {
        get: {
            parameters: {
                path: { address: string }
                query: {
                    network?: string
                    scam?: boolean
                    timestampLessThan?: number
                }
            }
            responses: {
                200: { content: { 'application/json': unknown } }
                500: unknown
            }
        }
    }
    [key: `/wallet/transaction/${string}/last-card-topup/`]: {
        get: {
            parameters: { path: { address: string } }
            responses: {
                200: { content: { 'application/json': unknown } }
            }
        }
    }

    '/wallet/rpc/paymaster/': {
        post: {
            parameters: { query: { network: string } }
            responses: {
                200: { content: { 'application/json': unknown } }
                500: unknown
            }
            requestBody: { content: { 'application/json': unknown } }
        }
    }
    '/wallet/rate/crypto': {
        post: {
            parameters: { query: { network: string } }
            responses: {
                200: { content: { 'application/json': unknown } }
                404: { content: { 'application/json': unknown } }
                500: unknown
            }
            requestBody: { content: { 'application/json': unknown } }
        }
    }
    '/wallet/transaction/simulate/': {
        post: {
            parameters: { query: { network: string } }
            responses: {
                200: { content: { 'application/json': unknown } }
                500: unknown
            }
            requestBody: { content: { 'application/json': unknown } }
        }
    }
    '/wallet/user-ops-transaction/simulate': {
        post: {
            parameters: { query: { network: string } }
            responses: {
                200: { content: { 'application/json': unknown } }
                500: unknown
            }
            requestBody: { content: { 'application/json': unknown } }
        }
    }
    '/wallet/rpc-sign-message/simulate/': {
        post: {
            parameters: { query: { network: string } }
            responses: {
                200: {
                    content: {
                        'application/json': {
                            currencies: unknown
                            checks: SignMessageSafetyCheck[]
                            message: unknown
                        }
                    }
                }
            }
            requestBody: { content: { 'application/json': unknown } }
        }
    }
    [key: `/wallet/transaction/${string}/result`]: {
        get: {
            parameters: { query: { network: string }; path: { hash: string } }
            responses: {
                200: { content: { 'application/json': unknown } }
                422: { content: { 'application/json': unknown } }
                500: unknown
            }
        }
    }
    '/wallet/user-ops-transaction/result': {
        post: {
            responses: {
                200: { content: { 'application/json': unknown } }
                422: { content: { 'application/json': unknown } }
                500: unknown
            }
            requestBody: { content: { 'application/json': unknown } }
        }
    }
    '/wallet/user-ops-transaction/fee': {
        post: {
            responses: {
                200: {
                    content: {
                        'application/json': {
                            feeInDefaultCurrency: unknown
                            feeInTokenCurrency: {
                                amount: string
                                currencyId: string
                            }
                            currencies: {
                                [key: string]: Record<string, unknown>
                            }
                        }
                    }
                }
                500: unknown
            }
            requestBody: { content: { 'application/json': unknown } }
        }
    }
    '/wallet/safetychecks/connection/': {
        post: {
            responses: {
                200: {
                    content: {
                        'application/json': {
                            dAppInfo: {
                                hostname: string
                                avatar: string | null
                                title: string | null
                            }
                            checks: any[]
                        }
                    }
                }
                500: unknown
            }
            requestBody: { content: { 'application/json': unknown } }
        }
    }
    '/wallet/metrics': {
        post: {
            responses: {
                200: unknown
                500: unknown
            }
            requestBody: { content: { 'application/json': any } }
        }
    }
}

type SafetyCheckSource = {
    source:
        | 'BlockAid'
        | 'Tenderly'
        | 'Alchemy'
        | 'DappRadar'
        | 'DefiLlama'
        | 'Zeal'
        | 'Rarible'
        | 'CoinGecko'
    url: string | null
}

type SignMessageSafetyCheck =
    | TokenVerificationCheckFailed
    | TokenVerificationCheckPassed
    | SmartContractedBlacklistCheckFailed
    | SmartContractBlacklistCheckPassed
    | ApprovalSpenderTypeCheckPassed
    | ApprovalSpenderTypeCheckFailed
    | ApprovalExpirationLimitCheckPassed
    | ApprovalExpirationLimitCheckFailed

type ApprovalExpirationLimitCheckPassed = {
    type: 'ApprovalExpirationLimitCheck'
    severity: 'Caution' | 'Danger'
    state: 'Passed'
}

type ApprovalExpirationLimitCheckFailed = {
    type: 'ApprovalExpirationLimitCheck'
    severity: 'Caution' | 'Danger'
    state: 'Failed'
}

type TokenVerificationCheckPassed = {
    type: 'TokenVerificationCheck'
    severity: 'Caution' | 'Danger'
    state: 'Passed'
    currencyId: string
    checkSource: SafetyCheckSource
}

type TokenVerificationCheckFailed = {
    type: 'TokenVerificationCheck'
    severity: 'Caution' | 'Danger'
    state: 'Failed'
    currencyId: string
    checkSource: SafetyCheckSource
}

type SmartContractBlacklistCheckPassed = {
    type: 'SmartContractBlacklistCheck'
    severity: 'Caution' | 'Danger'
    state: 'Passed'
    checkSource: SafetyCheckSource
}

type SmartContractedBlacklistCheckFailed = {
    type: 'SmartContractBlacklistCheck'
    severity: 'Caution' | 'Danger'
    state: 'Failed'
    checkSource: SafetyCheckSource
}

type ApprovalSpenderTypeCheckPassed = {
    type: 'ApprovalSpenderTypeCheck'
    severity: 'Caution' | 'Danger'
    state: 'Passed'
}

type ApprovalSpenderTypeCheckFailed = {
    type: 'ApprovalSpenderTypeCheck'
    severity: 'Caution' | 'Danger'
    state: 'Failed'
}
