import { ImperativeError } from '@zeal/toolkit/Error'
import { joinURL } from '@zeal/toolkit/URL/joinURL'

import { ResidentialAddress } from '@zeal/domains/Card'

import { Auth, getAuthHeaders } from './Auth'
import { processFetchFailure, processFetchResponse } from './interceptors'

export const GNOSIS_PAY_BASE_URL = 'https://api.gnosispay.com/api/v1'

export type Paths = {
    get: {
        '/user': { query: undefined }
        '/order': { query: undefined }
        '/safe-config': { query: undefined }
        '/user/terms': { query: undefined }
        '/auth/nonce': { query: undefined }
        '/transactions': { query: { after?: string } }
        '/account-balances': { query: undefined }
        '/external/ibans/available': { query: undefined }
        '/eoa-accounts': { query: undefined }
        '/kyc/integration/sdk': { query: undefined }
        '/account/signature-payload': { query: undefined }
        '/source-of-funds': { query: undefined }
        '/cards': { query: undefined }
    } & Record<
        `/cards/${string}/details`,
        { query: { encryptedKey: string } }
    > &
        Record<`/cards/${string}/pin`, { query: { encryptedKey: string } }> &
        Record<`/cards/${string}/status`, { query: undefined }>

    patch: {
        '/account/deploy-safe-modules': {
            query: undefined
            body: { signature: string }
        }
    }
    put: Record<
        `/order/${string}/confirm-payment`,
        { query: undefined; body: undefined }
    >

    post: {
        '/verification': {
            query: undefined
            body: {
                phoneNumber: string
            }
        }
        '/account': {
            query: undefined
            body: {
                chainId: string
            }
        }
        '/verification/check': {
            query: undefined
            body: {
                code: string
            }
        }
        '/safe/set-currency': {
            query: undefined
            body: undefined
        }
        '/eoa-accounts': {
            query: undefined
            body: {
                address: string
            }
        }
        '/auth/signup/otp': {
            query: undefined
            body: { email: string }
        }
        '/auth/challenge': {
            query: undefined
            body: { message: string; signature: string }
        }
        '/auth/signup': {
            query: undefined
            body: {
                authEmail: string
                otp: string
                referralCouponCode: string
                marketingCampaign: string
                partnerId: string
            }
        }
        '/delay-relay': {
            query: undefined
            body: {
                chainId: number
                target: string
                signedData: string
                safeAddress: string
                operationType: 'CALL' | 'DELEGATECALL'
                transactionData: {
                    data: string
                    value: string
                    to: string
                }
            }
        }
        '/order/create': {
            query: undefined
            body:
                | { virtual: true; personalizationSource: 'KYC' }
                | {
                      personalizationSource: 'KYC'
                      virtual: false
                      shippingAddress: ResidentialAddress
                  }
        }
        '/user/terms': {
            query: undefined
            body: { terms: string; version: string }
        }
        '/external/ibans': {
            query: undefined
            body: { address: string; signature: string }
        }
        '/external/ibans/transfer': {
            query: undefined
            body: { address: string; signature: string }
        }
        '/source-of-funds': {
            query: undefined
            body: { question: string; answer: string }[]
        }
    } & Record<
        `/order/${string}/card`,
        {
            query: undefined
            body: {
                options: {
                    setPin: boolean
                }
            }
        }
    > &
        Record<
            `/order/${string}/attach-coupon`,
            {
                query: undefined
                body: {
                    couponCode: string
                }
            }
        > &
        Record<
            `/cards/${string}/freeze`,
            { query: undefined; body: undefined }
        > &
        Record<
            `/cards/${string}/unfreeze`,
            { query: undefined; body: undefined }
        > &
        Record<
            `/cards/${string}/activate`,
            { query: undefined; body: undefined }
        >

    delete: Record<
        `/eoa-accounts/${string}`,
        {
            query: undefined
        }
    >
}

export const get = <T extends keyof Paths['get']>(
    path: T,
    params: { query?: Paths['get'][T]['query']; auth?: Auth }, // TODO @resetko-zeal fix query is not mandatory even if its there in Paths
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(GNOSIS_PAY_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`
    const requestStackError = new ImperativeError('Track request stack')

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'GET',
        headers: {
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error) => processFetchFailure({ error }))
        .then((response) =>
            processFetchResponse({
                params,
                method: 'GET',
                response,
                url,
                requestStackError,
            })
        )
        .then((response) => response.text())
}

export const post = <T extends keyof Paths['post']>(
    path: T,
    params: {
        query?: Paths['post'][T]['query']
        auth?: Auth
        body: Paths['post'][T]['body']
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(GNOSIS_PAY_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'POST',
        body: JSON.stringify(params.body),
        headers: {
            'Content-Type': 'application/json',
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error) => processFetchFailure({ error }))
        .then((response) =>
            processFetchResponse({ params, method: 'POST', response, url })
        )
        .then((response) => response.text())
}

export const put = <T extends keyof Paths['put']>(
    path: T,
    params: {
        query?: Paths['put'][T]['query']
        auth?: Auth
        body: Paths['put'][T]['body']
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(GNOSIS_PAY_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'PUT',
        body: JSON.stringify(params.body),
        headers: {
            'Content-Type': 'application/json',
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error) => processFetchFailure({ error }))
        .then((response) =>
            processFetchResponse({ params, method: 'PUT', response, url })
        )
        .then((response) => response.text())
}

export const patch = <T extends keyof Paths['patch']>(
    path: T,
    params: {
        query?: Paths['patch'][T]['query']
        auth?: Auth
        body: Paths['patch'][T]['body']
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(GNOSIS_PAY_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'PATCH',
        body: JSON.stringify(params.body),
        headers: {
            'Content-Type': 'application/json',
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error) => processFetchFailure({ error }))
        .then((response) =>
            processFetchResponse({ params, method: 'PATCH', response, url })
        )
        .then((response) => response.text())
}

export const del = <T extends keyof Paths['delete']>(
    path: T,
    params: {
        query?: Paths['delete'][T]['query']
        auth?: Auth
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(GNOSIS_PAY_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error) => processFetchFailure({ error }))
        .then((response) =>
            processFetchResponse({ params, method: 'DELETE', response, url })
        )
        .then((response) => response.text())
}
