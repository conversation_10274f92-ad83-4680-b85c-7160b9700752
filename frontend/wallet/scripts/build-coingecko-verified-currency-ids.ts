import { get } from '@zeal/api/requestBackend'
import * as fs from 'fs'

import { values } from '@zeal/toolkit/Object'
import {
    array,
    arrayOf,
    arrayOfLength,
    combine,
    failure,
    groupByType,
    object,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { currencyId } from '@zeal/domains/Currency'
import { NetworkHexId } from '@zeal/domains/Network'
import {
    COIN_GECKO_NETWORK_MAP,
    PREDEFINED_NETWORKS,
} from '@zeal/domains/Network/constants'

const main = async () => {
    const response = await get(
        '/proxy/cgv3/coins/list',
        { query: { include_platform: true } },
        undefined
    )

    const knownPlatforms = new Set(values(COIN_GECKO_NETWORK_MAP))

    const currencies = array(response)
        .andThen((arr) =>
            combine(
                arr.map((item) =>
                    object(item).andThen((obj) =>
                        object(obj.platforms)
                            .map((platformsObj) => Object.entries(platformsObj))
                            .andThen((arr) =>
                                combine(
                                    arr.map((item) =>
                                        arrayOf(item, string).andThen(
                                            (strArr) => arrayOfLength(2, strArr)
                                        )
                                    )
                                )
                            )
                    )
                )
            ).map((arr) => arr.flat())
        )
        .getSuccessResultOrThrow(
            'Failed to parse coingecko verified currencies'
        )

    const parseNetworkHexId = (input: string): Result<unknown, NetworkHexId> =>
        string(input).andThen((network) => {
            const networkEntry = (
                Object.entries(COIN_GECKO_NETWORK_MAP) as [
                    keyof typeof COIN_GECKO_NETWORK_MAP,
                    string,
                ][]
            ).find(([_, coingeckoNetwork]) => coingeckoNetwork === network)

            const predefinedNetwork = networkEntry
                ? PREDEFINED_NETWORKS.find(
                      (network) => network.name === networkEntry[0]
                  )
                : null

            return predefinedNetwork
                ? success(predefinedNetwork.hexChainId)
                : failure(`Unknown network ${network}`)
        })

    const [err, results] = groupByType(
        currencies
            .filter(([network]) => knownPlatforms.has(network))
            .filter(([, address]) => address !== '')
            .map(([network, address]) =>
                shape({
                    network: parseNetworkHexId(network),
                    address: Web3.address.parse(address),
                })
            )
    )

    if (err.length > 0) {
        console.log('Error parsing supported networks', err) // eslint-disable-line no-console
        process.exit(-1)
    }

    const coinGeckoCurrencyIds = results.map(currencyId).sort() // TODO @resetko-zeal why TS not seeing .toSorted?

    const currencyIds: string[] = [
        ...coinGeckoCurrencyIds,
        ...PREDEFINED_NETWORKS.map((network) => network.nativeCurrency.id),
    ]

    fs.writeFileSync(
        '../mobile/assets/data/coingeck-verified-currency-ids.json',
        JSON.stringify(currencyIds)
    )

    const stat = fs.statSync(
        '../mobile/assets/data/coingeck-verified-currency-ids.json'
    )

    // eslint-disable-next-line no-console
    console.log(
        `generated ${currencyIds.length} currencies. ${(stat.size / 1024).toFixed(2)} KiB`
    )
}

// eslint-disable-next-line no-console
main().catch(console.error)
