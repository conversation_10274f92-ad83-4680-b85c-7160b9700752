const path = require('path')
const fs = require('fs').promises
const webpack = require('webpack')
const { DefinePlugin } = require('webpack')

const FRONTEND_ROOT = path.resolve(__dirname, '../')
const OUTPUT_DIR = path.resolve(__dirname, './src/entrypoint/webview/')
const TS_CONFIG_PATH = path.join(__dirname, 'tsconfig.json')

console.log('Generating webview-provider bundle...')

class CleanupPlugin {
    apply(compiler) {
        compiler.hooks.afterEmit.tapAsync(
            'CleanupPlugin',
            async (compilation, callback) => {
                try {
                    const providerFilePath = path.join(
                        OUTPUT_DIR,
                        'injectProvider.js'
                    )
                    await fs
                        .unlink(providerFilePath + '.LICENSE.txt')
                        .catch(() => {
                            console.error(
                                'Error cleaning up webview-provider bundle'
                            )
                        })

                    const providerContent = await fs.readFile(
                        providerFilePath,
                        'utf8'
                    )

                    await fs.unlink(providerFilePath).catch(() => {
                        console.error(
                            'Error cleaning up webview-provider bundle'
                        )
                    })

                    const base64Content = Buffer.from(
                        providerContent,
                        'utf8'
                    ).toString('base64')

                    // language=TypeScript
                    const tsContent = `export const appBrowserProviderScript: string = Buffer.from(
    '${base64Content}',
    'base64'
).toString('utf8')
`
                    await fs.writeFile(
                        path.join(OUTPUT_DIR, 'appBrowserProviderScript.ts'),
                        tsContent
                    )
                } catch (e) {
                    console.error('Error creating webview-provider bundle', e)
                }
                callback()
            }
        )
    }
}

const config = {
    mode: 'production',
    entry: {
        injectProvider: path.join(
            FRONTEND_ROOT,
            'mobile/src/entrypoint/webview/index.ts'
        ),
    },
    module: {
        rules: [
            {
                test: /\.tsx?$/,
                loader: 'esbuild-loader',
                options: {
                    target: 'esnext',
                    tsconfig: TS_CONFIG_PATH,
                    jsx: 'automatic',
                },
            },
        ],
    },
    resolve: {
        extensions: ['.web.ts', '.web.js', '.ts', '.js'],
        alias: {
            'react-native$': 'react-native-web',
            '@sentry/node': false,
            '@intercom/intercom-react-native': false,
            'expo-notifications': false,
        },
        fallback: {
            '@intercom/intercom-react-native': false,
            'expo-notifications': false,
            crypto: false,
        },
    },
    output: {
        filename: '[name].js',
        path: OUTPUT_DIR,
    },
    optimization: {
        moduleIds: 'deterministic',
        chunkIds: 'deterministic',
    },
    plugins: [
        new CleanupPlugin(),
        new DefinePlugin({
            'process.env': {},
        }),
        new webpack.ProvidePlugin({
            process: 'process/browser',
        }),
        new DefinePlugin({
            __DEV__: false,
        }),
    ],
    ignoreWarnings: [
        /does not match its "include" patterns/,
        /export 'PermissionsAndroid' .* was not found in 'react-native'/,
    ],
}

module.exports = config
