{"name": "mobile", "version": "1.0.0", "private": true, "scripts": {"eas-build-pre-install": "corepack enable && yarn set version $(cat ../../package.json | sed -n 's/.*packageManager.*yarn@\\([^\"]*\\).*/\\1/p')", "android": "yarn build:provider && expo run:android", "build:android": "yarn build:provider && eas build --platform android", "build:ios": "yarn build:provider && eas build --platform ios", "build:local:dev:android": "yarn build:provider && yarn dlx eas-cli build --profile development --platform android --local --non-interactive --output ./zeal.development.apk", "build:local:dev:ios": "yarn build:provider && yarn dlx eas-cli build --profile development --platform ios --local --non-interactive --output ./zeal.development.ipa", "build:local:prod:android": "yarn build:provider && yarn dlx eas-cli build --profile production --platform android --local --non-interactive --output ./zeal.production.$(git rev-parse --short HEAD).aab", "build:local:prod:ios": "yarn build:provider && yarn dlx eas-cli build --profile production --platform ios --local --non-interactive --output ./zeal.production.$(git rev-parse --short HEAD).ipa", "build:provider": "webpack", "watch:provider": "webpack --watch --progress", "ios": "yarn build:provider && expo run:ios", "lint": "yarn eslint --cache --cache-location ../../.eslintcache/mobile.cache --max-warnings 0 ./src", "prebuild": "expo prebuild --clean", "start": "npm-run-all --parallel watch:provider expo:start", "expo:start": "expo start --dev-client --clear", "run-ts": "ts-node --compiler-options '{\"types\": [\"node\"],\"module\": \"commonjs\", \"isolatedModules\": false}'", "check-deps": "yarn run-ts ./scripts/check-monorepo-deps.ts"}, "lint-staged": {"*.{js,jsx,ts,tsx,mdx}": ["eslint --ignore-pattern '!.storybook' --max-warnings 0"]}, "dependencies": {"@expo/config-plugins": "8.0.8", "@formatjs/intl-datetimeformat": "6.12.3", "@formatjs/intl-getcanonicallocales": "2.3.0", "@formatjs/intl-locale": "3.4.5", "@formatjs/intl-numberformat": "8.10.1", "@formatjs/intl-pluralrules": "5.2.12", "@intercom/intercom-react-native": "8.3.0", "@json-rpc-tools/utils": "1.7.6", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "11.3.1", "@react-native-firebase/app": "20.4.0", "@react-native-firebase/messaging": "20.4.0", "@sentry/react-native": "5.35.0", "@sumsub/react-native-mobilesdk-module": "1.33.1", "@tanstack/react-query": "5.51.15", "@walletconnect/react-native-compat": "2.14.0", "@walletconnect/web3wallet": "1.11.2", "@web3modal/wagmi-react-native": "2.0.0", "@zeal/api": "workspace:*", "@zeal/domains": "workspace:*", "@zeal/react-native-packages": "workspace:*", "@zeal/toolkit": "workspace:*", "@zeal/uikit": "workspace:*", "axios": "0.27.2", "buffer": "6.0.3", "cbor": "9.0.1", "cbor-rn-prereqs": "9.0.0", "crypto-browserify": "3.12.0", "expo": "51.0.31", "expo-application": "5.9.1", "expo-blur": "13.0.2", "expo-build-properties": "0.12.5", "expo-camera": "15.0.15", "expo-clipboard": "6.0.3", "expo-constants": "16.0.2", "expo-crypto": "13.0.2", "expo-dev-client": "4.0.25", "expo-font": "12.0.9", "expo-haptics": "13.0.1", "expo-image": "1.12.15", "expo-linear-gradient": "13.0.2", "expo-linking": "6.3.1", "expo-localization": "15.0.3", "expo-navigation-bar": "3.0.7", "expo-notifications": "0.28.19", "expo-secure-store": "13.0.2", "expo-splash-screen": "0.27.5", "expo-status-bar": "1.12.1", "firebase-admin": "12.4.0", "isows": "1.0.3", "lottie-react-native": "6.7.0", "node-libs-browser": "2.2.1", "react": "18.2.0", "react-intl": "6.2.10", "react-native": "0.74.5", "react-native-aes-crypto": "3.0.1", "react-native-appsflyer": "6.16.2", "react-native-check-version": "1.1.1", "react-native-gesture-handler": "2.16.1", "react-native-get-random-values": "1.11.0", "react-native-in-app-review": "4.3.5", "react-native-modal": "13.0.1", "react-native-play-install-referrer": "1.1.9", "react-native-reanimated": "3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-svg": "15.2.0", "react-native-test-flight": "1.1.0", "react-native-webview": "13.8.6", "readable-stream": "3.6.2", "semver": "7.6.3", "text-encoding": "0.7.0", "viem": "2.18.5", "wagmi": "2.12.1"}, "devDependencies": {"@babel/core": "7.24.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.0.0", "@babel/plugin-proposal-optional-chaining": "7.0.0", "@babel/plugin-transform-arrow-functions": "7.0.0", "@babel/plugin-transform-shorthand-properties": "7.0.0", "@babel/plugin-transform-template-literals": "7.0.0", "@types/node": "20.11.29", "@types/react": "18.2.79", "@types/semver": "7", "@types/text-encoding": "0.0.39", "@walletconnect/jsonrpc-types": "1.0.3", "eslint": "8.3.0", "lint-staged": "15.2.2", "npm-run-all": "4.1.5", "ts-node": "10.9.2", "typescript": "5.5.3", "webpack": "5.74.0", "webpack-cli": "4.10.0"}, "installConfig": {"hoistingLimits": "workspaces"}}