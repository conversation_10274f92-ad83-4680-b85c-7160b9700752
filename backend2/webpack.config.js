const webpack = require('webpack')
const path = require('path')
const {
    EnvironmentPlugin,
    SourceMapDevToolPlugin,
    DefinePlugin,
} = require('webpack')
const { merge } = require('webpack-merge')

const ZEAL_ENV = process.env.ZEAL_ENV || 'local'
const ZEAL_APP_VERSION = require(
    path.resolve(__dirname, '../frontend/wallet/manifest.json')
).version

console.log(`We're in ${ZEAL_ENV} mode`)

const BACKEND_ROOT = path.resolve(__dirname, '.')
const TS_CONFIG_PATH = path.join(BACKEND_ROOT, './tsconfig.json')

const common = {
    entry: {
        main: 'src/index.ts',
        notificationsSender:
            'src/domains/Notifications/services/notificationsSender.ts',
    },
    target: 'node',
    externals: {
        sqlite3: 'commonjs sqlite3',
    },
    experiments: {
        topLevelAwait: true,
    },
    plugins: [
        new webpack.optimize.LimitChunkCountPlugin({
            maxChunks: 1,
        }),
        new DefinePlugin({
            __DEV__: process.env.NODE_ENV !== 'production',
        }),
        new EnvironmentPlugin({
            ZEAL_ENV,
            ZEAL_APP_VERSION,
        }),
    ],
    module: {
        rules: [
            {
                // Match js, jsx, ts & tsx files
                test: /\.ts$/,
                exclude: /node_modules/,
                loader: 'esbuild-loader',
                options: {
                    // JavaScript version to compile to
                    target: 'es2022',
                    tsconfig: TS_CONFIG_PATH,
                    jsx: 'automatic',
                },
            },
        ],
    },
    resolve: {
        extensions: ['.ts', '.js'],
        alias: {
            react: false,
            'react-native': false,
            'expo-constants': false,
            'expo-modules-core': false,
            'expo-notifications': false,
            'react-native-reanimated': false,
            '@sentry/react-native': false,
            '@sentry/react': false,
            '@intercom/intercom-react-native': false,
            '@ledgerhq/hw-app-eth': false,
            '@ledgerhq/hw-transport-webhid': false,
            '@metamask/eth-sig-util': false,
            'expo-local-authentication': false,
            'expo-secure-store': false,

            '@zeal/assets': path.join(
                BACKEND_ROOT,
                '../frontend/mobile/assets'
            ),
            src: path.join(BACKEND_ROOT, './src'),
        },
        fallback: {
            '@intercom/intercom-react-native': false,
            'expo-notifications': false,
            'expo-local-authentication': false,
            'expo-secure-store': false,
        },
    },
    output: {
        devtoolNamespace: 'Zeal backend',
        clean: true,
    },
    ignoreWarnings: [/does not match its "include" patterns/],
}

let config = null

switch (ZEAL_ENV) {
    case 'local':
        config = merge(common, {
            mode: 'development',
            devtool: 'inline-source-map',
            watch: true,
            watchOptions: {
                ignored: '**/node_modules',
            },
            plugins: [new SourceMapDevToolPlugin({})],
            stats: {
                warnings: false,
            },
        })
        break

    case 'production':
        config = merge(common, {
            mode: 'production',
            devtool: 'source-map',
            plugins: [
                new SourceMapDevToolPlugin({
                    filename: '[file].map',
                    module: true,
                    noSources: false,
                    columns: true,
                }),
            ],
        })
        break

    case 'development':
        config = merge(common, {
            mode: 'production',
            devtool: 'source-map',
            plugins: [
                new SourceMapDevToolPlugin({
                    filename: '[file].map',
                    module: true,
                    noSources: false,
                    columns: true,
                }),
            ],
        })
        break

    default:
        throw new Error(`Unknown ZEAL_ENV: ${ZEAL_ENV}`)
}

module.exports = config
