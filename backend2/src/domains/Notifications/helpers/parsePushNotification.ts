import { notReachable } from '@zeal/toolkit'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { keys, values } from '@zeal/toolkit/Object'
import {
    failure,
    groupByType,
    oneOf,
    Result,
    success,
} from '@zeal/toolkit/Result'
import * as Address from '@zeal/toolkit/Web3/address'

import { NULL_ADDRESS } from '@zeal/domains/Address/constants'
import {
    CASHBACK_CURRENCY,
    GNOSIS_PAY_CASHBACK_DISTRIBUTION_ADDRESS,
} from '@zeal/domains/Card/domains/Cashback/constants'
import {
    MONERIUM_V1_TOKENS,
    MONERIUM_V2_TOKENS,
} from '@zeal/domains/Currency/constants'
import { ERC20TransferLog, SendTransaction } from '@zeal/domains/RPCRequest'
import { IncludedBlock } from '@zeal/domains/RPCRequest/api/fetchBlockInfo'

import { IntercomWebhookEvent } from 'src/domains/Intercom'
import {
    AddressPushNotification,
    BlockPushNotification,
    CardCashbackRewardPushNotification,
    CardPushNotification,
    CardRefundPushNotification,
    CardSpendPushNotification,
    CardTopupPushNotification,
    IntercomReplyPushNotification,
    PushNotificationToken,
} from 'src/domains/Notifications'
import {
    selectPushNotificationTokensForAddress,
    selectPushNotificationTokensForInstallationId,
} from 'src/domains/Notifications/helpers/selectPushNotificationTokens'
import { DB } from 'src/toolkit/sqlite'

export const parsePushNotificationsForBlock = async (
    db: DB,
    block: IncludedBlock
): Promise<BlockPushNotification[]> => {
    const sendTransactions = block.reciepts.filter(
        (trx): trx is SendTransaction => {
            switch (trx.type) {
                case 'transaction':
                    return true
                case 'contract_deploy':
                    return false
                /* istanbul ignore next */
                default:
                    return notReachable(trx)
            }
        }
    )

    const participants = sendTransactions.reduce(
        (memo, trx) => {
            const from = memo[trx.from] || new Set()
            memo[trx.from] = from.add(trx.transactionHash)
            const to = memo[trx.to] || new Set()
            memo[trx.to] = to.add(trx.to)
            return trx.logs.reduce((result, log) => {
                switch (log.type) {
                    case 'unknown':
                    case 'added_owner':
                    case 'approval':
                    case 'account_deployed':
                    case 'threshold_updated':
                    case 'set_allowance':
                    case 'enable_module':
                    case 'disable_module':
                    case 'safe_module_transaction':
                    case 'safe_received':
                    case 'user_operation_event':
                    case 'user_operation_revert_reason':
                        return result
                    case 'erc20_transfer':
                        const fromSet = result[log.from] || new Set()
                        fromSet.add(trx.transactionHash)
                        result[log.from] = fromSet
                        const toSet = result[log.to] || new Set()
                        toSet.add(trx.transactionHash)
                        result[log.to] = toSet
                        return result
                    /* istanbul ignore next */
                    default:
                        return notReachable(log)
                }
            }, memo)
        },
        {} as Record<Address.Address, Set<Hexadecimal>>
    ) // address to trx hash

    const pushNotificationTokens = await selectPushNotificationTokensForAddress(
        db,
        keys(participants)
    )
    return pushNotificationTokens.reduce((memo, token) => {
        const transactionsForPushToken = [...participants[token.address]]
            .map((trxHash) =>
                sendTransactions.find((trx) => trx.transactionHash === trxHash)
            )
            .filter((trx) => !!trx)
        const pushNotificationResults = transactionsForPushToken.map(
            (trx): Result<unknown, BlockPushNotification> => {
                switch (token.type) {
                    case 'bank_transfer':
                        return failure({
                            type: 'bank_transfer_not_supported_yet',
                        })
                    case 'card_payment':
                        return parseCardPushNotification({ token, trx, block })
                    case 'token_transfer':
                        return parseAddressTransaction(token, trx)
                    /* istanbul ignore next */
                    default:
                        return notReachable(token.type)
                }
            }
        )
        const [_, notifications] = groupByType(pushNotificationResults)
        return [...memo, ...notifications]
    }, [] as BlockPushNotification[])
}

export const parsePushNotificationsForIntercomWebhookEvent = async ({
    event,
    db,
}: {
    db: DB
    event: IntercomWebhookEvent
}): Promise<IntercomReplyPushNotification[]> => {
    const tokens = await selectPushNotificationTokensForInstallationId({
        db,
        installationId: event.installationId,
    })

    const notifications = values(
        tokens.reduce(
            (acc, token) => {
                parseIntercomWebhookEventPushNotification({
                    event,
                    token,
                }).tap((notification) => {
                    acc[token.token] = notification
                })

                return acc
            },
            {} as Record<string, IntercomReplyPushNotification>
        )
    )

    return notifications
}

const GNOSIS_PAY_CARD_SPEND_CONTROLLER_ADDRESS = Address.staticFromString(
    '0x4822521e6135cd2599199c83ea35179229a172ee'
)

const COW_SWAP_SETTLEMENT_PROTOCOL = Address.staticFromString(
    '0x9008d19f58aabd9ed0d60971565aa8510560ab41'
)

const parseCardPushNotification = ({
    token,
    trx,
    block,
}: {
    token: PushNotificationToken
    trx: SendTransaction
    block: IncludedBlock
}): Result<unknown, CardPushNotification> =>
    oneOf(trx, [
        parseCardSpendNotification({ token, trx, block }),
        parseCardCashbackDepositNotification(token, trx),
        parseCardTopupNotification(token, trx),
        parseCardRefundNotification(token, trx),
    ])

const parseCardCashbackDepositNotification = (
    token: PushNotificationToken,
    trx: SendTransaction
): Result<unknown, CardCashbackRewardPushNotification> => {
    const log = trx.logs.find((log): log is ERC20TransferLog => {
        switch (log.type) {
            case 'unknown':
            case 'added_owner':
            case 'approval':
            case 'account_deployed':
            case 'threshold_updated':
            case 'set_allowance':
            case 'enable_module':
            case 'disable_module':
            case 'safe_module_transaction':
            case 'safe_received':
            case 'user_operation_event':
            case 'user_operation_revert_reason':
                return false
            case 'erc20_transfer':
                return (
                    log.from === GNOSIS_PAY_CASHBACK_DISTRIBUTION_ADDRESS &&
                    log.to === token.address &&
                    log.currencyId === CASHBACK_CURRENCY.id
                )
            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })

    return log
        ? success({
              type: 'card_cashback_reward',
              currencyId: log.currencyId,
              amount: log.amount,
              token,
          })
        : failure({ type: 'not_a_card_topup_send' })
}

const parseCardRefundNotification = (
    token: PushNotificationToken,
    trx: SendTransaction
): Result<unknown, CardRefundPushNotification> => {
    const log = trx.logs.find((log): log is ERC20TransferLog => {
        switch (log.type) {
            case 'unknown':
            case 'added_owner':
            case 'approval':
            case 'account_deployed':
            case 'threshold_updated':
            case 'set_allowance':
            case 'enable_module':
            case 'disable_module':
            case 'safe_module_transaction':
            case 'safe_received':
            case 'user_operation_event':
            case 'user_operation_revert_reason':
                return false
            case 'erc20_transfer':
                return (
                    log.to === token.address &&
                    log.from === GNOSIS_PAY_CARD_SPEND_CONTROLLER_ADDRESS
                )
            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })
    return log
        ? success({
              type: 'card_refund',
              currencyId: log.currencyId,
              amount: log.amount,
              token,
          })
        : failure({ type: 'not_a_card_refund' })
}

const parseCardTopupNotification = (
    token: PushNotificationToken,
    trx: SendTransaction
): Result<unknown, CardTopupPushNotification> => {
    const log = trx.logs.find((log): log is ERC20TransferLog => {
        switch (log.type) {
            case 'unknown':
            case 'added_owner':
            case 'approval':
            case 'account_deployed':
            case 'threshold_updated':
            case 'set_allowance':
            case 'enable_module':
            case 'disable_module':
            case 'safe_module_transaction':
            case 'safe_received':
            case 'user_operation_event':
            case 'user_operation_revert_reason':
                return false
            case 'erc20_transfer':
                return (
                    log.currencyId !== CASHBACK_CURRENCY.id &&
                    log.to === token.address &&
                    log.from !== GNOSIS_PAY_CARD_SPEND_CONTROLLER_ADDRESS &&
                    // TODO: @max this will filter all cow swaps
                    //  but we can predict earn accounts based on owner but this will be async.
                    //  IMHO we can drop this for now
                    log.from !== COW_SWAP_SETTLEMENT_PROTOCOL // recharge
                )
            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })
    return log
        ? success({
              type: 'card_topup',
              currencyId: log.currencyId,
              amount: log.amount,
              token,
          })
        : failure({ type: 'not_a_card_topup_send' })
}

const parseCardSpendNotification = ({
    token,
    trx,
    block,
}: {
    token: PushNotificationToken
    trx: SendTransaction
    block: IncludedBlock
}): Result<unknown, CardSpendPushNotification> => {
    const log = trx.logs.find((log): log is ERC20TransferLog => {
        switch (log.type) {
            case 'unknown':
            case 'added_owner':
            case 'approval':
            case 'account_deployed':
            case 'threshold_updated':
            case 'set_allowance':
            case 'enable_module':
            case 'disable_module':
            case 'safe_module_transaction':
            case 'safe_received':
            case 'user_operation_event':
            case 'user_operation_revert_reason':
                return false
            case 'erc20_transfer':
                return (
                    log.from === token.address &&
                    log.to === GNOSIS_PAY_CARD_SPEND_CONTROLLER_ADDRESS
                )
            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })
    // TODO :: @max-ter we can have more then 1 transfer, like monerium v1 and v2 tokens,
    //  but it should be fine to find first.
    //  delete this if no problem for while, otherwise we maybe want to do more strict parsing and reporting
    return log
        ? success({
              type: 'card_spend',
              amount: log.amount,
              currencyId: log.currencyId,
              blockNumber: block.blockNumber,
              blockTimestamp: block.timestamp,
              transactionHash: trx.transactionHash,
              token,
          })
        : failure({ type: 'not_a_spend_trx', value: trx })
}

const parseIntercomWebhookEventPushNotification = ({
    event,
    token,
}: {
    event: IntercomWebhookEvent
    token: PushNotificationToken
}): Result<unknown, IntercomReplyPushNotification> => {
    switch (event.type) {
        case 'admin_replied_event':
            return success({
                type: 'intercom_reply_notification',
                reply: event.reply,
                token,
            })

        default:
            return notReachable(event.type)
    }
}

const parseAddressTransaction = (
    token: PushNotificationToken,
    trx: SendTransaction
): Result<unknown, AddressPushNotification> => {
    const logs = trx.logs.filter((log): log is ERC20TransferLog => {
        switch (log.type) {
            case 'unknown':
            case 'added_owner':
            case 'approval':
            case 'account_deployed':
            case 'threshold_updated':
            case 'set_allowance':
            case 'enable_module':
            case 'disable_module':
            case 'safe_module_transaction':
            case 'safe_received':
            case 'user_operation_event':
            case 'user_operation_revert_reason':
                return false
            case 'erc20_transfer':
                // In some transactions Monerium produce 2 tokens so we should filter one of them.
                // We filter v1 (legacy one) since it's not logged for all transactions (mint)
                return !MONERIUM_V1_TOKENS.has(log.currencyId)

            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })
    // @max in one transaction you can have many transfers, do we want separate notifications for each transfer? I don't think so
    // this case should be classified better like swaps, withdraw etc
    if (logs.length !== 1) {
        return failure({ type: 'not_single_trasfer_trx' })
    }
    const [log] = logs
    switch (true) {
        case log.from === log.to: {
            return failure({ type: 'self_transfer' })
        }

        case log.from === NULL_ADDRESS &&
            log.to === token.address &&
            MONERIUM_V2_TOKENS.has(log.currencyId): {
            return success({
                type: 'address_receive_monerium_bank_transfer_notification',
                amount: log.amount,
                currencyId: log.currencyId,
                transactionHash: trx.transactionHash,
                token,
            })
        }

        case log.from === token.address: {
            return success({
                type: 'address_send_token_notification',
                amount: log.amount,
                currencyId: log.currencyId,
                token,
            })
        }

        case log.to === token.address: {
            return success({
                type: 'address_receive_token_notification',
                amount: log.amount,
                currencyId: log.currencyId,
                transactionHash: trx.transactionHash,
                token,
            })
        }
        default:
            return failure({ type: 'not_a_single_token_transfer' })
    }
}
